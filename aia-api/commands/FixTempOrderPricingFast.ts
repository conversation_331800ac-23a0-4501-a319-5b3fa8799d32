import { BaseCommand } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'

export default class FixTempOrderPricingFast extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'temp-orders:fix-pricing-fast'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Fast fix for missing pricing structure using direct database updates'

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  public async run() {
    this.logger.info('🚀 Starting FAST temp order pricing fix...')

    try {
      // Get count of orders without pricing
      const countResult = await Database.rawQuery(`
        SELECT COUNT(*) as total
        FROM orders
        WHERE status = 'Pending' AND meta->>'pricing' IS NULL
      `)
      const totalOrders = parseInt(countResult.rows[0].total)

      this.logger.info(`📊 Found ${totalOrders} temp orders without pricing`)

      if (totalOrders === 0) {
        this.logger.success('✅ All temp orders already have pricing structure!')
        return
      }

      // Update orders with charges but no temp_items (simple case)
      this.logger.info('🔄 Updating orders with charges only...')
      const simpleUpdateResult = await Database.rawQuery(`
        UPDATE orders
        SET meta = meta::jsonb || jsonb_build_object(
          'pricing', jsonb_build_object(
            'subtotal', 0,
            'modifiersTotal', 0,
            'chargesTotal', COALESCE(
              (SELECT SUM((value)::numeric)
               FROM jsonb_each_text(meta::jsonb->'charges')
               WHERE meta->'charges' IS NOT NULL),
              0
            ),
            'total', COALESCE(
              (SELECT SUM((value)::numeric)
               FROM jsonb_each_text(meta::jsonb->'charges')
               WHERE meta->'charges' IS NOT NULL),
              0
            ),
            'invoiceAmount', COALESCE(
              (SELECT SUM((value)::numeric)
               FROM jsonb_each_text(meta::jsonb->'charges')
               WHERE meta->'charges' IS NOT NULL),
              0
            ),
            'currency', 'KES'
          )
        )
        WHERE status = 'Pending'
        AND meta->>'pricing' IS NULL
        AND (meta->>'temp_items' IS NULL OR meta->>'temp_items' = '{}')
      `)

      this.logger.info(`✅ Updated ${simpleUpdateResult.rowCount} orders with charges only`)

      // Handle orders with no charges and no temp_items (default case)
      this.logger.info('🔄 Updating orders with no charges and no temp_items...')
      const defaultUpdateResult = await Database.rawQuery(`
        UPDATE orders
        SET meta = meta::jsonb || jsonb_build_object(
          'pricing', jsonb_build_object(
            'subtotal', 0,
            'modifiersTotal', 0,
            'chargesTotal', 0,
            'total', 0,
            'invoiceAmount', 0,
            'currency', 'KES'
          )
        )
        WHERE status = 'Pending'
        AND meta->>'pricing' IS NULL
        AND meta->'charges' IS NULL
        AND meta->'temp_items' IS NULL
      `)

      this.logger.info(`✅ Updated ${defaultUpdateResult.rowCount} orders with default pricing`)

      // Handle orders with temp_items (complex case)
      this.logger.info('🔄 Processing orders with temp_items...')

      const ordersWithItems = await Database.rawQuery(`
        SELECT id, meta->'temp_items' as temp_items, meta->'charges' as charges
        FROM orders
        WHERE status = 'Pending'
        AND meta->>'pricing' IS NULL
        AND (
          (meta->>'temp_items' IS NOT NULL AND meta->>'temp_items' != '{}')
          OR (meta->'charges' IS NULL AND meta->>'temp_items' IS NOT NULL)
        )
      `)

      let processedWithItems = 0
      const batchSize = 100

      for (let i = 0; i < ordersWithItems.rows.length; i += batchSize) {
        const batch = ordersWithItems.rows.slice(i, i + batchSize)

        for (const order of batch) {
          try {
            const tempItems = order.temp_items || {}
            const charges = order.charges || {}

            let subtotal = 0

            // Calculate subtotal from temp_items
            for (const [productId, itemData] of Object.entries(tempItems)) {
              const quantity = typeof itemData === 'object' ? itemData.quantity : itemData

              // Get product price
              const productResult = await Database.rawQuery(
                `
                SELECT price FROM products WHERE id = ?
              `,
                [productId]
              )

              if (productResult.rows.length > 0) {
                const price = parseFloat(productResult.rows[0].price) || 0
                subtotal += price * (parseInt(quantity) || 0)
              }
            }

            // Calculate charges total
            let chargesTotal = 0
            for (const charge of Object.values(charges)) {
              chargesTotal += parseFloat(charge as string) || 0
            }

            const total = subtotal + chargesTotal

            // Update order with pricing
            await Database.rawQuery(
              `
              UPDATE orders
              SET meta = meta::jsonb || jsonb_build_object(
                'pricing', jsonb_build_object(
                  'subtotal', ?,
                  'modifiersTotal', 0,
                  'chargesTotal', ?,
                  'total', ?,
                  'invoiceAmount', ?,
                  'currency', 'KES'
                )
              )
              WHERE id = ?
            `,
              [subtotal, chargesTotal, total, total, order.id]
            )

            processedWithItems++

            if (processedWithItems % 50 === 0) {
              this.logger.info(
                `✅ Processed ${processedWithItems}/${ordersWithItems.rows.length} orders with items`
              )
            }
          } catch (error) {
            this.logger.error(`❌ Failed to process order ${order.id}: ${error.message}`)
          }
        }
      }

      // Final verification
      const finalCountResult = await Database.rawQuery(`
        SELECT COUNT(*) as remaining
        FROM orders
        WHERE status = 'Pending' AND meta->>'pricing' IS NULL
      `)
      const remaining = parseInt(finalCountResult.rows[0].remaining)

      // Summary
      this.logger.info('📈 Migration Summary:')
      this.logger.info(`   Total orders found: ${totalOrders}`)
      this.logger.info(`   Orders with charges only: ${simpleUpdateResult.rowCount}`)
      this.logger.info(`   Orders with temp_items: ${processedWithItems}`)
      this.logger.info(`   Remaining without pricing: ${remaining}`)

      if (remaining === 0) {
        this.logger.success('🎉 ALL temp orders now have proper pricing structure!')
      } else {
        this.logger.warning(`⚠️  ${remaining} orders still need pricing.`)
      }
    } catch (error) {
      this.logger.error('💥 Migration failed:', error.message)
      this.logger.error(error.stack)
      process.exit(1)
    }
  }
}
