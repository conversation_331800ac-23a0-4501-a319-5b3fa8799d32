import { BaseCommand } from '@adonisjs/core/build/standalone'

export default class TestQRCode extends BaseCommand {
  public static commandName = 'test:qr-code'
  public static description = 'Test QR code generation and processing'

  public async run() {
    this.logger.info('🧪 Testing QR Code System...')

    try {
      // Import services dynamically to avoid IoC issues
      const { default: QRCodeGenerationService } = await import(
        '../app/Services/QRCodeGenerationService'
      )
      const { default: QRCodeProcessingService } = await import(
        '../app/Services/QRCodeProcessingService'
      )

      const qrGenerationService = new QRCodeGenerationService()
      const qrProcessingService = new QRCodeProcessingService()

      // Test data
      const testData = {
        vendorId: '01jrsrd52yshagpjyhmfqvdr5c',
        branchId: '01jrsrd530w37nm0c156af0v48',
        sectionId: '01jvahxmm0rp82tnhfpzze202q',
        lotId: '01jvspekgey7dg5dyhsy0qs8nc',
        tableNumber: 'Test Table 3',
      }

      this.logger.info('📝 Test data:', testData)

      // Test 1: Generate QR code
      this.logger.info('🔄 Generating QR code...')
      const qrCode = await qrGenerationService.generateTableQRCode(testData)

      this.logger.success('✅ QR code generated successfully!')
      this.logger.info('QR Code ID:', qrCode.id)
      this.logger.info('QR Code URL:', qrCode.qrCodeUrl)
      this.logger.info('Table Number:', qrCode.tableNumber)
      this.logger.info('Is Active:', qrCode.isActive)

      // Test 2: Validate QR code
      this.logger.info('🔄 Validating QR code...')
      const isValid = await qrProcessingService.validateQRCode(qrCode.id)
      this.logger.info('QR Code Valid:', isValid)

      // Test 3: Process QR code scan
      this.logger.info('🔄 Processing QR code scan...')
      const tableContext = await qrProcessingService.processQRCodeScan(qrCode.id)

      this.logger.success('✅ QR code scan processed successfully!')
      this.logger.info('Table Context:', {
        qrCodeId: tableContext.qrCodeId,
        tableNumber: tableContext.tableNumber,
        vendor: tableContext.vendor.name,
        branch: tableContext.branch.name,
        section: tableContext.section.name,
        scanCount: tableContext.scanMetadata.scanCount,
      })

      // Test 4: Test deactivation
      this.logger.info('🔄 Testing QR code deactivation...')
      await qrGenerationService.deactivateQRCode(qrCode.id)

      const isValidAfterDeactivation = await qrProcessingService.validateQRCode(qrCode.id)
      this.logger.info('QR Code Valid After Deactivation:', isValidAfterDeactivation)

      // Test 5: Test reactivation
      this.logger.info('🔄 Testing QR code reactivation...')
      await qrGenerationService.activateQRCode(qrCode.id)

      const isValidAfterReactivation = await qrProcessingService.validateQRCode(qrCode.id)
      this.logger.info('QR Code Valid After Reactivation:', isValidAfterReactivation)

      // Test 6: Test analytics
      this.logger.info('🔄 Testing analytics...')
      const analytics = await qrGenerationService.getQRCodeAnalytics(testData.vendorId)

      this.logger.success('✅ Analytics retrieved successfully!')
      this.logger.info('Analytics:', {
        totalQRCodes: analytics.totalQRCodes,
        activeQRCodes: analytics.activeQRCodes,
        totalScans: analytics.totalScans,
        averageScansPerTable: analytics.averageScansPerTable,
      })

      this.logger.success('🎉 All QR code tests passed!')

      return qrCode
    } catch (error) {
      this.logger.error('❌ QR code test failed:', error.message)
      throw error
    }
  }
}
