import { BaseCommand } from '@adonisjs/core/build/standalone'
import Invoice from 'App/Models/Invoice'
import Order from 'App/Models/Order'

export default class FixZeroAmountInvoices extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'fix:zero-invoices'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Fix invoices with zero amounts by calculating correct amounts from order items'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest` 
     * after creating the command to update the manifest file.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call 
     * `node ace generate:manifest` after creating the command to update the manifest file.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info('🚀 Starting zero-amount invoices fix...')

    try {
      // Find all invoices with zero amount
      const zeroInvoices = await Invoice.query()
        .where('amount', 0)
        .preload('order', (orderQuery) => {
          orderQuery.preload('items', (itemQuery) => {
            itemQuery.preload('product')
          })
        })
        .orderBy('created_at', 'desc')

      this.logger.info(`📊 Found ${zeroInvoices.length} invoices with zero amount`)

      if (zeroInvoices.length === 0) {
        this.logger.success('✅ No zero-amount invoices found. Nothing to fix.')
        return
      }

      let fixedCount = 0
      let skippedCount = 0

      // Process each zero-amount invoice
      for (const invoice of zeroInvoices) {
        this.logger.info(`🔍 Processing invoice ${invoice.number} (${invoice.id})`)

        try {
          // Calculate correct amount using the order's calculateTotalAmount method
          const correctAmount = await invoice.order.calculateTotalAmount()

          if (correctAmount > 0) {
            // Update invoice with correct amount
            invoice.amount = correctAmount
            await invoice.save()

            this.logger.success(`✅ Fixed: ${invoice.number} - Amount updated from 0 to ${correctAmount}`)
            fixedCount++
          } else {
            this.logger.warning(`⚠️  Skipped: ${invoice.number} - Could not calculate amount (order may have no items)`)
            skippedCount++
          }
        } catch (error) {
          this.logger.error(`❌ Error processing invoice ${invoice.number}: ${error.message}`)
          skippedCount++
        }
      }

      // Summary
      this.logger.info(`\n📈 Summary:`)
      this.logger.info(`   Fixed: ${fixedCount} invoices`)
      this.logger.info(`   Skipped: ${skippedCount} invoices`)
      this.logger.info(`   Total processed: ${zeroInvoices.length} invoices`)

      // Verify the fix
      const remainingZeroCount = await Invoice.query().where('amount', 0).count('* as total')
      const remainingZero = remainingZeroCount[0].$extras.total

      this.logger.info(`\n🔍 Verification:`)
      this.logger.info(`   Remaining zero-amount invoices: ${remainingZero}`)

      if (remainingZero == 0) {
        this.logger.success('🎉 All zero-amount invoices have been fixed!')
      } else {
        this.logger.warning(`⚠️  ${remainingZero} invoices still have zero amount (likely orders with no items)`)
      }

    } catch (error) {
      this.logger.error(`❌ Error fixing zero-amount invoices: ${error.message}`)
      this.logger.error(error.stack)
    }
  }
}
