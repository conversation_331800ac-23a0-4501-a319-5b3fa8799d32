import { BaseCommand } from '@adonisjs/core/build/standalone'
import Order from 'App/Models/Order'

export default class FixTempOrderPricing extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'temp-orders:fix-pricing'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Fix missing pricing structure for existing temp orders'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest`
     * after creating the command to update the manifest file.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call
     * `node ace generate:manifest` after creating the command to update the manifest file.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info('🔧 Starting temp order pricing fix...')

    try {
      // Find all temp orders without pricing structure
      const tempOrdersWithoutPricing = await Order.query()
        .where('status', 'Pending')
        .whereRaw("meta->>'pricing' IS NULL")

      this.logger.info(`📊 Found ${tempOrdersWithoutPricing.length} temp orders without pricing`)

      if (tempOrdersWithoutPricing.length === 0) {
        this.logger.success('✅ All temp orders already have pricing structure!')
        return
      }

      let processed = 0
      let errors = 0

      // Process orders in batches to avoid memory issues
      const batchSize = 50
      for (let i = 0; i < tempOrdersWithoutPricing.length; i += batchSize) {
        const batch = tempOrdersWithoutPricing.slice(i, i + batchSize)

        this.logger.info(
          `🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(tempOrdersWithoutPricing.length / batchSize)}`
        )

        for (const order of batch) {
          try {
            // Calculate and store pricing for this order
            await order.ensureTempOrderPricing()
            processed++

            if (processed % 10 === 0) {
              this.logger.info(
                `✅ Processed ${processed}/${tempOrdersWithoutPricing.length} orders`
              )
            }
          } catch (error) {
            errors++
            this.logger.error(`❌ Failed to process order ${order.id}: ${error.message}`)
          }
        }
      }

      // Summary
      this.logger.info('📈 Migration Summary:')
      this.logger.info(`   Total orders found: ${tempOrdersWithoutPricing.length}`)
      this.logger.info(`   Successfully processed: ${processed}`)
      this.logger.info(`   Errors: ${errors}`)

      if (errors === 0) {
        this.logger.success('🎉 All temp orders now have proper pricing structure!')
      } else {
        this.logger.warning(`⚠️  ${errors} orders had errors. Check logs above for details.`)
      }

      // Verify the fix
      const remainingWithoutPricing = await Order.query()
        .where('status', 'Pending')
        .whereRaw("meta->>'pricing' IS NULL")
        .count('* as total')

      const remaining = remainingWithoutPricing[0].$extras.total
      this.logger.info(`🔍 Verification: ${remaining} temp orders still without pricing`)

      if (remaining === 0) {
        this.logger.success('✅ Verification passed! All temp orders have pricing.')
      } else {
        this.logger.warning(
          `⚠️  ${remaining} orders still need pricing. You may need to run this command again.`
        )
      }
    } catch (error) {
      this.logger.error('💥 Migration failed:', error.message)
      this.logger.error(error.stack)
      process.exit(1)
    }
  }
}
