import test from 'japa'
import supertest from 'supertest'
import Database from '@ioc:Adonis/Lucid/Database'
import User from 'App/Models/User'
import Vendor from 'App/Models/Vendor'
import Order from 'App/Models/Order'
import DeliveryProviderProfile from 'App/Models/DeliveryProviderProfile'
import Branch from 'App/Models/Branch'
import Product from 'App/Models/Product'
import { DateTime } from 'luxon'

// Mock notification system to capture sent notifications
const mockNotifications: any[] = []
const originalNotify = User.prototype.notify

// Override the notify method to capture notifications
User.prototype.notify = async function (notification: any) {
  mockNotifications.push({
    userId: this.id,
    notificationType: notification.constructor.name,
    notification: notification,
    timestamp: DateTime.now()
  })
  return Promise.resolve()
}

const BASE_URL = `http://${process.env.HOST}:${process.env.PORT}`

test.group('Delivery Provider Notifications - Phase 2A Integration Tests', (group) => {
  let productVendor: Vendor
  let deliveryVendor: Vendor
  let productVendorUser: User
  let deliveryVendorUser: User
  let customer: User
  let admin: User
  let branch: Branch
  let product: Product
  let order: Order
  let deliveryProfile: DeliveryProviderProfile

  group.beforeEach(async () => {
    await Database.beginGlobalTransaction()
    mockNotifications.length = 0 // Clear notifications array

    // Create admin user
    admin = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123',
      role: 'admin'
    })

    // Create customer
    customer = await User.create({
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123'
    })

    // Create product vendor with user
    productVendorUser = await User.create({
      name: 'Product Vendor User',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123'
    })

    productVendor = await Vendor.create({
      name: 'Test Product Vendor',
      email: '<EMAIL>',
      phone: '+**********',
      active: true,
      verificationStatus: 'verified',
      userId: productVendorUser.id,
      address: '123 Product St',
      latitude: 40.7128,
      longitude: -74.0060
    })

    // Create delivery vendor with user
    deliveryVendorUser = await User.create({
      name: 'Delivery Vendor User',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123'
    })

    deliveryVendor = await Vendor.create({
      name: 'Test Delivery Vendor',
      email: '<EMAIL>',
      phone: '+**********',
      active: true,
      verificationStatus: 'verified',
      userId: deliveryVendorUser.id,
      address: '456 Delivery Ave',
      latitude: 40.7589,
      longitude: -73.9851
    })

    // Create delivery provider profile
    deliveryProfile = await DeliveryProviderProfile.create({
      vendorId: deliveryVendor.id,
      verificationStatus: 'verified',
      active: true,
      vehicleTypes: ['car', 'motorcycle'],
      maxDeliveryDistance: 10,
      maxConcurrentOrders: 3,
      workingHours: {
        monday: { start: '09:00', end: '18:00', active: true },
        tuesday: { start: '09:00', end: '18:00', active: true },
        wednesday: { start: '09:00', end: '18:00', active: true },
        thursday: { start: '09:00', end: '18:00', active: true },
        friday: { start: '09:00', end: '18:00', active: true },
        saturday: { start: '10:00', end: '16:00', active: true },
        sunday: { start: '10:00', end: '16:00', active: false }
      }
    })

    // Create branch
    branch = await Branch.create({
      vendorId: productVendor.id,
      name: 'Main Branch',
      details: 'Main product location',
      location: {
        name: 'Test Location',
        coordinates: { lat: 40.7128, lng: -74.0060 }
      },
      phone: '+**********'
    })

    // Create product
    product = await Product.create({
      name: 'Test Product',
      details: 'A test product for delivery',
      price: 25.99,
      vendorId: productVendor.id,
      branchId: branch.id,
      active: true,
      type: 'Product'
    })

    // Create test order
    order = await Order.create({
      ref: 'TEST-ORDER-001',
      vendorId: productVendor.id,
      branchId: branch.id,
      customerId: customer.id,
      totalAmount: 25.99,
      status: 'Confirmed',
      fulfillmentType: 'delivery',
      deliveryAddress: '789 Customer Blvd, Test City',
      deliveryLatitude: 40.7831,
      deliveryLongitude: -73.9712,
      deliveryFee: 5.00,
      estimatedDeliveryTime: 45
    })
  })

  group.afterEach(async () => {
    await Database.rollbackGlobalTransaction()
    mockNotifications.length = 0
  })

  group.after(() => {
    // Restore original notify method
    User.prototype.notify = originalNotify
  })

  test('1. Delivery Assignment Notification Test', async (assert) => {
    // Test that when OrderDeliveryController.assignVendor() assigns a delivery provider,
    // the DeliveryProviderOrderAssigned notification is sent to the correct delivery provider

    const response = await supertest(BASE_URL)
      .post(`/orders/${order.id}/delivery/assign`)
      .send({
        deliveryVendorId: deliveryVendor.id,
        deliveryBranchId: branch.id
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Verify the assignment was successful
    assert.isTrue(response.body.deliveryVendorId === deliveryVendor.id)

    // Check that notification was sent to delivery provider
    const deliveryNotifications = mockNotifications.filter(
      n => n.userId === deliveryVendorUser.id && n.notificationType === 'DeliveryProviderOrderAssigned'
    )

    assert.equal(deliveryNotifications.length, 1, 'Should send one DeliveryProviderOrderAssigned notification')

    const notification = deliveryNotifications[0].notification
    const notificationData = notification.getNotificationData()

    // Verify notification contains all required data
    assert.equal(notificationData.order_id, order.id)
    assert.equal(notificationData.order_ref, order.ref)
    assert.exists(notificationData.pickup_location)
    assert.exists(notificationData.delivery_location)
    assert.exists(notificationData.order_details)
    assert.exists(notificationData.timing)
    assert.exists(notificationData.assignment_expires_at)

    // Verify pickup location data
    assert.equal(notificationData.pickup_location.vendorName, productVendor.name)
    assert.equal(notificationData.pickup_location.address, productVendor.address)
    assert.equal(notificationData.pickup_location.contactPhone, productVendor.phone)

    // Verify delivery location data
    assert.equal(notificationData.delivery_location.customerName, customer.name)
    assert.equal(notificationData.delivery_location.address, order.deliveryAddress)
    assert.equal(notificationData.delivery_location.contactPhone, customer.phone)

    // Verify order details
    assert.equal(notificationData.order_details.deliveryFee, order.deliveryFee)
    assert.exists(notificationData.order_details.totalItems)

    console.log('✅ Delivery Assignment Notification Test passed')
  })

  test('2. Pickup Ready Notification Test', async (assert) => {
    // First assign delivery provider
    await order.merge({
      deliveryVendorId: deliveryVendor.id,
      deliveryStatus: 'assigned'
    }).save()

    // Clear previous notifications
    mockNotifications.length = 0

    // Test that when order status changes to "ready for pickup",
    // the DeliveryProviderPickupReady notification is sent

    const response = await supertest(BASE_URL)
      .put(`/orders/${order.id}/delivery/status`)
      .send({
        status: 'ready_for_pickup'
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Check that pickup ready notification was sent to delivery provider
    const pickupNotifications = mockNotifications.filter(
      n => n.userId === deliveryVendorUser.id && n.notificationType === 'DeliveryProviderPickupReady'
    )

    assert.equal(pickupNotifications.length, 1, 'Should send one DeliveryProviderPickupReady notification')

    const notification = pickupNotifications[0].notification
    const notificationData = notification.getNotificationData()

    // Verify notification contains required data
    assert.equal(notificationData.order_id, order.id)
    assert.equal(notificationData.order_ref, order.ref)
    assert.exists(notificationData.pickup_location)
    assert.exists(notificationData.preparation_completed_at)
    assert.exists(notificationData.pickup_deadline)
    assert.exists(notificationData.verification_code)

    // Verify pickup location data
    assert.equal(notificationData.pickup_location.vendorName, productVendor.name)
    assert.equal(notificationData.pickup_location.contactPhone, productVendor.phone)

    // Verify verification code is generated
    assert.isString(notificationData.verification_code)
    assert.isAtLeast(notificationData.verification_code.length, 6)

    console.log('✅ Pickup Ready Notification Test passed')
  })

  test('3. Application Approved Notification Test', async (assert) => {
    // Create a new delivery provider profile in pending status
    const pendingProfile = await DeliveryProviderProfile.create({
      vendorId: productVendor.id, // Using product vendor to test application
      verificationStatus: 'pending',
      active: false,
      vehicleTypes: ['car'],
      maxDeliveryDistance: 5,
      maxConcurrentOrders: 2
    })

    // Clear previous notifications
    mockNotifications.length = 0

    // Test admin approving the application
    const response = await supertest(BASE_URL)
      .put(`/admin/delivery-provider-applications/${pendingProfile.id}/review`)
      .send({
        status: 'verified',
        notes: 'Application meets all requirements'
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Check that approval notification was sent to vendor
    const approvalNotifications = mockNotifications.filter(
      n => n.userId === productVendorUser.id && n.notificationType === 'DeliveryProviderApplicationApproved'
    )

    assert.equal(approvalNotifications.length, 1, 'Should send one DeliveryProviderApplicationApproved notification')

    const notification = approvalNotifications[0].notification
    const notificationData = notification.getNotificationData()

    // Verify notification contains required data
    assert.equal(notificationData.vendor_id, productVendor.id)
    assert.equal(notificationData.delivery_profile_id, pendingProfile.id)
    assert.exists(notificationData.approval_date)
    assert.equal(notificationData.approved_by, admin.name)
    assert.exists(notificationData.next_steps)
    assert.isArray(notificationData.next_steps)
    assert.exists(notificationData.capabilities_approved)

    console.log('✅ Application Approved Notification Test passed')
  })

  test('4. Application Rejected Notification Test', async (assert) => {
    // Create a new delivery provider profile in pending status
    const pendingProfile = await DeliveryProviderProfile.create({
      vendorId: productVendor.id,
      verificationStatus: 'pending',
      active: false,
      vehicleTypes: ['motorcycle'],
      maxDeliveryDistance: 3,
      maxConcurrentOrders: 1
    })

    // Clear previous notifications
    mockNotifications.length = 0

    // Test admin rejecting the application
    const response = await supertest(BASE_URL)
      .put(`/admin/delivery-provider-applications/${pendingProfile.id}/review`)
      .send({
        status: 'rejected',
        notes: 'Vehicle documentation incomplete'
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Check that rejection notification was sent to vendor
    const rejectionNotifications = mockNotifications.filter(
      n => n.userId === productVendorUser.id && n.notificationType === 'DeliveryProviderApplicationRejected'
    )

    assert.equal(rejectionNotifications.length, 1, 'Should send one DeliveryProviderApplicationRejected notification')

    const notification = rejectionNotifications[0].notification
    const notificationData = notification.getNotificationData()

    // Verify notification contains required data
    assert.equal(notificationData.vendor_id, productVendor.id)
    assert.equal(notificationData.delivery_profile_id, pendingProfile.id)
    assert.exists(notificationData.rejection_date)
    assert.equal(notificationData.rejected_by, admin.name)
    assert.exists(notificationData.rejection_reasons)
    assert.isArray(notificationData.rejection_reasons)
    assert.include(notificationData.rejection_reasons[0], 'Vehicle documentation incomplete')
    assert.exists(notificationData.reapplication_allowed)
    assert.isTrue(notificationData.reapplication_allowed)
    assert.exists(notificationData.improvement_suggestions)
    assert.isArray(notificationData.improvement_suggestions)

    console.log('✅ Application Rejected Notification Test passed')
  })

  test('5. Multi-Stakeholder Notification Test - Complete Delivery Order Workflow', async (assert) => {
    // Test the complete delivery order workflow from order creation to delivery assignment
    // Verify that product vendors receive VendorNewOrder notifications (separate from delivery notifications)
    // Confirm customers receive appropriate notifications at each stage
    // Ensure delivery providers only receive delivery-specific notifications

    // Clear previous notifications
    mockNotifications.length = 0

    // Step 1: Create a new order (simulating VendorOrdersController.store)
    const newOrder = await Order.create({
      ref: 'WORKFLOW-TEST-001',
      vendorId: productVendor.id,
      branchId: branch.id,
      customerId: customer.id,
      totalAmount: 45.99,
      status: 'Confirmed',
      fulfillmentType: 'delivery',
      deliveryAddress: '321 Workflow St, Test City',
      deliveryLatitude: 40.7505,
      deliveryLongitude: -73.9934,
      deliveryFee: 7.50,
      estimatedDeliveryTime: 60
    })

    // Simulate sending product vendor notification (from VendorOrdersController)
    await productVendorUser.notify(new (await import('App/Notifications/Vendor/VendorNewOrder')).default(newOrder))
    await customer.notify(new (await import('App/Notifications/Customer/CustomerNewOrder')).default(newOrder))

    // Step 2: Assign delivery provider
    await supertest(BASE_URL)
      .post(`/orders/${newOrder.id}/delivery/assign`)
      .send({
        deliveryVendorId: deliveryVendor.id,
        deliveryBranchId: branch.id
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Step 3: Update order status to ready for pickup
    await supertest(BASE_URL)
      .put(`/orders/${newOrder.id}/delivery/status`)
      .send({
        status: 'ready_for_pickup'
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Verify notification distribution
    const productVendorNotifications = mockNotifications.filter(n => n.userId === productVendorUser.id)
    const deliveryVendorNotifications = mockNotifications.filter(n => n.userId === deliveryVendorUser.id)
    const customerNotifications = mockNotifications.filter(n => n.userId === customer.id)

    // Product vendor should receive VendorNewOrder notification (not delivery-specific)
    const vendorNewOrderNotifications = productVendorNotifications.filter(
      n => n.notificationType === 'VendorNewOrder'
    )
    assert.equal(vendorNewOrderNotifications.length, 1, 'Product vendor should receive VendorNewOrder notification')

    // Product vendor should NOT receive delivery-specific notifications
    const productVendorDeliveryNotifications = productVendorNotifications.filter(
      n => n.notificationType.includes('DeliveryProvider')
    )
    assert.equal(productVendorDeliveryNotifications.length, 0, 'Product vendor should not receive delivery provider notifications')

    // Delivery vendor should receive delivery-specific notifications only
    const deliveryAssignmentNotifications = deliveryVendorNotifications.filter(
      n => n.notificationType === 'DeliveryProviderOrderAssigned'
    )
    const pickupReadyNotifications = deliveryVendorNotifications.filter(
      n => n.notificationType === 'DeliveryProviderPickupReady'
    )

    assert.equal(deliveryAssignmentNotifications.length, 1, 'Delivery vendor should receive assignment notification')
    assert.equal(pickupReadyNotifications.length, 1, 'Delivery vendor should receive pickup ready notification')

    // Delivery vendor should NOT receive product vendor notifications
    const deliveryVendorProductNotifications = deliveryVendorNotifications.filter(
      n => n.notificationType === 'VendorNewOrder'
    )
    assert.equal(deliveryVendorProductNotifications.length, 0, 'Delivery vendor should not receive product vendor notifications')

    // Customer should receive order-related notifications
    const customerOrderNotifications = customerNotifications.filter(
      n => n.notificationType === 'CustomerNewOrder'
    )
    assert.equal(customerOrderNotifications.length, 1, 'Customer should receive new order notification')

    console.log('✅ Multi-Stakeholder Notification Test passed')
  })

  test('6. Error Handling Test - Missing Delivery Provider User Account', async (assert) => {
    // Test notification behavior when delivery provider user account is missing
    // Test graceful handling of notification sending failures
    // Verify proper logging of notification errors

    // Create delivery vendor without user account
    const vendorWithoutUser = await Vendor.create({
      name: 'Vendor Without User',
      email: '<EMAIL>',
      phone: '+**********',
      active: true,
      verificationStatus: 'verified',
      userId: null // No user account
    })

    const profileWithoutUser = await DeliveryProviderProfile.create({
      vendorId: vendorWithoutUser.id,
      verificationStatus: 'verified',
      active: true,
      vehicleTypes: ['car'],
      maxDeliveryDistance: 5,
      maxConcurrentOrders: 1
    })

    // Clear previous notifications
    mockNotifications.length = 0

    // Attempt to assign delivery provider without user account
    const response = await supertest(BASE_URL)
      .post(`/orders/${order.id}/delivery/assign`)
      .send({
        deliveryVendorId: vendorWithoutUser.id,
        deliveryBranchId: branch.id
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200) // Should still succeed but with warning logs

    // Verify no notifications were sent (since no user account exists)
    const notificationsForMissingUser = mockNotifications.filter(
      n => n.notificationType === 'DeliveryProviderOrderAssigned'
    )
    assert.equal(notificationsForMissingUser.length, 0, 'No notifications should be sent when user account is missing')

    // The assignment should still work (graceful degradation)
    assert.isTrue(response.body.deliveryVendorId === vendorWithoutUser.id)

    console.log('✅ Error Handling Test - Missing User Account passed')
  })

  test('7. Error Handling Test - Delivery Validation Failure', async (assert) => {
    // Test customer notification when delivery validation fails

    // Clear previous notifications
    mockNotifications.length = 0

    // Test delivery validation failure (using CartFulfillmentController)
    const response = await supertest(BASE_URL)
      .post('/cart/validate-fulfillment')
      .send({
        items: [
          { productId: product.id, quantity: 2 }
        ],
        fulfillmentType: 'delivery',
        vendorId: productVendor.id,
        deliveryAddress: {
          lat: 50.0000, // Far outside delivery radius
          lng: -100.0000,
          address: 'Far Away Location, Distant City'
        }
      })
      .set('Authorization', `Bearer ${await customer.generateToken()}`)
      .expect(422) // Unprocessable Entity due to delivery validation failure

    // Check that delivery unavailable notification was sent to customer
    const deliveryUnavailableNotifications = mockNotifications.filter(
      n => n.userId === customer.id && n.notificationType === 'CustomerDeliveryUnavailable'
    )

    assert.equal(deliveryUnavailableNotifications.length, 1, 'Should send CustomerDeliveryUnavailable notification')

    const notification = deliveryUnavailableNotifications[0].notification
    const notificationData = notification.getNotificationData()

    // Verify notification contains helpful information
    assert.exists(notificationData.reasons)
    assert.isArray(notificationData.reasons)
    assert.exists(notificationData.suggested_alternatives)
    assert.isArray(notificationData.suggested_alternatives)
    assert.include(notificationData.suggested_alternatives, 'Try pickup instead')

    console.log('✅ Error Handling Test - Delivery Validation Failure passed')
  })

  test('8. Backward Compatibility Test', async (assert) => {
    // Ensure existing notification workflows (customer orders, staff notifications) continue to work unchanged
    // Verify that vendors without delivery provider profiles don't receive delivery-specific notifications

    // Clear previous notifications
    mockNotifications.length = 0

    // Create a regular vendor without delivery provider profile
    const regularVendorUser = await User.create({
      name: 'Regular Vendor User',
      email: '<EMAIL>',
      phone: '+**********',
      password: 'password123'
    })

    const regularVendor = await Vendor.create({
      name: 'Regular Vendor (No Delivery)',
      email: '<EMAIL>',
      phone: '+**********',
      active: true,
      verificationStatus: 'verified',
      userId: regularVendorUser.id
    })

    // Create order for regular vendor (pickup only)
    const pickupOrder = await Order.create({
      ref: 'PICKUP-ORDER-001',
      vendorId: regularVendor.id,
      branchId: branch.id,
      customerId: customer.id,
      totalAmount: 15.99,
      status: 'Confirmed',
      fulfillmentType: 'pickup' // Pickup order, not delivery
    })

    // Simulate existing order creation workflow
    await customer.notify(new (await import('App/Notifications/Customer/CustomerNewOrder')).default(pickupOrder))
    await regularVendorUser.notify(new (await import('App/Notifications/Vendor/VendorNewOrder')).default(pickupOrder))

    // Verify existing notifications still work
    const customerOrderNotifications = mockNotifications.filter(
      n => n.userId === customer.id && n.notificationType === 'CustomerNewOrder'
    )
    const vendorOrderNotifications = mockNotifications.filter(
      n => n.userId === regularVendorUser.id && n.notificationType === 'VendorNewOrder'
    )

    assert.equal(customerOrderNotifications.length, 1, 'Customer should receive order notification')
    assert.equal(vendorOrderNotifications.length, 1, 'Regular vendor should receive order notification')

    // Verify no delivery-specific notifications are sent to regular vendor
    const deliveryNotificationsToRegularVendor = mockNotifications.filter(
      n => n.userId === regularVendorUser.id && n.notificationType.includes('DeliveryProvider')
    )
    assert.equal(deliveryNotificationsToRegularVendor.length, 0, 'Regular vendor should not receive delivery provider notifications')

    // Test that attempting to assign delivery to regular vendor fails gracefully
    const assignmentResponse = await supertest(BASE_URL)
      .post(`/orders/${pickupOrder.id}/delivery/assign`)
      .send({
        deliveryVendorId: regularVendor.id,
        deliveryBranchId: branch.id
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(400) // Should fail because regular vendor is not a delivery provider

    console.log('✅ Backward Compatibility Test passed')
  })

  test('9. Notification Channel Verification Test', async (assert) => {
    // Test that notifications are sent through correct channels based on user preferences

    // Set specific notification preferences for delivery vendor user
    await deliveryVendorUser.merge({
      notificationPreferences: {
        delivery: {
          push_enabled: true,
          email_enabled: false,
          sms_enabled: true
        }
      }
    }).save()

    // Clear previous notifications
    mockNotifications.length = 0

    // Assign delivery provider to trigger notification
    await supertest(BASE_URL)
      .post(`/orders/${order.id}/delivery/assign`)
      .send({
        deliveryVendorId: deliveryVendor.id,
        deliveryBranchId: branch.id
      })
      .set('Authorization', `Bearer ${await admin.generateToken()}`)
      .expect(200)

    // Verify notification was captured
    const assignmentNotifications = mockNotifications.filter(
      n => n.userId === deliveryVendorUser.id && n.notificationType === 'DeliveryProviderOrderAssigned'
    )

    assert.equal(assignmentNotifications.length, 1, 'Should send assignment notification')

    const notification = assignmentNotifications[0].notification

    // Test channel selection based on notification priority and user preferences
    const channels = notification.via(deliveryVendorUser)
    assert.include(channels, 'database', 'Should always include database channel')
    assert.include(channels, 'fcm', 'Should include FCM for high priority notifications')

    // Test notification data structure for different channels
    const databaseData = notification.toDatabase(deliveryVendorUser)
    assert.exists(databaseData.title)
    assert.exists(databaseData.message)
    assert.exists(databaseData.data)
    assert.equal(databaseData.data.notification_type, 'DeliveryProviderOrderAssigned')

    const fcmData = notification.toFcm(deliveryVendorUser)
    assert.exists(fcmData.title)
    assert.exists(fcmData.body)
    assert.exists(fcmData.data)
    assert.equal(fcmData.data.type, 'delivery_assignment')
    assert.equal(fcmData.android.priority, 'high')

    console.log('✅ Notification Channel Verification Test passed')
  })
})
