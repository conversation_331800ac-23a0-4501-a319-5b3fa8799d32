import test from 'japa'
import supertest from 'supertest'
import Database from '@ioc:Adonis/Lucid/Database'
import Duration from 'App/Models/Duration'

const BASE_URL = `http://${process.env.HOST}:${process.env.PORT}`

test.group('Durations Controller', (group) => {
  group.beforeEach(async () => {
    await Database.beginGlobalTransaction()
  })

  group.afterEach(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('GET /durations should return paginated durations', async (assert) => {
    // Create test durations
    await Duration.createMany([
      Duration.createTemplate('Quick Service', 30, 'short'),
      Duration.createTemplate('Standard Service', 120, 'medium'),
      Duration.createTemplate('Deep Service', 240, 'long')
    ])

    const response = await supertest(BASE_URL)
      .get('/durations')
      .expect(200)

    assert.isTrue(response.body.success)
    assert.exists(response.body.data)
    assert.exists(response.body.meta)
    assert.isAtLeast(response.body.data.data.length, 3)
  })

  test('GET /durations should filter by category', async (assert) => {
    await Duration.createMany([
      Duration.createTemplate('Quick Service', 30, 'short'),
      Duration.createTemplate('Standard Service', 120, 'medium')
    ])

    const response = await supertest(BASE_URL)
      .get('/durations?category=short')
      .expect(200)

    assert.isTrue(response.body.success)
    assert.equal(response.body.data.data.length, 1)
    assert.equal(response.body.data.data[0].category, 'short')
  })

  test('GET /durations should search by name', async (assert) => {
    await Duration.createMany([
      Duration.createTemplate('Quick Service', 30, 'short'),
      Duration.createTemplate('Standard Service', 120, 'medium')
    ])

    const response = await supertest(BASE_URL)
      .get('/durations?search=Quick')
      .expect(200)

    assert.isTrue(response.body.success)
    assert.equal(response.body.data.data.length, 1)
    assert.include(response.body.data.data[0].name, 'Quick')
  })

  test('POST /durations should create a new duration', async (assert) => {
    const durationData = {
      name: 'Test Duration',
      description: 'A test duration',
      minutes: 90,
      bufferMinutes: 20,
      category: 'medium',
      maxConcurrent: 3,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: [],
        blackoutDays: ['sunday']
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 2,
        equipmentRequired: ['basic-kit']
      },
      active: true
    }

    const response = await supertest(BASE_URL)
      .post('/durations')
      .send(durationData)
      .expect(201)

    assert.isTrue(response.body.success)
    assert.equal(response.body.data.name, durationData.name)
    assert.equal(response.body.data.minutes, durationData.minutes)
    assert.equal(response.body.data.category, durationData.category)
  })

  test('POST /durations should validate required fields', async (assert) => {
    const invalidData = {
      name: 'Te', // Too short
      minutes: -10, // Invalid
      category: 'invalid' // Invalid enum
    }

    const response = await supertest(BASE_URL)
      .post('/durations')
      .send(invalidData)
      .expect(400)

    assert.isFalse(response.body.success)
    assert.exists(response.body.error)
  })

  test('GET /durations/:id should return a specific duration', async (assert) => {
    const duration = await Duration.create(
      Duration.createTemplate('Test Duration', 60, 'short')
    )

    const response = await supertest(BASE_URL)
      .get(`/durations/${duration.id}`)
      .expect(200)

    assert.isTrue(response.body.success)
    assert.equal(response.body.data.id, duration.id)
    assert.equal(response.body.data.name, 'Test Duration')
    assert.exists(response.body.data.calendarBlockMinutes)
    assert.exists(response.body.data.totalHours)
    assert.exists(response.body.data.validation)
  })

  test('GET /durations/:id should return 404 for non-existent duration', async (assert) => {
    const response = await supertest(BASE_URL)
      .get('/durations/non-existent-id')
      .expect(404)

    assert.isFalse(response.body.success)
    assert.include(response.body.message, 'not found')
  })

  test('PUT /durations/:id should update a duration', async (assert) => {
    const duration = await Duration.create(
      Duration.createTemplate('Original Name', 60, 'short')
    )

    const updateData = {
      name: 'Updated Name',
      minutes: 90,
      description: 'Updated description'
    }

    const response = await supertest(BASE_URL)
      .put(`/durations/${duration.id}`)
      .send(updateData)
      .expect(200)

    assert.isTrue(response.body.success)
    assert.equal(response.body.data.name, 'Updated Name')
    assert.equal(response.body.data.minutes, 90)
    assert.equal(response.body.data.description, 'Updated description')
  })

  test('PUT /durations/:id should validate configuration on update', async (assert) => {
    const duration = await Duration.create(
      Duration.createTemplate('Test Duration', 60, 'short')
    )

    const invalidUpdate = {
      minutes: -10, // Invalid
      maxConcurrent: 0 // Invalid
    }

    const response = await supertest(BASE_URL)
      .put(`/durations/${duration.id}`)
      .send(invalidUpdate)
      .expect(400)

    assert.isFalse(response.body.success)
    assert.exists(response.body.errors)
  })

  test('DELETE /durations/:id should deactivate a duration', async (assert) => {
    const duration = await Duration.create(
      Duration.createTemplate('Test Duration', 60, 'short')
    )

    const response = await supertest(BASE_URL)
      .delete(`/durations/${duration.id}`)
      .expect(200)

    assert.isTrue(response.body.success)
    assert.include(response.body.message, 'deactivated')

    // Verify duration is deactivated
    await duration.refresh()
    assert.isFalse(duration.active)
  })

  test('GET /durations/categories should return category counts', async (assert) => {
    await Duration.createMany([
      Duration.createTemplate('Short 1', 30, 'short'),
      Duration.createTemplate('Short 2', 45, 'short'),
      Duration.createTemplate('Medium 1', 120, 'medium')
    ])

    const response = await supertest(BASE_URL)
      .get('/durations/categories')
      .expect(200)

    assert.isTrue(response.body.success)
    assert.isArray(response.body.data)
    
    const shortCategory = response.body.data.find(cat => cat.category === 'short')
    const mediumCategory = response.body.data.find(cat => cat.category === 'medium')
    
    assert.exists(shortCategory)
    assert.exists(mediumCategory)
    assert.equal(shortCategory.total, '2')
    assert.equal(mediumCategory.total, '1')
  })

  test('POST /durations/template should create duration from template', async (assert) => {
    const templateData = {
      templateType: 'medium',
      name: 'Custom Medium Service',
      minutes: 150,
      options: {
        maxConcurrent: 2,
        schedulingRules: {
          minAdvanceHours: 4,
          maxPerDay: 3,
          timeSlots: ['morning'],
          blackoutDays: ['sunday', 'saturday']
        }
      }
    }

    const response = await supertest(BASE_URL)
      .post('/durations/template')
      .send(templateData)
      .expect(201)

    assert.isTrue(response.body.success)
    assert.equal(response.body.data.name, 'Custom Medium Service')
    assert.equal(response.body.data.minutes, 150)
    assert.equal(response.body.data.category, 'medium')
    assert.equal(response.body.data.maxConcurrent, 2)
  })

  test('POST /durations/validate should validate configuration', async (assert) => {
    const validConfig = {
      minutes: 120,
      bufferMinutes: 30,
      maxConcurrent: 3,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 2,
        equipmentRequired: []
      }
    }

    const response = await supertest(BASE_URL)
      .post('/durations/validate')
      .send(validConfig)
      .expect(200)

    assert.isTrue(response.body.success)
    assert.isTrue(response.body.data.valid)
    assert.equal(response.body.data.errors.length, 0)
  })

  test('POST /durations/validate should return validation errors', async (assert) => {
    const invalidConfig = {
      minutes: -10,
      bufferMinutes: -5,
      maxConcurrent: 0
    }

    const response = await supertest(BASE_URL)
      .post('/durations/validate')
      .send(invalidConfig)
      .expect(200)

    assert.isTrue(response.body.success)
    assert.isFalse(response.body.data.valid)
    assert.isAbove(response.body.data.errors.length, 0)
  })
})
