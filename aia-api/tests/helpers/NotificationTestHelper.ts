import User from 'App/Models/User'
import { DateTime } from 'luxon'

/**
 * Helper class for testing notification functionality in Phase 2A delivery provider tests
 */
export class NotificationTestHelper {
  private static mockNotifications: any[] = []
  private static originalNotify: any = null

  /**
   * Set up notification mocking to capture sent notifications
   */
  static setupNotificationMocking() {
    this.mockNotifications = []
    this.originalNotify = User.prototype.notify

    // Override the notify method to capture notifications
    User.prototype.notify = async function (notification: any) {
      NotificationTestHelper.mockNotifications.push({
        userId: this.id,
        notificationType: notification.constructor.name,
        notification: notification,
        timestamp: DateTime.now(),
        userData: {
          id: this.id,
          name: this.name,
          email: this.email,
          phone: this.phone
        }
      })
      return Promise.resolve()
    }
  }

  /**
   * Restore original notification functionality
   */
  static restoreNotificationFunctionality() {
    if (this.originalNotify) {
      User.prototype.notify = this.originalNotify
    }
    this.clearNotifications()
  }

  /**
   * Clear captured notifications
   */
  static clearNotifications() {
    this.mockNotifications.length = 0
  }

  /**
   * Get all captured notifications
   */
  static getAllNotifications() {
    return [...this.mockNotifications]
  }

  /**
   * Get notifications for a specific user
   */
  static getNotificationsForUser(userId: string) {
    return this.mockNotifications.filter(n => n.userId === userId)
  }

  /**
   * Get notifications of a specific type
   */
  static getNotificationsByType(notificationType: string) {
    return this.mockNotifications.filter(n => n.notificationType === notificationType)
  }

  /**
   * Get notifications for a specific user and type
   */
  static getNotificationsForUserByType(userId: string, notificationType: string) {
    return this.mockNotifications.filter(
      n => n.userId === userId && n.notificationType === notificationType
    )
  }

  /**
   * Verify that a notification was sent to a specific user
   */
  static verifyNotificationSent(userId: string, notificationType: string): boolean {
    return this.getNotificationsForUserByType(userId, notificationType).length > 0
  }

  /**
   * Verify that a notification was NOT sent to a specific user
   */
  static verifyNotificationNotSent(userId: string, notificationType: string): boolean {
    return this.getNotificationsForUserByType(userId, notificationType).length === 0
  }

  /**
   * Get the latest notification for a user of a specific type
   */
  static getLatestNotificationForUser(userId: string, notificationType: string) {
    const notifications = this.getNotificationsForUserByType(userId, notificationType)
    if (notifications.length === 0) return null
    
    return notifications.sort((a, b) => b.timestamp.toMillis() - a.timestamp.toMillis())[0]
  }

  /**
   * Verify notification data structure and content
   */
  static verifyNotificationData(notification: any, expectedData: any): boolean {
    try {
      const notificationData = notification.notification.getNotificationData()
      
      for (const [key, expectedValue] of Object.entries(expectedData)) {
        if (notificationData[key] !== expectedValue) {
          console.warn(`Notification data mismatch for key ${key}: expected ${expectedValue}, got ${notificationData[key]}`)
          return false
        }
      }
      
      return true
    } catch (error) {
      console.error('Error verifying notification data:', error)
      return false
    }
  }

  /**
   * Verify notification channels are correctly selected
   */
  static verifyNotificationChannels(notification: any, user: User, expectedChannels: string[]): boolean {
    try {
      const channels = notification.notification.via(user)
      
      for (const expectedChannel of expectedChannels) {
        if (!channels.includes(expectedChannel)) {
          console.warn(`Expected channel ${expectedChannel} not found in notification channels:`, channels)
          return false
        }
      }
      
      return true
    } catch (error) {
      console.error('Error verifying notification channels:', error)
      return false
    }
  }

  /**
   * Get notification count by type
   */
  static getNotificationCountByType(notificationType: string): number {
    return this.getNotificationsByType(notificationType).length
  }

  /**
   * Get notification count for user
   */
  static getNotificationCountForUser(userId: string): number {
    return this.getNotificationsForUser(userId).length
  }

  /**
   * Verify delivery provider notifications are properly separated from product vendor notifications
   */
  static verifyDualVerificationSeparation(
    productVendorUserId: string,
    deliveryVendorUserId: string
  ): {
    isValid: boolean,
    errors: string[]
  } {
    const errors: string[] = []
    
    // Product vendor should not receive delivery provider notifications
    const productVendorDeliveryNotifications = this.mockNotifications.filter(
      n => n.userId === productVendorUserId && n.notificationType.includes('DeliveryProvider')
    )
    
    if (productVendorDeliveryNotifications.length > 0) {
      errors.push(`Product vendor received ${productVendorDeliveryNotifications.length} delivery provider notifications`)
    }
    
    // Delivery vendor should not receive product vendor notifications
    const deliveryVendorProductNotifications = this.mockNotifications.filter(
      n => n.userId === deliveryVendorUserId && n.notificationType === 'VendorNewOrder'
    )
    
    if (deliveryVendorProductNotifications.length > 0) {
      errors.push(`Delivery vendor received ${deliveryVendorProductNotifications.length} product vendor notifications`)
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Generate test notification summary for debugging
   */
  static generateNotificationSummary(): string {
    const summary = {
      totalNotifications: this.mockNotifications.length,
      notificationsByType: {},
      notificationsByUser: {},
      timeline: []
    }
    
    for (const notification of this.mockNotifications) {
      // Count by type
      if (!summary.notificationsByType[notification.notificationType]) {
        summary.notificationsByType[notification.notificationType] = 0
      }
      summary.notificationsByType[notification.notificationType]++
      
      // Count by user
      if (!summary.notificationsByUser[notification.userId]) {
        summary.notificationsByUser[notification.userId] = 0
      }
      summary.notificationsByUser[notification.userId]++
      
      // Add to timeline
      summary.timeline.push({
        timestamp: notification.timestamp.toISO(),
        type: notification.notificationType,
        userId: notification.userId,
        userName: notification.userData.name
      })
    }
    
    return JSON.stringify(summary, null, 2)
  }

  /**
   * Verify notification timing constraints (e.g., assignment expiration)
   */
  static verifyNotificationTiming(notification: any, expectedConstraints: any): boolean {
    try {
      const notificationData = notification.notification.getNotificationData()
      
      if (expectedConstraints.hasAssignmentExpiration && !notificationData.assignment_expires_at) {
        console.warn('Expected assignment expiration but not found in notification data')
        return false
      }
      
      if (expectedConstraints.hasPickupDeadline && !notificationData.pickup_deadline) {
        console.warn('Expected pickup deadline but not found in notification data')
        return false
      }
      
      return true
    } catch (error) {
      console.error('Error verifying notification timing:', error)
      return false
    }
  }
}
