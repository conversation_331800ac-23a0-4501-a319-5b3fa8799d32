/**
 * Config source: https://git.io/JOdi5
 *
 * Feel free to let us know via PR, if you find something broken in this config
 * file.
 */

import Env from '@ioc:Adonis/Core/Env'
import { AllyConfig } from '@ioc:Adonis/Addons/Ally'

/*
|--------------------------------------------------------------------------
| Ally Config
|--------------------------------------------------------------------------
|
| The `AllyConfig` relies on the `SocialProviders` interface which is
| defined inside `contracts/ally.ts` file.
|
*/
const allyConfig: AllyConfig = {
  // Initialize with required structure
  google: {
    driver: 'google',
    clientId: Env.get('GOOGLE_CLIENT_ID', ''),
    clientSecret: Env.get('GOOGLE_CLIENT_SECRET', ''),
    callbackUrl: (Env.get('APP_URL', '') + '/v1/auth/social/google/callback'),
    scopes: ['email', 'profile'],
  },
  facebook: {
    driver: 'facebook',
    clientId: Env.get('FACEBOOK_CLIENT_ID', ''),
    clientSecret: Env.get('FACEBOOK_CLIENT_SECRET', ''),
    callbackUrl: (Env.get('APP_URL', '') + '/v1/auth/social/facebook/callback'),
    scopes: ['email', 'public_profile'],
  }
}

export default allyConfig
