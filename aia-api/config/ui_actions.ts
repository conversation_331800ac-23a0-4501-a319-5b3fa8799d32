// config/ui_actions.ts

/**
 * Define the structure for each action card.
 * Even though we only return 'name' now, defining the structure
 * allows for easy future expansion (icon, route, color, etc.).
 */
export interface ActionCard {
  id: string // Unique identifier for the action
  name: string // Display name for the card
  // Add other properties later as needed:
  // icon?: string;
  // route?: string;
  // color?: string;
}

/**
 * Master list of all possible action cards available in the system.
 */
export const allActionCards: ActionCard[] = [
  { id: 'orders', name: 'Orders' },
  { id: 'new_order', name: 'New Order' },
  { id: 'onboard', name: 'Onboard' },
  { id: 'reservations', name: 'Reservations' },
  { id: 'menu', name: 'Menu' },
  { id: 'reports', name: 'Reports' },
  { id: 'rate_customer', name: 'Rate Customer' },
  { id: 'broadcast', name: 'Broadcast' },
  { id: 'bills', name: 'Bills' },
  { id: 'summaries', name: 'Summaries' },
]

/**
 * Defines which action card IDs are allowed for specific roles.
 * Roles should be lowercase.
 * Use '*' as a fallback key for roles not explicitly listed, typically granting all actions.
 */
export const roleActionMapping: Record<string, string[]> = {
  // Administrative Roles
  'admin': allActionCards.map((card) => card.id), // Full access to all actions
  'vendor': [
    'new_order',
    'onboard',
    'reservations',
    'reports',
    'rate_customer',
    'broadcast',
    'bills',
    'summaries',
  ],
  'manager': [
    'orders',
    'new_order',
    'onboard',
    'reservations',
    'menu',
    'reports',
    'rate_customer',
    'broadcast',
    'bills',
    'summaries',
  ],

  // Service Staff Roles
  'waiter': [
    'orders',
    'new_order',
    'onboard',
    'menu',
    'reports',
    'rate_customer',
    'broadcast',
    'summaries',
  ],
  'bartender': [
    'orders',
    'new_order',
    'onboard',
    'menu',
    'reports',
    'rate_customer',
    'broadcast',
    'summaries',
  ],
  'barista': [
    'orders',
    'new_order',
    'onboard',
    'menu',
    'reports',
    'rate_customer',
    'broadcast',
    'summaries',
  ],

  // Kitchen Staff Roles
  'chef': ['orders', 'menu', 'summaries'],

  // Support Staff Roles
  'cashier': [
    'orders',
    'new_order',
    'onboard',
    'reservations',
    'reports',
    'rate_customer',
    'broadcast',
    'bills',
    'summaries',
  ],
  'rider': ['orders', 'summaries'],

  // Customer Role
  'customer': ['orders', 'summaries'], // Basic customer actions

  // Fallback for any role not explicitly listed
  '*': allActionCards.map((card) => card.id),
}
