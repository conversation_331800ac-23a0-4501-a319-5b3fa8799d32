/**
 *
 * Feel free to let us know via PR, if you find something broken in this config
 * file.
 */

import { NotificationConfig } from '@ioc:Verful/Notification'

/*
|--------------------------------------------------------------------------
| Notification Mapping
|--------------------------------------------------------------------------
|
| List of available notification channels. You must first define them
| inside the `contracts/notification.ts` file before mentioning them here.
|
*/
const NotificationConfig: NotificationConfig = {
  channel: 'database',
  channels: {
    /*
    |--------------------------------------------------------------------------
    | Database channel
    |--------------------------------------------------------------------------
    |
    | Use this channel to store notifications in the database.
    |
    */
    database: {
      driver: 'database',
    },
    /*
    |--------------------------------------------------------------------------
    | Mail channel
    |--------------------------------------------------------------------------
    |
    | Use this channel to send notifications via email.
    |
    */
    mail: {
      driver: 'mail',
      // mailer: 'smtp'
    },

    sms: {
      driver: 'sms',
    },

    fcm: {
      driver: 'fcm',
    },
  },
  // notificationsTable: 'notifications',
}

export default NotificationConfig
