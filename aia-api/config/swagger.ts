export default {
  path: __dirname + '/../',
  title: 'App In App API Docs',
  version: '1.0.0',
  description: 'This is the API documentation for the App In App backend',
  tagIndex: 2,
  ignore: ['/swagger', '/docs', '/v1/kplc'],
  debug: true,
  preferredPutPatch: 'PUT',
  common: {
    parameters: {
      sortable: [
        // {
        //   in: 'query',
        //   name: 'per',
        //   schema: { type: 'number', example: 15 },
        // },
        // {
        //   in: 'query',
        //   name: 'page',
        //   schema: { type: 'number', example: 1 },
        // },
        // {
        //   in: "query",
        //   name: "sort",
        //   schema: { type: "string", example: "ASC" },
        // }
      ],
    },
    headers: {},
  },
  info: {
    title: 'App In App API Docs',
    version: '1.0.0',
    description: 'This is the API documentation for the App In App application',
  },
  snakeCase: false,
  securitySchemes: {
    BearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT'
    }
  },
  authMiddlewares: ['auth', 'auth:api'],
  defaultSecurityScheme: 'BearerAuth',
  persistAuthorization: true,
  showFullPath: false,
  components: {
    schemas: {
      FileUpload: {
        type: 'object',
        properties: {
          vendor_setup_csv: {
            type: 'string',
            format: 'binary',
            description: 'CSV file containing vendor data'
          }
        },
        required: ['vendor_setup_csv']
      },

      // Phase 2A Delivery Provider Schemas
      DeliveryProviderApplicationReviewRequest: {
        type: 'object',
        required: ['status'],
        properties: {
          status: {
            type: 'string',
            enum: ['under_review', 'verified', 'rejected', 'suspended'],
            example: 'verified',
            description: 'New verification status for the delivery provider application'
          },
          notes: {
            type: 'string',
            example: 'Application meets all requirements',
            description: 'Admin notes about the review decision'
          }
        }
      },

      DeliveryProviderProfile: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            example: 'uuid-123',
            description: 'Unique identifier for the delivery provider profile'
          },
          vendorId: {
            type: 'string',
            format: 'uuid',
            example: 'vendor-uuid',
            description: 'Associated vendor ID'
          },
          verificationStatus: {
            type: 'string',
            enum: ['pending', 'under_review', 'verified', 'rejected', 'suspended'],
            example: 'verified',
            description: 'Current verification status of the delivery provider'
          },
          vehicleTypes: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['car', 'motorcycle', 'bicycle', 'van', 'truck']
            },
            example: ['car', 'motorcycle'],
            description: 'Types of vehicles available for delivery'
          },
          maxDeliveryDistance: {
            type: 'number',
            minimum: 1,
            maximum: 50,
            example: 10,
            description: 'Maximum delivery distance in kilometers'
          },
          maxConcurrentOrders: {
            type: 'number',
            minimum: 1,
            maximum: 10,
            example: 3,
            description: 'Maximum number of concurrent orders'
          },
          active: {
            type: 'boolean',
            example: true,
            description: 'Whether the delivery provider is currently active'
          },
          verifiedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true,
            example: '2024-01-15T10:30:00Z',
            description: 'Timestamp when the application was verified'
          },
          verifiedBy: {
            type: 'string',
            format: 'uuid',
            nullable: true,
            example: 'admin-uuid',
            description: 'ID of the admin who verified the application'
          },
          verificationNotes: {
            type: 'string',
            nullable: true,
            example: 'Application meets all requirements',
            description: 'Admin notes about the verification decision'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-10T09:00:00Z',
            description: 'Application submission timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-15T10:30:00Z',
            description: 'Last update timestamp'
          }
        },
        required: ['id', 'vendorId', 'verificationStatus', 'vehicleTypes', 'maxDeliveryDistance']
      }
    },
    requestBodies: {
      FileUpload: {
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                vendor_setup_csv: {
                  type: 'string',
                  format: 'binary',
                  description: 'CSV file containing vendor data'
                }
              },
              required: ['vendor_setup_csv']
            }
          }
        }
      }
    }
  }
}
