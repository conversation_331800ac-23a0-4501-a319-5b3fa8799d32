# Module 5: Department-Based Fulfillment

## Overview

This module handles sophisticated department-based order fulfillment workflows, enabling restaurants to efficiently manage order items across different departments (kitchen, bar, pastry, etc.) with staff assignments, workload management, and real-time coordination between departments.

## Business Capabilities

- **Department-Based Order Processing**: Automatic assignment of order items to appropriate departments
- **Staff Assignment & Management**: Assign specific staff members to order items within departments
- **Workload Management**: Real-time tracking of department capacity and workload distribution
- **Status Progression Tracking**: Individual order item status management through preparation stages
- **Performance Analytics**: Department efficiency metrics and optimization recommendations
- **Real-Time Coordination**: Inter-department communication and handoff management

---

## API Endpoints

### Order Item Status Management

#### 1. Update Order Item Status
```http
PATCH /api/v1/order-items/{itemId}/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "preparing",
  "staff_id": "chef_001",
  "notes": "Started preparation",
  "estimated_preparation_time": 15,
  "priority_level": 3,
  "special_instructions": "Extra spicy as requested"
}
```

**Response (200 OK):**
```json
{
  "message": "Order item status updated successfully",
  "item": {
    "id": 1,
    "orderId": "order_456",
    "productId": "product_123",
    "quantity": 2,
    "price": 1200,
    "status": "preparing",
    "departmentId": "dept_kitchen",
    "assignedStaffId": "chef_001",
    "estimatedPreparationTime": 15,
    "preparationStartedAt": "2024-01-10T08:40:00.000Z",
    "priorityLevel": 3,
    "specialInstructions": "Extra spicy as requested",
    "statusHistory": {
      "started_2024-01-10T08:40:00.000Z": {
        "status": "preparing",
        "staffId": "chef_001",
        "timestamp": "2024-01-10T08:40:00.000Z"
      }
    },
    "product": {
      "id": "product_123",
      "name": "Margherita Pizza",
      "category": "Main Course"
    },
    "department": {
      "id": "dept_kitchen",
      "name": "Kitchen"
    },
    "assignedStaff": {
      "id": "chef_001",
      "firstName": "Mario",
      "lastName": "Chef",
      "role": "chef"
    }
  }
}
```

#### 2. Bulk Update Order Item Status
```http
PATCH /api/v1/order-items/bulk-status-update
Authorization: Bearer {token}
Content-Type: application/json

{
  "updates": [
    {
      "id": 1,
      "status": "ready",
      "staff_id": "chef_001",
      "notes": "Pizza ready for pickup"
    },
    {
      "id": 2,
      "status": "preparing",
      "staff_id": "chef_002",
      "notes": "Started pasta preparation"
    }
  ]
}
```

**Response (200 OK):**
```json
{
  "message": "Bulk status update completed",
  "results": [
    {
      "id": 1,
      "status": "updated",
      "new_status": "ready"
    },
    {
      "id": 2,
      "status": "updated",
      "new_status": "preparing"
    }
  ],
  "errors": [],
  "summary": {
    "total": 2,
    "successful": 2,
    "failed": 0
  }
}
```

#### 3. Assign Staff to Order Item
```http
PATCH /api/v1/order-items/{itemId}/assign
Authorization: Bearer {token}
Content-Type: application/json

{
  "assigned_staff_id": "chef_001",
  "department_id": "dept_kitchen",
  "estimated_preparation_time": 20,
  "priority_level": 4,
  "notes": "Assigned to head chef for special preparation"
}
```

**Response (200 OK):**
```json
{
  "message": "Item assigned successfully",
  "assignment": {
    "item_id": 1,
    "staff": {
      "id": "chef_001",
      "firstName": "Mario",
      "lastName": "Chef",
      "role": "chef",
      "skillLevel": 5
    },
    "department": {
      "id": "dept_kitchen",
      "name": "Kitchen",
      "currentWorkload": 8,
      "maxConcurrentOrders": 15
    },
    "estimated_time": 20,
    "priority_level": 4
  }
}
```

### Department Workload Management

#### 1. Get Department Workload
```http
GET /api/v1/departments/{departmentId}/workload
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "department": {
    "id": "dept_kitchen",
    "name": "Kitchen",
    "currentWorkload": 8,
    "maxConcurrentOrders": 15,
    "workloadPercentage": 53.33,
    "estimatedWaitTime": 25,
    "isAtCapacity": false,
    "canAcceptOrders": true,
    "performanceStatus": "good",
    "efficiencyRating": 4.2
  },
  "pending_items": [
    {
      "id": 1,
      "orderId": "order_456",
      "productId": "product_123",
      "quantity": 2,
      "priorityLevel": 3,
      "estimatedPreparationTime": 15,
      "specialInstructions": "Extra spicy",
      "order": {
        "id": "order_456",
        "orderNumber": "ORD-2024-001",
        "type": "Instant",
        "delivery": "Dinein",
        "customer": {
          "firstName": "John",
          "lastName": "Doe"
        }
      },
      "product": {
        "name": "Margherita Pizza",
        "category": "Main Course"
      },
      "createdAt": "2024-01-10T08:30:00.000Z"
    }
  ],
  "preparing_items": [
    {
      "id": 2,
      "orderId": "order_457",
      "productId": "product_124",
      "quantity": 1,
      "status": "preparing",
      "assignedStaffId": "chef_001",
      "preparationStartedAt": "2024-01-10T08:35:00.000Z",
      "estimatedPreparationTime": 20,
      "preparationProgress": 75,
      "isOverdue": false,
      "assignedStaff": {
        "firstName": "Mario",
        "lastName": "Chef"
      }
    }
  ],
  "ready_items": [
    {
      "id": 3,
      "orderId": "order_458",
      "productId": "product_125",
      "quantity": 1,
      "status": "ready",
      "preparationCompletedAt": "2024-01-10T08:50:00.000Z",
      "actualPreparationTime": 18
    }
  ]
}
```

#### 2. Get Department Statistics
```http
GET /api/v1/departments/{departmentId}/workload/statistics
Authorization: Bearer {token}
```

**Query Parameters:**
- `start_date` (date): Start date for statistics
- `end_date` (date): End date for statistics
- `include_hourly` (boolean): Include hourly breakdown

**Response (200 OK):**
```json
{
  "department_id": "dept_kitchen",
  "period": {
    "start_date": "2024-01-10T00:00:00.000Z",
    "end_date": "2024-01-10T23:59:59.000Z"
  },
  "summary": {
    "total_items_processed": 45,
    "completed_items": 42,
    "cancelled_items": 3,
    "average_preparation_time": 18.5,
    "total_preparation_time": 777
  },
  "performance": {
    "completion_rate": "93.33",
    "average_preparation_time": 18.5,
    "overdue_rate": "6.67"
  },
  "current_workload": {
    "capacity_utilization": 53.33,
    "estimated_wait_time": 25,
    "is_at_capacity": false,
    "can_accept_orders": true
  },
  "hourly_breakdown": [
    {
      "hour": "08:00",
      "items_started": 5,
      "items_completed": 4,
      "average_time": 16.2
    },
    {
      "hour": "09:00",
      "items_started": 8,
      "items_completed": 7,
      "average_time": 19.1
    }
  ]
}
```

#### 3. Get Staff Workload Distribution
```http
GET /api/v1/departments/{departmentId}/workload/staff-distribution
Authorization: Bearer {token}
```

**Query Parameters:**
- `include_inactive` (boolean): Include inactive staff

**Response (200 OK):**
```json
{
  "department_id": "dept_kitchen",
  "staff_workloads": [
    {
      "staff": {
        "id": "chef_001",
        "firstName": "Mario",
        "lastName": "Chef",
        "role": "head_chef",
        "skillLevel": 5,
        "active": true
      },
      "current_assignments": 3,
      "pending_items": 1,
      "preparing_items": 2,
      "ready_items": 0,
      "today_completed": 12,
      "average_completion_time": 17.5,
      "efficiency_rating": 4.8,
      "workload_percentage": 60,
      "is_available": true,
      "estimated_availability": "2024-01-10T09:15:00.000Z"
    },
    {
      "staff": {
        "id": "chef_002",
        "firstName": "Luigi",
        "lastName": "Sous",
        "role": "sous_chef",
        "skillLevel": 4,
        "active": true
      },
      "current_assignments": 2,
      "pending_items": 0,
      "preparing_items": 2,
      "ready_items": 0,
      "today_completed": 8,
      "average_completion_time": 19.2,
      "efficiency_rating": 4.3,
      "workload_percentage": 40,
      "is_available": true,
      "estimated_availability": "2024-01-10T09:10:00.000Z"
    }
  ],
  "recommendations": [
    {
      "type": "rebalance",
      "message": "Consider redistributing workload - Mario is at 60% capacity while Luigi is at 40%",
      "suggested_action": "Assign next order to Luigi"
    }
  ]
}
```

#### 4. Get Overdue Items
```http
GET /api/v1/departments/{departmentId}/workload/overdue-items
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "department_id": "dept_kitchen",
  "overdue_items": [
    {
      "id": 4,
      "orderId": "order_459",
      "productId": "product_126",
      "quantity": 1,
      "status": "preparing",
      "assignedStaffId": "chef_001",
      "estimatedPreparationTime": 15,
      "preparationStartedAt": "2024-01-10T08:20:00.000Z",
      "overdueMinutes": 25,
      "escalationLevel": "high",
      "order": {
        "orderNumber": "ORD-2024-002",
        "customer": {
          "firstName": "Jane",
          "lastName": "Smith",
          "phone": "254712345679"
        }
      },
      "assignedStaff": {
        "firstName": "Mario",
        "lastName": "Chef"
      },
      "recommendations": [
        "Escalate to manager",
        "Consider reassigning to available staff",
        "Notify customer of delay"
      ]
    }
  ],
  "summary": {
    "total_overdue": 1,
    "critical_level": 0,
    "high_level": 1,
    "medium_level": 0,
    "low_level": 0
  }
}
```

#### 5. Get Ready Items for Serving
```http
GET /api/v1/departments/{departmentId}/workload/ready-items
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "department_id": "dept_kitchen",
  "ready_items": [
    {
      "id": 5,
      "orderId": "order_460",
      "productId": "product_127",
      "quantity": 2,
      "status": "ready",
      "preparationCompletedAt": "2024-01-10T08:55:00.000Z",
      "actualPreparationTime": 18,
      "waitingTime": 5,
      "order": {
        "orderNumber": "ORD-2024-003",
        "delivery": "Dinein",
        "tableNumber": "T-05",
        "customer": {
          "firstName": "Bob",
          "lastName": "Customer"
        }
      },
      "product": {
        "name": "Chicken Tikka",
        "category": "Main Course"
      },
      "modifiers": [
        {
          "id": 1,
          "name": "Extra Spicy",
          "status": "completed"
        }
      ]
    }
  ],
  "summary": {
    "total_ready": 1,
    "average_waiting_time": 5,
    "longest_waiting": 5,
    "requires_immediate_attention": 0
  }
}
```

### Department Management

#### 1. List Departments
```http
GET /api/v1/departments
Authorization: Bearer {token}
```

**Query Parameters:**
- `vendor` (string): Filter by vendor ID
- `branch` (string): Filter by branch ID
- `active` (boolean): Filter by active status

#### 2. Get Department Details
```http
GET /api/v1/departments/{departmentId}
Authorization: Bearer {token}
```

#### 3. Assign Staff to Department
```http
POST /api/v1/departments/{departmentId}/staff
Authorization: Bearer {token}
Content-Type: application/json

{
  "user_id": "chef_003",
  "role": "chef",
  "skill_level": 3,
  "is_primary_department": true
}
```

#### 4. Update Staff Role in Department
```http
PUT /api/v1/departments/{departmentId}/staff/{staffId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "role": "head_chef",
  "skill_level": 5,
  "is_primary_department": true
}
```

#### 5. Remove Staff from Department
```http
DELETE /api/v1/departments/{departmentId}/staff/{staffId}
Authorization: Bearer {token}
```

---

## Authentication & Authorization

### Required Headers
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### Role-Based Permissions
- **Admin**: Full department and workload management
- **Manager**: Manage department operations and staff assignments
- **Department Head**: Manage own department workload and staff
- **Staff**: Update assigned order item status, view department workload

---

## Core Workflows

### 1. Order Item Assignment and Processing

```mermaid
sequenceDiagram
    participant Order as Order System
    participant Dept as Department System
    participant Staff as Kitchen Staff
    participant Manager as Department Manager
    participant Customer as Customer

    Order->>Dept: New order items created
    Dept->>Dept: Auto-assign items to departments
    Dept->>Manager: Notify new items pending
    
    Manager->>Staff: Assign items to available staff
    Staff->>Dept: Update status to 'preparing'
    Dept->>Order: Broadcast status update
    Order->>Customer: Notify preparation started
    
    Staff->>Dept: Update status to 'ready'
    Dept->>Manager: Notify items ready for pickup
    Manager->>Staff: Coordinate item handoff
    Staff->>Dept: Update status to 'served'
    Dept->>Order: Broadcast completion
    Order->>Customer: Notify order completed
```

### 2. Department Workload Management

```mermaid
flowchart TD
    A[New Order Items] --> B{Check Department Capacity}
    B -->|Under Capacity| C[Auto-assign to Department]
    B -->|At Capacity| D[Queue for Next Available]
    B -->|Over Capacity| E[Escalate to Manager]
    
    C --> F{Available Staff?}
    F -->|Yes| G[Assign to Staff Member]
    F -->|No| H[Add to Department Queue]
    
    G --> I[Staff Updates Status]
    H --> J[Wait for Staff Availability]
    J --> G
    
    I --> K{Status = Ready?}
    K -->|Yes| L[Notify Service Staff]
    K -->|No| M[Continue Processing]
    
    L --> N[Hand off to Service]
    N --> O[Mark as Served]
```

### 3. Real-Time Status Broadcasting

```mermaid
sequenceDiagram
    participant Staff as Kitchen Staff
    participant API as Status API
    participant WS as WebSocket Service
    participant Manager as Manager App
    participant Waiter as Waiter App
    participant Customer as Customer App

    Staff->>API: Update item status
    API->>API: Validate status transition
    API->>API: Update database
    
    API->>WS: Broadcast status change
    WS->>Manager: Department workload update
    WS->>Waiter: Order item status update
    WS->>Customer: Order progress update
    
    API->>API: Check order completion
    alt All items ready
        API->>WS: Broadcast order ready
        WS->>Waiter: Order ready for pickup
        WS->>Customer: Order ready notification
    end
```

---

## Role-Based Operations

### Kitchen Staff Operations
- View assigned order items
- Update item status (pending → preparing → ready)
- Add preparation notes and special instructions
- Report issues or delays
- View department workload

### Department Manager Operations
- Assign order items to staff members
- Monitor department performance and efficiency
- Manage staff workload distribution
- Handle overdue items and escalations
- View comprehensive department analytics

### Service Staff Operations
- View ready items for pickup/serving
- Mark items as served to customers
- Coordinate with kitchen on special requests
- Handle customer inquiries about order status

### Restaurant Manager Operations
- Monitor all department workloads
- Optimize staff assignments across departments
- View performance analytics and reports
- Handle escalations and capacity issues

---

## Data Models

### Order Item Schema
```typescript
interface OrderItem {
  id: number
  orderId: string
  productId: string
  quantity: number
  price: number
  status: 'pending' | 'preparing' | 'ready' | 'served' | 'cancelled' | 'on_hold' | 'delayed'
  departmentId: string | null
  assignedStaffId: string | null
  estimatedPreparationTime: number | null
  preparationStartedAt: string | null
  preparationCompletedAt: string | null
  servedAt: string | null
  priorityLevel: number
  requiresSpecialAttention: boolean
  specialInstructions: string | null
  preparationNotes: Record<string, any> | null
  statusHistory: Record<string, any> | null
  qualityCheckStatus: 'pending' | 'passed' | 'failed' | 'not_required'
  actualPreparationTime: number | null
  product: Product
  department: Department
  assignedStaff: User
  modifiers: OrderItemModifier[]
}
```

### Department Schema
```typescript
interface Department {
  id: string
  vendorId: string
  branchId: string
  name: string
  description: string | null
  active: boolean
  averagePreparationTime: number | null
  maxConcurrentOrders: number | null
  priorityLevel: number
  workingHours: Record<string, any> | null
  workflowSettings: Record<string, any> | null
  efficiencyRating: number | null
  totalOrdersCompleted: number
  averageCompletionTime: number | null
  currentWorkload: number
  workloadPercentage: number
  estimatedWaitTime: number
  isAtCapacity: boolean
  canAcceptNewOrder: boolean
  performanceStatus: 'excellent' | 'good' | 'average' | 'poor' | 'unknown'
  staff: DepartmentStaff[]
  orderItems: OrderItem[]
}
```

### Department Staff Schema
```typescript
interface DepartmentStaff {
  userId: string
  departmentId: string
  role: string
  active: boolean
  isPrimaryDepartment: boolean
  skillLevel: number
  performanceRating: number | null
  currentWorkload: number
  availabilityStatus: 'available' | 'busy' | 'break' | 'offline'
  user: User
}
```

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Staff ID is required when starting preparation"
}
```

#### 403 Forbidden
```json
{
  "error": "Insufficient permissions to assign staff to this department"
}
```

#### 409 Conflict
```json
{
  "error": "Cannot transition from 'served' to 'preparing'",
  "details": "Invalid status transition"
}
```

### Retry Strategies
- **Status Updates**: Use optimistic updates with rollback
- **Staff Assignments**: Retry with alternative staff members
- **Workload Calculations**: Refresh data and retry
- **Real-time Updates**: Implement reconnection logic for WebSocket

---

## Performance Optimization

### Caching Recommendations
- **Department Workload**: Cache for 30 seconds
- **Staff Assignments**: Cache for 2 minutes
- **Performance Statistics**: Cache for 15 minutes
- **Department Configuration**: Cache for 1 hour

### Real-Time Updates
- Use WebSocket for instant status updates
- Implement efficient broadcasting to relevant users only
- Use database triggers for automatic workload calculations
- Implement proper connection management

### Best Practices
- Batch status updates when possible
- Use database indexes on frequently queried fields
- Implement proper pagination for large datasets
- Use efficient queries with proper joins and preloading

---

## Implementation Notes

### Status Transition Validation
- Implement proper state machine for status transitions
- Validate business rules before allowing transitions
- Maintain comprehensive audit trail
- Handle concurrent updates gracefully

### Workload Management
- Implement real-time capacity monitoring
- Use intelligent assignment algorithms
- Support manual override capabilities
- Provide performance optimization recommendations

### Integration Patterns
- Use event-driven architecture for status changes
- Implement proper error boundaries
- Support offline capabilities where appropriate
- Use proper state management for real-time data
