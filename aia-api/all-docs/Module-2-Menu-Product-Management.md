# Module 2: Menu & Product Management

## Overview

This module handles comprehensive menu and product management for restaurants, enabling staff to create, update, and manage their product catalog with categories, pricing, stock tracking, and availability management.

## Business Capabilities

- **Product Catalog Management**: Create and manage restaurant menu items
- **Category Organization**: Organize products into logical categories (appetizers, mains, drinks, etc.)
- **Stock & Availability Tracking**: Real-time inventory management
- **Pricing Management**: Regular and discounted pricing with modifiers
- **Multi-Branch Support**: Branch-specific menu management
- **Product Modifiers**: Accompaniments, upsells, and customizations

---

## API Endpoints

### Product Management

#### 1. List All Products
```http
GET /api/v1/products
Authorization: Bearer {token}
```

**Query Parameters:**
- `per` (number, default: 25): Items per page
- `page` (number, default: 1): Page number
- `order` (string, default: 'createdAt'): Order by field
- `sort` (string, default: 'desc'): Sort direction (asc/desc)
- `vendor` (string): Filter by vendor ID
- `branch` (string): Filter by branch ID
- `category` (string): Filter by category ID
- `status` (string): Filter by status (Draft, Published, etc.)
- `availability` (string): Filter by availability (In Stock, Out of Stock)

**Response (200 OK):**
```json
{
  "meta": {
    "total": 150,
    "per_page": 25,
    "current_page": 1,
    "last_page": 6,
    "first_page": 1,
    "first_page_url": "/?page=1",
    "last_page_url": "/?page=6",
    "next_page_url": "/?page=2",
    "previous_page_url": null
  },
  "data": [
    {
      "id": "product_123",
      "name": "Margherita Pizza",
      "ref": "PIZZA_001",
      "details": "Classic pizza with tomato sauce, mozzarella, and fresh basil",
      "price": 1200,
      "discounted": 1000,
      "stock": 50,
      "active": true,
      "featured": true,
      "type": "Physical",
      "condition": "New",
      "status": "Published",
      "availability": "In Stock",
      "shipping": "Free",
      "unit": "piece",
      "mode": "Single",
      "payment": "Prepaid",
      "visibility": "Public",
      "productCategoryId": "cat_456",
      "vendorId": "vendor_789",
      "branchId": "branch_101",
      "meta": {
        "preparation_time": 15,
        "spice_level": "mild",
        "dietary_info": ["vegetarian"]
      },
      "extra": {
        "ingredients": ["tomato", "mozzarella", "basil"],
        "allergens": ["dairy", "gluten"]
      },
      "image": {
        "url": "https://example.com/images/pizza.jpg",
        "name": "pizza.jpg",
        "size": 245760
      },
      "category": {
        "id": "cat_456",
        "name": "Main Course",
        "details": "Main course items"
      },
      "vendor": {
        "id": "vendor_789",
        "name": "Mama Oliech Restaurant",
        "type": "restaurant"
      },
      "branch": {
        "id": "branch_101",
        "name": "Main Branch",
        "address": "Nairobi, Kenya"
      },
      "accompaniments": [
        {
          "id": "product_124",
          "name": "Garlic Bread",
          "price": 200
        }
      ],
      "upsells": [
        {
          "id": "product_125",
          "name": "Extra Cheese",
          "price": 150
        }
      ],
      "available_modifiers": {
        "size": ["small", "medium", "large"],
        "crust": ["thin", "thick", "stuffed"]
      },
      "createdAt": "2024-01-10T08:30:00.000Z",
      "updatedAt": "2024-01-10T08:30:00.000Z"
    }
  ]
}
```

#### 2. Get Vendor Products
```http
GET /api/v1/vendors/{vendorId}/products
Authorization: Bearer {token}
```

**Query Parameters:**
- `include_modifiers` (boolean, default: true): Include product modifiers
- Standard pagination and filtering parameters

#### 3. Create Product
```http
POST /api/v1/products
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
  "name": "Chicken Tikka",
  "ref": "CHICKEN_001",
  "details": "Grilled chicken marinated in spices",
  "price": 1500,
  "discounted": 1300,
  "stock": 30,
  "active": true,
  "featured": false,
  "type": "Physical",
  "condition": "New",
  "status": "Draft",
  "availability": "In Stock",
  "shipping": "Free",
  "unit": "piece",
  "mode": "Single",
  "payment": "Prepaid",
  "visibility": "Public",
  "productCategoryId": "cat_456",
  "vendorId": "vendor_789",
  "branchId": "branch_101",
  "meta": {
    "preparation_time": 20,
    "spice_level": "medium",
    "dietary_info": ["gluten-free"]
  },
  "extra": {
    "ingredients": ["chicken", "yogurt", "spices"],
    "allergens": ["dairy"]
  },
  "tagIds": ["tag_1", "tag_2"],
  "accompaniments": {
    "product_126": {
      "price": 100
    }
  },
  "upsells": {
    "product_127": {
      "price": 200
    }
  },
  "available_modifiers": {
    "spice_level": ["mild", "medium", "hot"],
    "portion": ["half", "full"]
  }
}
```

**Files:**
- `image`: Product main image
- `gallery[]`: Additional product images

**Response (201 Created):**
```json
{
  "id": "product_128",
  "name": "Chicken Tikka",
  "ref": "CHICKEN_001",
  // ... full product object
}
```

#### 4. Update Product
```http
PUT /api/v1/products/{productId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Updated Product Name",
  "price": 1600,
  "stock": 25,
  "availability": "In Stock"
}
```

#### 5. Delete Product
```http
DELETE /api/v1/products/{productId}
Authorization: Bearer {token}
```

#### 6. Update Product Stock
```http
PATCH /api/v1/products/{productId}/stock
Authorization: Bearer {token}
Content-Type: application/json

{
  "stock": 45,
  "availability": "In Stock"
}
```

### Product Categories

#### 1. List Categories
```http
GET /api/v1/product-categories
Authorization: Bearer {token}
```

**Query Parameters:**
- `vendor` (string): Filter by vendor ID
- `branch` (string): Filter by branch ID
- `service` (string): Filter by service ID
- `productsPerCategory` (number, default: 20): Products to include per category

**Response (200 OK):**
```json
{
  "meta": {
    "total": 10,
    "per_page": 50,
    "current_page": 1
  },
  "data": [
    {
      "id": "cat_456",
      "name": "Main Course",
      "details": "Main course dishes",
      "productTypeId": "type_123",
      "image": {
        "url": "https://example.com/images/main-course.jpg"
      },
      "products": [
        {
          "id": "product_123",
          "name": "Margherita Pizza",
          "price": 1200,
          "availability": "In Stock"
        }
      ],
      "createdAt": "2024-01-10T08:30:00.000Z"
    }
  ]
}
```

#### 2. Create Category
```http
POST /api/v1/product-categories
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
  "name": "Beverages",
  "details": "Hot and cold beverages",
  "productTypeId": "type_124"
}
```

**Files:**
- `image`: Category image

#### 3. Get Category Products
```http
GET /api/v1/product-categories/{categoryId}/products
Authorization: Bearer {token}
```

#### 4. Add Product to Category
```http
POST /api/v1/product-categories/{categoryId}/products
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Cappuccino",
  "details": "Italian coffee with steamed milk",
  "serviceId": "service_123"
}
```

### Product Types

#### 1. List Product Types
```http
GET /api/v1/product-types
Authorization: Bearer {token}
```

#### 2. Create Product Type
```http
POST /api/v1/product-types
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Food",
  "details": "Food items"
}
```

#### 3. Get Type Categories
```http
GET /api/v1/product-types/{typeId}/categories
Authorization: Bearer {token}
```

---

## Authentication & Authorization

### Required Headers
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### Role-Based Permissions
- **Admin**: Full product management across all vendors
- **Vendor**: Manage own vendor products
- **Manager**: Manage branch-specific products
- **Staff**: View products, update stock levels

---

## Core Workflows

### 1. Menu Setup Workflow

```mermaid
sequenceDiagram
    participant Manager as Restaurant Manager
    participant API as Product API
    participant DB as Database

    Manager->>API: POST /product-types
    Note over Manager,API: Create "Food", "Beverages"
    
    API->>DB: Create product types
    DB-->>API: Types created
    
    Manager->>API: POST /product-categories
    Note over Manager,API: Create categories per type
    
    API->>DB: Create categories
    DB-->>API: Categories created
    
    Manager->>API: POST /products
    Note over Manager,API: Add menu items with details
    
    API->>DB: Create products with relationships
    DB-->>API: Products created
    
    API-->>Manager: Menu setup complete
```

### 2. Stock Management Workflow

```mermaid
sequenceDiagram
    participant Staff as Kitchen Staff
    participant API as Product API
    participant System as Notification System
    participant Manager as Manager

    Staff->>API: PATCH /products/{id}/stock
    Note over Staff,API: Update stock levels
    
    API->>API: Check stock thresholds
    
    alt Stock below threshold
        API->>System: Trigger low stock alert
        System->>Manager: Send notification
    end
    
    API-->>Staff: Stock updated
```

### 3. Product Availability Management

```mermaid
flowchart TD
    A[Product Request] --> B{Check Stock}
    B -->|Stock > 0| C[Available]
    B -->|Stock = 0| D[Out of Stock]
    B -->|Stock = -1| E[Unlimited Stock]
    
    C --> F{Check Status}
    D --> G[Mark Unavailable]
    E --> F
    
    F -->|Published| H[Show to Customers]
    F -->|Draft| I[Hide from Customers]
    F -->|Archived| I
    
    G --> J[Hide from Menu]
    H --> K[Add to Order]
    I --> L[Staff Only View]
    J --> L
```

---

## Role-Based Operations

### Restaurant Manager Operations
- Create and manage product categories
- Add new menu items with full details
- Set pricing and discounts
- Manage product visibility and status
- Configure product modifiers and accompaniments

### Kitchen Staff Operations
- Update stock levels for ingredients
- Mark items as unavailable when out of stock
- View preparation times and requirements
- Update product availability in real-time

### Waiter Operations
- View available menu items
- Check product availability
- Access product details for customer queries
- View pricing and modifiers

### Customer Operations
- Browse available products by category
- View product details, images, and pricing
- See real-time availability status
- Access dietary information and allergens

---

## Data Models

### Product Schema
```typescript
interface Product {
  id: string
  name: string
  ref: string                    // Product reference code
  details: string
  price: number                  // Price in cents
  discounted: number            // Discounted price in cents
  stock: number                 // -1 for unlimited
  active: boolean
  featured: boolean
  type: 'Physical' | 'Digital' | 'Service'
  condition: 'New' | 'Used' | 'Refurbished'
  status: 'Draft' | 'Pending' | 'Published' | 'Unpublished' | 'Archived'
  availability: 'In Stock' | 'Out of Stock' | 'Pre Order'
  shipping: 'Free' | 'Paid' | 'Pickup'
  unit: string                  // Measurement unit
  mode: 'Single' | 'Variable'
  payment: 'Free' | 'Prepaid' | 'Postpaid' | 'Concurrent'
  visibility: 'Private' | 'Public' | 'Restricted'
  productCategoryId: string
  vendorId: string
  branchId: string
  serviceId: string
  meta: Record<string, any>     // Custom metadata
  extra: Record<string, any>    // Additional data
  image: AttachmentContract
  accompaniments: Product[]     // Related products
  upsells: Product[]           // Upsell products
  available_modifiers: Record<string, string[]>
  notes?: Note[]               // Associated notes
  createdAt: string
  updatedAt: string
}
```

### Note Schema
```typescript
interface Note {
  id: number
  type: string                  // Note type/category
  content: string              // Note content
  branchId: string            // Associated branch
  userId: string              // User who created the note
  productId?: string          // Associated product (optional)
  meta?: Record<string, any>  // Additional metadata
  product?: Product           // Product relationship
  createdAt: string
  updatedAt: string
}
```

### Product Category Schema
```typescript
interface ProductCategory {
  id: string
  name: string
  details: string
  productTypeId: string
  image: AttachmentContract
  products: Product[]
  createdAt: string
  updatedAt: string
}
```

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Invalid product data",
  "details": {
    "price": ["Price must be a positive number"],
    "name": ["Product name is required"]
  }
}
```

#### 404 Not Found
```json
{
  "error": "Product not found"
}
```

#### 409 Conflict
```json
{
  "error": "Product with this reference already exists"
}
```

### Retry Strategies
- **Stock Updates**: Retry with exponential backoff
- **Image Uploads**: Retry failed uploads automatically
- **Validation Errors**: Display field-specific errors to user

---

## Performance Optimization

### Caching Recommendations
- **Product Catalog**: Cache for 30 minutes
- **Categories**: Cache for 2 hours
- **Product Images**: CDN caching with long TTL
- **Stock Levels**: Real-time, no caching

### Query Optimization
- Use pagination for large product lists
- Implement search with debouncing
- Preload related data (categories, vendors)
- Use database indexes on frequently queried fields

### Best Practices
- Implement lazy loading for product images
- Use optimistic updates for stock changes
- Batch stock updates when possible
- Implement offline support for viewing products

---

## Implementation Notes

### File Upload Handling
- Support multiple image formats (JPEG, PNG, WebP)
- Implement image compression and resizing
- Use progressive image loading
- Provide fallback images for missing assets

### Stock Management
- Implement real-time stock tracking
- Set up low stock alerts and thresholds
- Support negative stock for backorders
- Track stock movement history

### Search and Filtering
- Implement full-text search on product names and descriptions
- Support filtering by multiple criteria
- Provide autocomplete suggestions
- Enable sorting by various fields (price, popularity, etc.)

### Technology-Agnostic Patterns
- Use repository pattern for data access
- Implement proper error boundaries
- Use state management for product catalog
- Implement proper loading states and skeletons
