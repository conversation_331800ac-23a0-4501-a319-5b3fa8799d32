# Module 6: Real-Time Order Tracking

## Overview

This module provides comprehensive real-time order tracking capabilities for restaurants, enabling customers, staff, and managers to receive instant updates about order status, preparation progress, and completion notifications through WebSocket connections and push notifications.

## Business Capabilities

- **Real-Time Order Status Updates**: Instant notifications when order status changes
- **Individual Item Progress Tracking**: Track preparation progress of individual order items
- **Department-Based Updates**: Real-time updates from kitchen, bar, and service departments
- **Customer Progress Visibility**: Live order tracking for customers with estimated completion times
- **Staff Coordination**: Real-time communication between departments and staff members
- **Manager Oversight**: Live dashboard for managers to monitor all orders and operations

---

## API Endpoints

### WebSocket Connection Management

#### 1. WebSocket Server Status
```http
GET /api/v1/websocket/status
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "status": "active",
  "server_initialized": true,
  "active_connections": 45,
  "uptime": "2h 15m 30s",
  "last_restart": "2024-01-10T06:30:00.000Z",
  "version": "1.0.0"
}
```

#### 2. WebSocket Health Check
```http
GET /api/v1/websocket/health
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "status": "healthy",
  "checks": {
    "server_running": true,
    "memory_usage": "45MB",
    "cpu_usage": "12%",
    "connection_pool": "normal",
    "message_queue": "empty"
  },
  "metrics": {
    "messages_sent_last_hour": 1250,
    "messages_received_last_hour": 890,
    "average_response_time": "15ms",
    "error_rate": "0.02%"
  }
}
```

### Real-Time Broadcasting

#### 1. Trigger Order Status Broadcast
```http
POST /api/v1/websocket/trigger/order-status
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_id": "order_456",
  "vendor_id": "vendor_789",
  "branch_id": "branch_101",
  "customer_id": "customer_123",
  "status": "Ready",
  "previous_status": "Processing"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Order status broadcast triggered successfully",
  "broadcast_details": {
    "order_id": "order_456",
    "status": "Ready",
    "previous_status": "Processing",
    "triggered_by": "Jane Waiter",
    "timestamp": "2024-01-10T08:55:00.000Z",
    "recipients": {
      "customer": 1,
      "staff": 8,
      "managers": 2
    }
  }
}
```

#### 2. Trigger Item Status Broadcast
```http
POST /api/v1/websocket/trigger/item-status
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_id": "order_456",
  "item_id": 1,
  "vendor_id": "vendor_789",
  "branch_id": "branch_101",
  "department_id": "dept_kitchen",
  "status": "ready",
  "previous_status": "preparing",
  "assigned_staff_id": "chef_001"
}
```

#### 3. Trigger Overdue Alert
```http
POST /api/v1/websocket/trigger/overdue-alert
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_id": "order_456",
  "item_id": 1,
  "vendor_id": "vendor_789",
  "branch_id": "branch_101",
  "department_id": "dept_kitchen",
  "overdue_minutes": 15,
  "escalation_level": "high"
}
```

### Order Tracking

#### 1. Get Order Status with Real-Time Data
```http
GET /api/v1/orders/{orderId}/tracking
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "order": {
    "id": "order_456",
    "orderNumber": "ORD-2024-001",
    "status": "Processing",
    "delivery": "Dinein",
    "type": "Instant",
    "placedAt": "2024-01-10T08:35:00.000Z",
    "estimatedCompletionTime": "2024-01-10T09:05:00.000Z",
    "actualCompletionTime": null,
    "customer": {
      "firstName": "John",
      "lastName": "Doe",
      "phone": "254712345678"
    }
  },
  "progress": {
    "overall_progress": 65,
    "estimated_remaining_time": 10,
    "current_stage": "Kitchen Preparation",
    "next_stage": "Ready for Pickup",
    "stages": [
      {
        "name": "Order Placed",
        "status": "completed",
        "timestamp": "2024-01-10T08:35:00.000Z"
      },
      {
        "name": "Kitchen Preparation",
        "status": "in_progress",
        "timestamp": "2024-01-10T08:40:00.000Z",
        "progress": 65
      },
      {
        "name": "Ready for Pickup",
        "status": "pending",
        "estimated_timestamp": "2024-01-10T09:05:00.000Z"
      },
      {
        "name": "Served",
        "status": "pending",
        "estimated_timestamp": "2024-01-10T09:10:00.000Z"
      }
    ]
  },
  "items": [
    {
      "id": 1,
      "productName": "Margherita Pizza",
      "quantity": 2,
      "status": "preparing",
      "department": "Kitchen",
      "assignedStaff": "Mario Chef",
      "estimatedTime": 15,
      "actualTime": null,
      "preparationStartedAt": "2024-01-10T08:40:00.000Z",
      "progress": 70,
      "isOverdue": false,
      "specialInstructions": "Extra cheese"
    },
    {
      "id": 2,
      "productName": "Caesar Salad",
      "quantity": 1,
      "status": "ready",
      "department": "Kitchen",
      "assignedStaff": "Luigi Sous",
      "estimatedTime": 10,
      "actualTime": 8,
      "preparationStartedAt": "2024-01-10T08:40:00.000Z",
      "preparationCompletedAt": "2024-01-10T08:48:00.000Z",
      "progress": 100,
      "isOverdue": false
    }
  ],
  "department_breakdown": {
    "Kitchen": {
      "total_items": 2,
      "pending": 0,
      "preparing": 1,
      "ready": 1,
      "served": 0,
      "progress": 75
    }
  },
  "real_time_updates": {
    "websocket_channel": "order:order_456",
    "last_update": "2024-01-10T08:50:00.000Z",
    "update_frequency": "real-time"
  }
}
```

#### 2. Get Live Order Updates Stream
```http
GET /api/v1/orders/{orderId}/live-updates
Authorization: Bearer {token}
```

**Query Parameters:**
- `since` (timestamp): Get updates since specific timestamp
- `include_items` (boolean): Include item-level updates

**Response (200 OK):**
```json
{
  "order_id": "order_456",
  "updates": [
    {
      "id": "update_001",
      "type": "order_status_change",
      "timestamp": "2024-01-10T08:40:00.000Z",
      "data": {
        "status": "Processing",
        "previous_status": "Placed",
        "updated_by": "Mario Chef",
        "message": "Kitchen started preparation"
      }
    },
    {
      "id": "update_002",
      "type": "item_status_change",
      "timestamp": "2024-01-10T08:48:00.000Z",
      "data": {
        "item_id": 2,
        "product_name": "Caesar Salad",
        "status": "ready",
        "previous_status": "preparing",
        "updated_by": "Luigi Sous",
        "message": "Salad ready for pickup"
      }
    },
    {
      "id": "update_003",
      "type": "progress_update",
      "timestamp": "2024-01-10T08:50:00.000Z",
      "data": {
        "overall_progress": 65,
        "estimated_completion": "2024-01-10T09:05:00.000Z",
        "message": "Pizza 70% complete"
      }
    }
  ],
  "has_more": false,
  "next_cursor": null
}
```

### Customer Tracking Interface

#### 1. Get Customer Order Tracking
```http
GET /api/v1/customers/{customerId}/orders/{orderId}/tracking
Authorization: Bearer {customer_token}
```

**Response (200 OK):**
```json
{
  "order": {
    "id": "order_456",
    "orderNumber": "ORD-2024-001",
    "status": "Processing",
    "placedAt": "2024-01-10T08:35:00.000Z",
    "estimatedCompletionTime": "2024-01-10T09:05:00.000Z"
  },
  "tracking": {
    "current_status": "Your order is being prepared",
    "progress_percentage": 65,
    "estimated_wait_time": "10 minutes",
    "can_modify": false,
    "can_cancel": false,
    "timeline": [
      {
        "stage": "Order Confirmed",
        "status": "completed",
        "time": "2024-01-10T08:35:00.000Z",
        "message": "Your order has been confirmed and sent to the kitchen"
      },
      {
        "stage": "Preparation Started",
        "status": "completed",
        "time": "2024-01-10T08:40:00.000Z",
        "message": "Kitchen has started preparing your order"
      },
      {
        "stage": "In Progress",
        "status": "current",
        "time": "2024-01-10T08:50:00.000Z",
        "message": "Your pizza is 70% complete, salad is ready"
      },
      {
        "stage": "Ready for Pickup",
        "status": "upcoming",
        "estimated_time": "2024-01-10T09:05:00.000Z",
        "message": "Your order will be ready for pickup"
      }
    ]
  },
  "items_progress": [
    {
      "name": "Margherita Pizza",
      "quantity": 2,
      "status": "preparing",
      "progress": 70,
      "estimated_completion": "2024-01-10T09:05:00.000Z"
    },
    {
      "name": "Caesar Salad",
      "quantity": 1,
      "status": "ready",
      "progress": 100,
      "completed_at": "2024-01-10T08:48:00.000Z"
    }
  ],
  "notifications": {
    "next_update": "When your order is ready",
    "call_waiter": {
      "available": true,
      "button_text": "Call Waiter",
      "action": "call_waiter"
    }
  }
}
```

---

## WebSocket Event Types

### Connection Events

#### 1. Client Connection
```javascript
// Client connects to WebSocket
socket.emit('authenticate', {
  token: 'jwt_token_here',
  user_type: 'customer', // or 'staff', 'manager'
  user_id: 'user_123'
})

// Server response
socket.on('authenticated', {
  status: 'success',
  user_id: 'user_123',
  subscriptions: ['order:order_456', 'vendor:vendor_789:customer']
})
```

#### 2. Subscription Management
```javascript
// Subscribe to order updates
socket.emit('subscribe', {
  type: 'order',
  target_id: 'order_456'
})

// Subscribe to department updates (staff only)
socket.emit('subscribe', {
  type: 'department',
  target_id: 'dept_kitchen'
})

// Subscribe to vendor updates (staff/manager)
socket.emit('subscribe', {
  type: 'vendor',
  target_id: 'vendor_789',
  sub_type: 'staff' // or 'manager'
})
```

### Order Events

#### 1. Order Status Update
```javascript
socket.on('order_status_update', {
  type: 'order_status_update',
  order_id: 'order_456',
  status: 'Ready',
  previous_status: 'Processing',
  updated_by: 'Mario Chef',
  timestamp: '2024-01-10T08:55:00.000Z',
  message: 'Your order is ready for pickup',
  estimated_completion: null,
  progress_percentage: 100
})
```

#### 2. Item Status Update
```javascript
socket.on('item_status_update', {
  type: 'item_status_update',
  order_id: 'order_456',
  item_id: 1,
  product_name: 'Margherita Pizza',
  status: 'ready',
  previous_status: 'preparing',
  assigned_staff_id: 'chef_001',
  updated_by: 'Mario Chef',
  timestamp: '2024-01-10T08:55:00.000Z',
  progress_percentage: 100,
  department: 'Kitchen'
})
```

#### 3. Order Completion
```javascript
socket.on('order_completed', {
  type: 'order_completed',
  order_id: 'order_456',
  order_type: 'Instant',
  delivery_type: 'Dinein',
  completion_time: '2024-01-10T09:00:00.000Z',
  total_items: 2,
  department_breakdown: {
    'Kitchen': {
      'total': 2,
      'completed': 2
    }
  },
  message: 'Your order is complete and ready to be served'
})
```

### Staff Events

#### 1. Staff Assignment
```javascript
socket.on('staff_assignment', {
  type: 'staff_assignment',
  staff_id: 'chef_001',
  order_id: 'order_456',
  item_id: 1,
  department_id: 'dept_kitchen',
  assigned_by: 'Manager Smith',
  estimated_time: 15,
  priority: 3,
  timestamp: '2024-01-10T08:40:00.000Z',
  special_instructions: 'Extra cheese as requested'
})
```

#### 2. Overdue Alert
```javascript
socket.on('overdue_alert', {
  type: 'overdue_alert',
  order_id: 'order_456',
  item_id: 1,
  product_name: 'Margherita Pizza',
  department_id: 'dept_kitchen',
  assigned_staff_id: 'chef_001',
  overdue_minutes: 15,
  escalation_level: 'high',
  timestamp: '2024-01-10T09:00:00.000Z',
  recommended_actions: [
    'Escalate to manager',
    'Reassign to available staff',
    'Notify customer of delay'
  ]
})
```

### Department Events

#### 1. Department Workload Update
```javascript
socket.on('department_workload_update', {
  type: 'department_workload_update',
  department_id: 'dept_kitchen',
  workload: {
    pending_items: 3,
    preparing_items: 5,
    ready_items: 2,
    total_items: 10,
    staff_count: 4,
    capacity_utilization: 67
  },
  timestamp: '2024-01-10T08:55:00.000Z'
})
```

---

## Authentication & Authorization

### WebSocket Authentication
```javascript
// Include JWT token in connection
const socket = io('ws://localhost:3333', {
  auth: {
    token: 'jwt_token_here'
  }
})

// Server validates token and assigns user to appropriate rooms
```

### Room-Based Access Control
- **Customer Rooms**: `order:{orderId}`, `customer:{customerId}`
- **Staff Rooms**: `vendor:{vendorId}:staff`, `branch:{branchId}:staff`, `department:{departmentId}`
- **Manager Rooms**: `vendor:{vendorId}:manager`, `branch:{branchId}:manager`

---

## Core Workflows

### 1. Customer Order Tracking

```mermaid
sequenceDiagram
    participant Customer as Customer App
    participant WS as WebSocket Server
    participant API as Order API
    participant Kitchen as Kitchen Staff
    participant Service as Service Staff

    Customer->>WS: Connect & authenticate
    WS->>Customer: Connection confirmed
    Customer->>WS: Subscribe to order updates
    
    Kitchen->>API: Update item status to 'preparing'
    API->>WS: Broadcast item status update
    WS->>Customer: Item preparation started
    
    Kitchen->>API: Update item status to 'ready'
    API->>WS: Broadcast item status update
    WS->>Customer: Item ready notification
    
    Service->>API: Update item status to 'served'
    API->>WS: Broadcast order completion
    WS->>Customer: Order completed notification
```

### 2. Staff Coordination Workflow

```mermaid
sequenceDiagram
    participant Manager as Manager App
    participant WS as WebSocket Server
    participant Kitchen as Kitchen Staff
    participant Service as Service Staff
    participant Customer as Customer App

    Manager->>WS: Assign item to kitchen staff
    WS->>Kitchen: Staff assignment notification
    
    Kitchen->>WS: Update status to 'preparing'
    WS->>Manager: Workload update
    WS->>Customer: Preparation started
    
    Kitchen->>WS: Update status to 'ready'
    WS->>Service: Item ready for pickup
    WS->>Manager: Department status update
    WS->>Customer: Item ready notification
    
    Service->>WS: Update status to 'served'
    WS->>Manager: Order completion
    WS->>Customer: Order served notification
```

---

## Implementation Notes

### WebSocket Client Integration

#### React/TypeScript Example
```typescript
import { useWebSocket } from '@/hooks/useWebSocket'

const OrderTracking = ({ orderId }: { orderId: string }) => {
  const { socket, connected, subscribe } = useWebSocket()
  const [orderStatus, setOrderStatus] = useState(null)

  useEffect(() => {
    if (connected && socket) {
      // Subscribe to order updates
      subscribe('order', orderId)
      
      // Listen for order status updates
      socket.on('order_status_update', (data) => {
        if (data.order_id === orderId) {
          setOrderStatus(data)
        }
      })
      
      // Listen for item status updates
      socket.on('item_status_update', (data) => {
        if (data.order_id === orderId) {
          // Update specific item status
        }
      })
    }
  }, [connected, socket, orderId])

  return (
    <div>
      {orderStatus && (
        <div>
          <h3>Order Status: {orderStatus.status}</h3>
          <p>Progress: {orderStatus.progress_percentage}%</p>
        </div>
      )}
    </div>
  )
}
```

### Performance Optimization
- Use room-based broadcasting to limit message scope
- Implement connection pooling for high-traffic scenarios
- Use message queuing for reliable delivery
- Implement proper reconnection logic with exponential backoff

### Error Handling
- Handle WebSocket disconnections gracefully
- Implement fallback to HTTP polling if WebSocket fails
- Provide offline indicators and cached data
- Use proper error boundaries for WebSocket components

### Security Considerations
- Validate JWT tokens on WebSocket connections
- Implement rate limiting for WebSocket messages
- Use room-based access control
- Sanitize all broadcast data
