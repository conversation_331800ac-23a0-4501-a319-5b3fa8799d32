# Module 4: Order Creation & Management

## Overview

This module handles the complete order lifecycle for restaurants, from initial order creation through fulfillment and completion. It supports multiple order creation workflows including temp orders (draft), direct orders, and staff-assisted ordering with comprehensive order management capabilities.

## Business Capabilities

- **Multiple Order Creation Workflows**: Temp orders, direct orders, unified orders
- **Order Lifecycle Management**: Complete status tracking from creation to completion
- **Staff-Assisted Ordering**: Staff can create orders on behalf of customers
- **Order Validation**: Comprehensive validation for items, pricing, and business rules
- **Order Modification**: Update orders before and after placement
- **Multi-Delivery Types**: Dine-in, takeaway, delivery, self-pickup support
- **Order Tracking**: Real-time order status and progress tracking

---

## API Endpoints

### Order Creation

#### 1. Create Temp Order (Draft)
```http
POST /api/v1/temp-orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "vendorId": "vendor_789",
  "branchId": "branch_101",
  "sectionId": "section_123",
  "lotId": "lot_456",
  "staffId": "staff_789",
  "userId": "customer_123",
  "action": "Purchase",
  "type": "Instant",
  "delivery": "Dinein",
  "items": {
    "product_123": 2,
    "product_124": 1
  },
  "meta": {
    "customerName": "John Doe",
    "customerPhone": "254712345678",
    "customerEmail": "<EMAIL>",
    "specialInstructions": "No onions please",
    "tableNumber": "T-05"
  }
}
```

**Response (201 Created):**
```json
{
  "id": "order_456",
  "orderNumber": null,
  "vendorId": "vendor_789",
  "branchId": "branch_101",
  "sectionId": "section_123",
  "lotId": "lot_456",
  "userId": "customer_123",
  "staffId": "staff_789",
  "action": "Purchase",
  "type": "Instant",
  "delivery": "Dinein",
  "status": "Pending",
  "items": {
    "product_123": 2,
    "product_124": 1
  },
  "meta": {
    "customerName": "John Doe",
    "customerPhone": "254712345678",
    "customerEmail": "<EMAIL>",
    "specialInstructions": "No onions please",
    "tableNumber": "T-05",
    "temp_items": {
      "product_123": 2,
      "product_124": 1
    }
  },
  "pricing": {
    "subtotal": 2800,
    "modifiersTotal": 0,
    "chargesTotal": 280,
    "total": 3080,
    "invoiceAmount": 3080
  },
  "customer": {
    "id": "customer_123",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "254712345678"
  },
  "vendor": {
    "id": "vendor_789",
    "name": "Mama Oliech Restaurant",
    "type": "restaurant"
  },
  "branch": {
    "id": "branch_101",
    "name": "Main Branch"
  },
  "createdAt": "2024-01-10T08:30:00.000Z",
  "updatedAt": "2024-01-10T08:30:00.000Z"
}
```

#### 2. Place Temp Order (Convert to Active Order)
```http
POST /api/v1/temp-orders/{orderId}/place-order
Authorization: Bearer {staff_token}
Content-Type: application/json

{
  "ref": "ORD-2024-001",
  "meta": {
    "paymentMethod": "cash",
    "notes": "Customer paid in advance"
  }
}
```

**Response (200 OK):**
```json
{
  "id": "order_456",
  "orderNumber": "ORD-2024-001",
  "status": "Placed",
  "ref": "ORD-2024-001",
  "placedAt": "2024-01-10T08:35:00.000Z",
  "items": [
    {
      "id": 1,
      "orderId": "order_456",
      "productId": "product_123",
      "quantity": 2,
      "price": 1200,
      "status": "pending",
      "departmentId": "dept_kitchen",
      "assignedStaffId": null,
      "estimatedPreparationTime": 15,
      "product": {
        "id": "product_123",
        "name": "Margherita Pizza",
        "price": 1200
      }
    }
  ],
  "pricing": {
    "subtotal": 2800,
    "modifiersTotal": 0,
    "chargesTotal": 280,
    "total": 3080,
    "invoiceAmount": 3080
  }
}
```

#### 3. Create Direct Order (Unified)
```http
POST /api/v1/unified-orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": "customer_123",
  "vendorId": "vendor_789",
  "branchId": "branch_101",
  "sectionId": "section_123",
  "staffId": "staff_789",
  "action": "Purchase",
  "type": "Instant",
  "delivery": "Takeaway",
  "status": "Placed",
  "items": {
    "product_123": 2,
    "product_124": 1
  },
  "meta": {
    "customerName": "John Doe",
    "customerPhone": "254712345678",
    "paymentMethod": "mobile_money",
    "specialInstructions": "Extra spicy"
  }
}
```

#### 4. Create Branch Order
```http
POST /api/v1/branches/{branchId}/orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "staffId": "staff_789",
  "vendorId": "vendor_789",
  "sectionId": "section_123",
  "action": "Purchase",
  "type": "Instant",
  "delivery": "Delivery",
  "status": "Placed",
  "items": [
    {
      "product_123": 2
    }
  ],
  "meta": {
    "deliveryAddress": {
      "street": "123 Main Street",
      "city": "Nairobi",
      "phone": "254712345678"
    }
  }
}
```

### Order Management

#### 1. List Orders
```http
GET /api/v1/orders
Authorization: Bearer {token}
```

**Query Parameters:**
- `per` (number, default: 25): Items per page
- `page` (number, default: 1): Page number
- `order` (string, default: 'createdAt'): Order by field
- `sort` (string, default: 'desc'): Sort direction
- `status` (string): Filter by status
- `vendor` (string): Filter by vendor ID
- `branch` (string): Filter by branch ID
- `delivery` (string): Filter by delivery type
- `date_from` (date): Filter by date range
- `date_to` (date): Filter by date range

**Response (200 OK):**
```json
{
  "meta": {
    "total": 150,
    "per_page": 25,
    "current_page": 1,
    "last_page": 6
  },
  "data": [
    {
      "id": "order_456",
      "orderNumber": "ORD-2024-001",
      "vendorId": "vendor_789",
      "branchId": "branch_101",
      "userId": "customer_123",
      "staffId": "staff_789",
      "action": "Purchase",
      "type": "Instant",
      "delivery": "Dinein",
      "status": "Processing",
      "ref": "ORD-2024-001",
      "items": [
        {
          "id": 1,
          "productId": "product_123",
          "quantity": 2,
          "price": 1200,
          "status": "preparing",
          "departmentId": "dept_kitchen",
          "assignedStaffId": "chef_001",
          "estimatedPreparationTime": 15,
          "preparationStartedAt": "2024-01-10T08:40:00.000Z",
          "product": {
            "id": "product_123",
            "name": "Margherita Pizza",
            "category": "Main Course"
          },
          "department": {
            "id": "dept_kitchen",
            "name": "Kitchen"
          },
          "assignedStaff": {
            "id": "chef_001",
            "firstName": "Mario",
            "lastName": "Chef"
          }
        }
      ],
      "pricing": {
        "subtotal": 2800,
        "modifiersTotal": 0,
        "chargesTotal": 280,
        "total": 3080,
        "invoiceAmount": 3080
      },
      "customer": {
        "id": "customer_123",
        "firstName": "John",
        "lastName": "Doe",
        "phone": "254712345678"
      },
      "vendor": {
        "id": "vendor_789",
        "name": "Mama Oliech Restaurant"
      },
      "branch": {
        "id": "branch_101",
        "name": "Main Branch"
      },
      "staff": {
        "id": "staff_789",
        "firstName": "Jane",
        "lastName": "Waiter"
      },
      "orderSummary": {
        "totalItems": 2,
        "completedItems": 0,
        "pendingItems": 2,
        "estimatedCompletionTime": "2024-01-10T08:55:00.000Z",
        "departmentBreakdown": {
          "Kitchen": {
            "total": 2,
            "pending": 0,
            "preparing": 2,
            "ready": 0,
            "served": 0
          }
        }
      },
      "createdAt": "2024-01-10T08:30:00.000Z",
      "updatedAt": "2024-01-10T08:40:00.000Z",
      "placedAt": "2024-01-10T08:35:00.000Z"
    }
  ]
}
```

#### 2. Get Order Details
```http
GET /api/v1/orders/{orderId}
Authorization: Bearer {token}
```

#### 3. Update Order
```http
PUT /api/v1/orders/{orderId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "staffId": "staff_789",
  "delivery": "Takeaway",
  "status": "Processing",
  "ref": "ORD-2024-001-UPDATED",
  "meta": {
    "specialInstructions": "Updated instructions",
    "paymentMethod": "card"
  },
  "auto_update_status": true
}
```

#### 4. Delete Order
```http
DELETE /api/v1/orders/{orderId}
Authorization: Bearer {token}
```

### Order Status Management

#### 1. Update Order Status
```http
PATCH /api/v1/orders/{orderId}/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "Ready",
  "notes": "All items prepared and ready for pickup"
}
```

#### 2. Get Order Status History
```http
GET /api/v1/orders/{orderId}/status-history
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "order_id": "order_456",
  "current_status": "Ready",
  "status_history": [
    {
      "status": "Pending",
      "timestamp": "2024-01-10T08:30:00.000Z",
      "updated_by": "system",
      "notes": "Order created"
    },
    {
      "status": "Placed",
      "timestamp": "2024-01-10T08:35:00.000Z",
      "updated_by": "Jane Waiter",
      "notes": "Order placed by staff"
    },
    {
      "status": "Processing",
      "timestamp": "2024-01-10T08:40:00.000Z",
      "updated_by": "Mario Chef",
      "notes": "Kitchen started preparation"
    },
    {
      "status": "Ready",
      "timestamp": "2024-01-10T08:55:00.000Z",
      "updated_by": "Mario Chef",
      "notes": "All items prepared and ready for pickup"
    }
  ]
}
```

### User Orders

#### 1. List User Orders
```http
GET /api/v1/users/{userId}/orders
Authorization: Bearer {token}
```

#### 2. Create User Order
```http
POST /api/v1/users/{userId}/orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "staffId": "staff_789",
  "vendorId": "vendor_789",
  "branchId": "branch_101",
  "action": "Purchase",
  "type": "Instant",
  "delivery": "Delivery",
  "status": "Placed",
  "items": [
    {
      "product_123": 2,
      "product_124": 1
    }
  ]
}
```

---

## Authentication & Authorization

### Required Headers
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### Role-Based Permissions
- **Admin**: Full order management across all vendors
- **Vendor**: Manage orders for their vendor
- **Manager**: Manage orders for their branch
- **Staff**: Create orders, update assigned orders
- **Customer**: View own orders, create self-service orders

### Special Permissions
- **place-order**: Required for placing temp orders (staff only)
- **modify-order**: Required for modifying placed orders
- **cancel-order**: Required for canceling orders

---

## Core Workflows

### 1. Temp Order to Placed Order Workflow

```mermaid
sequenceDiagram
    participant Staff as Restaurant Staff
    participant API as Order API
    participant DB as Database
    participant Kitchen as Kitchen System
    participant Customer as Customer

    Staff->>API: POST /temp-orders
    Note over Staff,API: Create draft order with items
    
    API->>DB: Create order with status='Pending'
    API->>API: Calculate pricing
    DB-->>API: Order created
    API-->>Staff: Temp order response
    
    Staff->>Customer: Review order details
    Customer->>Staff: Confirm order
    
    Staff->>API: POST /temp-orders/{id}/place-order
    API->>DB: Update status to 'Placed'
    API->>DB: Convert temp_items to OrderItems
    API->>DB: Generate order number
    API->>DB: Create invoice
    
    DB-->>API: Order placed successfully
    API->>Kitchen: Notify new order
    API-->>Staff: Placed order response
```

### 2. Direct Order Creation Workflow

```mermaid
sequenceDiagram
    participant Staff as Restaurant Staff
    participant API as Order API
    participant DB as Database
    participant Payment as Payment System
    participant Kitchen as Kitchen System

    Staff->>API: POST /unified-orders
    Note over Staff,API: Direct order with status='Placed'
    
    API->>DB: Validate items and pricing
    API->>DB: Create order with OrderItems
    API->>DB: Generate order number
    API->>Payment: Create invoice
    
    Payment-->>API: Invoice created
    DB-->>API: Order created
    API->>Kitchen: Notify new order
    API-->>Staff: Order response
```

### 3. Order Status Progression

```mermaid
flowchart TD
    A[Pending] --> B[Placed]
    B --> C[Processing]
    C --> D[Ready]
    D --> E[Delivering]
    E --> F[Delivered]
    F --> G[Completed]
    
    B --> H[Cancelled]
    C --> H
    D --> H
    
    D --> I[Picked Up]
    I --> G
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#9f9,stroke:#333,stroke-width:2px
    style H fill:#f99,stroke:#333,stroke-width:2px
```

---

## Role-Based Operations

### Restaurant Staff Operations
- Create temp orders for walk-in customers
- Place orders after customer confirmation
- Update order details before placement
- View assigned orders and their status
- Cancel orders when necessary

### Kitchen Staff Operations
- View new orders assigned to kitchen
- Update order status to 'Processing'
- Mark orders as 'Ready' when completed
- View order preparation details and special instructions

### Manager Operations
- View all branch orders
- Override order statuses
- Generate order reports
- Manage order cancellations and refunds

### Customer Operations
- Create self-service orders
- View own order history
- Track order status in real-time
- Cancel orders within allowed timeframe

---

## Data Models

### Order Schema
```typescript
interface Order {
  id: string
  orderNumber: string | null
  vendorId: string
  branchId: string
  sectionId: string
  lotId: string
  userId: string
  staffId: string
  action: 'Purchase' | 'Booking' | 'Registration' | 'Access' | 'Process'
  type: 'Preorder' | 'Instant'
  delivery: 'Takeaway' | 'Dinein' | 'Delivery' | 'Selfpick'
  status: 'Pending' | 'Placed' | 'Processing' | 'Ready' | 'Delivering' | 'Delivered' | 'Completed' | 'Cancelled'
  ref: string | null
  items: Record<string, number> | null
  meta: Record<string, any>
  pricing: OrderPricing
  customer: Customer
  vendor: Vendor
  branch: Branch
  staff: User
  orderItems: OrderItem[]
  invoices: Invoice[]
  payments: Payment[]
  createdAt: string
  updatedAt: string
  placedAt: string | null
  completedAt: string | null
}
```

### Order Item Schema
```typescript
interface OrderItem {
  id: number
  orderId: string
  productId: string
  quantity: number
  price: number
  status: 'pending' | 'preparing' | 'ready' | 'served' | 'cancelled'
  departmentId: string | null
  assignedStaffId: string | null
  estimatedPreparationTime: number | null
  preparationStartedAt: string | null
  preparationCompletedAt: string | null
  servedAt: string | null
  specialInstructions: string | null
  product: Product
  department: Department
  assignedStaff: User
}
```

### Order Pricing Schema
```typescript
interface OrderPricing {
  subtotal: number
  modifiersTotal: number
  chargesTotal: number
  total: number
  invoiceAmount: number
  discounts?: number
  taxes?: number
}
```

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Order must contain at least one item",
  "details": "Cannot create an order without items"
}
```

#### 403 Forbidden
```json
{
  "error": "Only authorized staff can place orders"
}
```

#### 404 Not Found
```json
{
  "error": "Order not found"
}
```

#### 422 Unprocessable Entity
```json
{
  "error": "Invalid order data",
  "details": {
    "vendorId": ["Vendor ID is required"],
    "items": ["At least one item is required"]
  }
}
```

### Retry Strategies
- **Order Creation**: Retry with exponential backoff
- **Status Updates**: Implement optimistic updates with rollback
- **Payment Processing**: Retry failed payment operations
- **Item Validation**: Re-validate items if products change

---

## Performance Optimization

### Caching Recommendations
- **Order Lists**: Cache for 5 minutes
- **Order Details**: Cache for 2 minutes
- **Order Status**: Real-time, no caching
- **Product Pricing**: Cache for 30 minutes

### Query Optimization
- Use pagination for large order lists
- Preload related data (customer, vendor, items)
- Implement database indexes on frequently queried fields
- Use efficient filtering and sorting

### Best Practices
- Implement optimistic updates for status changes
- Use WebSocket for real-time order updates
- Batch order item operations when possible
- Implement proper error boundaries and fallbacks

---

## Implementation Notes

### Order Validation
- Validate product availability before order creation
- Check vendor/branch operating hours
- Verify customer information completeness
- Validate payment method compatibility

### Status Management
- Implement proper status transition validation
- Use database transactions for status updates
- Maintain audit trail for all status changes
- Implement automatic status progression where appropriate

### Integration Patterns
- Use event-driven architecture for order updates
- Implement proper order state management
- Support offline order creation with sync
- Use proper error handling and user feedback

### Technology-Agnostic Patterns
- Implement order state machines
- Use repository pattern for data access
- Implement proper loading states and skeletons
- Use optimistic updates with proper rollback mechanisms
