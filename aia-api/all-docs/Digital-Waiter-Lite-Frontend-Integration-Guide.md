# Digital Waiter Lite Frontend Integration Guide

## Overview

This module provides a comprehensive API workflow for implementing a customer pre-sign-in browsing experience. Digital Waiter Lite enables customers to scan QR codes, browse digital menus, search products, and view detailed product information without authentication, with checkout attempts triggering the sign-in flow.

## Business Capabilities

- **QR-Based Vendor Discovery**: Scan QR codes to access vendor/branch information
- **Digital Menu Browsing**: View complete product catalogs without authentication
- **Product Search & Filtering**: Advanced search capabilities with category and price filtering
- **Product Detail Views**: Comprehensive product information including modifiers and pricing
- **Authentication Gateway**: Seamless transition to sign-in when checkout is attempted
- **Public API Access**: All browsing functionality available without authentication

---

## API Endpoints

### Base Configuration

**Base URL**: `http://127.0.0.1:7077` (Development) / `https://uat-api.appinapp.ke` (UAT)
**API Version**: `v1`
**Authentication**: None required for public endpoints
**Content-Type**: `application/json`

---

## Workflow A: QR-Based Vendor/Branch Discovery

### A1. Scan QR Code and Extract Vendor/Branch ID

**QR Code Format Examples:**
```
https://app.appinapp.ke/vendor/01jxyn97gj6659hc0qaw14kt4e
https://app.appinapp.ke/branch/01jxyn97gmp3bw3914nh4rvdee
```

**ID Extraction Logic:**
- Extract vendor ID or branch ID from QR code URL
- Determine if scanning vendor or branch based on URL pattern

### A2. Get Vendor Information

```http
GET /v1/public/vendors/{vendor_id}
```

**Example Request:**
```http
GET /v1/public/vendors/01jxyn97gj6659hc0qaw14kt4e
```

**Response (200 OK):**
```json
{
  "id": "01jxyn97gj6659hc0qaw14kt4e",
  "name": "RocoMamas Kenya",
  "slug": "rocomamas-kenya-P0WWVJ",
  "details": "A smashburger restaurant offering burgers, wings, ribs and freakshakes with a bold, casual vibe.",
  "logoUrl": null,
  "cover": {
    "url": "https://uat-aia-bucket.s3.eu-north-1.amazonaws.com/covers/cmc0damha01wn4go751mi8yiu.jpeg",
    "name": "covers/cmc0damha01wn4go751mi8yiu.jpeg",
    "extname": "jpeg",
    "size": 14723,
    "mimeType": "image/jpeg"
  },
  "createdAt": "2025-06-17T12:59:46.962+03:00",
  "branches": [
    {
      "id": "01jxyn97gmp3bw3914nh4rvdee",
      "vendorId": "01jxyn97gj6659hc0qaw14kt4e",
      "name": "RocoMamas Village Market",
      "details": "Popular fast-food branch in Village Market serving smashburgers, ribs, wings & shakes.",
      "email": "<EMAIL>",
      "phone": "254743162600",
      "location": {
        "name": "RocoMamas Village Market",
        "address": "Ground Floor Courtyard, Village Market, Limuru Road, Nairobi, Kenya",
        "coordinates": {
          "lat": -1.27,
          "lng": 36.8114
        }
      }
    }
  ],
  "categories": [
    {
      "id": "01jrxmb2mfssjcf2qyhca73b5s",
      "name": "Restaurants"
    }
  ],
  "specialities": []
}
```

### A3. Get Branch Information (Alternative)

```http
GET /v1/public/branches/{branch_id}
```

**Example Request:**
```http
GET /v1/public/branches/01jxyn97gmp3bw3914nh4rvdee
```

**Response (200 OK):**
```json
{
  "id": "01jxyn97gmp3bw3914nh4rvdee",
  "name": "RocoMamas Village Market",
  "details": "Popular fast-food branch in Village Market serving smashburgers, ribs, wings & shakes.",
  "location": {
    "name": "RocoMamas Village Market",
    "address": "Ground Floor Courtyard, Village Market, Limuru Road, Nairobi, Kenya",
    "coordinates": {
      "lat": -1.27,
      "lng": 36.8114
    }
  },
  "image": null,
  "createdAt": "2025-06-17T12:59:46.965+03:00",
  "vendor": {
    "id": "01jxyn97gj6659hc0qaw14kt4e",
    "name": "RocoMamas Kenya",
    "slug": "rocomamas-kenya-P0WWVJ",
    "details": "A smashburger restaurant offering burgers, wings, ribs and freakshakes with a bold, casual vibe.",
    "logo": {
      "url": "https://uat-aia-bucket.s3.eu-north-1.amazonaws.com/logos/cmc0dal9h01wm4go7fo2p8u4y.png"
    },
    "cover": {
      "url": "https://uat-aia-bucket.s3.eu-north-1.amazonaws.com/covers/cmc0damha01wn4go751mi8yiu.jpeg"
    }
  }
}
```

---

## Workflow B: Digital Menu Browsing

### B1. Get Branch Product Catalog

```http
GET /v1/public/branches/{branch_id}/products
```

**Query Parameters:**
- `per` (optional): Items per page (default: 25, max: 100)
- `page` (optional): Page number (default: 1)
- `order` (optional): Sort field (default: 'createdAt')
- `sort` (optional): Sort direction - 'asc' or 'desc' (default: 'desc')

**Example Request:**
```http
GET /v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?per=10&page=1&order=name&sort=asc
```

**Response (200 OK):**
```json
{
  "meta": {
    "total": 604,
    "perPage": 10,
    "currentPage": 1,
    "lastPage": 61,
    "firstPage": 1,
    "firstPageUrl": "/?page=1",
    "lastPageUrl": "/?page=61",
    "nextPageUrl": "/?page=2",
    "previousPageUrl": null
  },
  "data": [
    {
      "id": "01js9v2c2xn455e38jvj3hhhb5",
      "name": "Virgin Colada",
      "details": "Pineapple Juice & Coconut Cream.",
      "price": 600,
      "discounted": null,
      "image": null,
      "createdAt": "2025-04-20T17:54:03.357+03:00",
      "category": {
        "id": "01jrt0wp2dfeykeb76jvnztgbs",
        "name": "Mocktails",
        "slug": "mocktails"
      },
      "service": {
        "id": "01jrsjk1hj25wabb8ap26g57qa",
        "name": "Buy Food & Drinks",
        "slug": "food-drinks"
      },
      "vendor": {
        "id": "01js22288vefwkwpz8m94zt9jc",
        "name": "Roberto's Italian Restaurant",
        "slug": "roberto's-italian-restaurant-3WD4A2"
      }
    }
  ]
}
```

### B2. Get All Public Products (Alternative)

```http
GET /v1/public/products
```

**Query Parameters:** Same as branch products endpoint

**Example Request:**
```http
GET /v1/public/products?per=20&page=1
```

---

## Workflow C: Product Search & Filtering

### C1. Search Products by Name/Description

```http
GET /v1/public/branches/{branch_id}/products?search={query}
```

**Example Request:**
```http
GET /v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?search=virgin&per=5
```

### C2. Filter by Category

```http
GET /v1/public/branches/{branch_id}/products?category={category_name}
```

**Example Request:**
```http
GET /v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?category=Mocktails
```

### C3. Filter by Price Range

```http
GET /v1/public/branches/{branch_id}/products?price_min={min}&price_max={max}
```

**Example Request:**
```http
GET /v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?price_min=500&price_max=1000
```

### C4. Combined Filtering

```http
GET /v1/public/branches/{branch_id}/products?search=chicken&category=Main%20Course&price_min=800&price_max=1500&per=10&page=1&order=price&sort=asc
```

---

## Workflow D: Single Product Details

### D1. Get Product Details by ID

```http
GET /v1/public/products/{product_id}
```

**Example Request:**
```http
GET /v1/public/products/01js9v2c2xn455e38jvj3hhhb5
```

**Response (200 OK):**
```json
{
  "id": "01js9v2c2xn455e38jvj3hhhb5",
  "name": "Virgin Colada",
  "details": "Pineapple Juice & Coconut Cream.",
  "price": 600,
  "discounted": null,
  "image": null,
  "createdAt": "2025-04-20T17:54:03.357+03:00",
  "category": {
    "id": "01jrt0wp2dfeykeb76jvnztgbs",
    "name": "Mocktails",
    "slug": "mocktails",
    "details": null,
    "image": null
  },
  "vendor": {
    "id": "01js22288vefwkwpz8m94zt9jc",
    "name": "Roberto's Italian Restaurant",
    "slug": "roberto's-italian-restaurant-3WD4A2",
    "details": "Authentic Italian cuisine with wood-fired pizzas and homemade gelato",
    "logo": {
      "url": "https://uat-aia-bucket.s3.eu-north-1.amazonaws.com/logos/cmagzjmjq09vwydo7gym3akff.png"
    }
  },
  "branch": {
    "id": "01js22288wx16qdvtpmzbqftbw",
    "name": "Roberto's Mombasa",
    "location": {
      "address": "Mombasa Road, Nairobi"
    }
  },
  "service": {
    "id": "01jrsjk1hj25wabb8ap26g57qa",
    "name": "Buy Food & Drinks",
    "slug": "food-drinks",
    "details": "Buy food and drinks."
  }
}
```

### D2. Get Product Under Specific Branch

```http
GET /v1/public/branches/{branch_id}/products/{product_id}
```

**Example Request:**
```http
GET /v1/public/branches/01js22288wx16qdvtpmzbqftbw/products/01js9v2c2xn455e38jvj3hhhb5
```

---

## Workflow E: Authentication Gateway

### E1. Detect Checkout Attempt

When user attempts to add items to cart or proceed to checkout, trigger authentication flow.

**Frontend Logic:**
```javascript
// Pseudo-code for checkout attempt detection
function handleAddToCart(productId, quantity) {
  if (!isUserAuthenticated()) {
    // Redirect to sign-in with return URL
    redirectToSignIn(`/checkout?product=${productId}&qty=${quantity}`);
  } else {
    // Proceed with authenticated cart operations
    addToAuthenticatedCart(productId, quantity);
  }
}
```

### E2. Authentication Endpoints (Post Sign-In)

After successful authentication, use these endpoints for cart and order operations:

```http
POST /v1/auth/login
Authorization: Required for subsequent operations
```

**Authenticated Cart Operations:**
```http
GET /v1/getcart
POST /v1/postcart
Authorization: Bearer {token}
```

**Order Creation:**
```http
POST /v1/orders
POST /v1/temp-orders
Authorization: Bearer {token}
```

---

## TypeScript Interface Definitions

### Vendor Interface
```typescript
interface Vendor {
  id: string;
  name: string;
  slug: string;
  details: string;
  logoUrl?: string;
  cover?: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  };
  createdAt: string;
  branches: Branch[];
  categories: Category[];
  specialities: Speciality[];
}
```

### Branch Interface
```typescript
interface Branch {
  id: string;
  vendorId?: string;
  name: string;
  details: string;
  email?: string;
  phone?: string;
  image?: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  };
  location: {
    name: string;
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    place_id?: string;
  };
  createdAt: string;
  vendor?: Vendor;
}
```

### Product Interface
```typescript
interface Product {
  id: string;
  name: string;
  details: string;
  price: number;
  discounted?: number;
  image?: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  };
  createdAt: string;
  category: Category;
  vendor: Vendor;
  branch?: Branch;
  service: Service;
  meta?: {
    sku?: string;
  };
  notes?: Note[];
}
```

### Note Interface
```typescript
interface Note {
  id: number;
  type: string;
  content: string;
  branchId: string;
  userId: string;
  productId?: string;
  meta?: Record<string, any>;
  product?: Product;
  createdAt: string;
  updatedAt: string;
}
```

### Pagination Interface
```typescript
interface PaginationMeta {
  total: number;
  perPage: number;
  currentPage: number;
  lastPage: number;
  firstPage: number;
  firstPageUrl: string;
  lastPageUrl: string;
  nextPageUrl?: string;
  previousPageUrl?: string;
}

interface PaginatedResponse<T> {
  meta: PaginationMeta;
  data: T[];
}
```

### Category Interface
```typescript
interface Category {
  id: string;
  name: string;
  slug?: string;
  details?: string;
  image?: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  };
}
```

### Service Interface
```typescript
interface Service {
  id: string;
  name: string;
  slug: string;
  details: string;
  image?: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  };
  taskId?: string;
  active: boolean;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  order: number;
}
```

---

## Error Handling

### Common Error Responses

#### 404 Not Found
```json
{
  "message": "E_ROW_NOT_FOUND: Row not found",
  "code": "E_ROW_NOT_FOUND"
}
```

#### 500 Internal Server Error
```json
{
  "message": "Internal server error",
  "code": "E_INTERNAL_SERVER_ERROR"
}
```

### Error Handling Strategy

```typescript
async function handleApiRequest<T>(request: Promise<T>): Promise<T | null> {
  try {
    return await request;
  } catch (error) {
    if (error.response?.status === 404) {
      console.warn('Resource not found');
      return null;
    }
    if (error.response?.status >= 500) {
      console.error('Server error:', error);
      // Show user-friendly error message
    }
    throw error;
  }
}
```

---

## API Response Characteristics

### Pagination Support
- All list endpoints support pagination with `per` and `page` parameters
- Default page size is 25 items, maximum is 100
- Pagination metadata includes total count and navigation URLs

### Search and Filtering Parameters
- Search queries apply to product names and descriptions
- Category filtering uses exact category name matching
- Price filtering supports minimum and maximum price ranges
- Multiple filters can be combined in a single API request
- Search is case-insensitive with partial match support
- Empty search returns all products for the branch

### QR Code URL Format
- Vendor URLs: `https://app.appinapp.ke/vendor/{vendor_id}`
- Branch URLs: `https://app.appinapp.ke/branch/{branch_id}`
- Extract IDs from URLs for API calls
- Validate extracted IDs before making API requests

---

## Authentication Transition Points

### Public to Authenticated API Transition

**Public Browsing (No Authentication Required):**
- All `/v1/public/*` endpoints
- Vendor, branch, product, task, and service information
- Product search and filtering
- Product detail viewing

**Authentication Required For:**
- Cart operations: `GET /v1/getcart`, `POST /v1/postcart`
- Order creation: `POST /v1/orders`, `POST /v1/temp-orders`
- Customer profile access
- Payment processing

**Authentication Endpoints:**
```http
POST /v1/auth/login
POST /v1/auth/register
```

**Token Usage:**
```http
Authorization: Bearer {jwt_token}
```

### Data Security Notes
- Public endpoints expose only business-safe information
- No personal customer data in public responses
- Authentication tokens required for sensitive operations

---

## API Testing Examples

### Endpoint Validation

**Test Vendor Endpoint:**
```bash
curl -X GET "http://127.0.0.1:7077/v1/public/vendors/01jxyn97gj6659hc0qaw14kt4e"
```

**Test Branch Products:**
```bash
curl -X GET "http://127.0.0.1:7077/v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?per=5"
```

**Test Product Details:**
```bash
curl -X GET "http://127.0.0.1:7077/v1/public/products/01js9v2c2xn455e38jvj3hhhb5"
```

**Test Search and Filtering:**
```bash
# Search products
curl -X GET "http://127.0.0.1:7077/v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?search=virgin"

# Filter by category
curl -X GET "http://127.0.0.1:7077/v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?category=Mocktails"

# Filter by price range
curl -X GET "http://127.0.0.1:7077/v1/public/branches/01js22288wx16qdvtpmzbqftbw/products?price_min=500&price_max=1000"
```

### Error Response Testing

**Invalid IDs:**
```bash
# Returns 404 with error message
curl -X GET "http://127.0.0.1:7077/v1/public/vendors/invalid_id"
curl -X GET "http://127.0.0.1:7077/v1/public/branches/invalid_id/products"
```

---

## API Integration Checklist

### 1. Base Configuration
- **Development URL**: `http://127.0.0.1:7077/v1`
- **Production URL**: `https://uat-api.appinapp.ke/v1`
- **Content-Type**: `application/json`
- **Authentication**: None required for public endpoints

### 2. QR Code Processing
- Extract vendor/branch IDs from QR code URLs
- Validate IDs before API calls
- Handle malformed URLs gracefully

### 3. API Workflow Implementation
- **Workflow A**: Vendor/branch discovery via `/v1/public/vendors/{id}` or `/v1/public/branches/{id}`
- **Workflow B**: Product listing via `/v1/public/branches/{id}/products`
- **Workflow C**: Search and filtering using query parameters
- **Workflow D**: Product details via `/v1/public/products/{id}`
- **Workflow E**: Authentication transition for checkout operations

### 4. Error Handling
- Handle 404 responses for invalid IDs
- Manage 500 errors with appropriate fallbacks
- Validate API response structure before processing

### 5. Authentication Integration
- Implement transition from public to authenticated endpoints
- Handle JWT token management for post-authentication operations
- Integrate with existing authentication system

### Sample API Integration Flow

```typescript
// 1. QR Code Processing
const processQRCode = async (qrData: string) => {
  const { type, id } = parseQRCode(qrData); // Extract ID from URL

  if (type === 'vendor') {
    const response = await fetch(`/v1/public/vendors/${id}`);
    const vendor = await response.json();
    return vendor;
  } else if (type === 'branch') {
    const response = await fetch(`/v1/public/branches/${id}`);
    const branch = await response.json();
    return branch;
  }
};

// 2. Load Branch Products
const loadBranchProducts = async (branchId: string, page = 1, per = 25) => {
  const response = await fetch(
    `/v1/public/branches/${branchId}/products?page=${page}&per=${per}`
  );
  const products = await response.json();
  return products; // Returns { meta: PaginationMeta, data: Product[] }
};

// 3. Search Products
const searchProducts = async (branchId: string, query: string) => {
  const response = await fetch(
    `/v1/public/branches/${branchId}/products?search=${encodeURIComponent(query)}`
  );
  const results = await response.json();
  return results;
};

// 4. Get Product Details
const getProductDetails = async (productId: string) => {
  const response = await fetch(`/v1/public/products/${productId}`);
  const product = await response.json();
  return product;
};
```

**Your Digital Waiter Lite API integration is ready to begin!** This guide provides complete API workflow documentation for building a customer pre-sign-in browsing experience using the verified public endpoints.
