# Module 1: Authentication & Staff Management

## Overview

This module provides the foundation for all restaurant operations by handling user authentication, staff management, and role-based access control. It supports both customer and staff authentication with different permission levels for various restaurant roles.

## Business Capabilities

- **Multi-Role Authentication**: Support for customers, waiters, chefs, managers, admins, and vendors
- **Staff Management**: Registration, role assignment, and permission management
- **Hierarchical Access Control**: 5-level access system (Admin → Owner → Manager → Supervisor → Staff)
- **Vendor-Specific Staff**: Staff can be associated with specific vendors and branches
- **Token Management**: JWT-based authentication with refresh token support

---

## API Endpoints

### Authentication Endpoints

#### 1. Customer Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",  // Email or phone number
  "password": "password123",
  "portal": false                      // Optional: for admin portal access
}
```

**Response (200 OK):**
```json
{
  "user": {
    "id": "user_123",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "254712345678",
    "status": "Active",
    "roles": [
      {
        "id": "role_1",
        "name": "customer"
      }
    ]
  },
  "type": "bearer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "a1b2c3d4e5f6..."
}
```

#### 2. Staff Login
```http
POST /api/v1/auth/login/staff
Content-Type: application/json

{
  "username": "<EMAIL>",  // Email or phone number
  "password": "password123",
  "identifier": "254722332233"          // Staff identifier for vendor/branch
}
```

**Response (200 OK):**
```json
{
  "user": {
    "id": "staff_456",
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "phone": "254712345679",
    "status": "Active",
    "roles": [
      {
        "id": "role_2",
        "name": "waiter"
      }
    ]
  },
  "vendor": {
    "id": "vendor_789",
    "name": "Mama Oliech Restaurant",
    "type": "restaurant"
  },
  "branch": {
    "id": "branch_101",
    "name": "Main Branch",
    "address": "Nairobi, Kenya"
  },
  "type": "bearer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "x1y2z3a4b5c6...",
  "roleActions": [
    "orders",
    "new_order",
    "onboard",
    "menu",
    "reports",
    "rate_customer"
  ],
  "expiresAt": "2024-01-15T10:30:00.000Z"
}
```

#### 3. Get Current User Info
```http
GET /api/v1/auth/me
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "id": "user_123",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "254712345678",
  "status": "Active",
  "roles": ["customer"],
  "vendor": null,
  "branch": null
}
```

#### 4. Get Staff User Info
```http
GET /api/v1/auth/login/staff/me
Authorization: Bearer {token}
```

#### 5. Refresh Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "a1b2c3d4e5f6..."
}
```

**Response (200 OK):**
```json
{
  "type": "bearer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "new_refresh_token_here"
}
```

#### 6. Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

### Registration Endpoints

#### 1. Customer Registration
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "254712345678",
  "password": "password123",
  "role": "customer"
}
```

#### 2. Staff-Assisted Customer Registration
```http
POST /api/v1/auth/register/{staffId}
Authorization: Bearer {staff_token}
Content-Type: application/json

{
  "firstName": "Jane",
  "lastName": "Customer",
  "email": "<EMAIL>",
  "phone": "254712345679",
  "password": "password123",
  "gender": "female",
  "dob": "1990-01-01",
  "idpass": "12345678"
}
```

### Role Management Endpoints

#### 1. Add Role to User
```http
POST /api/v1/auth/addroletouser
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "userId": "user_123",
  "role": "waiter"
}
```

#### 2. Remove Role from User
```http
POST /api/v1/auth/removerolefromuser
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "userId": "user_123",
  "role": "waiter"
}
```

#### 3. Get User Role Actions
```http
GET /api/v1/auth/role-actions
Authorization: Bearer {token}
```

#### 4. Get Staff Role Actions
```http
GET /api/v1/auth/login/staff/role-actions
Authorization: Bearer {staff_token}
```

---

## Authentication & Authorization

### Required Headers
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### Token Expiration
- **Access Token**: 7 days
- **Refresh Token**: 30 days

### Role Hierarchy
1. **Admin** (Level 5): Full system access
2. **Owner** (Level 4): Vendor-wide access
3. **Manager** (Level 3): Branch-wide access
4. **Supervisor** (Level 2): Department + supervised staff access
5. **Staff** (Level 1): Own assigned tasks only

### Available Roles
- `admin`: System administrator
- `vendor`: Vendor owner
- `manager`: Branch manager
- `supervisor`: Department supervisor
- `waiter`: Restaurant waiter
- `chef`: Kitchen chef
- `barista`: Coffee/drinks specialist
- `cashier`: Payment processing
- `rider`: Delivery personnel
- `customer`: End customer

---

## Core Workflows

### 1. Staff Login Workflow

```mermaid
sequenceDiagram
    participant App as Staff App
    participant API as Auth API
    participant DB as Database

    App->>API: POST /auth/login/staff
    Note over App,API: {username, password, identifier}
    
    API->>DB: Validate user credentials
    API->>DB: Check staff association with vendor
    API->>DB: Load user roles
    
    DB-->>API: User + Vendor + Branch data
    API->>API: Generate JWT + Refresh tokens
    API->>API: Get role-based UI actions
    
    API-->>App: Login response with tokens
    Note over API,App: {user, vendor, branch, tokens, roleActions}
```

### 2. Customer Registration by Staff

```mermaid
sequenceDiagram
    participant Staff as Staff App
    participant API as Auth API
    participant Customer as Customer
    participant DB as Database

    Staff->>API: POST /auth/register/{staffId}
    Note over Staff,API: Customer details + Staff token
    
    API->>DB: Validate staff permissions
    API->>DB: Check vendor association
    API->>DB: Create customer account
    API->>DB: Link customer to vendor/branch
    
    DB-->>API: Customer created
    API-->>Staff: Registration success
    
    Staff->>Customer: Provide login credentials
```

### 3. Role-Based Access Control

```mermaid
flowchart TD
    A[API Request] --> B{Valid Token?}
    B -->|No| C[401 Unauthorized]
    B -->|Yes| D[Extract User Roles]
    
    D --> E{Required Permission?}
    E -->|Admin| F[Full Access]
    E -->|Vendor| G[Vendor Scope]
    E -->|Manager| H[Branch Scope]
    E -->|Staff| I[Personal Scope]
    
    F --> J[Process Request]
    G --> K{Same Vendor?}
    H --> L{Same Branch?}
    I --> M{Own Data?}
    
    K -->|Yes| J
    K -->|No| N[403 Forbidden]
    L -->|Yes| J
    L -->|No| N
    M -->|Yes| J
    M -->|No| N
```

---

## Role-Based Operations

### Admin Operations
- Full system access
- Manage all vendors and staff
- System configuration
- Global reporting

### Vendor Owner Operations
- Manage vendor settings
- Add/remove branches
- Manage vendor staff
- Vendor-wide reporting

### Manager Operations
- Manage branch operations
- Assign staff to departments
- Branch reporting
- Customer management

### Staff Operations
- View assigned orders
- Update order status
- Customer registration
- Personal performance metrics

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Invalid credentials."
}
```

#### 401 Unauthorized
```json
{
  "error": "Invalid or expired token"
}
```

#### 403 Forbidden
```json
{
  "error": "Insufficient privileges for this operation."
}
```

#### 409 Conflict
```json
{
  "error": "User already exists with this email/phone"
}
```

### Retry Strategies
- **Token Expired**: Automatically refresh using refresh token
- **Network Errors**: Exponential backoff (1s, 2s, 4s, 8s)
- **Rate Limiting**: Respect rate limit headers and retry after delay

---

## Performance Optimization

### Caching Recommendations
- **User Profile**: Cache for 1 hour
- **Role Actions**: Cache for 24 hours
- **Vendor/Branch Info**: Cache for 4 hours

### Token Management
- Store tokens securely (encrypted storage)
- Implement automatic token refresh
- Clear tokens on logout

### Best Practices
- Validate tokens on app startup
- Implement offline mode for basic operations
- Use role-based UI rendering
- Implement proper session management

---

## Implementation Notes

### Security Considerations
- Always use HTTPS in production
- Implement proper token storage
- Validate all user inputs
- Use role-based access control consistently

### Technology-Agnostic Patterns
- Implement token interceptors for API calls
- Use role-based routing/navigation
- Implement proper error boundaries
- Use state management for user context

### Integration Patterns
- Centralized authentication service
- Role-based component rendering
- Automatic token refresh mechanism
- Proper logout cleanup
