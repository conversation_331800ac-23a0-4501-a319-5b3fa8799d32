# Module 3: Customer Management

## Overview

This module handles comprehensive customer management for restaurants, enabling staff to register customers, manage customer profiles, track customer-vendor relationships, and provide personalized service through customer data management.

## Business Capabilities

- **Customer Registration**: Staff-assisted and self-service customer registration
- **Customer Profiles**: Comprehensive customer information management
- **Vendor-Customer Relationships**: Track customer associations with specific vendors/branches
- **Customer Search & Discovery**: Find and manage existing customers
- **Customer Rating & Feedback**: Rate customer interactions and service quality
- **Customer Subscriptions**: Manage loyalty programs and subscription services
- **Customer Analytics**: Track customer behavior and preferences

---

## API Endpoints

### Customer Registration & Management

#### 1. Staff-Assisted Customer Registration
```http
POST /api/v1/auth/register/{staffId}
Authorization: Bearer {staff_token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "254712345678",
  "password": "securePassword123",
  "gender": "male",
  "dob": "1990-01-15",
  "idpass": "12345678"
}
```

**Response (201 Created):**
```json
{
  "id": "customer_123",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "254712345678",
  "gender": "male",
  "dob": "1990-01-15",
  "idpass": "12345678",
  "status": "Active",
  "registeredBy": {
    "staffId": "staff_456",
    "staffName": "Jane Smith",
    "vendorId": "vendor_789",
    "branchId": "branch_101"
  },
  "createdAt": "2024-01-10T08:30:00.000Z",
  "updatedAt": "2024-01-10T08:30:00.000Z"
}
```

#### 2. Self-Service Customer Registration
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "firstName": "Jane",
  "lastName": "Customer",
  "email": "<EMAIL>",
  "phone": "254712345679",
  "password": "password123",
  "role": "customer"
}
```

#### 3. Get Staff Customers
```http
GET /api/v1/auth/staff/{staffId}/customers
Authorization: Bearer {staff_token}
```

**Query Parameters:**
- `per` (number, default: 10): Items per page
- `page` (number, default: 1): Page number
- `order` (string, default: 'createdAt'): Order by field
- `sort` (string, default: 'asc'): Sort direction

**Response (200 OK):**
```json
{
  "meta": {
    "total": 25,
    "per_page": 10,
    "current_page": 1,
    "last_page": 3
  },
  "data": [
    {
      "id": "customer_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "254712345678",
      "status": "Active",
      "totalOrders": 15,
      "totalSpent": 45000,
      "lastOrderDate": "2024-01-09T14:30:00.000Z",
      "registrationDate": "2024-01-01T08:30:00.000Z",
      "loyaltyPoints": 450,
      "preferredBranch": {
        "id": "branch_101",
        "name": "Main Branch"
      }
    }
  ]
}
```

### Vendor Customer Management

#### 1. List Vendor Customers
```http
GET /api/v1/vendors/{vendorId}/customers
Authorization: Bearer {token}
```

**Query Parameters:**
- Standard pagination and filtering parameters
- `branch` (string): Filter by specific branch
- `status` (string): Filter by customer status
- `registration_date_from` (date): Filter by registration date range
- `registration_date_to` (date): Filter by registration date range

**Response (200 OK):**
```json
{
  "meta": {
    "total": 150,
    "per_page": 10,
    "current_page": 1
  },
  "data": [
    {
      "id": "customer_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "254712345678",
      "status": "Active",
      "relationship": {
        "vendorId": "vendor_789",
        "branchId": "branch_101",
        "active": true,
        "registeredByStaffId": "staff_456",
        "registrationDate": "2024-01-01T08:30:00.000Z"
      },
      "orderStats": {
        "totalOrders": 15,
        "totalSpent": 45000,
        "averageOrderValue": 3000,
        "lastOrderDate": "2024-01-09T14:30:00.000Z"
      },
      "preferences": {
        "favoriteCategory": "Main Course",
        "dietaryRestrictions": ["vegetarian"],
        "preferredPaymentMethod": "mobile_money"
      }
    }
  ]
}
```

#### 2. Add Customer to Vendor
```http
POST /api/v1/vendors/{vendorId}/customers
Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": "customer_123",
  "branchId": "branch_101",
  "active": true
}
```

#### 3. Get Customer Details
```http
GET /api/v1/users/{customerId}
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
  "id": "customer_123",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "254712345678",
  "gender": "male",
  "dob": "1990-01-15",
  "status": "Active",
  "roles": ["customer"],
  "profile": {
    "avatar": {
      "url": "https://example.com/avatars/customer_123.jpg"
    },
    "preferences": {
      "language": "en",
      "notifications": {
        "email": true,
        "sms": true,
        "push": true
      },
      "dietary": ["vegetarian"],
      "allergies": ["nuts"]
    }
  },
  "addresses": [
    {
      "id": "addr_456",
      "type": "home",
      "street": "123 Main Street",
      "city": "Nairobi",
      "county": "Nairobi",
      "country": "Kenya",
      "postalCode": "00100",
      "isDefault": true
    }
  ],
  "vendorRelationships": [
    {
      "vendorId": "vendor_789",
      "vendorName": "Mama Oliech Restaurant",
      "branchId": "branch_101",
      "branchName": "Main Branch",
      "active": true,
      "registeredByStaffId": "staff_456",
      "registrationDate": "2024-01-01T08:30:00.000Z",
      "loyaltyPoints": 450,
      "membershipTier": "silver"
    }
  ],
  "orderHistory": {
    "totalOrders": 15,
    "totalSpent": 45000,
    "averageOrderValue": 3000,
    "lastOrderDate": "2024-01-09T14:30:00.000Z",
    "favoriteItems": [
      {
        "productId": "product_123",
        "productName": "Margherita Pizza",
        "orderCount": 8
      }
    ]
  },
  "createdAt": "2024-01-01T08:30:00.000Z",
  "updatedAt": "2024-01-10T08:30:00.000Z"
}
```

#### 4. Update Customer Profile
```http
PUT /api/v1/users/{customerId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "254712345678",
  "preferences": {
    "dietary": ["vegetarian", "gluten-free"],
    "allergies": ["nuts"],
    "notifications": {
      "email": true,
      "sms": false,
      "push": true
    }
  }
}
```

### Customer Search & Discovery

#### 1. Search Customers
```http
GET /api/v1/search?type=customers
Authorization: Bearer {token}
```

**Query Parameters:**
- `q` (string): Search query (name, email, phone)
- `vendor` (string): Filter by vendor ID
- `branch` (string): Filter by branch ID
- `status` (string): Filter by customer status
- `limit` (number, default: 20): Maximum results

**Response (200 OK):**
```json
{
  "results": [
    {
      "id": "customer_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "254712345678",
      "status": "Active",
      "highlight": {
        "field": "name",
        "match": "John Doe"
      },
      "orderCount": 15,
      "lastOrderDate": "2024-01-09T14:30:00.000Z"
    }
  ],
  "total": 1,
  "query": "john",
  "executionTime": 45
}
```

### Customer Addresses

#### 1. List Customer Addresses
```http
GET /api/v1/users/{customerId}/addresses
Authorization: Bearer {token}
```

#### 2. Add Customer Address
```http
POST /api/v1/users/{customerId}/addresses
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "home",
  "street": "456 Oak Avenue",
  "city": "Nairobi",
  "county": "Nairobi",
  "country": "Kenya",
  "postalCode": "00200",
  "isDefault": false
}
```

### Customer Ratings & Feedback

#### 1. List Customer Ratings
```http
GET /api/v1/ratings/customers
Authorization: Bearer {token}
```

**Query Parameters:**
- `customer_id` (string): Filter by customer ID
- `staff_id` (string): Filter by staff member who rated
- `points_min` (number): Minimum rating points
- `points_max` (number): Maximum rating points

**Response (200 OK):**
```json
{
  "meta": {
    "total": 50,
    "per_page": 10,
    "current_page": 1
  },
  "data": [
    {
      "id": 1,
      "staffId": "staff_456",
      "customerId": "customer_123",
      "name": "Service Quality",
      "points": 5,
      "comment": "Excellent customer, very polite and understanding",
      "meta": {
        "interaction_type": "order_service",
        "order_id": "order_789"
      },
      "staff": {
        "id": "staff_456",
        "firstName": "Jane",
        "lastName": "Smith",
        "role": "waiter"
      },
      "customer": {
        "id": "customer_123",
        "firstName": "John",
        "lastName": "Doe"
      },
      "createdAt": "2024-01-09T14:30:00.000Z"
    }
  ]
}
```

#### 2. Rate Customer
```http
POST /api/v1/ratings/customers
Authorization: Bearer {staff_token}
Content-Type: application/json

{
  "customerId": "customer_123",
  "name": "Service Quality",
  "points": 5,
  "comment": "Great customer, very patient and friendly",
  "meta": {
    "interaction_type": "order_service",
    "order_id": "order_789"
  }
}
```

### Customer Subscriptions

#### 1. List Customer Subscriptions
```http
GET /api/v1/customer-subscriptions
Authorization: Bearer {customer_token}
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": 1,
      "customerId": "customer_123",
      "planId": 1,
      "status": "active",
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.000Z",
      "billingCycle": "monthly",
      "amount": 2000,
      "currency": "KES",
      "autoRenew": true,
      "lastBilledAt": "2024-01-01T00:00:00.000Z",
      "nextBillingAt": "2024-02-01T00:00:00.000Z",
      "plan": {
        "id": 1,
        "name": "Premium Loyalty",
        "description": "Premium loyalty program with exclusive benefits",
        "benefits": [
          "10% discount on all orders",
          "Free delivery",
          "Priority customer service"
        ]
      }
    }
  ]
}
```

#### 2. Get Current Subscription
```http
GET /api/v1/customer-subscriptions/current
Authorization: Bearer {customer_token}
```

#### 3. Cancel Subscription
```http
POST /api/v1/customer-subscriptions/{subscriptionId}/cancel
Authorization: Bearer {customer_token}
Content-Type: application/json

{
  "reason": "No longer needed",
  "immediate": false
}
```

---

## Authentication & Authorization

### Required Headers
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### Role-Based Permissions
- **Admin**: Full customer management across all vendors
- **Vendor**: Manage customers associated with their vendor
- **Manager**: Manage customers for their branch
- **Staff**: Register customers, view assigned customers, rate customers
- **Customer**: Manage own profile and subscriptions

---

## Core Workflows

### 1. Staff-Assisted Customer Registration

```mermaid
sequenceDiagram
    participant Customer as Walk-in Customer
    participant Staff as Restaurant Staff
    participant API as Customer API
    participant DB as Database
    participant Notification as Notification Service

    Customer->>Staff: Provides personal information
    Staff->>API: POST /auth/register/{staffId}
    Note over Staff,API: Customer details + Staff token
    
    API->>DB: Validate staff permissions
    API->>DB: Check if customer exists
    API->>DB: Create customer account
    API->>DB: Link customer to vendor/branch
    
    DB-->>API: Customer created successfully
    API->>Notification: Send welcome OTP
    API-->>Staff: Registration response
    
    Staff->>Customer: Provide login credentials
    Staff->>Customer: Explain loyalty benefits
```

### 2. Customer Profile Management

```mermaid
sequenceDiagram
    participant Staff as Restaurant Staff
    participant API as Customer API
    participant Customer as Customer
    participant DB as Database

    Staff->>API: GET /users/{customerId}
    API->>DB: Fetch customer profile
    DB-->>API: Customer data with relationships
    API-->>Staff: Complete customer profile
    
    Staff->>API: PUT /users/{customerId}
    Note over Staff,API: Update preferences/info
    
    API->>DB: Update customer record
    DB-->>API: Update confirmation
    API-->>Staff: Profile updated
    
    Staff->>Customer: Inform about updates
```

### 3. Customer Search & Discovery

```mermaid
flowchart TD
    A[Staff Search Request] --> B{Search Type}
    B -->|Name| C[Search by Name]
    B -->|Phone| D[Search by Phone]
    B -->|Email| E[Search by Email]
    
    C --> F[Query Database]
    D --> F
    E --> F
    
    F --> G{Results Found?}
    G -->|Yes| H[Return Customer List]
    G -->|No| I[Suggest Registration]
    
    H --> J[Display Customer Details]
    I --> K[Initiate Registration Flow]
    
    J --> L[Select Customer]
    L --> M[Load Full Profile]
```

---

## Role-Based Operations

### Restaurant Staff Operations
- Register new customers during service
- Search for existing customers
- View customer order history and preferences
- Rate customer interactions
- Update customer contact information

### Manager Operations
- View all branch customers
- Analyze customer demographics and behavior
- Manage customer loyalty programs
- Review customer feedback and ratings
- Export customer data for marketing

### Customer Operations
- Update personal profile information
- Manage delivery addresses
- View order history and preferences
- Manage subscription services
- Update notification preferences

---

## Data Models

### Customer Profile Schema
```typescript
interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  gender?: 'male' | 'female' | 'other'
  dob?: string
  idpass?: string
  status: 'Active' | 'Inactive' | 'Suspended'
  roles: string[]
  profile: {
    avatar?: AttachmentContract
    preferences: {
      language: string
      notifications: {
        email: boolean
        sms: boolean
        push: boolean
      }
      dietary: string[]
      allergies: string[]
    }
  }
  addresses: Address[]
  vendorRelationships: VendorRelationship[]
  orderHistory: OrderHistory
  createdAt: string
  updatedAt: string
}
```

### Vendor Relationship Schema
```typescript
interface VendorRelationship {
  vendorId: string
  vendorName: string
  branchId: string
  branchName: string
  active: boolean
  registeredByStaffId: string
  registrationDate: string
  loyaltyPoints: number
  membershipTier: 'bronze' | 'silver' | 'gold' | 'platinum'
}
```

### Customer Rating Schema
```typescript
interface CustomerRating {
  id: number
  staffId: string
  customerId: string
  name: string
  points: number
  comment: string
  meta: Record<string, any>
  staff: Staff
  customer: Customer
  createdAt: string
  updatedAt: string
}
```

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Invalid customer data",
  "details": {
    "email": ["Email format is invalid"],
    "phone": ["Phone number is required"]
  }
}
```

#### 404 Not Found
```json
{
  "error": "Customer not found"
}
```

#### 409 Conflict
```json
{
  "error": "Customer already exists with this email/phone"
}
```

### Retry Strategies
- **Registration Conflicts**: Check existing customer and offer to link
- **Search Timeouts**: Implement progressive search with fallbacks
- **Profile Updates**: Use optimistic updates with rollback on failure

---

## Performance Optimization

### Caching Recommendations
- **Customer Profiles**: Cache for 1 hour
- **Search Results**: Cache for 15 minutes
- **Customer Lists**: Cache for 30 minutes
- **Subscription Data**: Cache for 4 hours

### Search Optimization
- Implement full-text search with indexing
- Use autocomplete with debouncing
- Cache frequent search queries
- Implement search result pagination

### Best Practices
- Lazy load customer order history
- Implement infinite scroll for customer lists
- Use optimistic updates for profile changes
- Implement proper data validation and sanitization

---

## Implementation Notes

### Privacy & Security
- Implement proper data encryption for sensitive information
- Follow GDPR/data protection regulations
- Provide customer data export/deletion capabilities
- Audit customer data access and modifications

### Integration Patterns
- Use customer context throughout the application
- Implement customer preference-based recommendations
- Integrate with loyalty and rewards systems
- Support customer segmentation for marketing

### Technology-Agnostic Patterns
- Implement customer state management
- Use proper form validation and error handling
- Implement customer search with filters
- Support offline customer data viewing
