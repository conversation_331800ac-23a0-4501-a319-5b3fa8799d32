import Order from 'App/Models/Order'
import Product from 'App/Models/Product'
import Kplc from 'App/Services/Kplc'

export default class ProcessKplc {
  public static async process(item: Product, order: Order): Promise<any> {
    switch (item.ref) {
      case 'QUERYBILL':
        const kplc = new Kplc()

        const response = order.meta.responses

        const itemResponse = response[item.id]

        Object.keys(itemResponse).forEach(async (section) => {
          const { account: accountNumber } = itemResponse[section]
          const kRes = await kplc.getAccountBalance({ accountNumber })

          console.log(accountNumber, kRes)
        })
        break
      case 'POSTPAID':
        // process kplc postpaid
        break
      default:
        break
    }
  }
}
