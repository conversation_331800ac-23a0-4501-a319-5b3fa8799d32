import { NotificationActionType } from 'App/Enums/NotificationActionType'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationType } from 'App/Enums/NotificationType'

/**
 * Helper class for creating contract-compliant notification data
 */
export class NotificationHelper {
  
  /**
   * Create a contract-compliant notification action
   */
  static createAction(
    type: string,
    label: string,
    context: Record<string, any> = {}
  ) {
    if (!NotificationActionType.isValid(type)) {
      throw new Error(`Invalid action type: ${type}`)
    }

    return {
      type,
      label,
      context
    }
  }

  /**
   * Create contract-compliant notification metadata
   */
  static createMeta(
    category: string,
    notificationType: string,
    priority?: string,
    entityId?: string | number,
    entityType?: string,
    additionalMeta: Record<string, any> = {}
  ) {
    if (!NotificationCategory.isValid(category)) {
      throw new Error(`Invalid category: ${category}`)
    }

    if (!NotificationType.isValid(notificationType)) {
      throw new Error(`Invalid notification type: ${notificationType}`)
    }

    const finalPriority = priority || NotificationType.getDefaultPriority(notificationType)
    
    if (!NotificationPriority.isValid(finalPriority)) {
      throw new Error(`Invalid priority: ${finalPriority}`)
    }

    return {
      category,
      notificationType,
      priority: finalPriority,
      ...(entityId && { entityId: String(entityId) }),
      ...(entityType && { entityType }),
      ...additionalMeta
    }
  }

  /**
   * Create complete contract-compliant notification data
   */
  static createNotificationData(
    title: string,
    body: string,
    actions: Array<{type: string, label: string, context?: Record<string, any>}>,
    meta: {
      category: string,
      notificationType: string,
      priority?: string,
      entityId?: string | number,
      entityType?: string,
      [key: string]: any
    },
    icon?: string,
    url?: string
  ) {
    // Validate and create actions
    const validatedActions = actions.map(action => 
      this.createAction(action.type, action.label, action.context || {})
    )

    // Extract additional meta fields
    const { category, notificationType, priority, entityId, entityType, ...additionalMeta } = meta

    // Create validated meta
    const validatedMeta = this.createMeta(
      category,
      notificationType,
      priority,
      entityId,
      entityType,
      additionalMeta
    )

    return {
      title,
      body,
      actions: validatedActions,
      meta: validatedMeta,
      ...(icon && { icon }),
      ...(url && { url })
    }
  }

  /**
   * Create billing-specific actions
   */
  static createBillingActions(entityId: string | number, entityType: 'bill' | 'invoice' | 'statement') {
    const actions: Array<{type: string, label: string, context: Record<string, any>}> = []

    switch (entityType) {
      case 'bill':
        actions.push(
          this.createAction(NotificationActionType.PAY_BILL, 'Pay Now', { billId: entityId }),
          this.createAction(NotificationActionType.VIEW_BILL_DETAILS, 'View Bill', { billId: entityId }),
          this.createAction(NotificationActionType.DISPUTE_BILL, 'Dispute', { billId: entityId })
        )
        break

      case 'invoice':
        actions.push(
          this.createAction(NotificationActionType.PAY_INVOICE, 'Pay Invoice', { invoiceId: entityId }),
          this.createAction(NotificationActionType.VIEW_INVOICE, 'View Invoice', { invoiceId: entityId }),
          this.createAction(NotificationActionType.DOWNLOAD, 'Download PDF', { 
            invoiceId: entityId,
            downloadUrl: `/api/invoices/${entityId}/download`
          })
        )
        break

      case 'statement':
        actions.push(
          this.createAction(NotificationActionType.VIEW_STATEMENT, 'View Statement', { statementId: entityId }),
          this.createAction(NotificationActionType.DOWNLOAD_STATEMENT, 'Download PDF', { 
            statementId: entityId,
            downloadUrl: `/api/billing/statements/${entityId}/download`
          }),
          this.createAction(NotificationActionType.VIEW_PAYMENT_HISTORY, 'Payment History', {})
        )
        break
    }

    return actions
  }

  /**
   * Create order-specific actions
   */
  static createOrderActions(orderId: string | number, orderStatus?: string, vendorName?: string) {
    const actions: Array<{type: string, label: string, context: Record<string, any>}> = [
      this.createAction(NotificationActionType.VIEW_ORDER, 'View Order', { 
        orderId,
        ...(orderStatus && { status: orderStatus }),
        ...(vendorName && { vendorName })
      }),
      this.createAction(NotificationActionType.TRACK_ORDER, 'Track Order', { orderId })
    ]

    // Add status-specific actions
    if (orderStatus) {
      switch (orderStatus.toLowerCase()) {
        case 'pending':
        case 'processing':
          actions.push(
            this.createAction(NotificationActionType.CANCEL_ORDER, 'Cancel Order', { orderId })
          )
          break

        case 'delivered':
        case 'completed':
          actions.push(
            this.createAction(NotificationActionType.RATE_ORDER, 'Rate Order', { orderId }),
            this.createAction(NotificationActionType.REORDER, 'Reorder', { orderId })
          )
          break

        case 'cancelled':
        case 'failed':
          actions.push(
            this.createAction(NotificationActionType.CONTACT_SUPPORT, 'Contact Support', { 
              orderId,
              issue: 'order_issue'
            })
          )
          break
      }
    }

    // Always add support option
    if (!actions.some(action => action.type === NotificationActionType.CONTACT_SUPPORT)) {
      actions.push(
        this.createAction(NotificationActionType.CONTACT_SUPPORT, 'Contact Support', { orderId })
      )
    }

    return actions
  }

  /**
   * Create promotion/broadcast actions
   */
  static createPromotionActions(
    promotionType: 'rate' | 'product' | 'general',
    context: Record<string, any> = {}
  ) {
    const actions: Array<{type: string, label: string, context: Record<string, any>}> = []

    switch (promotionType) {
      case 'rate':
        actions.push(
          this.createAction(NotificationActionType.VIEW_RATES, 'View New Rates', context),
          this.createAction(NotificationActionType.COMPARE_PLANS, 'Compare Plans', context),
          this.createAction(NotificationActionType.CONTACT_SALES, 'Contact Sales', { 
            topic: 'pricing_inquiry',
            ...context
          })
        )
        break

      case 'product':
        if (context.productId) {
          actions.push(
            this.createAction(NotificationActionType.BUY_PRODUCT, 'Buy Now', context),
            this.createAction(NotificationActionType.VIEW_PRODUCT, 'View Details', { 
              productId: context.productId 
            })
          )
        }
        break

      case 'general':
        actions.push(
          this.createAction(NotificationActionType.VIEW_DETAILS, 'Read More', context)
        )
        break
    }

    return actions
  }

  /**
   * Create temp order actions
   */
  static createTempOrderActions(tempOrderId: string | number, userRole: 'staff' | 'customer' = 'customer') {
    const actions: Array<{type: string, label: string, context: Record<string, any>}> = [
      this.createAction(NotificationActionType.VIEW_TEMP_ORDER, 'View Order', { tempOrderId })
    ]

    if (userRole === 'staff') {
      actions.push(
        this.createAction(NotificationActionType.APPROVE_TEMP_ORDER, 'Approve', { tempOrderId }),
        this.createAction(NotificationActionType.REJECT_TEMP_ORDER, 'Reject', { tempOrderId })
      )
    }

    return actions
  }
}
