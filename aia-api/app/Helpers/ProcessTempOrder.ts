// Updated: Rewritten for unified temp orders system
import Order from 'App/Models/Order'
import Product from 'App/Models/Product'
import Database from '@ioc:Adonis/Lucid/Database'

export default class ProcessTempOrder {
  /**
   * Convert temp order to placed order in unified system
   * Updated: Works with unified orders table instead of separate temp_orders table
   */
  public static async convert(tempOrder: Order, payload: any): Promise<Order> {
    // Validate that this is actually a temp order
    if (tempOrder.status !== 'Pending') {
      throw new Error('Order is not a temp order (status must be Pending)')
    }
    // Updated: Instead of creating new order, update existing temp order status
    // Extract temp items from meta before conversion
    const tempItems = tempOrder.meta?.temp_items || {}

    console.log('=== DEBUG: Converting temp order to placed order ===')
    console.log('TempOrder.meta.temp_items:', JSON.stringify(tempItems, null, 2))
    console.log('Object.keys(tempItems):', Object.keys(tempItems))

    // Update order status to Placed and add conversion metadata
    tempOrder.status = 'Placed'
    tempOrder.meta = {
      ...tempOrder.meta,
      // Keep temp_items for reference but mark as converted
      temp_items_converted: true,
      conversion_date: new Date().toISOString(),
      payment_method: payload.method,
      converted_by: payload.userId,
    }

    await tempOrder.save()

    // Updated: Create order items from temp_items in meta
    const orderItems = []

    await Promise.all(
      Object.keys(tempItems).map(async (key) => {
        console.log(`=== Processing temp item key: ${key} ===`)
        console.log(`Key type: ${typeof key}`)
        console.log(`Value:`, tempItems[key])

        const itemData = tempItems[key]

        // Check if the key itself is a complex object (this might be the issue)
        let actualProductId: string
        let quantity: number
        let meta: any = null

        // First, determine the actual product ID
        if (typeof key === 'string' && key.length > 0 && !key.startsWith('{')) {
          // Normal case: key is the product ID
          actualProductId = key

          if (typeof itemData === 'number') {
            // Simple structure: productId -> quantity
            quantity = itemData
          } else if (typeof itemData === 'object' && itemData !== null) {
            // Complex structure: productId -> { quantity, meta?, ... }
            quantity = itemData.quantity || 1
            meta = itemData.meta || null
          } else {
            console.warn(`Invalid item data for product ${actualProductId}:`, itemData)
            quantity = 1 // Default fallback
          }
        } else {
          // Problematic case: the key itself might be a stringified object
          console.error(`Invalid product ID key: ${key}`)

          // Try to parse the key as JSON to extract the actual product ID
          try {
            const parsedKey = JSON.parse(key)
            if (parsedKey && typeof parsedKey === 'object') {
              // Extract product ID from the parsed object
              actualProductId = parsedKey.productId || parsedKey.id
              quantity = parsedKey.quantity || 1
              meta = parsedKey.meta || null

              // If we still don't have a product ID, try to find it in the structure
              if (!actualProductId && parsedKey.meta && parsedKey.meta.productId) {
                actualProductId = parsedKey.meta.productId
              }

              console.log(
                `Extracted from parsed key - productId: ${actualProductId}, quantity: ${quantity}`
              )

              if (!actualProductId) {
                console.error(`Could not extract product ID from parsed key:`, parsedKey)
                return // Skip this item
              }
            } else {
              throw new Error('Parsed key is not an object')
            }
          } catch (parseError) {
            console.error(`Failed to parse key as JSON: ${parseError.message}`)

            // Last resort: check if the value contains the product ID
            if (typeof itemData === 'object' && itemData !== null) {
              actualProductId = itemData.productId || itemData.id
              quantity = itemData.quantity || 1
              meta = itemData.meta || null

              if (actualProductId) {
                console.log(
                  `Extracted from item data - productId: ${actualProductId}, quantity: ${quantity}`
                )
              } else {
                console.error(`Skipping invalid item with key: ${key}`)
                return // Skip this item
              }
            } else {
              console.error(`Skipping invalid item with key: ${key}`)
              return // Skip this item
            }
          }
        }

        console.log(
          `Final values - productId: ${actualProductId}, quantity: ${quantity}, meta:`,
          meta
        )

        // Validate that we have valid data before creating the order item
        if (!actualProductId || typeof actualProductId !== 'string') {
          console.error(`Invalid productId: ${actualProductId} (type: ${typeof actualProductId})`)
          return // Skip this item
        }

        if (!quantity || typeof quantity !== 'number' || quantity <= 0) {
          console.error(`Invalid quantity: ${quantity} (type: ${typeof quantity})`)
          quantity = 1 // Default to 1
        }

        // Get product to calculate price
        const product = await Product.find(actualProductId)
        const price = product ? product.price : 0

        console.log(`Creating order item with validated data:`, {
          productId: actualProductId,
          quantity,
          price,
          meta: meta ? 'present' : 'null',
        })

        // Updated: Collect order items for batch insert
        orderItems.push({
          order_id: tempOrder.id,
          product_id: actualProductId,
          quantity: Number(quantity),
          price: Number(price),
          meta: meta || {},
          created_at: new Date(),
          updated_at: new Date(),
        })
      })
    )

    // Updated: Batch insert order items instead of deleting temp order
    if (orderItems.length > 0) {
      await Database.table('order_items').insert(orderItems)
    }

    // Load the updated order with its new items
    await tempOrder.load('items')
    await Promise.all(tempOrder.items?.map((item) => item.load('product')) ?? [])

    // Calculate total amount including charges
    let amount =
      tempOrder.items?.reduce((acc, item) => acc + item.product.price * item.quantity, 0) ?? 0

    if (tempOrder.meta && tempOrder.meta.charges) {
      amount += Object.values(tempOrder.meta.charges as Record<string, number>)?.reduce(
        (acc, charge) => acc + charge,
        0
      )
    }

    // Create invoice for the placed order
    await tempOrder.related('invoices').create({
      amount,
      status: 'Pending',
    })

    // Load customer and associate with branch
    await tempOrder.load('customer')

    if (tempOrder.customer) {
      await tempOrder.customer.related('branches').sync(
        {
          [tempOrder.branchId]: {
            active: true,
            vendor_id: tempOrder.vendorId,
            branch_id: tempOrder.branchId,
          },
        },
        false
      )
    }

    return tempOrder
  }
}
