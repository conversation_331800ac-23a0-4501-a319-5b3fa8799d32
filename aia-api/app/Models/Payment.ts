import { DateTime } from 'luxon'
import {
  column,
  BaseModel,
  belongsTo,
  BelongsTo,
  beforeCreate,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import PaymentFilter from './Filters/PaymentFilter'
import User from './User'
import Vendor from './Vendor'
import { ulid } from 'ulidx'
import Invoice from './Invoice'
import * as bwipjs from 'bwip-js'

export default class Payment extends compose(BaseModel, Filterable) {
  public static $filter = () => PaymentFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public userId: string

  @column()
  public vendorId: string

  @column()
  public invoiceId: string

  @column()
  public ref: string

  @column()
  public req: string | null

  @column()
  public receipt: string | null

  @column()
  public amount: number

  @column()
  public method: string

  @column()
  public status: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @computed()
  public get barcode() {
    let barcode = ''

    bwipjs.toBuffer(
      {
        bcid: 'code128',
        text: this.ref,
        height: 50,
        width: 150,
      },
      (err, buffer) => {
        if (err) {
          console.error(err)
        } else {
          barcode = buffer.toString('base64')
        }
      }
    )

    return barcode
  }

  @belongsTo(() => User, { foreignKey: 'userId' })
  public customer: BelongsTo<typeof User>

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Invoice)
  public invoice: BelongsTo<typeof Invoice>

  @beforeCreate()
  public static async setId(payment: Payment) {
    payment.id = ulid().toLowerCase()
  }
}
