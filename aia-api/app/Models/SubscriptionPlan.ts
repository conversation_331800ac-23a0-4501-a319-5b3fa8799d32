import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, Has<PERSON>any } from '@ioc:Adonis/Lucid/Orm'
import CustomerSubscription from './CustomerSubscription'

export default class SubscriptionPlan extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public slug: string

  @column()
  public details: string

  @column()
  public price: number

  @column()
  public duration: number

  @column()
  public duration_type: string

  @column()
  public is_active: boolean

  @column()
  public meta: any

  // Additional properties used in services and controllers
  @column()
  public features: string[] | null

  @column()
  public orderLimit: number | null

  @column()
  public notificationLimit: number | null

  @column()
  public billingCycle: 'monthly' | 'yearly' | null

  @column()
  public amount: number | null

  @column()
  public currency: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => CustomerSubscription)
  public subscriptions: <PERSON><PERSON><PERSON><typeof CustomerSubscription>

  /**
   * Check if a feature is included in the plan
   */
  public hasFeature(featureCode: string): boolean {
    return this.features ? this.features.includes(featureCode) : false
  }

  /**
   * Check if usage is within limits
   */
  public isWithinLimits(usage: { orders: number; notifications: number }): boolean {
    if (this.orderLimit && usage.orders > this.orderLimit) {
      return false
    }
    if (this.notificationLimit && usage.notifications > this.notificationLimit) {
      return false
    }
    return true
  }
}
