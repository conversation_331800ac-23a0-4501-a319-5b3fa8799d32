import { DateTime } from 'luxon'
import { column, BaseModel } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import AttachmentFilter from './Filters/AttachmentFilter'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'

export default class Attachment extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => AttachmentFilter
  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public attachableId: string

  @column()
  public attachableType: string

  @attachment({ folder: 'attachments', preComputeUrl: true })
  public path: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
