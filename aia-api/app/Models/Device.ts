import { DateTime } from 'luxon'
import { column, BaseModel } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { SoftDeletes } from '@ioc:Adonis/Addons/LucidSoftDeletes'
import { compose } from '@ioc:Adonis/Core/Helpers'
import DeviceFilter from './Filters/DeviceFilter'

export default class Device extends compose(BaseModel, Filterable, SoftDeletes) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => DeviceFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public token: string

  @column()
  public userId: string

  @column()
  public status: string

  @column()
  public meta: Record<string, any>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime
}
