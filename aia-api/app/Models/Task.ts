import { DateTime } from 'luxon'
import { column, <PERSON>M<PERSON><PERSON>, beforeCreate, has<PERSON>any, Has<PERSON><PERSON>, computed } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import TaskFilter from './Filters/TaskFilter'
import Service from './Service'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { ulid } from 'ulidx'
import crypto from 'crypto'
import Vendor from './Vendor'

export default class Task extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => TaskFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public slug: string

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @attachment({ folder: 'tasks', preComputeUrl: true })
  public image: AttachmentContract | null

  @computed()
  public get imageUrl() {
    if (!this.image) {
      const hash = crypto
        .createHash('md5')
        .update(this.slug ?? '<EMAIL>')
        .digest('hex')

      return `https://www.gravatar.com/avatar/${hash}?s=200&d=mp`
    }
  }

  @beforeCreate()
  public static async generateUlid(task: Task) {
    task.id = ulid().toLowerCase()
  }

  @beforeCreate()
  public static generateSlug(task: Task) {
    task.slug = task.name.toLowerCase().replace('& ', '').replace(/ /g, '-')
  }

  @hasMany(() => Vendor)
  public vendors: HasMany<typeof Vendor>

  @hasMany(() => Service)
  public services: HasMany<typeof Service>
}
