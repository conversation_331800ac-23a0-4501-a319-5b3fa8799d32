import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import Ajv from 'ajv'

export default class ActionType extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public type: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public configSchema: Record<string, any>

  @column()
  public defaultConfig: Record<string, any>

  @column()
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  /**
   * Validate a configuration against the schema
   */
  public validateConfig(config: Record<string, any>): boolean {
    if (!this.configSchema) return true

    const ajv = new Ajv()
    const validate = ajv.compile(this.configSchema)
    return validate(config) as boolean
  }

  /**
   * Get validation errors for a configuration
   */
  public getValidationErrors(config: Record<string, any>): string[] {
    if (!this.configSchema) return []

    const ajv = new Ajv()
    const validate = ajv.compile(this.configSchema)
    validate(config)
    return (validate.errors || []).map(e => e.message || 'Unknown error')
  }

  /**
   * Merge a configuration with default values
   */
  public mergeConfig(config: Record<string, any>): Record<string, any> {
    return {
      ...this.defaultConfig,
      ...config
    }
  }
} 