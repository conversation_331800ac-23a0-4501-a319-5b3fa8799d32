import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, manyToMany, beforeCreate, BelongsTo, ManyToMany } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import { ModifierType } from 'App/Enums/ModifierType'
import Vendor from './Vendor'
import Product from './Product'

/**
 * @swagger
 * components:
 *   schemas:
 *     ModifierOption:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the modifier option
 *         vendor_id:
 *           type: string
 *           nullable: true
 *           description: ID of the vendor who owns this modifier option
 *         name:
 *           type: string
 *           description: Name of the modifier option
 *         type:
 *           type: string
 *           enum: [preparation, condiment, extra]
 *           description: Type of the modifier
 *         description:
 *           type: string
 *           nullable: true
 *           description: Description of the modifier option
 *         default_price_adjustment:
 *           type: number
 *           format: float
 *           description: Default price adjustment when this modifier is applied
 *         max_quantity:
 *           type: number
 *           nullable: true
 *           description: Maximum quantity for this modifier option
 *         active:
 *           type: boolean
 *           description: Whether the modifier option is active
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *       required:
 *         - id
 *         - name
 *         - type
 *         - default_price_adjustment
 *         - active
 */
export default class ModifierOption extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string | null

  @column()
  public name: string

  @column()
  public type: ModifierType

  @column()
  public description: string | null

  @column()
  public defaultPriceAdjustment: number

  @column()
  public maxQuantity: number | null

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(modifierOption: ModifierOption) {
    modifierOption.id = ulid()
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @manyToMany(() => Product, {
    pivotTable: 'product_modifiers',
    pivotColumns: ['price_adjustment_override', 'is_default', 'sort_order'],
  })
  public products: ManyToMany<typeof Product>

  // Computed properties
  public get isPreparation(): boolean {
    return this.type === ModifierType.PREPARATION
  }

  public get isCondiment(): boolean {
    return this.type === ModifierType.CONDIMENT
  }

  public get isExtra(): boolean {
    return this.type === ModifierType.EXTRA
  }
} 