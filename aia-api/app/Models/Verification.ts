import { DateTime } from 'luxon'
import { <PERSON>Model, HasMany, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Attachment from './Attachment'

export default class Verification extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => Attachment, {
    foreignKey: 'attachableId',
    onQuery: (query) => query.where('attachableType', 'Verification'),
  })
  public gallery: <PERSON><PERSON><PERSON><typeof Attachment>
}
