import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  beforeCreate,
  belongsTo,
  column,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ProductTypeFilter from './Filters/ProductTypeFilter'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import ProductCategory from './ProductCategory'
import Service from './Service'
import { ulid } from 'ulidx'

export default class ProductType extends compose(BaseModel, Filterable) {
  public static $filter = () => ProductTypeFilter
  public static namingStrategy = new CamelCaseStrategy()
  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string | null

  @column()
  public serviceId: string

  // @column()
  // public slug: string

  @attachment({ folder: 'types', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(producttype: ProductType) {
    producttype.id = ulid().toLowerCase()
  }

  // @beforeCreate()
  // public static generateUUID(producttype: ProductType) {
  //   producttype.slug =
  //     producttype.serviceId + '-' + producttype.name.toLowerCase().replace(/ /g, '_')
  // }

  @hasMany(() => ProductCategory)
  public categories: HasMany<typeof ProductCategory>

  @belongsTo(() => Service)
  public service: BelongsTo<typeof Service>
}
