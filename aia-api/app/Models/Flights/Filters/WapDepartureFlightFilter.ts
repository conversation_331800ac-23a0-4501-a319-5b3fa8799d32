import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import WapDepartureFlight from '../WapDepartureFlight'

export default class WapDepartureFlightFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof WapDepartureFlight>

  public name(value: string): void {
    this.$query.where('name', value)
  }
}
