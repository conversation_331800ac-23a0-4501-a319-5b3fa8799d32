import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import MiaDepartureFlight from '../MiaDepartureFlight'

export default class MiaDepartureFlightFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof MiaDepartureFlight>

  public name(value: string): void {
    this.$query.where('name', value)
  }
}
