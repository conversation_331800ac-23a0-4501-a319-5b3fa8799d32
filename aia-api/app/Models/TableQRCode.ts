import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  beforeCreate,
  belongsTo,
  column,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { ulid } from 'ulidx'
import Vendor from './Vendor'
import Branch from './Branch'
import Section from './Section'
import Lot from './Lot'
import TableQRCodeFilter from './Filters/TableQRCodeFilter'

export default class TableQRCode extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true
  public static table = 'table_qr_codes'
  public static $filter = () => TableQRCodeFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public sectionId: string

  @column()
  public lotId: string

  @column()
  public tableNumber: string

  @column({ columnName: 'qr_code_url' })
  public qrCodeUrl: string

  @column({ columnName: 'qr_code_image' })
  public qrCodeImage: string | null

  @column()
  public isActive: boolean

  @column.dateTime()
  public generatedAt: DateTime

  @column.dateTime()
  public lastScannedAt: DateTime | null

  @column()
  public scanCount: number

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @belongsTo(() => Section)
  public section: BelongsTo<typeof Section>

  @belongsTo(() => Lot)
  public lot: BelongsTo<typeof Lot>

  // Computed properties
  @computed()
  public get isRecentlyScanned(): boolean {
    if (!this.lastScannedAt) return false
    const hoursSinceLastScan = DateTime.now().diff(this.lastScannedAt, 'hours').hours
    return hoursSinceLastScan < 24
  }

  @computed()
  public get usageFrequency(): 'high' | 'medium' | 'low' {
    if (this.scanCount > 100) return 'high'
    if (this.scanCount > 20) return 'medium'
    return 'low'
  }

  @computed()
  public get displayName(): string {
    return `Table ${this.tableNumber}`
  }

  // Hooks
  @beforeCreate()
  public static async generateUlid(tableQRCode: TableQRCode) {
    tableQRCode.id = ulid().toLowerCase()
  }

  // Instance methods
  public async recordScan(): Promise<void> {
    this.lastScannedAt = DateTime.now()
    this.scanCount += 1
    await this.save()
  }

  public async activate(): Promise<void> {
    this.isActive = true
    await this.save()
  }

  public async deactivate(): Promise<void> {
    this.isActive = false
    await this.save()
  }

  public getPublicData() {
    return {
      id: this.id,
      tableNumber: this.tableNumber,
      vendor: {
        id: this.vendor?.id,
        name: this.vendor?.name,
        details: this.vendor?.details,
      },
      branch: {
        id: this.branch?.id,
        name: this.branch?.name,
        location: this.branch?.location,
      },
      section: {
        id: this.section?.id,
        name: this.section?.name,
        details: this.section?.details,
      },
      lot: {
        id: this.lot?.id,
        name: this.lot?.name,
        details: this.lot?.details,
      },
      isActive: this.isActive,
      lastScannedAt: this.lastScannedAt,
    }
  }
}
