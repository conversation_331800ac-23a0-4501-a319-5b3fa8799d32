import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { string } from '@ioc:Adonis/Core/Helpers'
import DeliveryPricingConfig from './DeliveryPricingConfig'

export default class DeliveryPricingTier extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  @beforeCreate()
  public static assignUuid(tier: DeliveryPricingTier) {
    if (!tier.id) {
      tier.id = string.generateRandom(32)
    }
  }

  @column({ isPrimary: true })
  public id: string

  @column()
  public deliveryPricingConfigId: string

  @column()
  public tierName: string

  // Distance range
  @column()
  public distanceFromKm: number

  @column()
  public distanceToKm: number | null

  // Pricing for this tier
  @column()
  public basePrice: number

  @column()
  public pricePerKm: number

  @column()
  public flatRate: number | null

  // Time estimates
  @column()
  public baseTimeMinutes: number

  @column()
  public timePerKmMinutes: number

  // Constraints
  @column()
  public minimumFee: number | null

  @column()
  public maximumFee: number | null

  @column()
  public isActive: boolean

  @column()
  public priority: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @belongsTo(() => DeliveryPricingConfig)
  public config: BelongsTo<typeof DeliveryPricingConfig>

  // Methods
  public appliesToDistance(distance: number): boolean {
    if (distance < this.distanceFromKm) return false
    if (this.distanceToKm && distance > this.distanceToKm) return false
    return true
  }

  public calculateFee(distance: number): number {
    // Use flat rate if specified
    if (this.flatRate) {
      return Number(this.flatRate)
    }

    // Calculate based on base + per km (ensure numeric conversion)
    const basePrice = Number(this.basePrice)
    const pricePerKm = Number(this.pricePerKm)
    let fee = basePrice + distance * pricePerKm

    // Apply tier-specific constraints
    if (this.minimumFee && fee < this.minimumFee) {
      fee = this.minimumFee
    }

    if (this.maximumFee && fee > this.maximumFee) {
      fee = this.maximumFee
    }

    return Math.round(fee * 100) / 100
  }

  public calculateEstimatedTime(distance: number): number {
    return Math.round(this.baseTimeMinutes + distance * this.timePerKmMinutes)
  }
}
