import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  HasManyThrough,
  beforeCreate,
  afterCreate,
  afterUpdate,
  belongsTo,
  column,
  computed,
  hasMany,
  hasManyThrough,
  scope,
} from '@ioc:Adonis/Lucid/Orm'
import Database from '@ioc:Adonis/Lucid/Database'
import Vendor from './Vendor'
import Branch from './Branch'
import Section from './Section'
import User from './User'

import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import OrderFilter from './Filters/OrderFilter'
import { ulid } from 'ulidx'
import Payment from './Payment'
import Invoice from './Invoice'
import Lot from './Lot'
import OrderItem from './OrderItem'
import Booking from './Booking'
import DepartmentCategoryMapping from './DepartmentCategoryMapping'

export default class Order extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => OrderFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public orderNumber: string | null

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public sectionId: string

  @column()
  public lotId: string

  @column({ columnName: 'user_id' })
  public userId: string

  @column({ columnName: 'staff_id' })
  public staffId: string

  @column()
  public action: 'Purchase' | 'Booking' | 'Registration' | 'Access' | 'Process'

  @column()
  public type: 'Preorder' | 'Instant'

  @column()
  public delivery: 'Takeaway' | 'Dinein' | 'Delivery' | 'Selfpick'

  @column()
  public status:
    | 'Pending'
    | 'Placed'
    | 'Processing'
    | 'Ready'
    | 'Delivering'
    | 'Delivered'
    | 'Completed'
    | 'Cancelled'

  @column()
  public ref: string

  @column()
  public meta: Record<string, any>

  // Delivery-related properties
  @column()
  public deliveryVendorId: string | null

  @column()
  public deliveryBranchId: string | null

  @column()
  public deliveryStatus:
    | 'pending_assignment'
    | 'assigned'
    | 'picked_up'
    | 'in_transit'
    | 'delivered'
    | 'failed'
    | 'cancelled'
    | null

  @column()
  public deliveryLocation: Record<string, any> | null

  @column()
  public pickupLocation: Record<string, any> | null

  @column()
  public deliveryDistance: number | null

  @column()
  public deliveryFee: number | null

  @column()
  public estimatedDeliveryTime: number | null

  @column.dateTime()
  public actualDeliveryTime: DateTime | null

  @column.dateTime()
  public pickupTime: DateTime | null

  @column()
  public deliveryNotes: Record<string, any> | null

  @column()
  public trackingData: Record<string, any> | null

  @column()
  public trackingCode: string | null

  @column()
  public requiresSignature: boolean

  @column()
  public signatureImage: string | null

  @column()
  public deliveryMeta: Record<string, any> | null

  @column()
  public total: number | null

  @column.dateTime({ autoCreate: true })
  public acceptedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true })
  public startAt: DateTime

  @column.dateTime({ autoCreate: true })
  public endAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(order: Order) {
    order.id = ulid().toLowerCase()
  }

  /**
   * Scopes for unified temp order system
   */

  /**
   * Scope to filter pending orders (temp orders)
   */
  public static tempOrders = scope((query) => {
    query.where('status', 'Pending')
  })

  /**
   * Scope to filter placed orders (regular orders)
   */
  public static placedOrders = scope((query) => {
    query.whereNot('status', 'Pending')
  })

  /**
   * Scope to filter orders with temp_items in meta
   */
  public static withTempItems = scope((query) => {
    query.whereRaw("meta->>'temp_items' IS NOT NULL")
  })

  /**
   * Scope to filter orders by vendor
   */
  public static byVendor = scope((query, vendorId: string) => {
    query.where('vendorId', vendorId)
  })

  /**
   * Scope to filter orders by branch
   */
  public static byBranch = scope((query, branchId: string) => {
    query.where('branchId', branchId)
  })

  /**
   * Scope to filter orders by staff
   */
  public static byStaff = scope((query, staffId: string) => {
    query.where('staffId', staffId)
  })

  /**
   * Scope to filter orders by customer
   */
  public static byCustomer = scope((query, userId: string) => {
    query.where('userId', userId)
  })

  /**
   * Helper methods for unified temp order system
   */

  /**
   * Check if this order is a temp order (Pending status)
   */
  public get isTempOrder(): boolean {
    return this.status === 'Pending'
  }

  /**
   * Check if this order has temp items in meta
   */
  public get hasTempItems(): boolean {
    return !!(this.meta?.temp_items && Object.keys(this.meta.temp_items).length > 0)
  }

  /**
   * Check if this order has items in the new items column
   */
  public get hasDirectItems(): boolean {
    return !!(this.items && Object.keys(this.items).length > 0)
  }

  /**
   * Get items from either the new items column or meta.temp_items (unified approach)
   */
  public getUnifiedItems(): Record<string, any> {
    if (this.hasDirectItems) {
      return this.items || {}
    }
    return this.getTempItems()
  }

  /**
   * Check if this order has any items (either format)
   */
  public get hasAnyItems(): boolean {
    return this.hasDirectItems || this.hasTempItems
  }

  /**
   * Get temp items from meta
   */
  public getTempItems(): Record<string, any> {
    return this.meta?.temp_items || {}
  }

  /**
   * Get unified items for API response - combines OrderItems and temp_items
   * This ensures the API always returns complete item data regardless of storage format
   */
  @computed()
  public get unifiedItemsForApi(): any[] {
    // Try to access items through the relationship
    try {
      if (this.$preloaded.items) {
        // Access the preloaded items directly
        const preloadedItems = this.$preloaded.items

        if (Array.isArray(preloadedItems) && preloadedItems.length > 0) {
          // Return serialized items to include preloaded product data
          return preloadedItems.map((item) => item.serialize())
        }
      }

      // Fallback: try this.items
      if (this.items && this.items.length > 0) {
        return this.items.map((item) => item.serialize())
      }
    } catch (error) {
      // Silently handle errors accessing items
    }

    // If no OrderItems but we have temp_items in meta, convert them to API format
    const tempItems = this.getTempItems()
    if (Object.keys(tempItems).length > 0) {
      return Object.entries(tempItems)
        .filter(([key, value]) => {
          // Filter out malformed entries - only include valid product IDs
          return key.length > 10 && key.startsWith('01') && typeof value === 'object'
        })
        .map(([productId, itemData]: [string, any]) => {
          // Handle different temp_items formats
          let quantity = 1
          let meta = {}

          if (typeof itemData === 'object') {
            if (itemData.quantity) {
              quantity =
                typeof itemData.quantity === 'object'
                  ? itemData.quantity.quantity || 1
                  : itemData.quantity
            }
            meta = itemData.meta || itemData
          } else {
            quantity = itemData
          }

          return {
            id: `temp_${productId}`,
            orderId: this.id,
            productId: productId,
            quantity: quantity,
            meta: meta,
            price: 0, // Price not available in temp_items
            status: 'pending',
            // Add placeholder product info
            product: {
              id: productId,
              name: 'Product (from temp items)',
              details: 'Item stored in temporary format',
            },
            modifiers: [],
            // Computed fields for compatibility
            actualPreparationTimeMinutes: null,
            isOverdue: false,
            preparationProgress: 0,
            statusDisplayName: 'Pending',
            canBeStarted: true,
            canBeCompleted: false,
            canBeServed: false,
            requiresAttention: false,
            estimatedCompletionTime: null,
            allModifiersCompleted: true,
            totalItemCost: 0,
          }
        })
    }

    return []
  }

  /**
   * Custom serialization for message center API that includes unified items
   */
  public serializeForMessageCenter() {
    const serialized = this.serialize()

    // Replace the items array with unified items that include temp_items
    serialized.items = this.unifiedItemsForApi

    return serialized
  }

  /**
   * Set temp items in meta
   */
  public setTempItems(items: Record<string, any>): void {
    this.meta = {
      ...this.meta,
      temp_items: items,
    }
  }

  /**
   * Convert temp items to order items and remove from meta (unified approach)
   */
  public async convertTempItemsToOrderItems(): Promise<void> {
    // Get items from unified approach (either items column or meta.temp_items)
    const itemsToConvert = this.getUnifiedItems()

    if (Object.keys(itemsToConvert).length === 0) {
      return
    }

    // Clear existing order items
    await this.related('items').query().delete()

    // Create new order items from unified items
    for (const [productId, itemData] of Object.entries(itemsToConvert)) {
      let quantity = 1

      if (typeof itemData === 'object' && itemData.quantity) {
        // Format: {"productId": {"quantity": 2}}
        quantity = itemData.quantity
      } else if (typeof itemData === 'number') {
        // Format: {"productId": 2}
        quantity = itemData
      }

      await this.related('items').create({
        productId,
        quantity,
      })
    }

    // Clean up: Remove temp_items from meta
    const newMeta = { ...this.meta }
    delete newMeta.temp_items
    this.meta = newMeta
    await this.save()
  }

  /**
   * Calculate total for temp order (from temp_items)
   * @deprecated Use calculateStructuredPricing() for comprehensive pricing
   */
  public async calculateTempOrderTotal(): Promise<number> {
    const pricing = await this.calculateStructuredPricing()
    return pricing.total
  }

  /**
   * Calculate comprehensive pricing structure for temp orders
   */
  public async calculateStructuredPricing(): Promise<{
    subtotal: number
    modifiersTotal: number
    chargesTotal: number
    total: number
    invoiceAmount: number
    currency: string
  }> {
    let subtotal = 0
    let modifiersTotal = 0
    let chargesTotal = 0

    // First, try to calculate from actual OrderItem records (for created orders)
    try {
      const orderItems = await OrderItem.query().where('order_id', this.id)
      if (orderItems.length > 0) {
        subtotal = orderItems.reduce((total, item) => {
          return total + Number(item.price || 0) * Number(item.quantity || 0)
        }, 0)
      } else {
        // Fallback: Calculate from unified items (for temp orders without OrderItems yet)
        if (this.hasAnyItems) {
          const unifiedItems = this.getUnifiedItems()
          const productIds = Object.keys(unifiedItems)

          if (productIds.length > 0) {
            // Import Product model dynamically to avoid circular dependency
            const { default: Product } = await import('./Product')
            const products = await Product.query().whereIn('id', productIds)

            products.forEach((product) => {
              const itemData = unifiedItems[product.id]
              let quantity = 0

              if (typeof itemData === 'object' && itemData.quantity) {
                quantity = itemData.quantity
              } else if (typeof itemData === 'number') {
                quantity = itemData
              }

              subtotal += Number(product.price || 0) * quantity
            })
          }
        }
      }
    } catch (error) {
      console.error('Error calculating pricing:', error)
      subtotal = 0
    }

    // Calculate charges total
    if (this.meta?.charges) {
      chargesTotal = Object.values(this.meta.charges as Record<string, any>).reduce(
        (acc, charge) => {
          // Handle string charges (convert to number)
          const numericCharge = typeof charge === 'string' ? parseFloat(charge) : Number(charge)
          return acc + (isNaN(numericCharge) ? 0 : numericCharge)
        },
        0
      )
    }

    // Calculate total
    const total = subtotal + modifiersTotal + chargesTotal
    const invoiceAmount = total

    return {
      subtotal,
      modifiersTotal,
      chargesTotal,
      total,
      invoiceAmount,
      currency: 'KES', // Default currency
    }
  }

  /**
   * Get temp order pricing (from stored meta.pricing or calculate if missing)
   */
  public async getTempOrderPricing(): Promise<{
    subtotal: number
    modifiersTotal: number
    chargesTotal: number
    total: number
    invoiceAmount: number
    currency: string
  }> {
    // Return stored pricing if available
    if (this.meta?.pricing) {
      return {
        subtotal: Number(this.meta.pricing.subtotal || 0),
        modifiersTotal: Number(this.meta.pricing.modifiersTotal || 0),
        chargesTotal: Number(this.meta.pricing.chargesTotal || 0),
        total: Number(this.meta.pricing.total || 0),
        invoiceAmount: Number(this.meta.pricing.invoiceAmount || 0),
        currency: this.meta.pricing.currency || 'KES',
      }
    }

    // Calculate and return if not stored
    return await this.calculateStructuredPricing()
  }

  /**
   * Ensure temp order has pricing structure stored in meta
   */
  public async ensureTempOrderPricing(): Promise<void> {
    // Skip if pricing already exists
    if (this.meta?.pricing) {
      return
    }

    // Ensure charges are numeric before calculating pricing
    this.normalizeCharges()

    // Calculate pricing
    const pricing = await this.calculateStructuredPricing()

    // Store in meta
    this.meta = {
      ...this.meta,
      pricing,
    }

    // Save to database
    await this.save()
  }

  /**
   * Normalize charges to ensure they are stored as numbers
   */
  public normalizeCharges(): void {
    if (this.meta?.charges) {
      const normalizedCharges: Record<string, number> = {}

      for (const [key, value] of Object.entries(this.meta.charges)) {
        if (value === 'NaN' || value === '"NaN"') {
          normalizedCharges[key] = 0
        } else if (typeof value === 'string') {
          const cleanValue = value.replace(/^"|"$/g, '') // Remove surrounding quotes
          const numericValue = parseFloat(cleanValue)
          normalizedCharges[key] = isNaN(numericValue) ? 0 : numericValue
        } else {
          normalizedCharges[key] = Number(value) || 0
        }
      }

      this.meta = {
        ...this.meta,
        charges: normalizedCharges,
      }
    }
  }

  /**
   * Get temp order items with product details (unified approach)
   */
  public async getTempOrderItems(): Promise<any[]> {
    if (!this.hasAnyItems) {
      return []
    }

    const unifiedItems = this.getUnifiedItems()
    const productIds = Object.keys(unifiedItems)

    if (productIds.length === 0) {
      return []
    }

    // Import Product model dynamically to avoid circular dependency
    const { default: Product } = await import('./Product')
    const products = await Product.query().whereIn('id', productIds)

    return products.map((product) => {
      const itemData = unifiedItems[product.id]
      let quantity = 0

      if (typeof itemData === 'object' && itemData.quantity) {
        quantity = itemData.quantity
      } else if (typeof itemData === 'number') {
        quantity = itemData
      }

      return {
        ...product.toJSON(),
        quantity,
      }
    })
  }

  /**
   * Place temp order (convert to regular order)
   */
  public async placeOrder(overrides: Partial<Order> = {}): Promise<void> {
    if (!this.isTempOrder) {
      throw new Error('Order is not a temp order')
    }

    // Convert temp items to order items
    await this.convertTempItemsToOrderItems()

    // Update order status and apply overrides
    await this.merge({
      ...overrides,
      status: 'Placed',
    }).save()

    // Generate order number if not exists
    if (!this.orderNumber) {
      this.orderNumber = await this.generateOrderNumber()
      await this.save()
    }

    // Calculate and create invoice
    await this.load('items')
    let amount = 0

    if (this.items && this.items.length > 0) {
      await Promise.all(
        this.items.map(async (item) => {
          await item.load('product')
          const price = Number(item.product?.price || 0)
          const quantity = Number(item.quantity || 0)
          amount += price * quantity
        })
      )
    }

    // Add charges from meta
    if (this.meta?.charges) {
      amount += Object.values(this.meta.charges as Record<string, number>).reduce(
        (acc, charge) => acc + Number(charge || 0),
        0
      )
    }

    // Create invoice
    await this.related('invoices').create({
      amount,
      status: 'Pending',
    })
  }

  /**
   * Generate order number
   */
  private async generateOrderNumber(): Promise<string> {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    return `ORD-${timestamp}-${random}`
  }

  /**
   * Create initial invoice immediately upon order creation
   * This ensures every order has an invoice for tracking purposes
   */
  public async createInitialInvoice() {
    // Check if invoice already exists to prevent duplicates
    await this.load('invoices')
    if (this.invoices && this.invoices.length > 0) {
      return
    }

    // FIXED: Always calculate amount regardless of order status
    const amount = await this.calculateTotalAmount()
    const invoiceStatus = 'Pending' // All invoices start as Pending

    // Create invoice with calculated amount
    await this.related('invoices').create({
      amount,
      status: invoiceStatus,
    })
  }

  /**
   * Calculate the total amount for this order including items and charges
   */
  public async calculateTotalAmount(): Promise<number> {
    let amount = 0

    // Calculate items total using direct query (more reliable than relationship)
    const directItems = await Database.from('order_items')
      .where('order_id', this.id)
      .leftJoin('products', 'order_items.product_id', 'products.id')
      .select(
        'order_items.id',
        'order_items.quantity',
        'order_items.price',
        'products.price as product_price'
      )

    if (directItems && directItems.length > 0) {
      for (const item of directItems) {
        // Use item price if available, otherwise use product price
        const price = Number(item.price || item.product_price || 0)
        const quantity = Number(item.quantity || 0)
        const itemTotal = price * quantity
        amount += itemTotal

        // Add modifier costs for this item
        const modifiers = await Database.from('order_item_modifiers')
          .where('order_item_id', item.id)
          .select('price_at_time_of_order', 'quantity')

        for (const modifier of modifiers) {
          const modifierPrice = Number(modifier.price_at_time_of_order || 0)
          const modifierQuantity = Number(modifier.quantity || 0)
          amount += modifierPrice * modifierQuantity
        }
      }
    }

    // Add charges from meta
    if (this.meta?.charges) {
      const chargesTotal = Object.values(this.meta.charges as Record<string, number>).reduce(
        (acc, charge) => acc + Number(charge || 0),
        0
      )
      amount += chargesTotal
    }

    // SECURITY: Never use meta pricing from frontend - always calculate server-side
    // Meta pricing can be manipulated by clients, so we only use calculated amounts
    // The calculated amount from items and charges is the authoritative source
    return amount
  }

  /**
   * Update invoice amounts for this order after items are created
   */
  public async updateInvoiceAmounts(): Promise<void> {
    // Calculate the correct total amount
    const correctAmount = await this.calculateTotalAmount()

    // Update all invoices for this order
    await Database.from('invoices').where('order_id', this.id).update({
      amount: correctAmount,
      updated_at: new Date(),
    })

    // Reload invoices to reflect the changes
    await this.load('invoices')
  }

  /**
   * Static method to fix all existing invoices with 0 amounts
   */
  public static async fixZeroAmountInvoices(): Promise<{ fixed: number; errors: number }> {
    let fixed = 0
    let errors = 0

    // Find all invoices with amount = 0
    const zeroAmountInvoices = await Database.from('invoices')
      .where('amount', 0)
      .select('id', 'order_id')

    for (const invoice of zeroAmountInvoices) {
      try {
        // Load the order and update its invoice amounts
        const order = await Order.find(invoice.order_id)
        if (order) {
          await order.updateInvoiceAmounts()
          fixed++
        }
      } catch (error) {
        console.error(`Failed to fix invoice ${invoice.id}:`, error)
        errors++
      }
    }

    return { fixed, errors }
  }

  /**
   * Calculate the base amount of the order (sum of items)
   */
  public async calculateBaseAmount(): Promise<number> {
    const items = await OrderItem.query().where('order_id', this.id).preload('product')

    return items.reduce((total, item) => {
      const itemPrice = item.product?.price || 0
      return total + itemPrice * item.quantity
    }, 0)
  }

  /**
   * Calculate the total amount including all charges
   */
  public async calculateTotal(): Promise<number> {
    const baseAmount = await this.calculateBaseAmount()
    const meta = this.meta || {}
    const charges = meta.charges || []

    const totalCharges = charges.reduce((total, charge) => total + charge.amount, 0)
    return baseAmount + totalCharges
  }

  // Computed properties for fulfillment tracking
  @computed()
  public get fulfillmentProgress(): number {
    // Check if items is loaded and is an array
    if (!this.items || !Array.isArray(this.items) || this.items.length === 0) return 0

    const totalItems = this.items.length
    const completedItems = this.items.filter((item) =>
      ['ready', 'served'].includes(item.status)
    ).length

    return Math.round((completedItems / totalItems) * 100)
  }

  @computed()
  public get estimatedCompletionTime(): DateTime | null {
    // Check if items is loaded and is an array
    if (!this.items || !Array.isArray(this.items) || this.items.length === 0) return null

    // Find the item with the latest estimated completion time
    const latestCompletion = this.items.reduce(
      (latest, item) => {
        if (!item.estimatedCompletionTime) return latest
        if (!latest) return item.estimatedCompletionTime
        return item.estimatedCompletionTime > latest ? item.estimatedCompletionTime : latest
      },
      null as DateTime | null
    )

    return latestCompletion
  }

  @computed()
  public get hasOverdueItems(): boolean {
    // Check if items is loaded and is an array
    if (!this.items || !Array.isArray(this.items)) return false
    return this.items.some((item) => item.isOverdue)
  }

  @computed()
  public get departmentBreakdown(): Record<string, any> {
    // Check if items is loaded and is an array
    if (!this.items || !Array.isArray(this.items)) return {}

    const breakdown = {}
    this.items.forEach((item) => {
      if (!item.department) return

      const deptName = item.department.name
      if (!breakdown[deptName]) {
        breakdown[deptName] = {
          total: 0,
          pending: 0,
          preparing: 0,
          ready: 0,
          served: 0,
        }
      }

      breakdown[deptName].total++
      breakdown[deptName][item.status]++
    })

    return breakdown
  }

  @computed()
  public get canBeCompleted(): boolean {
    // Check if items is loaded and is an array
    if (!this.items || !Array.isArray(this.items) || this.items.length === 0) return false

    return this.items.every((item) => {
      const itemReady = ['ready', 'served'].includes(item.status)
      const modifiersReady = item.allModifiersCompleted
      return itemReady && modifiersReady
    })
  }

  @computed()
  public get requiresAttention(): boolean {
    // Check if items is loaded and is an array before using array methods
    const hasItemsRequiringAttention =
      this.items && Array.isArray(this.items)
        ? this.items.some((item) => item.requiresAttention)
        : false
    return this.hasOverdueItems || hasItemsRequiringAttention || false
  }

  @computed()
  public get totalPreparationTime(): number {
    // Check if items is loaded and is an array
    if (!this.items || !Array.isArray(this.items)) return 0
    return this.items.reduce(
      (total, item) => total + (item.actualPreparationTime || item.estimatedPreparationTime || 0),
      0
    )
  }

  // Order fulfillment methods
  public async checkAndUpdateOrderStatus(): Promise<void> {
    await this.load('items', (itemsQuery) => {
      itemsQuery.preload('modifiers')
    })

    if (this.canBeCompleted && this.status === 'Processing') {
      this.status = 'Ready'
      await this.save()

      // Trigger notifications here
      // await this.notifyCustomerOrderReady()
    }
  }

  public async getOrderSummary(): Promise<{
    totalItems: number
    completedItems: number
    pendingItems: number
    preparingItems: number
    readyItems: number
    progress: number
    estimatedCompletion: DateTime | null
    departments: Record<string, any>
    requiresAttention: boolean
  }> {
    await this.load('items', (itemsQuery) => {
      // Include product relationship to ensure complete item data
      itemsQuery.preload('product', (productQuery) => {
        productQuery.preload('forms')
        productQuery.preload('category')
        productQuery.preload('gallery')
        productQuery.preload('vendor')
        productQuery.preload('branch')
      })
      itemsQuery.preload('department')
      itemsQuery.preload('modifiers', (modifierQuery) => {
        modifierQuery.preload('option')
        modifierQuery.preload('preparedByStaff')
        modifierQuery.preload('qualityChecker')
      })
      itemsQuery.preload('assignedStaff')
      itemsQuery.preload('qualityChecker')
    })

    // Handle temp orders that might not have items relationship loaded
    // Handle both OrderItems relationship and items column
    let items = []
    if (this.$preloaded.items && Array.isArray(this.items)) {
      // Use OrderItems relationship if loaded
      items = this.items
    } else if (this.items && typeof this.items === 'object' && !Array.isArray(this.items)) {
      // Handle items stored as JSON object in items column
      items = []
    } else if (Array.isArray(this.items)) {
      // Handle items as array
      items = this.items
    }

    const totalItems = items.length
    const completedItems = items.filter((item) =>
      ['ready', 'served'].includes(item.status || 'pending')
    ).length
    const pendingItems = items.filter((item) => (item.status || 'pending') === 'pending').length
    const preparingItems = items.filter((item) => (item.status || 'pending') === 'preparing').length
    const readyItems = items.filter((item) => (item.status || 'pending') === 'ready').length

    return {
      totalItems,
      completedItems,
      pendingItems,
      preparingItems,
      readyItems,
      progress: this.fulfillmentProgress,
      estimatedCompletion: this.estimatedCompletionTime,
      departments: this.departmentBreakdown,
      requiresAttention: this.requiresAttention,
    }
  }

  public async assignItemsToDepartments(): Promise<void> {
    await this.load('items', (itemsQuery) => {
      itemsQuery.preload('product')
    })

    // Auto-assign items to departments based on product categories or rules
    for (const item of this.items) {
      if (!item.departmentId && item.product) {
        // This would be enhanced with actual department assignment logic
        // based on product categories, vendor settings, etc.
        const departmentId = await this.determineDepartmentForProduct(item.product)
        if (departmentId) {
          item.departmentId = departmentId
          await item.save()
        }
      }
    }
  }

  private async determineDepartmentForProduct(product: any): Promise<string | null> {
    try {
      // Load product with category if not already loaded
      if (!product.category) {
        await product.load('category')
      }

      if (!product.category) {
        return null
      }

      // Find mapping for this product's category and vendor using the database-driven approach
      const mapping = await DepartmentCategoryMapping.findMappingForProduct(
        product.category.id,
        this.vendorId
      )

      if (mapping && mapping.meetsConditions(product, this)) {
        return mapping.departmentId
      }

      return null
    } catch (error) {
      console.error('Error determining department for product:', error)
      return null
    }
  }

  // @computed()
  // public get qrCode() {
  //   let code: string | null = null

  //   QRCode.toString(
  //     `aiastaff://orders/${this.id}`,
  //     {
  //       color: {
  //         dark: '#000000',
  //         light: '#ffffff',
  //       },
  //       errorCorrectionLevel: 'L',
  //     },
  //     (err, url) => {
  //       if (err) throw err

  //       code = url
  //     }
  //   )

  //   return code
  // }

  /**
   * Updated: Hooks for unified order system
   * Create invoice immediately for all orders and dispatch queue jobs
   */
  @afterCreate()
  public static async processOrderCreation(order: Order) {
    try {
      // 1. ALWAYS create an invoice immediately upon order creation
      await order.createInitialInvoice()

      // 2. For temp orders (status = 'Pending'), ensure pricing is calculated
      if (order.status === 'Pending') {
        await order.ensureTempOrderPricing()
      }
    } catch (error) {
      // Log error but don't throw to prevent order creation failure
    }

    // Try to dispatch queue job, but don't fail if Redis is unavailable
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Queue dispatch timeout')), 2000)
      })

      const queuePromise = (async () => {
        const { Queue } = await import('@ioc:Rlanz/Queue')
        await Queue.dispatch('App/Jobs/ProcessTempOrder', {
          orderId: order.id,
        })
      })()

      await Promise.race([queuePromise, timeoutPromise])
    } catch (error) {
      // Queue dispatch failed (Redis unavailable or timeout), but pricing was calculated
    }
  }

  @afterUpdate()
  public static async processTempOrderUpdate(order: Order) {
    // Only process temp orders (status = 'Pending')
    if (order.status === 'Pending') {
      // Ensure pricing is updated when temp order changes
      try {
        await order.ensureTempOrderPricing()
      } catch (error) {
        // Failed to ensure temp order pricing on update
      }

      // Try to dispatch queue job, but don't fail if Redis is unavailable
      try {
        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Queue dispatch timeout')), 2000)
        })

        const queuePromise = (async () => {
          const { Queue } = await import('@ioc:Rlanz/Queue')
          await Queue.dispatch('App/Jobs/ProcessOrder', {
            orderId: order.id,
          })
        })()

        await Promise.race([queuePromise, timeoutPromise])
      } catch (error) {
        // Queue dispatch failed (Redis unavailable or timeout), but pricing was updated
      }
    }
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @belongsTo(() => Section, {
    foreignKey: 'sectionId',
  })
  public section: BelongsTo<typeof Section>

  @belongsTo(() => Lot)
  public lot: BelongsTo<typeof Lot>

  @belongsTo(() => User, { foreignKey: 'userId' })
  public customer: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'staffId' })
  public staff: BelongsTo<typeof User>

  @belongsTo(() => Vendor, { foreignKey: 'deliveryVendorId' })
  public deliveryVendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch, { foreignKey: 'deliveryBranchId' })
  public deliveryBranch: BelongsTo<typeof Branch>

  @hasMany(() => Invoice)
  public invoices: HasMany<typeof Invoice>

  @hasMany(() => Booking)
  public bookings: HasMany<typeof Booking>

  @hasManyThrough([() => Payment, () => Invoice], {
    foreignKey: 'orderId',
    throughForeignKey: 'invoiceId',
  })
  public payments: HasManyThrough<typeof Payment>

  @hasMany(() => OrderItem, {
    foreignKey: 'orderId',
    localKey: 'id',
  })
  public items: HasMany<typeof OrderItem>
}
