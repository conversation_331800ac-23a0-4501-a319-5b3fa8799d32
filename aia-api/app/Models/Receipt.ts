import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import User from './User'
import Payment from './Payment'

export default class Receipt extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public number: string

  @column()
  public userId: string

  @column()
  public paymentId: string

  @column()
  public filePath: string | null

  @column()
  public fileUrl: string | null

  @column()
  public status: 'pending' | 'generated' | 'delivered' | 'failed'

  @column()
  public deliveryMethod: 'email' | 'sms' | 'download' | null

  @column()
  public deliveredAt: DateTime | null

  @column()
  public metadata: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @belongsTo(() => User, { foreignKey: 'userId' })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Payment, { foreignKey: 'paymentId' })
  public payment: BelongsTo<typeof Payment>

  // Hooks
  @beforeCreate()
  public static async setId(receipt: Receipt) {
    receipt.id = ulid().toLowerCase()
  }

  @beforeCreate()
  public static async generateReceiptNumber(receipt: Receipt) {
    if (!receipt.number) {
      const now = DateTime.now()
      const yearMonth = now.toFormat('yyyyMM')

      // Get the count of receipts for this month
      const count = await Receipt.query()
        .where('number', 'like', `RCP-${yearMonth}-%`)
        .count('* as total')

      const sequence = (parseInt(count[0].$extras.total) + 1).toString().padStart(6, '0')
      receipt.number = `RCP-${yearMonth}-${sequence}`
    }
  }

  // Computed properties
  public get isGenerated(): boolean {
    return this.status === 'generated' || this.status === 'delivered'
  }

  public get isDelivered(): boolean {
    return this.status === 'delivered'
  }

  public get downloadUrl(): string | null {
    if (!this.fileUrl) return null
    return `/v1/receipts/${this.id}/download`
  }
}
