import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import MessageAction from './MessageAction'

export default class ActionResponse extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public messageActionId: number

  @column()
  public type: string

  @column()
  public text: string | null

  @column()
  public data: object | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => MessageAction)
  public messageAction: BelongsTo<typeof MessageAction>
} 