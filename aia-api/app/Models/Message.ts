import { DateTime } from 'luxon'
import { column, BaseModel, belongsTo, BelongsTo, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import MessageFilter from './Filters/MessageFilter'
import MessageTemplate from './MessageTemplate'
import User from './User'
import MessageAction from './MessageAction'

export default class Message extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => MessageFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public details: string

  @column()
  public userId: string

  @column()
  public templateId: string

  @column()
  public branchId: string

  @column()
  public vendorId: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @belongsTo(() => MessageTemplate, { foreignKey: 'templateId' })
  public template: BelongsTo<typeof MessageTemplate>

  @belongsTo(() => User, { foreignKey: 'userId' })
  public author: BelongsTo<typeof User>

  @hasMany(() => MessageAction, { foreignKey: 'messageId' })
  public actions: HasMany<typeof MessageAction>
}
