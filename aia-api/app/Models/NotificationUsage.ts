import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Vendor from './Vendor'
import NotificationBillingTier from './NotificationBillingTier'

export default class NotificationUsage extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public vendorId: string

  @column()
  public tierId: number | null

  @column()
  public notificationId: string | null

  @column()
  public notificationType: string

  @column()
  public channel: string

  @column()
  public recipient: string

  @column()
  public cost: number

  @column()
  public currency: string

  @column()
  public status: 'pending' | 'billed' | 'failed'

  @column.dateTime()
  public billedAt: DateTime | null

  @column()
  public billingPeriod: string

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => NotificationBillingTier)
  public tier: BelongsTo<typeof NotificationBillingTier>

  /**
   * Mark the usage as billed
   */
  public async markAsBilled(): Promise<void> {
    this.status = 'billed'
    this.billedAt = DateTime.now()
    await this.save()
  }

  /**
   * Mark the usage as failed
   */
  public async markAsFailed(): Promise<void> {
    this.status = 'failed'
    await this.save()
  }

  /**
   * Get total cost for a user/vendor within a date range
   */
  public static async getTotalCost(
    userId: string,
    vendorId: string | null,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<number> {
    const query = this.query()
      .where('recipient', userId)
      .whereBetween('created_at', [startDate.toSQL(), endDate.toSQL()])
      .sum('cost as total')

    if (vendorId) {
      query.where('vendor_id', vendorId)
    }

    const result = await query.first()
    return result ? parseFloat(result.$extras.total || '0') : 0
  }
} 