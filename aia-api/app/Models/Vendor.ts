import { DateTime } from 'luxon'
import {
  column,
  beforeSave,
  BaseModel,
  computed,
  HasMany,
  hasMany,
  manyToMany,
  ManyToMany,
  belongsTo,
  BelongsTo,
  beforeCreate,
  hasOne,
  HasOne,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { attachment, AttachmentContract } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { SoftDeletes } from '@ioc:Adonis/Addons/LucidSoftDeletes'
import Env from '@ioc:Adonis/Core/Env'
import Database from '@ioc:Adonis/Lucid/Database'
import VendorFilter from './Filters/VendorFilter'
import Device from './Device'
import crypto from 'crypto'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import Payment from './Payment'
import User from './User'
// import Attachment from './Attachment'
import Product from './Product'
import { ulid } from 'ulidx'
import Speciality from './Speciality'
import VendorCategory from './VendorCategory'
import Branch from './Branch'
import Service from './Service'
import Task from './Task'
import Group from './Group'
import NotificationUsage from './NotificationUsage'
import ServiceArea from './ServiceArea'
import DeliveryProviderProfile from './DeliveryProviderProfile'

// QR Code Preferences Interface
export interface VendorQRPreferences {
  autoGeneration: {
    enabled: boolean
    onLotCreation: boolean
    onBulkImport: boolean
  }
  defaultOptions: {
    width: number
    errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H'
    color: {
      dark: string
      light: string
    }
  }
  notifications: {
    onSuccess: boolean
    onFailure: boolean
  }
}

export default class Vendor extends compose(BaseModel, Filterable, SoftDeletes) {
  public static $filter = () => VendorFilter
  public static namingStrategy = new CamelCaseStrategy()

  @column({ isPrimary: true })
  public id: string

  @column()
  public userId: string

  @column()
  public serviceId: string

  @column()
  public name: string

  @column()
  public code: string

  @column()
  public slug: string

  @column()
  public email: string

  @column()
  public phone: string

  @column()
  public reg: string

  @column()
  public kra: string

  @column()
  public permit: string

  @column()
  public details: string | null

  @column()
  public active: boolean

  @column()
  public featured: boolean

  @attachment({ folder: 'logos', preComputeUrl: true })
  public logo: AttachmentContract | null

  @attachment({ folder: 'covers', preComputeUrl: true })
  public cover: AttachmentContract | null

  @column()
  public location: {
    name?: string
    address: string
    regions: {
      administrative_area_level_3?: string | null
      administrative_area_level_1?: string | null
      country: string
    }
    coordinates: {
      lat: number
      lng: number
    }
    place_id: string
  } | null

  @column()
  public geom?: any

  // Delivery-related properties
  @column()
  public deliveryPreferences: Record<string, any> | null

  @column()
  public availabilitySettings: Record<string, any> | null

  @column()
  public pricingStructure: Record<string, any> | null

  @column()
  public qrCodePreferences: VendorQRPreferences | null

  @column()
  public verificationStatus: 'pending' | 'verified' | 'rejected'

  @column()
  public verificationNotes: string | null

  @column.dateTime()
  public verifiedAt: DateTime | null

  @computed()
  public get logoUrl() {
    if (!this.logo) {
      const hash = crypto
        .createHash('md5')
        .update(this.email ?? '<EMAIL>')
        .digest('hex')

      return `https://www.gravatar.com/avatar/${hash}?s=200&d=mp`
    }
  }

  @beforeCreate()
  public static async generateUlid(vendor: Vendor) {
    vendor.id = ulid().toLowerCase()
  }

  @beforeSave()
  public static async setGeom(vendor: Vendor) {
    if (vendor.location?.coordinates) {
      if (Env.get('DB_CONNECTION') === 'pg') {
        vendor.geom = Database.st().geomFromText(
          `POINT(${vendor.location.coordinates.lng} ${vendor.location.coordinates.lat})`,
          4326
        )
      } else {
        vendor.geom = Database.raw(
          `GeomFromText('POINT(${vendor.location.coordinates.lng} ${vendor.location.coordinates.lat})', 4326)`
        )
      }
    }
  }

  @column()
  public rememberMeToken: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @hasMany(() => Branch)
  public branches: HasMany<typeof Branch>

  @hasMany(() => Payment)
  public payments: HasMany<typeof Payment>

  @hasMany(() => Device)
  public devices: HasMany<typeof Device>

  @hasMany(() => Product)
  public products: HasMany<typeof Product>

  @hasMany(() => Group)
  public groups: HasMany<typeof Group>

  // @hasMany(() => Attachment, {
  //   foreignKey: 'attachableId',
  //   onQuery: (query) => query.where('attachableType', 'Vendor'),
  // })
  // public attachments: HasMany<typeof Attachment>

  @manyToMany(() => User, {
    pivotTable: 'staff',
    pivotTimestamps: true,
    pivotColumns: ['branch_id', 'identifier'],
  })
  public staff: ManyToMany<typeof User>

  @manyToMany(() => Service, {
    pivotTable: 'vendor_services',
    pivotTimestamps: true,
    pivotColumns: ['active'],
  })
  public services: ManyToMany<typeof Service>

  @manyToMany(() => Task, {
    pivotTable: 'vendor_tasks',
    pivotTimestamps: true,
    pivotColumns: ['active'],
  })
  public tasks: ManyToMany<typeof Task>

  @manyToMany(() => VendorCategory, {
    pivotTable: 'vendor_set_categories',
    pivotTimestamps: true,
  })
  public categories: ManyToMany<typeof VendorCategory>

  @manyToMany(() => Speciality, {
    pivotTable: 'vendor_specialities',
    pivotTimestamps: true,
  })
  public specialities: ManyToMany<typeof Speciality>

  @manyToMany(() => User, {
    pivotTable: 'customers',
    pivotTimestamps: true,
    pivotColumns: ['active', 'branch_id'],
    onQuery(query) {
      query.pivotColumns(['active', 'branch_id'])
    },
  })
  public customers: ManyToMany<typeof User>

  @hasMany(() => NotificationUsage)
  public notificationUsages: HasMany<typeof NotificationUsage>

  @hasMany(() => ServiceArea)
  public serviceAreas: HasMany<typeof ServiceArea>

  @hasOne(() => DeliveryProviderProfile)
  public deliveryProfile: HasOne<typeof DeliveryProviderProfile>

  /**
   * DELIVERY-SPECIFIC AUTHORIZATION METHODS
   * These methods are additive and do not affect existing vendor functionality
   */

  /**
   * Check if vendor can provide delivery services (NEW - delivery-specific)
   * This is completely separate from existing vendor capabilities
   */
  @computed()
  public get canProvideDeliveryServices(): boolean {
    // Must be a verified vendor AND have verified delivery provider profile
    return (
      this.active &&
      this.verificationStatus === 'verified' &&
      this.deliveryProfile?.verificationStatus === 'verified' &&
      this.deliveryProfile?.active === true
    )
  }

  /**
   * Check if vendor can manage delivery service areas (NEW - delivery-specific)
   * Only controls delivery-related service area management
   */
  @computed()
  public get canManageDeliveryServiceAreas(): boolean {
    return this.canProvideDeliveryServices
  }

  /**
   * Check if vendor can accept delivery orders (NEW - delivery-specific)
   * Includes real-time operational checks (working hours, capacity)
   */
  public async canAcceptDeliveryOrders(): Promise<boolean> {
    if (!this.canProvideDeliveryServices) return false
    if (!this.deliveryProfile) return false

    // Check if within working hours
    if (!this.deliveryProfile.isWithinWorkingHours()) return false

    // Check if can accept more orders based on daily limit
    return await this.deliveryProfile.canAcceptMoreOrders()
  }

  /**
   * Check if vendor is eligible for delivery order assignment (NEW - delivery-specific)
   * Comprehensive eligibility check including documentation validation
   */
  public async isEligibleForDeliveryAssignment(): Promise<boolean> {
    if (!this.canProvideDeliveryServices) return false
    if (!this.deliveryProfile) return false

    // Check if delivery provider profile is eligible
    if (!this.deliveryProfile.isEligibleForOrders) return false

    // Check if has valid documentation
    if (!this.deliveryProfile.hasValidDocuments) return false
    if (!this.deliveryProfile.hasValidInsurance) return false
    if (!this.deliveryProfile.hasValidVehicleDocumentation) return false

    return true
  }

  /**
   * Check if vendor can access delivery provider settings (NEW - delivery-specific)
   * Controls access to delivery-specific configuration
   */
  @computed()
  public get canAccessDeliverySettings(): boolean {
    // Allow access if vendor is verified (to apply) OR already has delivery profile
    return (
      this.active &&
      this.verificationStatus === 'verified' &&
      (this.deliveryProfile !== null || this.deliveryProfile === undefined)
    )
  }

  /**
   * Get delivery capabilities summary (NEW - delivery-specific)
   * Provides delivery service information without affecting existing functionality
   */
  @computed()
  public get deliveryCapabilities(): {
    canDeliver: boolean
    hasDeliveryProfile: boolean
    profileStatus: string | null
    maxDistance: number | null
    vehicleTypes: string[] | null
    maxConcurrentOrders: number | null
  } {
    return {
      canDeliver: this.canProvideDeliveryServices,
      hasDeliveryProfile: this.deliveryProfile !== null,
      profileStatus: this.deliveryProfile?.verificationStatus || null,
      maxDistance: this.deliveryProfile?.maxDeliveryDistance || null,
      vehicleTypes: this.deliveryProfile?.vehicleTypes || null,
      maxConcurrentOrders: this.deliveryProfile?.maxConcurrentOrders || null,
    }
  }

  /**
   * BACKWARD COMPATIBILITY HELPERS
   * These methods maintain existing behavior patterns
   */

  /**
   * Check if vendor has delivery enabled (LEGACY COMPATIBILITY)
   * Maintains backward compatibility with existing deliveryPreferences.enableDelivery
   */
  @computed()
  public get hasDeliveryEnabled(): boolean {
    // Check legacy delivery preferences first (existing behavior)
    if (this.deliveryPreferences?.enableDelivery === true) {
      return true
    }

    // Also check new delivery provider profile (additive)
    return this.canProvideDeliveryServices
  }
}
