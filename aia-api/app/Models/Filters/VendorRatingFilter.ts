import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import VendorRating from '../VendorRating'

export default class VendorRatingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof VendorRating, VendorRating>

  public name(value: string): void {
    this.$query.whereILike('name', value)
  }

  public vendor(vendorId: string) {
    this.$query.where('vendor_id', vendorId)
  }
}
