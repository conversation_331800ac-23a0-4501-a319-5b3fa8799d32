import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Branch from '../Branch'

export default class BranchFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Branch, Branch>

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }

  public with(relations: string) {
    relations.split(',').map((relation: 'vendor') => {
      this.$query.preload(relation)
    })
  }

  public staffid(value: any): void {
    this.$query.where('staff_id', value)
  }
}
