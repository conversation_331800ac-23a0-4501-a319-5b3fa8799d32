import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import TableQRCode from 'App/Models/TableQRCode'

export default class TableQRCodeFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof TableQRCode, TableQRCode>

  // Filter by vendor
  public vendor(vendorId: string): void {
    this.$query.where('vendorId', vendorId)
  }

  // Filter by branch
  public branch(branchId: string): void {
    this.$query.where('branchId', branchId)
  }

  // Filter by section
  public section(sectionId: string): void {
    this.$query.where('sectionId', sectionId)
  }

  // Filter by active status
  public active(isActive: boolean | string): void {
    const active = typeof isActive === 'string' ? isActive === 'true' : isActive
    this.$query.where('isActive', active)
  }

  // Filter by table number
  public tableNumber(tableNumber: string): void {
    this.$query.whereILike('tableNumber', `%${tableNumber}%`)
  }

  // Filter by scan count range
  public minScans(count: number): void {
    this.$query.where('scanCount', '>=', count)
  }

  public maxScans(count: number): void {
    this.$query.where('scanCount', '<=', count)
  }

  // Filter by recent activity
  public recentlyScanned(hours: number = 24): void {
    const cutoff = new Date()
    cutoff.setHours(cutoff.getHours() - hours)
    this.$query.where('lastScannedAt', '>=', cutoff.toISOString())
  }

  // Filter by usage frequency
  public highUsage(): void {
    this.$query.where('scanCount', '>', 100)
  }

  public mediumUsage(): void {
    this.$query.whereBetween('scanCount', [21, 100])
  }

  public lowUsage(): void {
    this.$query.where('scanCount', '<=', 20)
  }

  // Search across multiple fields
  public search(term: string): void {
    this.$query.where((query) => {
      query
        .whereILike('tableNumber', `%${term}%`)
        .orWhereHas('vendor', (vendorQuery) => {
          vendorQuery.whereILike('name', `%${term}%`)
        })
        .orWhereHas('section', (sectionQuery) => {
          sectionQuery.whereILike('name', `%${term}%`)
        })
    })
  }
}
