import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import User from 'App/Models/User'

export default class UserFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof User, User>

  public s(value: any): void {
    this.$query
      .whereILike('firstName', value)
      .orWhereILike('lastName', value)
      .orWhereILike('phone', value)
      .orWhereILike('email', value)
  }

  public email(value: any): void {
    this.$query.where('email', value)
  }

  public phone(value: any): void {
    this.$query.where('phone', value)
  }

  public gender(value: any): void {
    this.$query.where('gender', value)
  }

  public status(value: any): void {
    this.$query.where('status', value)
  }

  public active(state: boolean) {
    this.$query.where('active', state)
  }

  // public role(value: any): void {
  //   this.$query.whereHas('roles', (qr) => qr.where<PERSON>ike('name', `%${value}%`))
  // }
}
