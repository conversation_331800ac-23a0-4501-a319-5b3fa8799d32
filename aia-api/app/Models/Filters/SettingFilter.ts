import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Setting from '../Setting'

export default class SettingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Setting, Setting>

  public name(name: string) {
    this.$query.where('name', name)
  }

  public branch(branchId: string) {
    this.$query.where('branch_id', branchId)
  }
}
