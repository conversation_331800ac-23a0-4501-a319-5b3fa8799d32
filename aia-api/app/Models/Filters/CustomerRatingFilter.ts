import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import CustomerRating from '../CustomerRating'

export default class CustomerRatingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof CustomerRating, CustomerRating>

  public name(value: string): void {
    this.$query.whereILike('name', value)
  }
}
