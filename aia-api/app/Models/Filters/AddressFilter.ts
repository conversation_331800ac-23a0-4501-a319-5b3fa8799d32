import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Address from '../Address'

export default class AddressFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Address, Address>

  public s(value: any): void {
    this.$query.whereILike('name', value)
  }

  public user(id: string): void {
    this.$query.where('user_id', id)
  }
}
