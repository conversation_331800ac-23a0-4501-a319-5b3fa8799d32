import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Brand from '../Brand'

export default class BrandFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Brand, Brand>

  public active(value: boolean): void {
    this.$query.where('active', value)
  }

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }
}
