import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ProductRating from '../ProductRating'

export default class ProductRatingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ProductRating, ProductRating>

  public name(value: string): void {
    this.$query.whereILike('name', value)
  }
}
