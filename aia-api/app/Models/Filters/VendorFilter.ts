import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Vendor from '../Vendor'

export default class VendorFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Vendor, Vendor>

  public s(name: string) {
    this.$query.where((builder) => {
      builder.whereILike('name', `%${name}%`)
    })
  }

  public searchQuery(name: string) {
    this.$query.whereHas('products', (pq) => {
      pq.whereILike('name', `%${name}%`)
    })
  }

  public vendorCategory(category: string) {
    this.$query.whereHas('categories', (builder) => {
      builder.whereIn('vendor_categories.id', [category])
    })
  }

  public productType(type: string) {
    this.$query.whereHas('products', (pq) => {
      pq.whereHas('category', (cq) => {
        cq.where('productTypeId', type)
      })
    })
  }

  public phone(phone: string) {
    this.$query.whereILike('phone', `${phone}%`)
  }

  public withTrashed() {
    this.$query.withTrashed()
  }

  public onlyTrashed() {
    this.$query.onlyTrashed()
  }

  public active(value: boolean) {
    this.$query.where('active', Boolean(value))
  }

  public featured(value: boolean) {
    this.$query.where('featured', Boolean(value))
  }
}
