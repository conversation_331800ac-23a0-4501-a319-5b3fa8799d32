import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Order from '../Order'
import { DateTime } from 'luxon'

export default class OrderFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Order, Order>

  public status(value: any): void {
    this.$query.where('status', value)
  }

  public start(value: any): void {
    this.$query.where('start_at', value)
  }

  public end(value: any): void {
    this.$query.where('end_at', value)
  }

  public user(value: any): void {
    this.$query.where('user_id', value)
  }

  public vendor(value: any): void {
    this.$query.where('vendor_id', value)
  }

  public with(relations: string) {
    relations.split(',').map((relation: 'branch') => {
      this.$query.preload(relation)
    })
  }

  public createdAt(value: any): void {
    this.$query.where('created_at', '>=', DateTime.fromISO(value).toSQL() as string)
  }
}
