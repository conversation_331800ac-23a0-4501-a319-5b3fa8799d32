import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ProductCategory from '../ProductCategory'

export default class ProductCategoryFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ProductCategory, ProductCategory>

  public s(name: string) {
    this.$query.where((builder) => {
      builder.whereILike('name', `%${name}%`)
    })
  }

  // public vendor(vendorId: string) {
  //   this.$query
  //     .whereHas('products', (pq) => {
  //       pq.where('vendor_id', vendorId)
  //     })
  //     .preload('products', (pq) => pq.preload('branch'))
  // }

  // public branch(branchId: string | null) {
  //   if (branchId) {
  //     this.$query
  //       .whereHas('products', (pq) => {
  //         pq.where('branch_id', branchId)
  //       })
  //       .preload('products', (pq) => pq.preload('vendor'))
  //   }
  // }

  public with(relations: string) {
    relations.split(',').map((relation: 'products' | 'productType') => {
      this.$query.preload(relation)
    })
  }

  public service(serviceId: string) {
    this.$query.whereHas('productType', (tq) => {
      tq.where('service_id', serviceId)
    })
  }
}
