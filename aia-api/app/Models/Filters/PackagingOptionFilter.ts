import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import PackagingOption from 'App/Models/PackagingOption'

export default class PackagingOptionFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof PackagingOption, PackagingOption>

  public name(value: string): void {
    this.$query.where('name', 'ILIKE', `%${value}%`)
  }

  public vendorId(value: string): void {
    this.$query.where('vendor_id', value)
  }

  public active(value: boolean | string): void {
    const isActive = typeof value === 'string' ? value === 'true' : value
    this.$query.where('active', isActive)
  }

  public priceMin(value: number): void {
    this.$query.where('price', '>=', value)
  }

  public priceMax(value: number): void {
    this.$query.where('price', '<=', value)
  }
}
