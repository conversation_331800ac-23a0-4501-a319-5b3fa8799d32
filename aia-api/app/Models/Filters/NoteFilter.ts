import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Note from '../Note'

export default class NoteFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Note, Note>

  public type(value: string): void {
    this.$query.where('type', value)
  }

  public content(value: string): void {
    this.$query.where('content', 'ILIKE', `%${value}%`)
  }

  public branchId(value: string): void {
    this.$query.where('branch_id', value)
  }

  public userId(value: string): void {
    this.$query.where('user_id', value)
  }

  public productId(value: string): void {
    this.$query.where('product_id', value)
  }

  public with(relations: string): void {
    relations.split(',').forEach((relation: string) => {
      const trimmedRelation = relation.trim()
      if (trimmedRelation === 'product') {
        this.$query.preload('product')
      }
    })
  }
}
