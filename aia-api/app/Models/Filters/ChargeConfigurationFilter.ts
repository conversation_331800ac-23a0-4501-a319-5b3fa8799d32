import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ChargeConfiguration from '../ChargeConfiguration'

export default class ChargeConfigurationFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ChargeConfiguration, ChargeConfiguration>

  public name(value: string): void {
    this.$query.whereILike('name', `%${value}%`)
  }

  public code(value: string): void {
    this.$query.where('code', value)
  }

  public type(value: 'fixed' | 'percentage'): void {
    this.$query.where('type', value)
  }

  public vendorId(value: string): void {
    this.$query.where('vendor_id', value)
  }

  public branchId(value: string): void {
    this.$query.where('branch_id', value)
  }

  public serviceId(value: string): void {
    this.$query.where('service_id', value)
  }

  public isActive(value: boolean): void {
    this.$query.where('is_active', value)
  }

  public isTax(value: boolean): void {
    this.$query.where('is_tax', value)
  }

  public isMandatory(value: boolean): void {
    this.$query.where('is_mandatory', value)
  }
}