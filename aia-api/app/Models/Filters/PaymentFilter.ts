import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Payment from '../Payment'

export default class PaymentFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Payment, Payment>

  public s(name: string) {
    this.$query
      .whereHas('customer', (builder) => {
        builder.where((builder) => {
          builder.where('first_name', 'LIKE', `%${name}%`).orWhere('last_name', 'LIKE', `%${name}%`)
        })
      })
      .orWhereHas('vendor', (builder) => {
        builder.where('name', 'LIKE', `%${name}%`)
      })
  }

  public user(value: any): void {
    this.$query.where('user_id', value)
  }
}
