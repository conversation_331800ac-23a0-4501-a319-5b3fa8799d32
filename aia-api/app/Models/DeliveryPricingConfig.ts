import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, <PERSON><PERSON><PERSON>, computed, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { string } from '@ioc:Adonis/Core/Helpers'
import DeliveryPricingTier from './DeliveryPricingTier'

export default class DeliveryPricingConfig extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  @beforeCreate()
  public static assignUuid(config: DeliveryPricingConfig) {
    if (!config.id) {
      config.id = string.generateRandom(32)
    }
  }

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public vehicleType: 'motorcycle' | 'car' | 'bicycle' | 'van'

  @column()
  public isActive: boolean

  @column()
  public isDefault: boolean

  @column()
  public priority: number

  // Base pricing structure
  @column()
  public basePrice: number

  @column()
  public pricePerKm: number

  @column()
  public minimumFee: number | null

  @column()
  public maximumFee: number | null

  // Distance constraints
  @column()
  public maxDistanceKm: number | null

  @column()
  public minDistanceKm: number

  // Order value constraints
  @column()
  public minimumOrderValue: number | null

  @column()
  public freeDeliveryThreshold: number | null

  // Time estimates
  @column()
  public baseTimeMinutes: number

  @column()
  public timePerKmMinutes: number

  @column()
  public preparationBufferMinutes: number

  // Availability settings
  @column()
  public workingHours: Record<string, any> | null

  @column()
  public blackoutDates: string[] | null

  @column()
  public availableOnHolidays: boolean

  // Surge pricing
  @column()
  public surgeMultipliers: Record<string, any> | null

  @column()
  public weatherAdjustments: Record<string, any> | null

  // Admin metadata
  @column()
  public createdByAdminId: string | null

  @column()
  public updatedByAdminId: string | null

  @column()
  public adminNotes: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @hasMany(() => DeliveryPricingTier)
  public tiers: HasMany<typeof DeliveryPricingTier>

  // Computed properties
  @computed()
  public get displayName(): string {
    return `${this.name} (${this.vehicleType})`
  }

  @computed()
  public get isAvailable(): boolean {
    if (!this.isActive) return false

    // Add time-based availability checks here
    // Check working hours, blackout dates, etc.

    return true
  }

  // Methods
  public calculateEstimatedTime(distance: number): number {
    return Math.round(
      this.baseTimeMinutes + distance * this.timePerKmMinutes + this.preparationBufferMinutes
    )
  }

  public calculateBaseFee(distance: number): number {
    let fee = this.basePrice + distance * this.pricePerKm

    // Apply minimum fee
    if (this.minimumFee && fee < this.minimumFee) {
      fee = this.minimumFee
    }

    // Apply maximum fee cap
    if (this.maximumFee && fee > this.maximumFee) {
      fee = this.maximumFee
    }

    return Math.round(fee * 100) / 100 // Round to 2 decimal places
  }

  public isWithinDistanceLimit(distance: number): boolean {
    if (distance < this.minDistanceKm) return false
    if (this.maxDistanceKm && distance > this.maxDistanceKm) return false
    return true
  }

  public meetsMinimumOrderValue(orderValue: number): boolean {
    if (!this.minimumOrderValue) return true
    return orderValue >= this.minimumOrderValue
  }

  public qualifiesForFreeDelivery(orderValue: number): boolean {
    if (!this.freeDeliveryThreshold) return false
    return orderValue >= this.freeDeliveryThreshold
  }
}
