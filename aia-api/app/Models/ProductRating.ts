import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import ProductRatingFilter from './Filters/ProductRatingFilter'
import User from './User'
import Product from './Product'

export default class ProductRating extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => ProductRatingFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public customerId: string

  @column()
  public productId: string

  @column()
  public name: string

  @column()
  public points: number

  @column()
  public comment: string

  @column()
  public meta: JSON

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public customer: BelongsTo<typeof User>

  @belongsTo(() => Product)
  public product: BelongsTo<typeof Product>
}
