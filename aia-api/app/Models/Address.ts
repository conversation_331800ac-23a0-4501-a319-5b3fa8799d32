import { DateTime } from 'luxon'
import {
  column,
  BaseModel,
  beforeCreate,
  beforeSave,
  belongsTo,
  BelongsTo,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import AddressFilter from './Filters/AddressFilter'
import { ulid } from 'ulidx'
import Database from '@ioc:Adonis/Lucid/Database'
import User from './User'

export default class Address extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => AddressFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public phone: string

  @column()
  public userId: string

  @column()
  public primary: boolean

  @column()
  public location: {
    name?: string
    address: string
    regions: {
      administrative_area_level_3?: string | null
      administrative_area_level_1?: string | null
      country: string
    }
    coordinates: {
      lat: number
      lng: number
    }
    place_id: string
  } | null

  @column()
  public geom?: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(address: Address) {
    address.id = ulid().toLowerCase()
  }

  @beforeSave()
  public static async setGeom(address: Address) {
    if (address.location?.coordinates) {
      address.geom = Database.st().geomFromText(
        `POINT(${address.location.coordinates.lng} ${address.location.coordinates.lat})`,
        4326
      )
    }
  }

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  // Computed properties for API response format
  @computed()
  public get coordinates() {
    return this.location?.coordinates || null
  }

  @computed()
  public get address() {
    return this.location?.address || null
  }

  @computed()
  public get regions() {
    return this.location?.regions || null
  }

  // Custom serialization for clean API response
  public serialize() {
    return {
      id: this.id,
      name: this.name,
      coordinates: this.coordinates,
      regions: this.regions,
      address: this.address,
      details: this.details,
      userId: this.userId,
      createdAt: this.createdAt,
    }
  }
}
