import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import CustomerRatingFilter from './Filters/CustomerRatingFilter'
import User from './User'

export default class CustomerRating extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => CustomerRatingFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public staffId: string

  @column()
  public customerId: string

  @column()
  public name: string

  @column()
  public points: number

  @column()
  public comment: string

  @column()
  public meta: JSON

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, { foreignKey: 'staffId' })
  public staff: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'customerId' })
  public customer: BelongsTo<typeof User>
}
