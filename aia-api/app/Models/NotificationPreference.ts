import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Vendor from './Vendor'

export default class NotificationPreference extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: string

  @column()
  public vendorId: string | null

  @column()
  public type: string

  @column()
  public channels: string[]

  @column()
  public preferences: Record<string, boolean>

  @column()
  public meta: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  public static async getUserPreferences(
    userId: string,
    vendorId: string | null = null,
    type: string = 'delivery'
  ): Promise<NotificationPreference | null> {
    const query = this.query()
      .where('user_id', userId)
      .where('type', type)

    if (vendorId) {
      query.where('vendor_id', vendorId)
    } else {
      query.whereNull('vendor_id')
    }

    return await query.first()
  }

  public isChannelEnabled(channel: string): boolean {
    return this.channels.includes(channel) && this.preferences[channel] === true
  }

  public isNotificationEnabled(notificationType: string): boolean {
    return this.preferences[notificationType] === true
  }
} 