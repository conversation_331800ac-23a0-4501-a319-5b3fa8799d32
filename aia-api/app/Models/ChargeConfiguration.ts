import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, beforeSave } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import Vendor from './Vendor'
import Branch from './Branch'
import Service from './Service'

export default class ChargeConfiguration extends compose(BaseModel, Filterable) {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public code: string

  @column()
  public description: string | null

  @column()
  public type: 'fixed' | 'percentage'

  @column({
    serialize: (value: string | number) => Number(value) || 0,
  })
  public amount: number

  @column({
    serialize: (value: string | number | null) => (value ? Number(value) : null),
  })
  public percentageRate: number | null

  @column()
  public vendorId: string | null

  @column()
  public branchId: string | null

  @column()
  public serviceId: string | null

  @column()
  public conditions: Record<string, any>

  @column()
  public isTax: boolean

  @column()
  public isMandatory: boolean

  @column()
  public isActive: boolean

  @column()
  public priority: number

  @column()
  public sortOrder: number

  @column()
  public currency: string | null

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @belongsTo(() => Service)
  public service: BelongsTo<typeof Service>

  /**
   * Calculate the charge amount based on the base amount
   */
  public calculateCharge(baseAmount: number): number {
    const numericBaseAmount = Number(baseAmount) || 0

    if (this.type === 'percentage' && this.percentageRate) {
      const rate = Number(this.percentageRate) || 0
      return Number(((numericBaseAmount * rate) / 100).toFixed(2))
    }

    return Number(this.amount) || 0
  }

  /**
   * Check if the charge is applicable for the given order
   */
  public async isApplicable(order: any, baseAmount: number): Promise<boolean> {
    if (!this.isActive) {
      return false
    }

    // Check vendor match
    if (this.vendorId && this.vendorId !== order.vendorId) {
      return false
    }

    // Check branch match
    if (this.branchId && this.branchId !== order.branchId) {
      return false
    }

    // Check service match
    if (this.serviceId && this.serviceId !== order.serviceId) {
      return false
    }

    // Check conditions
    if (this.conditions) {
      // Check minimum order amount
      if (this.conditions.minOrderAmount && baseAmount < this.conditions.minOrderAmount) {
        return false
      }

      // Check maximum order amount
      if (this.conditions.maxOrderAmount && baseAmount > this.conditions.maxOrderAmount) {
        return false
      }

      // Check applicable products
      if (this.conditions.applicableProducts && order.items) {
        const hasApplicableProduct = order.items.some((item: any) =>
          this.conditions.applicableProducts.includes(item.productId)
        )
        if (!hasApplicableProduct) {
          return false
        }
      }

      // Check applicable user types
      if (this.conditions.applicableUserTypes && order.user) {
        if (!this.conditions.applicableUserTypes.includes(order.user.type)) {
          return false
        }
      }

      // Check order types (for preorder surcharge, reservation surcharge)
      if (this.conditions.order_types && order.type) {
        if (!this.conditions.order_types.includes(order.type)) {
          return false
        }
      }

      // Check excluded order types (for service charges that shouldn't apply to preorders)
      if (this.conditions.exclude_order_types && order.type) {
        if (this.conditions.exclude_order_types.includes(order.type)) {
          return false
        }
      }

      // Check delivery types (for service charges, delivery fees)
      if (this.conditions.delivery && order.delivery) {
        if (!this.conditions.delivery.includes(order.delivery)) {
          return false
        }
      }

      // Check service types (for digital menu/waiter surcharges)
      if (this.conditions.service_types) {
        // This would need to be determined based on order context
        // For now, we'll assume all orders are digital menu orders
        // In a real implementation, this would check order.serviceType or similar
      }
    }

    return true
  }

  @beforeSave()
  public static async validateChargeType(charge: ChargeConfiguration) {
    if (charge.type === 'percentage' && !charge.percentageRate) {
      throw new Error('Percentage rate is required for percentage type charges')
    }
    if (charge.type === 'fixed' && charge.percentageRate) {
      charge.percentageRate = null
    }
  }
}
