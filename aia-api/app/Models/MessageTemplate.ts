import { DateTime } from 'luxon'
import { column, BaseModel, hasMany, Has<PERSON>any } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import MessageTemplateFilter from './Filters/MessageTemplateFilter'
import Message from './Message'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'

export default class MessageTemplate extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => MessageTemplateFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public content: string

  @column()
  public branchId: string

  @column()
  public vendorId: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @attachment({ folder: 'templates', preComputeUrl: true })
  public image: AttachmentContract | null

  @hasMany(() => Message)
  public messages: HasMany<typeof Message>
}
