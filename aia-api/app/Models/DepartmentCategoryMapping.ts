import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import Department from './Department'
import ProductCategory from './ProductCategory'

/**
 * @name Department Category Mapping
 * @version 1.0.0
 * @description Maps product categories to departments for auto-assignment
 */
export default class DepartmentCategoryMapping extends BaseModel {
  public static selfAssignPrimaryKey = true
  public static table = 'department_category_mappings'

  @column({ isPrimary: true })
  public id: string

  @column()
  public departmentId: string

  @column()
  public productCategoryId: string

  @column()
  public priority: number

  @column()
  public active: boolean

  @column()
  public conditions: Record<string, any> | null

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(mapping: DepartmentCategoryMapping) {
    mapping.id = ulid().toLowerCase()
  }

  // Relationships
  @belongsTo(() => Department)
  public department: BelongsTo<typeof Department>

  @belongsTo(() => ProductCategory)
  public productCategory: BelongsTo<typeof ProductCategory>

  // Business logic methods
  public static async findMappingForProduct(
    productCategoryId: string,
    vendorId: string
  ): Promise<DepartmentCategoryMapping | null> {
    return await this.query()
      .where('product_category_id', productCategoryId)
      .where('active', true)
      .whereHas('department', (deptQuery) => {
        deptQuery.where('vendor_id', vendorId).where('active', true)
      })
      .orderBy('priority', 'asc')
      .preload('department')
      .first()
  }

  public static async findMappingsForVendor(
    vendorId: string
  ): Promise<DepartmentCategoryMapping[]> {
    return await this.query()
      .where('active', true)
      .whereHas('department', (deptQuery) => {
        deptQuery.where('vendor_id', vendorId).where('active', true)
      })
      .preload('department')
      .preload('productCategory')
      .orderBy('priority', 'asc')
  }

  public static async createMapping(
    departmentId: string,
    productCategoryId: string,
    priority: number = 1,
    conditions: Record<string, any> | null = null
  ): Promise<DepartmentCategoryMapping> {
    return await this.create({
      departmentId,
      productCategoryId,
      priority,
      active: true,
      conditions,
    })
  }

  public async updatePriority(newPriority: number): Promise<void> {
    this.priority = newPriority
    await this.save()
  }

  public async deactivate(): Promise<void> {
    this.active = false
    await this.save()
  }

  public async activate(): Promise<void> {
    this.active = true
    await this.save()
  }

  public meetsConditions(product: any, order: any): boolean {
    if (!this.conditions) return true

    // Check time-based conditions
    if (this.conditions.timeRestrictions) {
      const now = DateTime.now()
      const currentHour = now.hour
      const currentDay = now.weekdayShort?.toLowerCase()

      if (this.conditions.timeRestrictions.hours) {
        const { start, end } = this.conditions.timeRestrictions.hours
        if (currentHour < start || currentHour > end) return false
      }

      if (this.conditions.timeRestrictions.days) {
        if (!this.conditions.timeRestrictions.days.includes(currentDay)) return false
      }
    }

    // Check product-specific conditions
    if (this.conditions.productConditions) {
      const { minPrice, maxPrice, tags } = this.conditions.productConditions

      if (minPrice && product.price < minPrice) return false
      if (maxPrice && product.price > maxPrice) return false
      if (tags && !tags.some((tag: string) => product.tags?.includes(tag))) return false
    }

    // Check order-specific conditions
    if (this.conditions.orderConditions) {
      const { deliveryTypes, orderTypes } = this.conditions.orderConditions

      if (deliveryTypes && !deliveryTypes.includes(order.delivery)) return false
      if (orderTypes && !orderTypes.includes(order.type)) return false
    }

    return true
  }
}
