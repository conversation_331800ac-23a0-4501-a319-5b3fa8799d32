import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  afterCreate,
  afterUpdate,
  beforeCreate,
  belongsTo,
  column,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import Vendor from './Vendor'
import Branch from './Branch'
import Section from './Section'
import User from './User'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import OrderFilter from './Filters/OrderFilter'
import { ulid } from 'ulidx'
import Lot from './Lot'
import Product from './Product'
import { Queue } from '@ioc:Rlanz/Queue'

export default class TempOrder extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => OrderFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public sectionId: string

  @column()
  public lotId: string

  @column()
  public userId: string

  @column()
  public staffId: string

  @column()
  public action: 'Purchase' | 'Booking' | 'Registration' | 'Access' | 'Process'

  @column()
  public type: 'Preorder' | 'Instant'

  @column()
  public delivery: 'Takeaway' | 'Dinein' | 'Delivery' | 'Selfpick'

  @column()
  public status:
    | 'Pending'
    | 'Placed'
    | 'Processing'
    | 'Delivering'
    | 'Delivered'
    | 'Completed'
    | 'Cancelled'

  @column()
  public ref: string

  @column()
  public meta: Record<string, any>

  @column()
  public items: Record<string, any>

  @column.dateTime({ autoCreate: true })
  public acceptedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(order: TempOrder) {
    order.id = ulid().toLowerCase()
  }

  @computed()
  public get total() {
    let amount = 0

    Product.query()
      .whereIn('id', Object.keys(this.items))
      .exec()
      .then((items) => {
        items?.forEach((item) => {
          amount += parseInt(String(item.price)) * this.items[item.id]?.quantity
        })
      })

    if (this.meta && this.meta.charges) {
      amount += Object.values(this.meta.charges as Record<string, number>)?.reduce(
        (acc, charge) => acc + parseInt(String(charge)),
        0
      )
    }

    return amount
  }

  @computed()
  public get orderItems() {
    return Product.query().whereIn('id', Object.keys(this.items)).exec()
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @belongsTo(() => Section)
  public section: BelongsTo<typeof Section>

  @belongsTo(() => Lot)
  public lot: BelongsTo<typeof Lot>

  @belongsTo(() => User, { foreignKey: 'userId' })
  public customer: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'staffId' })
  public staff: BelongsTo<typeof User>

  @afterCreate()
  public static async process(order: TempOrder) {
    console.log("AFTER CREATE");
    await Queue.dispatch('App/Jobs/ProcessTempOrder', {
      orderId: order.id,
    })
  }

  @afterUpdate()
  public static async generateOrder(order: TempOrder) {
    await Queue.dispatch('App/Jobs/ProcessOrder', {
      orderId: order.id,
    })
  }
}
