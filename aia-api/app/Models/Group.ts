import { DateTime } from 'luxon'
import { BaseModel, ManyToMany, beforeCreate, column, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import GroupFilter from './Filters/GroupFilter'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { ulid } from 'ulidx'

export default class Group extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => GroupFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public name: string

  @column()
  public details: string

  @attachment({ folder: 'groups' })
  public image: AttachmentContract

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async assignId(group: Group) {
    group.id = ulid().toLowerCase()
  }

  @manyToMany(() => User, {
    pivotTable: 'customer_groups',
    pivotTimestamps: true,
    pivotColumns: ['vendor_id', 'branch_id'],
  })
  public members: ManyToMany<typeof User>
}
