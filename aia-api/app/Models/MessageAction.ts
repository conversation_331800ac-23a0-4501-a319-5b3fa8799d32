import { DateTime } from 'luxon'
import { BaseModel, <PERSON><PERSON>sTo, <PERSON><PERSON>any, belongsTo, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Message from './Message'
import ActionType from './ActionType'
import ActionResponse from './ActionResponse'

export default class MessageAction extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public messageId: number

  @column()
  public typeId: number

  @column()
  public config: object

  @column()
  public status: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Message)
  public message: BelongsTo<typeof Message>

  @belongsTo(() => ActionType)
  public type: BelongsTo<typeof ActionType>

  @hasMany(() => ActionResponse)
  public responses: HasMany<typeof ActionResponse>

  public validateConfig(config: object): boolean {
    return this.type.validateConfig(config)
  }

  public getValidationErrors(config: object): string[] {
    return this.type.getValidationErrors(config)
  }

  public mergeConfig(config: object): object {
    return this.type.mergeConfig(config)
  }
} 