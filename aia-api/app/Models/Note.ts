import { DateTime } from 'luxon'
import { column, BaseModel, BelongsTo, belongsTo } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import NoteFilter from './Filters/NoteFilter'
import Product from './Product'

export default class Note extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => NoteFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public type: string

  @column()
  public content: string

  @column()
  public branchId: string

  @column()
  public userId: string

  @column()
  public productId: string | null

  @column()
  public meta: Record<string, any>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Product)
  public product: BelongsTo<typeof Product>
}
