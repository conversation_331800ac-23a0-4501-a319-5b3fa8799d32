import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class OrderNote extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public orderId: string

  @column()
  public userId: string

  @column()
  public staffId: string

  @column()
  public title: string

  @column()
  public content: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
