import { DateTime } from 'luxon'
import {
  <PERSON>Model,
  BelongsTo,
  beforeCreate,
  belongsTo,
  column,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { ulid } from 'ulidx'
import Vendor from './Vendor'
import User from './User'

export type VerificationStatus = 'pending' | 'under_review' | 'verified' | 'rejected' | 'suspended'

export interface VehicleDocumentation {
  registration: {
    number: string
    expires_at: string
    status: 'pending' | 'verified' | 'expired'
  }
  inspection: {
    certificate_number: string
    expires_at: string
    status: 'pending' | 'verified' | 'expired'
  }
}

export interface InsuranceDetails {
  provider: string
  policy_number: string
  coverage_amount: number
  expires_at: string
  status: 'pending' | 'verified' | 'expired'
}

export interface RequiredDocuments {
  driver_license: {
    number: string
    expires_at: string
    status: 'pending' | 'verified' | 'expired'
  }
  background_check: {
    certificate_number: string
    issued_at: string
    status: 'pending' | 'verified' | 'expired'
  }
  safety_training: {
    certificate_number: string
    completed_at: string
    expires_at: string
    status: 'pending' | 'verified' | 'expired'
  }
}

export interface WorkingHours {
  [key: string]: {
    start: string
    end: string
    available: boolean
  }
}

export interface BreakTime {
  start: string
  end: string
  duration_minutes: number
}

export default class DeliveryProviderProfile extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public verificationStatus: VerificationStatus

  @column.dateTime()
  public applicationDate: DateTime

  @column.dateTime()
  public verifiedAt: DateTime | null

  @column()
  public verificationNotes: string | null

  @column()
  public verifierAdminId: string | null

  // Delivery capabilities
  @column()
  public vehicleTypes: string[] | null

  @column()
  public maxDeliveryDistance: number | null

  @column()
  public maxConcurrentOrders: number | null

  @column()
  public maxOrderWeight: number | null

  @column()
  public maxOrderVolume: number | null

  // Operational settings
  @column()
  public workingHours: WorkingHours | null

  @column()
  public breakTimes: BreakTime[] | null

  @column()
  public autoAcceptOrders: boolean

  @column()
  public maxOrdersPerDay: number | null

  @column()
  public preparationBufferMinutes: number

  // Compliance and documentation
  @column()
  public requiredDocuments: RequiredDocuments | null

  @column()
  public insuranceDetails: InsuranceDetails | null

  @column()
  public vehicleDocumentation: VehicleDocumentation | null

  // Performance metrics
  @column()
  public averageRating: number | null

  @column()
  public totalDeliveries: number

  @column()
  public successfulDeliveries: number

  @column()
  public completionRate: number | null

  @column()
  public averageDeliveryTime: number | null

  @column()
  public customerComplaints: number

  // Status management
  @column()
  public suspensionReason: string | null

  @column.dateTime()
  public suspendedAt: DateTime | null

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(profile: DeliveryProviderProfile) {
    profile.id = ulid().toLowerCase()
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => User, {
    foreignKey: 'verifierAdminId',
  })
  public verifierAdmin: BelongsTo<typeof User>

  /**
   * Check if the delivery provider is eligible to accept orders
   */
  @computed()
  public get isEligibleForOrders(): boolean {
    return (
      this.verificationStatus === 'verified' &&
      this.active &&
      !this.isSuspended
    )
  }

  /**
   * Check if the delivery provider is currently suspended
   */
  @computed()
  public get isSuspended(): boolean {
    return this.verificationStatus === 'suspended' || this.suspendedAt !== null
  }

  /**
   * Check if all required documents are verified
   */
  @computed()
  public get hasValidDocuments(): boolean {
    if (!this.requiredDocuments) return false

    const documents = this.requiredDocuments
    return (
      documents.driver_license?.status === 'verified' &&
      documents.background_check?.status === 'verified' &&
      documents.safety_training?.status === 'verified'
    )
  }

  /**
   * Check if insurance is valid and not expired
   */
  @computed()
  public get hasValidInsurance(): boolean {
    if (!this.insuranceDetails) return false

    const insurance = this.insuranceDetails
    const expiryDate = DateTime.fromISO(insurance.expires_at)
    return insurance.status === 'verified' && expiryDate > DateTime.now()
  }

  /**
   * Check if vehicle documentation is valid
   */
  @computed()
  public get hasValidVehicleDocumentation(): boolean {
    if (!this.vehicleDocumentation) return false

    const docs = this.vehicleDocumentation
    const registrationExpiry = DateTime.fromISO(docs.registration.expires_at)
    const inspectionExpiry = DateTime.fromISO(docs.inspection.expires_at)

    return (
      docs.registration.status === 'verified' &&
      docs.inspection.status === 'verified' &&
      registrationExpiry > DateTime.now() &&
      inspectionExpiry > DateTime.now()
    )
  }

  /**
   * Check if the provider is currently within working hours
   */
  public isWithinWorkingHours(): boolean {
    if (!this.workingHours) return true // Always available if no hours set

    const now = DateTime.now()
    const dayOfWeek = now.weekdayLong?.toLowerCase()
    const currentTime = now.toFormat('HH:mm')

    if (!dayOfWeek || !this.workingHours[dayOfWeek]) return false

    const dayHours = this.workingHours[dayOfWeek]
    if (!dayHours.available) return false

    return currentTime >= dayHours.start && currentTime <= dayHours.end
  }

  /**
   * Check if the provider can accept more orders based on daily limit
   */
  public async canAcceptMoreOrders(): Promise<boolean> {
    if (!this.maxOrdersPerDay) return true

    // This would need to be implemented with actual order counting logic
    // For now, return true as a placeholder
    return true
  }

  /**
   * Update performance metrics after a delivery
   */
  public async updatePerformanceMetrics(
    deliveryTime: number,
    rating: number,
    wasSuccessful: boolean
  ): Promise<void> {
    this.totalDeliveries += 1

    if (wasSuccessful) {
      this.successfulDeliveries += 1
    }

    // Update completion rate
    this.completionRate = (this.successfulDeliveries / this.totalDeliveries) * 100

    // Update average rating
    if (this.averageRating === null) {
      this.averageRating = rating
    } else {
      this.averageRating = (this.averageRating + rating) / 2
    }

    // Update average delivery time
    if (this.averageDeliveryTime === null) {
      this.averageDeliveryTime = deliveryTime
    } else {
      this.averageDeliveryTime = (this.averageDeliveryTime + deliveryTime) / 2
    }

    await this.save()
  }

  /**
   * Suspend the delivery provider
   */
  public async suspend(reason: string, adminId: string): Promise<void> {
    this.verificationStatus = 'suspended'
    this.suspensionReason = reason
    this.suspendedAt = DateTime.now()
    this.verifierAdminId = adminId
    await this.save()
  }

  /**
   * Reactivate a suspended delivery provider
   */
  public async reactivate(adminId: string): Promise<void> {
    this.verificationStatus = 'verified'
    this.suspensionReason = null
    this.suspendedAt = null
    this.verifierAdminId = adminId
    await this.save()
  }
}
