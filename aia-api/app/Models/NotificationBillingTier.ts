import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import NotificationUsage from './NotificationUsage'

export default class NotificationBillingTier extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public code: string

  @column()
  public description: string

  @column()
  public base_price: number

  @column()
  public currency: string

  @column()
  public monthly_limit: number

  @column()
  public overage_rate: number

  @column()
  public sms_rate: number

  @column()
  public email_rate: number

  @column()
  public push_rate: number

  @column()
  public is_active: boolean

  @column()
  public meta: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => NotificationUsage)
  public usages: HasMany<typeof NotificationUsage>

  /**
   * Calculate the cost for a notification based on the channel
   */
  public calculateCost(channel: 'sms' | 'email' | 'push'): number {
    switch (channel) {
      case 'sms':
        return this.sms_rate
      case 'email':
        return this.email_rate
      case 'push':
        return this.push_rate
      default:
        return 0
    }
  }

  /**
   * Calculate overage cost for exceeding monthly limit
   */
  public calculateOverageCost(usageCount: number): number {
    if (usageCount <= this.monthly_limit) {
      return 0
    }
    return (usageCount - this.monthly_limit) * this.overage_rate
  }
} 