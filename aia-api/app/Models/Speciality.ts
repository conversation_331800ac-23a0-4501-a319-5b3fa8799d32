import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  ManyToMany,
  beforeCreate,
  belongsTo,
  column,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import Vendor from './Vendor'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import SpecialityFilter from './Filters/SpecialityFilter'
import { ulid } from 'ulidx'
import Service from './Service'

export default class Speciality extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => SpecialityFilter
  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public slug: string

  @column()
  public details: string | null

  @attachment({ folder: 'specialities', preComputeUrl: true })
  public image: AttachmentContract | null

  @column()
  public serviceId: string

  @beforeCreate()
  public static async generateUlid(speciality: Speciality) {
    speciality.id = ulid().toLowerCase()
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async slugify(speciality: Speciality) {
    speciality.slug = speciality.name
      .toLowerCase()
      .replace(/ /g, '-')
      .replace(/[^\w-]+/g, '')
  }

  @belongsTo(() => Service)
  public service: BelongsTo<typeof Service>

  @manyToMany(() => Vendor, {
    pivotTable: 'vendor_specialities',
    pivotTimestamps: true,
  })
  public vendors: ManyToMany<typeof Vendor>
}
