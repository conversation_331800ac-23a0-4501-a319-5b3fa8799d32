import { DateTime } from 'luxon'
import { compose, string } from '@ioc:Adonis/Core/Helpers'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  beforeCreate,
  belongsTo,
  column,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import { ulid } from 'ulidx'
import Product from './Product'
import ProductType from './ProductType'
import ProductCategoryFilter from './Filters/ProductCategoryFilter'
export default class ProductCategory extends compose(BaseModel, Filterable) {
  public static $filter = () => ProductCategoryFilter
  public static namingStrategy = new CamelCaseStrategy()

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public slug: string

  @column()
  public details: string

  @column()
  public productTypeId: string

  @attachment({ folder: 'categories', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(category: ProductCategory) {
    category.id = ulid().toLowerCase()
  }

  @beforeCreate()
  public static async generateSlug(category: ProductCategory) {
    category.slug = (
      category.slug || category.productTypeId + '-' + string.toSlug(category.name)
    ).toLowerCase()
  }

  @belongsTo(() => ProductType)
  public productType: BelongsTo<typeof ProductType>

  @hasMany(() => Product)
  public products: HasMany<typeof Product>
}
