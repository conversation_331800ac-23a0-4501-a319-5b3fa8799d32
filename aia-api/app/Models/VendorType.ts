import { DateTime } from 'luxon'
import { compose } from '@ioc:<PERSON>onis/Core/Helpers'
import { BaseModel, HasMany, beforeCreate, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import VendorTypeFilter from './Filters/VendorTypeFilter'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import VendorCategory from './VendorCategory'
import { ulid } from 'ulidx'

export default class VendorType extends compose(BaseModel, Filterable) {
  public static $filter = () => VendorTypeFilter
  public static namingStrategy = new CamelCaseStrategy()

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public slug: string

  @column()
  public details: string

  @column()
  public serviceId: string

  @attachment({ folder: 'types', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(model: VendorType) {
    model.id = ulid().toLowerCase()
  }

  @beforeCreate()
  public static generateSlug(model: VendorType) {
    model.slug = model.name.toLowerCase().replace(/ /g, '_')
  }

  @hasMany(() => VendorCategory)
  public vendorCategories: HasMany<typeof VendorCategory>
}
