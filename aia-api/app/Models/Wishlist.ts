import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { BaseModel, beforeCreate, column } from '@ioc:Adonis/Lucid/Orm'
import WishlistFilter from './Filters/WishlistFilter'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { ulid } from 'ulidx'

export default class Wishlist extends compose(BaseModel, Filterable) {
  public static $filter = () => WishlistFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public userId: string

  @column()
  public productId: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(model: Wishlist) {
    model.id = ulid().toLowerCase()
  }
}
