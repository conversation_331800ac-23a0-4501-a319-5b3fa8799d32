import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, computed } from '@ioc:Adonis/Lucid/Orm'
import OrderItem from './OrderItem'
import ModifierOption from './ModifierOption'
import User from './User'

export default class OrderItemModifier extends BaseModel {
  public static table = 'order_item_modifiers'

  @column({ isPrimary: true })
  public id: number

  @column()
  public orderItemId: number

  @column()
  public modifierOptionId: string

  @column()
  public quantity: number

  @column()
  public priceAtTimeOfOrder: number

  // Status tracking fields
  @column()
  public status: 'pending' | 'preparing' | 'completed' | 'skipped' | 'cancelled' | 'failed' | 'on_hold'

  @column()
  public preparedByStaffId: string | null

  // Timing tracking
  @column.dateTime()
  public preparationStartedAt: DateTime | null

  @column.dateTime()
  public preparationCompletedAt: DateTime | null

  // Priority and complexity tracking
  @column()
  public complexityLevel: number

  @column()
  public requiresSpecialSkill: boolean

  @column()
  public affectsAllergens: boolean

  // Preparation details and notes
  @column()
  public preparationNotes: string | null

  @column()
  public specialInstructions: string | null

  @column()
  public preparationSteps: Record<string, any> | null

  // Quality control
  @column()
  public qualityCheckStatus: 'pending' | 'passed' | 'failed' | 'not_required'

  @column()
  public qualityCheckedBy: string | null

  @column.dateTime()
  public qualityCheckedAt: DateTime | null

  // Error handling and retry tracking
  @column()
  public failureReason: string | null

  @column()
  public preparationAttempts: number

  @column()
  public attemptHistory: Record<string, any> | null

  // Customer satisfaction
  @column()
  public customerApproved: boolean | null

  @column()
  public customerFeedback: string | null

  // Cost and inventory impact
  @column()
  public actualCost: number | null

  @column()
  public affectsInventory: boolean

  @column()
  public inventoryImpact: Record<string, any> | null

  // Performance metrics
  @column()
  public actualPreparationTime: number | null

  @column()
  public efficiencyScore: number | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Existing relationships
  @belongsTo(() => OrderItem)
  public orderItem: BelongsTo<typeof OrderItem>

  @belongsTo(() => ModifierOption)
  public option: BelongsTo<typeof ModifierOption>

  // New relationships for status tracking
  @belongsTo(() => User, { foreignKey: 'preparedByStaffId' })
  public preparedByStaff: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'qualityCheckedBy' })
  public qualityChecker: BelongsTo<typeof User>

  // Computed properties
  @computed()
  public get preparationTimeMinutes(): number | null {
    if (this.preparationStartedAt && this.preparationCompletedAt) {
      return this.preparationCompletedAt.diff(this.preparationStartedAt, 'minutes').minutes
    }
    return null
  }

  @computed()
  public get statusDisplayName(): string {
    const statusMap = {
      'pending': 'Pending',
      'preparing': 'Preparing',
      'completed': 'Completed',
      'skipped': 'Skipped',
      'cancelled': 'Cancelled',
      'failed': 'Failed',
      'on_hold': 'On Hold'
    }
    return statusMap[this.status] || this.status
  }

  @computed()
  public get complexityDisplayName(): string {
    const complexityMap = {
      1: 'Simple',
      2: 'Easy',
      3: 'Medium',
      4: 'Complex',
      5: 'Very Complex'
    }
    return complexityMap[this.complexityLevel] || 'Unknown'
  }

  @computed()
  public get canBeStarted(): boolean {
    return this.status === 'pending' || this.status === 'on_hold'
  }

  @computed()
  public get canBeCompleted(): boolean {
    return this.status === 'preparing'
  }

  @computed()
  public get canBeSkipped(): boolean {
    return this.status === 'pending' || this.status === 'on_hold'
  }

  @computed()
  public get isCompleted(): boolean {
    return ['completed', 'skipped'].includes(this.status)
  }

  @computed()
  public get requiresAttention(): boolean {
    return this.requiresSpecialSkill || this.affectsAllergens || this.status === 'failed'
  }

  @computed()
  public get totalCost(): number {
    return this.priceAtTimeOfOrder * this.quantity
  }

  @computed()
  public get hasQualityIssues(): boolean {
    return this.qualityCheckStatus === 'failed' || this.preparationAttempts > 1
  }

  // Status transition methods
  public async startPreparation(staffId: string): Promise<void> {
    if (!this.canBeStarted) {
      throw new Error(`Cannot start preparation for modifier with status: ${this.status}`)
    }

    this.status = 'preparing'
    this.preparedByStaffId = staffId
    this.preparationStartedAt = DateTime.now()

    // Update attempt history
    const history = this.attemptHistory || {}
    const attemptKey = `attempt_${this.preparationAttempts}_${DateTime.now().toISO()}`
    history[attemptKey] = {
      status: 'started',
      staffId,
      timestamp: DateTime.now().toISO(),
      attempt: this.preparationAttempts
    }
    this.attemptHistory = history

    await this.save()
  }

  public async completePreparation(): Promise<void> {
    if (!this.canBeCompleted) {
      throw new Error(`Cannot complete preparation for modifier with status: ${this.status}`)
    }

    this.status = 'completed'
    this.preparationCompletedAt = DateTime.now()

    // Calculate actual preparation time
    if (this.preparationStartedAt) {
      this.actualPreparationTime = this.preparationCompletedAt.diff(
        this.preparationStartedAt, 'minutes'
      ).minutes
    }

    // Update attempt history
    const history = this.attemptHistory || {}
    const attemptKey = `attempt_${this.preparationAttempts}_completed_${DateTime.now().toISO()}`
    history[attemptKey] = {
      status: 'completed',
      timestamp: DateTime.now().toISO(),
      actualTime: this.actualPreparationTime,
      attempt: this.preparationAttempts
    }
    this.attemptHistory = history

    await this.save()
  }

  public async skip(reason: string, staffId?: string): Promise<void> {
    if (!this.canBeSkipped) {
      throw new Error(`Cannot skip modifier with status: ${this.status}`)
    }

    this.status = 'skipped'
    this.preparationNotes = reason

    // Update attempt history
    const history = this.attemptHistory || {}
    history[`skipped_${DateTime.now().toISO()}`] = {
      status: 'skipped',
      reason,
      staffId,
      timestamp: DateTime.now().toISO()
    }
    this.attemptHistory = history

    await this.save()
  }

  public async markAsFailed(reason: string, staffId?: string): Promise<void> {
    this.status = 'failed'
    this.failureReason = reason

    // Update attempt history
    const history = this.attemptHistory || {}
    history[`failed_${DateTime.now().toISO()}`] = {
      status: 'failed',
      reason,
      staffId,
      timestamp: DateTime.now().toISO(),
      attempt: this.preparationAttempts
    }
    this.attemptHistory = history

    await this.save()
  }

  public async retry(staffId: string): Promise<void> {
    if (this.status !== 'failed') {
      throw new Error('Can only retry failed modifiers')
    }

    this.preparationAttempts += 1
    this.status = 'pending'
    this.failureReason = null
    this.preparationStartedAt = null
    this.preparationCompletedAt = null

    // Update attempt history
    const history = this.attemptHistory || {}
    history[`retry_${DateTime.now().toISO()}`] = {
      status: 'retry',
      staffId,
      timestamp: DateTime.now().toISO(),
      attempt: this.preparationAttempts
    }
    this.attemptHistory = history

    await this.save()
  }

  public async performQualityCheck(staffId: string, passed: boolean, notes?: string): Promise<void> {
    this.qualityCheckStatus = passed ? 'passed' : 'failed'
    this.qualityCheckedBy = staffId
    this.qualityCheckedAt = DateTime.now()

    if (notes) {
      this.preparationNotes = (this.preparationNotes || '') + `\nQuality Check: ${notes}`
    }

    // If quality check failed, mark modifier as failed
    if (!passed) {
      await this.markAsFailed(`Quality check failed: ${notes || 'No details provided'}`, staffId)
    }

    await this.save()
  }
}
