import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate, hasMany, belongsTo, HasMany, BelongsTo, computed } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import Service from './Service'
import ServiceConfigurationOption from './ServiceConfigurationOption'

export default class ServiceConfiguration extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public serviceId: string

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(serviceConfiguration: ServiceConfiguration) {
    serviceConfiguration.id = ulid().toLowerCase()
  }

  @belongsTo(() => Service)
  public service: BelongsTo<typeof Service>

  @hasMany(() => ServiceConfigurationOption)
  public options: Has<PERSON>any<typeof ServiceConfigurationOption>

  @computed()
  public get optionCount(): number {
    return this.$extras.option_count || 0
  }

  @computed()
  public get durationOptionCount(): number {
    return this.$extras.duration_option_count || 0
  }

  @computed()
  public get activeOptionCount(): number {
    return this.$extras.active_option_count || 0
  }

  /**
   * Get options grouped by type
   */
  public async getOptionsByType(): Promise<Record<string, ServiceConfigurationOption[]>> {
    const options = await this.related('options').query().where('active', true).orderBy('sortOrder')
    
    return options.reduce((grouped, option) => {
      if (!grouped[option.type]) {
        grouped[option.type] = []
      }
      grouped[option.type].push(option)
      return grouped
    }, {} as Record<string, ServiceConfigurationOption[]>)
  }

  /**
   * Get all duration options with their linked Duration entities
   */
  public async getDurationOptions(): Promise<ServiceConfigurationOption[]> {
    return await this.related('options')
      .query()
      .where('type', 'duration')
      .where('active', true)
      .whereNotNull('durationId')
      .preload('duration')
      .orderBy('sortOrder')
  }

  /**
   * Get non-duration options
   */
  public async getNonDurationOptions(): Promise<ServiceConfigurationOption[]> {
    return await this.related('options')
      .query()
      .where('type', '!=', 'duration')
      .where('active', true)
      .orderBy(['type', 'sortOrder'])
  }

  /**
   * Clone this configuration with a new name
   */
  public async clone(newName: string, newDescription?: string): Promise<ServiceConfiguration> {
    const clonedConfig = await ServiceConfiguration.create({
      name: newName,
      description: newDescription || `${this.description} (Copy)`,
      serviceId: this.serviceId,
      active: this.active
    })

    // Clone all options
    const options = await this.related('options').query()
    for (const option of options) {
      await ServiceConfigurationOption.create({
        serviceConfigurationId: clonedConfig.id,
        name: option.name,
        type: option.type,
        description: option.description,
        priceAdjustment: option.priceAdjustment,
        durationId: option.durationId,
        isDefault: option.isDefault,
        sortOrder: option.sortOrder,
        constraints: option.constraints,
        active: option.active
      })
    }

    return clonedConfig
  }

  /**
   * Validate the configuration
   */
  public async validateConfiguration(): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    // Check if configuration has options
    const optionCount = await this.related('options').query().where('active', true).count('* as total')
    if (optionCount[0].$extras.total === 0) {
      warnings.push('Configuration has no active options')
    }

    // Check for duration options
    const durationOptions = await this.getDurationOptions()
    if (durationOptions.length === 0) {
      warnings.push('Configuration has no duration options - services may not have time-based scheduling')
    }

    // Check for duplicate default options per type
    const optionsByType = await this.getOptionsByType()
    for (const [type, options] of Object.entries(optionsByType)) {
      const defaultOptions = options.filter(option => option.isDefault)
      if (defaultOptions.length > 1) {
        errors.push(`Multiple default options found for type '${type}'`)
      }
    }

    // Check for orphaned duration references
    for (const option of durationOptions) {
      if (!option.duration) {
        errors.push(`Duration option '${option.name}' references non-existent duration ID: ${option.durationId}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Get configuration statistics
   */
  public async getStatistics(): Promise<{
    totalOptions: number
    optionsByType: Record<string, number>
    durationOptions: number
    activeOptions: number
    defaultOptions: number
  }> {
    const options = await this.related('options').query()
    
    const optionsByType = options.reduce((counts, option) => {
      counts[option.type] = (counts[option.type] || 0) + 1
      return counts
    }, {} as Record<string, number>)

    return {
      totalOptions: options.length,
      optionsByType,
      durationOptions: options.filter(opt => opt.type === 'duration').length,
      activeOptions: options.filter(opt => opt.active).length,
      defaultOptions: options.filter(opt => opt.isDefault).length
    }
  }

  /**
   * Bulk update option sort orders
   */
  public async updateOptionSortOrders(optionOrders: { id: string; sortOrder: number }[]): Promise<void> {
    for (const { id, sortOrder } of optionOrders) {
      await ServiceConfigurationOption.query()
        .where('id', id)
        .where('serviceConfigurationId', this.id)
        .update({ sortOrder })
    }
  }

  /**
   * Create a template configuration for common service types
   */
  public static createTemplate(
    name: string,
    serviceId: string,
    templateType: 'basic' | 'standard' | 'premium' | 'custom',
    options: Partial<ServiceConfiguration> = {}
  ): Partial<ServiceConfiguration> {
    const templates = {
      basic: {
        description: 'Basic service configuration with essential options',
      },
      standard: {
        description: 'Standard service configuration with common options',
      },
      premium: {
        description: 'Premium service configuration with comprehensive options',
      },
      custom: {
        description: 'Custom service configuration',
      }
    }

    return {
      name,
      serviceId,
      description: templates[templateType].description,
      active: true,
      ...options
    }
  }

  /**
   * Search configurations by name or description
   */
  public static async search(query: string, serviceId?: string) {
    const searchQuery = ServiceConfiguration.query()
      .where((builder) => {
        builder
          .where('name', 'ILIKE', `%${query}%`)
          .orWhere('description', 'ILIKE', `%${query}%`)
      })
      .where('active', true)

    if (serviceId) {
      searchQuery.where('serviceId', serviceId)
    }

    return await searchQuery
      .preload('service')
      .withCount('options')
      .withCount('options', (query) => {
        query.where('active', true).as('activeOptionCount')
      })
      .withCount('options', (query) => {
        query.where('type', 'duration').as('durationOptionCount')
      })
      .orderBy('name')
  }
}
