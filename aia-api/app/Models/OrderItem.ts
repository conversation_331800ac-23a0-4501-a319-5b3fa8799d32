import { DateTime } from 'luxon'
import {
  BaseModel,
  column,
  belongsTo,
  hasMany,
  BelongsTo,
  <PERSON><PERSON><PERSON>,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import Order from './Order'
import Product from './Product'
import OrderItemModifier from './OrderItemModifier'
import Department from './Department'
import User from './User'

export default class OrderItem extends BaseModel {
  public static table = 'order_items'

  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'order_id' })
  public orderId: string

  @column()
  public productId: string

  @column()
  public quantity: number

  @column()
  public price: number

  @column()
  public meta: any

  // Status tracking fields
  @column()
  public status:
    | 'pending'
    | 'placed'
    | 'preparing'
    | 'ready'
    | 'served'
    | 'cancelled'
    | 'on_hold'
    | 'delayed'

  @column()
  public departmentId: string | null

  @column()
  public assignedStaffId: string | null

  // Timing and preparation tracking
  @column()
  public estimatedPreparationTime: number | null

  @column.dateTime()
  public preparationStartedAt: DateTime | null

  @column.dateTime()
  public preparationCompletedAt: DateTime | null

  @column.dateTime()
  public servedAt: DateTime | null

  // Priority and workflow management
  @column()
  public priorityLevel: number

  @column()
  public requiresSpecialAttention: boolean

  @column()
  public specialInstructions: string | null

  // Preparation notes and communication
  @column()
  public preparationNotes: Record<string, any> | null

  @column()
  public statusHistory: Record<string, any> | null

  // Quality control
  @column()
  public qualityCheckStatus: 'pending' | 'passed' | 'failed' | 'not_required'

  @column()
  public qualityCheckedBy: string | null

  // Customer feedback and modifications
  @column()
  public customerModifications: Record<string, any> | null

  @column()
  public cancellationReason: string | null

  // Performance tracking
  @column()
  public actualPreparationTime: number | null

  @column()
  public preparationAttempts: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Existing relationships
  @belongsTo(() => Order, {
    foreignKey: 'orderId',
  })
  public order: BelongsTo<typeof Order>

  @belongsTo(() => Product)
  public product: BelongsTo<typeof Product>

  @hasMany(() => OrderItemModifier)
  public modifiers: HasMany<typeof OrderItemModifier>

  // New relationships for status tracking
  @belongsTo(() => Department, { foreignKey: 'departmentId' })
  public department: BelongsTo<typeof Department>

  @belongsTo(() => User, { foreignKey: 'assignedStaffId' })
  public assignedStaff: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'qualityCheckedBy' })
  public qualityChecker: BelongsTo<typeof User>

  // Computed properties
  @computed()
  public get actualPreparationTimeMinutes(): number | null {
    if (this.preparationStartedAt && this.preparationCompletedAt) {
      return this.preparationCompletedAt.diff(this.preparationStartedAt, 'minutes').minutes
    }
    return null
  }

  @computed()
  public get isOverdue(): boolean {
    if (!this.preparationStartedAt || !this.estimatedPreparationTime) return false
    const elapsed = DateTime.now().diff(this.preparationStartedAt, 'minutes').minutes
    return (
      elapsed > this.estimatedPreparationTime && this.status !== 'ready' && this.status !== 'served'
    )
  }

  @computed()
  public get preparationProgress(): number {
    if (!this.preparationStartedAt || !this.estimatedPreparationTime) return 0
    const elapsed = DateTime.now().diff(this.preparationStartedAt, 'minutes').minutes
    return Math.min(100, (elapsed / this.estimatedPreparationTime) * 100)
  }

  @computed()
  public get statusDisplayName(): string {
    const statusMap = {
      pending: 'Pending',
      placed: 'Placed',
      preparing: 'Preparing',
      ready: 'Ready',
      served: 'Served',
      cancelled: 'Cancelled',
      on_hold: 'On Hold',
      delayed: 'Delayed',
    }
    return statusMap[this.status] || this.status
  }

  @computed()
  public get canBeStarted(): boolean {
    return this.status === 'pending' || this.status === 'on_hold'
  }

  @computed()
  public get canBePlaced(): boolean {
    return this.status === 'pending'
  }

  @computed()
  public get canBeCompleted(): boolean {
    return this.status === 'preparing'
  }

  @computed()
  public get canBeServed(): boolean {
    return this.status === 'ready'
  }

  @computed()
  public get requiresAttention(): boolean {
    return this.requiresSpecialAttention || this.isOverdue || this.status === 'delayed'
  }

  @computed()
  public get estimatedCompletionTime(): DateTime | null {
    if (!this.preparationStartedAt || !this.estimatedPreparationTime) return null
    return this.preparationStartedAt.plus({ minutes: this.estimatedPreparationTime })
  }

  @computed()
  public get allModifiersCompleted(): boolean {
    if (!this.modifiers || this.modifiers.length === 0) return true
    return this.modifiers.every((modifier) => ['completed', 'skipped'].includes(modifier.status))
  }

  @computed()
  public get totalItemCost(): number {
    let total = this.price * this.quantity
    if (this.modifiers && this.modifiers.length > 0) {
      total += this.modifiers.reduce(
        (sum, modifier) => sum + modifier.priceAtTimeOfOrder * modifier.quantity,
        0
      )
    }
    return total
  }

  // Status transition methods
  public async placeOrder(staffId: string): Promise<void> {
    if (!this.canBePlaced) {
      throw new Error(`Cannot place order for item with status: ${this.status}`)
    }

    this.status = 'placed'
    this.assignedStaffId = staffId

    // Update status history
    const history = this.statusHistory || {}
    history[`placed_${DateTime.now().toISO()}`] = {
      status: 'placed',
      staffId,
      timestamp: DateTime.now().toISO(),
      action: 'order_placed_to_pos',
    }
    this.statusHistory = history

    await this.save()
  }

  public async startPreparation(staffId: string): Promise<void> {
    if (!this.canBeStarted) {
      throw new Error(`Cannot start preparation for item with status: ${this.status}`)
    }

    this.status = 'preparing'
    this.assignedStaffId = staffId
    this.preparationStartedAt = DateTime.now()

    // Update status history
    const history = this.statusHistory || {}
    history[`started_${DateTime.now().toISO()}`] = {
      status: 'preparing',
      staffId,
      timestamp: DateTime.now().toISO(),
    }
    this.statusHistory = history

    await this.save()
  }

  public async completePreparation(): Promise<void> {
    if (!this.canBeCompleted) {
      throw new Error(`Cannot complete preparation for item with status: ${this.status}`)
    }

    this.status = 'ready'
    this.preparationCompletedAt = DateTime.now()

    // Calculate actual preparation time
    if (this.preparationStartedAt) {
      this.actualPreparationTime = this.preparationCompletedAt.diff(
        this.preparationStartedAt,
        'minutes'
      ).minutes
    }

    // Update status history
    const history = this.statusHistory || {}
    history[`completed_${DateTime.now().toISO()}`] = {
      status: 'ready',
      timestamp: DateTime.now().toISO(),
      actualTime: this.actualPreparationTime,
    }
    this.statusHistory = history

    await this.save()
  }

  public async markAsServed(staffId?: string): Promise<void> {
    if (!this.canBeServed) {
      throw new Error(`Cannot serve item with status: ${this.status}`)
    }

    this.status = 'served'
    this.servedAt = DateTime.now()

    // Update status history
    const history = this.statusHistory || {}
    history[`served_${DateTime.now().toISO()}`] = {
      status: 'served',
      staffId,
      timestamp: DateTime.now().toISO(),
    }
    this.statusHistory = history

    await this.save()
  }

  public async cancel(reason: string, staffId?: string): Promise<void> {
    this.status = 'cancelled'
    this.cancellationReason = reason

    // Update status history
    const history = this.statusHistory || {}
    history[`cancelled_${DateTime.now().toISO()}`] = {
      status: 'cancelled',
      reason,
      staffId,
      timestamp: DateTime.now().toISO(),
    }
    this.statusHistory = history

    await this.save()
  }
}
