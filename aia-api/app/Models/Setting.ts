import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import SettingFilter from './Filters/SettingFilter'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'

export default class Setting extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => SettingFilter
  public static routeLookupKey = 'name'

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public options: Record<string, any>

  @column()
  public branchId: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
