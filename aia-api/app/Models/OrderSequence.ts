import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import Database from '@ioc:Adonis/Lucid/Database'

export default class OrderSequence extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public branchId: string | null

  @column()
  public year: string

  @column()
  public sequence: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  public static async getNextSequence(vendorId: string, branchId: string | null): Promise<number> {
    const year = DateTime.now().toFormat('yy')
    
    // Use a transaction to ensure atomicity
    const trx = await Database.transaction()
    
    try {
      // Lock the row for update
      const sequence = await this.query({ client: trx })
        .where('vendor_id', vendorId)
        .where('branch_id', branchId || '')
        .where('year', year)
        .forUpdate()
        .first()

      if (sequence) {
        // Increment and save
        sequence.sequence += 1
        await sequence.useTransaction(trx).save()
        await trx.commit()
        return sequence.sequence
      } else {
        // Create new sequence
        const newSequence = await this.create({
          id: ulid().toLowerCase(),
          vendorId,
          branchId,
          year,
          sequence: 1
        }, { client: trx })
        await trx.commit()
        return newSequence.sequence
      }
    } catch (error) {
      await trx.rollback()
      throw error
    }
  }
} 