import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import BrandFilter from './Filters/BrandFilter'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'

export default class Brand extends compose(BaseModel, Filterable) {
  public static $filter = () => BrandFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public details: string

  @attachment()
  public image: AttachmentContract

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
