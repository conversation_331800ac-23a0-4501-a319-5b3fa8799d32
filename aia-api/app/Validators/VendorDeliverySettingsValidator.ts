import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

// type VehicleType = 'motorcycle' | 'bicycle' | 'car' | 'van' | 'truck'
// type VendorStatus = 'online' | 'offline' | 'busy'
// type SurgeCondition = 'peak_hours' | 'bad_weather' | 'high_demand' | 'special_events'
// type DiscountType = 'percentage' | 'fixed' | 'first_order' | 'loyalty'
// type DiscountCondition = 'min_order_value' | 'time_of_day' | 'day_of_week' | 'customer_type'
// type VerificationStatus = 'pending' | 'verified' | 'rejected'

export default class VendorDeliverySettingsValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    preferences: schema.object.optional().members({
      maxDistance: schema.number([
        rules.unsigned(),
        rules.range(1, 100), // Maximum 100km
      ]),
      preferredVehicleTypes: schema.array().members(
        schema.string([
          rules.regex(/^(motorcycle|bicycle|car|van|truck)$/),
        ])
      ),
      workingHours: schema.object().members({
        start: schema.string([
          rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:mm format
        ]),
        end: schema.string([
          rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:mm format
        ]),
        days: schema.array().members(
          schema.number([
            rules.range(0, 6), // 0 = Sunday, 6 = Saturday
          ])
        ),
      }),
      autoAcceptOrders: schema.boolean(),
      maxOrdersPerDay: schema.number([
        rules.unsigned(),
        rules.range(1, 100), // Maximum 100 orders per day
      ]),
    }),

    settings: schema.object.optional().members({
      isAvailable: schema.boolean(),
      currentStatus: schema.string([
        rules.regex(/^(online|offline|busy)$/),
      ]),
      breakTimes: schema.array().members(
        schema.object().members({
          start: schema.string([
            rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:mm format
          ]),
          end: schema.string([
            rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:mm format
          ]),
        })
      ),
      unavailableDates: schema.array().members(
        schema.string([
          rules.regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
        ])
      ),
    }),

    pricing: schema.object.optional().members({
      basePrice: schema.number([
        rules.unsigned(),
        rules.range(0, 10000), // Maximum 10,000 base price
      ]),
      pricePerKm: schema.number([
        rules.unsigned(),
        rules.range(0, 1000), // Maximum 1,000 per km
      ]),
      minimumOrderValue: schema.number([
        rules.unsigned(),
        rules.range(0, 5000), // Maximum 5,000 minimum order value
      ]),
      surgePricing: schema.object.optional().members({
        enabled: schema.boolean(),
        multiplier: schema.number([
          rules.range(1, 5), // Maximum 5x multiplier
        ]),
        conditions: schema.array().members(
          schema.string([
            rules.regex(/^(peak_hours|bad_weather|high_demand|special_events)$/),
          ])
        ),
      }),
      discounts: schema.array.optional().members(
        schema.object().members({
          type: schema.string([
            rules.regex(/^(percentage|fixed|first_order|loyalty)$/),
          ]),
          value: schema.number([
            rules.unsigned(),
            rules.range(0, 100), // Maximum 100% or 100 fixed amount
          ]),
          conditions: schema.array().members(
            schema.string([
              rules.regex(/^(min_order_value|time_of_day|day_of_week|customer_type)$/),
            ])
          ),
        })
      ),
    }),

    status: schema.string.optional([
      rules.regex(/^(pending|verified|rejected)$/),
    ]),
    notes: schema.string.optional([
      rules.maxLength(500), // Maximum 500 characters for verification notes
    ]),
  })

  public messages = {
    'preferences.maxDistance.range': 'Maximum distance must be between 1 and 100 kilometers',
    'preferences.preferredVehicleTypes.*.regex': 'Invalid vehicle type',
    'preferences.workingHours.start.regex': 'Start time must be in HH:mm format',
    'preferences.workingHours.end.regex': 'End time must be in HH:mm format',
    'preferences.workingHours.days.*.range': 'Day must be between 0 (Sunday) and 6 (Saturday)',
    'preferences.maxOrdersPerDay.range': 'Maximum orders per day must be between 1 and 100',
    
    'settings.currentStatus.regex': 'Status must be online, offline, or busy',
    'settings.breakTimes.*.start.regex': 'Break start time must be in HH:mm format',
    'settings.breakTimes.*.end.regex': 'Break end time must be in HH:mm format',
    'settings.unavailableDates.*.regex': 'Date must be in YYYY-MM-DD format',
    
    'pricing.basePrice.range': 'Base price must be between 0 and 10,000',
    'pricing.pricePerKm.range': 'Price per km must be between 0 and 1,000',
    'pricing.minimumOrderValue.range': 'Minimum order value must be between 0 and 5,000',
    'pricing.surgePricing.multiplier.range': 'Surge multiplier must be between 1 and 5',
    'pricing.surgePricing.conditions.*.regex': 'Invalid surge pricing condition',
    'pricing.discounts.*.type.regex': 'Invalid discount type',
    'pricing.discounts.*.value.range': 'Discount value must be between 0 and 100',
    'pricing.discounts.*.conditions.*.regex': 'Invalid discount condition',
    
    'status.regex': 'Invalid verification status',
    'notes.maxLength': 'Verification notes cannot exceed 500 characters',
  }
} 