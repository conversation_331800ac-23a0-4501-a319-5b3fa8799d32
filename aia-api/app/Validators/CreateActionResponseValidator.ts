import { schema, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class CreateActionResponseValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    type: schema.string({ trim: true }, [
      rules.minLength(1),
      rules.maxLength(50)
    ]),
    text: schema.string.optional({ trim: true }),
    data: schema.object.optional().members({})
  })

  public messages = {
    'type.required': 'Response type is required',
    'type.minLength': 'Response type must be at least 1 character',
    'type.maxLength': 'Response type must not exceed 50 characters'
  }
} 