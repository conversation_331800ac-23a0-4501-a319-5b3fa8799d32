import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class CreateNoteValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    content: schema.string({ trim: true }, [
      rules.minLength(1),
      rules.maxLength(5000)
    ]),
    
    type: schema.string({ trim: true }, [
      rules.minLength(1),
      rules.maxLength(100)
    ]),
    
    branchId: schema.string({ trim: true }, [
      rules.exists({ table: 'branches', column: 'id' })
    ]),
    
    userId: schema.string({ trim: true }, [
      rules.exists({ table: 'users', column: 'id' })
    ]),
    
    productId: schema.string.optional({ trim: true }, [
      rules.exists({ table: 'products', column: 'id' })
    ]),
    
    meta: schema.object.optional().anyMembers()
  })

  public messages: CustomMessages = {
    'content.required': 'Note content is required',
    'content.minLength': 'Note content must be at least 1 character long',
    'content.maxLength': 'Note content cannot exceed 5000 characters',
    
    'type.required': 'Note type is required',
    'type.minLength': 'Note type must be at least 1 character long',
    'type.maxLength': 'Note type cannot exceed 100 characters',
    
    'branchId.required': 'Branch ID is required',
    'branchId.exists': 'Selected branch does not exist',
    
    'userId.required': 'User ID is required',
    'userId.exists': 'Selected user does not exist',
    
    'productId.exists': 'Selected product does not exist'
  }
}
