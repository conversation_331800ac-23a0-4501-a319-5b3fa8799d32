import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

/**
 * Validator for delivery provider application review (Admin only)
 * Phase 2A: Validates admin review data for delivery provider applications
 */
export default class DeliveryProviderReviewValidator {
  constructor(protected ctx: HttpContextContract) {}

  /**
   * Define schema to validate the "shape", "type", "formatting" and "integrity" of data.
   *
   * For example:
   * 1. The username must be of data type string. But then also, it should
   *    not contain special characters or numbers.
   *    ```
   *     schema.string({}, [ rules.alpha() ])
   *    ```
   *
   * 2. The email must be of data type string, formatted as a valid
   *    email. But also, not used by any other user.
   *    ```
   *     schema.string({}, [
   *       rules.email(),
   *       rules.unique({ table: 'users', column: 'email' }),
   *     ])
   *    ```
   */
  public schema = schema.create({
    status: schema.enum(['under_review', 'verified', 'rejected', 'suspended'] as const, [
      rules.required(),
    ]),
    notes: schema.string.optional({}, [
      rules.maxLength(1000),
    ]),
  })

  /**
   * Custom messages for validation failures. You can make use of dot notation `(.)`
   * for targeting nested fields and array expressions `(*)` for targeting all
   * children of an array. For example:
   *
   * {
   *   'profile.username.required': 'Username is required',
   *   'scores.*.number': 'Define scores as valid numbers'
   * }
   *
   */
  public messages: CustomMessages = {
    'status.required': 'Verification status is required',
    'status.enum': 'Status must be one of: under_review, verified, rejected, suspended',
    'notes.maxLength': 'Review notes cannot exceed 1000 characters',
  }
}
