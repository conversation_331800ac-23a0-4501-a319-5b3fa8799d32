import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class DeliveryProviderApplicationValidator {
  constructor(protected ctx: HttpContextContract) {}

  /*
   * Define schema to validate the "shape", "type", "formatting" and "integrity" of data.
   */
  public schema = schema.create({
    // Delivery capabilities
    vehicleTypes: schema.array([rules.minLength(1)]).members(
      schema.enum(['motorcycle', 'bicycle', 'car', 'van', 'truck'])
    ),
    maxDeliveryDistance: schema.number([
      rules.unsigned(),
      rules.range(1, 100), // Maximum 100km delivery radius
    ]),
    maxConcurrentOrders: schema.number.optional([
      rules.unsigned(),
      rules.range(1, 50), // Maximum 50 concurrent orders
    ]),
    maxOrderWeight: schema.number.optional([
      rules.unsigned(),
      rules.range(0.1, 1000), // 0.1kg to 1000kg
    ]),
    maxOrderVolume: schema.number.optional([
      rules.unsigned(),
      rules.range(0.01, 100), // 0.01 to 100 cubic meters
    ]),

    // Operational settings
    workingHours: schema.object().members({
      monday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
      tuesday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
      wednesday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
      thursday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
      friday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
      saturday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
      sunday: schema.object.optional().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        available: schema.boolean(),
      }),
    }),

    breakTimes: schema.array.optional().members(
      schema.object().members({
        start: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        end: schema.string([rules.regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)]),
        duration_minutes: schema.number([rules.unsigned(), rules.range(5, 480)]), // 5 minutes to 8 hours
      })
    ),

    autoAcceptOrders: schema.boolean.optional(),
    maxOrdersPerDay: schema.number.optional([
      rules.unsigned(),
      rules.range(1, 200), // Maximum 200 orders per day
    ]),
    preparationBufferMinutes: schema.number.optional([
      rules.unsigned(),
      rules.range(0, 120), // 0 to 2 hours buffer
    ]),

    // Required documents
    requiredDocuments: schema.object().members({
      driver_license: schema.object().members({
        number: schema.string([rules.minLength(5), rules.maxLength(50)]),
        expires_at: schema.date({
          format: 'yyyy-MM-dd',
        }),
        status: schema.enum(['pending', 'verified', 'expired']),
      }),
      background_check: schema.object().members({
        certificate_number: schema.string([rules.minLength(5), rules.maxLength(50)]),
        issued_at: schema.date({
          format: 'yyyy-MM-dd',
        }),
        status: schema.enum(['pending', 'verified', 'expired']),
      }),
      safety_training: schema.object().members({
        certificate_number: schema.string([rules.minLength(5), rules.maxLength(50)]),
        completed_at: schema.date({
          format: 'yyyy-MM-dd',
        }),
        expires_at: schema.date({
          format: 'yyyy-MM-dd',
        }),
        status: schema.enum(['pending', 'verified', 'expired']),
      }),
    }),

    // Insurance details
    insuranceDetails: schema.object().members({
      provider: schema.string([rules.minLength(2), rules.maxLength(100)]),
      policy_number: schema.string([rules.minLength(5), rules.maxLength(50)]),
      coverage_amount: schema.number([rules.unsigned(), rules.range(10000, 10000000)]), // $10k to $10M
      expires_at: schema.date({
        format: 'yyyy-MM-dd',
      }),
      status: schema.enum(['pending', 'verified', 'expired']),
    }),

    // Vehicle documentation
    vehicleDocumentation: schema.object().members({
      registration: schema.object().members({
        number: schema.string([rules.minLength(5), rules.maxLength(50)]),
        expires_at: schema.date({
          format: 'yyyy-MM-dd',
        }),
        status: schema.enum(['pending', 'verified', 'expired']),
      }),
      inspection: schema.object().members({
        certificate_number: schema.string([rules.minLength(5), rules.maxLength(50)]),
        expires_at: schema.date({
          format: 'yyyy-MM-dd',
        }),
        status: schema.enum(['pending', 'verified', 'expired']),
      }),
    }),
  })

  /**
   * Custom messages for validation failures.
   */
  public messages: CustomMessages = {
    'vehicleTypes.required': 'At least one vehicle type must be selected',
    'vehicleTypes.minLength': 'At least one vehicle type must be selected',
    'maxDeliveryDistance.required': 'Maximum delivery distance is required',
    'maxDeliveryDistance.range': 'Maximum delivery distance must be between 1 and 100 kilometers',
    'maxConcurrentOrders.range': 'Maximum concurrent orders must be between 1 and 50',
    'maxOrderWeight.range': 'Maximum order weight must be between 0.1kg and 1000kg',
    'maxOrderVolume.range': 'Maximum order volume must be between 0.01 and 100 cubic meters',
    'workingHours.*.start.regex': 'Working hours must be in HH:mm format (e.g., 09:00)',
    'workingHours.*.end.regex': 'Working hours must be in HH:mm format (e.g., 18:00)',
    'breakTimes.*.start.regex': 'Break times must be in HH:mm format (e.g., 12:00)',
    'breakTimes.*.end.regex': 'Break times must be in HH:mm format (e.g., 13:00)',
    'breakTimes.*.duration_minutes.range': 'Break duration must be between 5 minutes and 8 hours',
    'maxOrdersPerDay.range': 'Maximum orders per day must be between 1 and 200',
    'preparationBufferMinutes.range': 'Preparation buffer must be between 0 and 120 minutes',
    'requiredDocuments.driver_license.number.minLength': 'Driver license number must be at least 5 characters',
    'requiredDocuments.driver_license.number.maxLength': 'Driver license number must not exceed 50 characters',
    'requiredDocuments.background_check.certificate_number.minLength': 'Background check certificate number must be at least 5 characters',
    'requiredDocuments.safety_training.certificate_number.minLength': 'Safety training certificate number must be at least 5 characters',
    'insuranceDetails.provider.minLength': 'Insurance provider name must be at least 2 characters',
    'insuranceDetails.provider.maxLength': 'Insurance provider name must not exceed 100 characters',
    'insuranceDetails.policy_number.minLength': 'Insurance policy number must be at least 5 characters',
    'insuranceDetails.coverage_amount.range': 'Insurance coverage amount must be between $10,000 and $10,000,000',
    'vehicleDocumentation.registration.number.minLength': 'Vehicle registration number must be at least 5 characters',
    'vehicleDocumentation.inspection.certificate_number.minLength': 'Vehicle inspection certificate number must be at least 5 characters',
  }
}
