import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StaffRegisterValidator {
  constructor(protected ctx: HttpContextContract) {}

  /*
   * Define schema to validate the "shape", "type", "formatting" and "integrity" of data.
   */
  public schema = schema.create({
    firstName: schema.string({ trim: true }, [
      rules.minLength(2),
      rules.maxLength(50),
    ]),
    lastName: schema.string({ trim: true }, [
      rules.minLength(2),
      rules.maxLength(50),
    ]),
    email: schema.string({ trim: true }, [
      rules.email(),
      rules.unique({ table: 'users', column: 'email' }),
    ]),
    phone: schema.string.optional({ trim: true }, [
      rules.mobile({ strict: false }),
      rules.unique({ table: 'users', column: 'phone' }),
    ]),
    password: schema.string({}, [
      rules.minLength(6),
      rules.maxLength(100),
    ]),
    gender: schema.enum.optional(['Male', 'Female', 'Other'] as const),
    dob: schema.date.optional({
      format: 'yyyy-MM-dd',
    }),
    idpass: schema.string.optional({ trim: true }, [
      rules.maxLength(20),
    ]),
  })

  /**
   * Custom messages for validation failures.
   */
  public messages: CustomMessages = {
    'firstName.required': 'First name is required',
    'firstName.minLength': 'First name must be at least 2 characters long',
    'firstName.maxLength': 'First name cannot exceed 50 characters',
    'lastName.required': 'Last name is required',
    'lastName.minLength': 'Last name must be at least 2 characters long',
    'lastName.maxLength': 'Last name cannot exceed 50 characters',
    'email.required': 'Email address is required',
    'email.email': 'Please provide a valid email address',
    'email.unique': 'This email address is already registered',
    'phone.mobile': 'Please provide a valid phone number',
    'phone.unique': 'This phone number is already registered',
    'password.required': 'Password is required',
    'password.minLength': 'Password must be at least 6 characters long',
    'password.maxLength': 'Password cannot exceed 100 characters',
    'gender.enum': 'Gender must be Male, Female, or Other',
    'dob.date': 'Date of birth must be a valid date in YYYY-MM-DD format',
    'idpass.maxLength': 'ID/Passport number cannot exceed 20 characters',
  }
}
