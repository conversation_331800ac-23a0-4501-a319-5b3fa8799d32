import { schema, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class UpdateMessageActionValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    type_id: schema.number.optional([
      rules.exists({ table: 'action_types', column: 'id' })
    ]),
    config: schema.object.optional().members({}),
    status: schema.string.optional({ trim: true }, [
      rules.regex(/^(pending|active|completed|cancelled)$/)
    ])
  })

  public messages = {
    'type_id.exists': 'Selected action type does not exist',
    'status.regex': 'Status must be one of: pending, active, completed, cancelled'
  }
} 