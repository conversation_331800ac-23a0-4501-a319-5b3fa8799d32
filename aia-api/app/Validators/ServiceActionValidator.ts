import { schema } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class ServiceActionValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    action: schema.enum([
      'Purchase',
      'Booking',
      'Registration',
      'Access',
      'Process',
      'Reserve',
      'Application',
      'Query',
      'Reservation',
      'Prequalification'
    ]),
    config: schema.object.optional().members({}),
    isActive: schema.boolean.optional()
  })

  public messages = {
    'action.required': 'Action is required',
    'action.enum': 'Invalid action type'
  }
} 