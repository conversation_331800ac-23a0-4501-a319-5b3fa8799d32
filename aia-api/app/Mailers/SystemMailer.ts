import { BaseMailer, MessageContract } from '@ioc:Adonis/Addons/Mail'
import User from '../Models/User'

export default class SystemMailer extends BaseMailer {
  constructor(
    private user: User,
    private template: string,
    public data: any
  ) {
    super()
  }

  public prepare(message: MessageContract) {
    message
      .subject('Test email')
      .from('<EMAIL>')
      .to(this.user.email)
      .htmlView(this.template, this.data)
  }
}
