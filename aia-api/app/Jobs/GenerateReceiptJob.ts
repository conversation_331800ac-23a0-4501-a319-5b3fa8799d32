import { JobContract } from '@ioc:Adonis/Addons/Queue'
import Logger from '@ioc:Adonis/Core/Logger'
import ReceiptService from 'App/Services/ReceiptService'

interface GenerateReceiptJobPayload {
  paymentId: string
  sendEmail?: boolean
  retryCount?: number
}

export default class GenerateReceiptJob implements JobContract {
  public key = 'GenerateReceiptJob'
  public retries = 3
  public backoff = 'exponential'

  /**
   * Handle the receipt generation job
   */
  public async handle(payload: GenerateReceiptJobPayload) {
    const { paymentId, sendEmail = true, retryCount = 0 } = payload

    try {
      Logger.info(`Starting receipt generation job for payment ${paymentId}`)

      const receiptService = new ReceiptService()

      // Generate the receipt
      const receipt = await receiptService.generateReceipt(paymentId)

      Logger.info(`Receipt ${receipt.number} generated successfully for payment ${paymentId}`)

      // Send email if requested
      if (sendEmail) {
        try {
          await receiptService.sendReceiptByEmail(receipt.id)
          Logger.info(`Receipt ${receipt.number} sent via email successfully`)
        } catch (emailError) {
          Logger.error(`Failed to send receipt ${receipt.number} via email: ${emailError.message}`)
          // Don't fail the job if email sending fails - receipt is still generated
        }
      }

      Logger.info(`Receipt generation job completed successfully for payment ${paymentId}`)
    } catch (error) {
      Logger.error(`Receipt generation job failed for payment ${paymentId}: ${error.message}`)
      
      // Log retry information
      if (retryCount < this.retries) {
        Logger.info(`Will retry receipt generation for payment ${paymentId} (attempt ${retryCount + 1}/${this.retries})`)
      } else {
        Logger.error(`Receipt generation permanently failed for payment ${paymentId} after ${this.retries} attempts`)
      }
      
      throw error
    }
  }

  /**
   * Handle job failure
   */
  public async failed(payload: GenerateReceiptJobPayload, error: Error) {
    const { paymentId } = payload

    Logger.error(`Receipt generation job permanently failed for payment ${paymentId}`, {
      error: error.message,
      stack: error.stack,
      payload,
    })

    // You could implement additional failure handling here, such as:
    // - Sending notification to admin
    // - Creating a failed receipt record
    // - Adding to a manual review queue
  }
}
