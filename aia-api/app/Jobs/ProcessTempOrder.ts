import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
// import ProcessKaa from 'App/Helpers/ProcessKaa'
// import ProcessKplc from 'App/Helpers/ProcessKplc'
import Lot from 'App/Models/Lot'
// Updated: Use unified Order model instead of TempOrder
import Order from 'App/Models/Order'
import User from 'App/Models/User'
import CustomerNewTempOrder from 'App/Notifications/Customer/CustomerNewTempOrder'
import StaffNewTempOrder from 'App/Notifications/Staff/StaffNewTempOrder'
import Hotelplus from 'App/Services/Hotelplus'

export type ProcessOrderPayload = {
  orderId: string
}

export default class ProcessOrder implements JobHandlerContract {
  constructor(public job: Job) {
    this.job = job
  }

  // Updated: Accept unified Order model instead of TempOrder
  public async notifyCustomer(order: Order) {
    await order.customer.notify(new CustomerNewTempOrder(order))
  }

  // Updated: Accept unified Order model instead of TempOrder
  public async notifyStaff(order: Order) {
    console.log('notify staff')
    const staff = order.lotId
      ? await Lot.findOrFail(order.lotId).then(
          async (lot) => await lot.related('staff').query().orderBy('created_at', 'desc')
        )
      : await User.query().whereHas('employers', (q) => {
          q.where('branch_id', order.branchId)
          q.wherePivot('online', true)
        })

    staff.map((waiter) => waiter.notify(new StaffNewTempOrder(order)))
  }

  /**
   * Base Entry point
   * Updated: Query unified orders table for temp orders (status = 'Pending')
   */
  public async handle(payload: ProcessOrderPayload) {
    console.log('Processing temp order in unified system')
    const { orderId } = payload

    // Updated: Query orders table with status = 'Pending' for temp orders
    const order = await Order.query()
      .where('id', orderId)
      .where('status', 'Pending') // Only process temp orders
      .preload('customer')
      .preload('staff')
      .preload('vendor')
      .preload('branch')
      .firstOrFail()

    switch (order.vendor?.slug) {
      case 'kplc':
        await this.notifyCustomer(order)
        // order.items.forEach((item) => {
        //   ProcessKplc.process(item, order)
        // })
        await this.notifyStaff(order)

        break
      case 'kaa':
        await this.notifyCustomer(order)
        // order.items.forEach(async (item) => {
        //   const apiRes = await ProcessKaa.process(item, order)
        //   if (apiRes) {
        //     await order.merge({ meta: { ...order.meta, ...{ apiRes } } }).save()
        //   }
        // })
        await this.notifyStaff(order)

        break

      default:
        await this.notifyCustomer(order)

        const settings = await order.branch
          ?.related('settings')
          .query()
          .where('name', 'hotelplus')
          .first()

        if (settings) {
          const hotelplus = new Hotelplus(order.branchId, settings.options)

          await hotelplus.postTempOrder(order)
        }

        await this.notifyStaff(order)
    }
  }

  /**
   * This is an optional method that gets called if it exists when the retries has exceeded and is marked failed.
   */
  public async failed() {}
}
