import { JobContract } from '@ioc:Adonis/Addons/Queue'
import Event from '@ioc:Adonis/Core/Event'
import Logger from '@ioc:Adonis/Core/Logger'
import Lot from 'App/Models/Lot'
import QRCodeGenerationService from 'App/Services/QRCodeGenerationService'

interface GenerateTableQRCodePayload {
  lotId: string
  vendorId: string
  options?: {
    width?: number
    errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
    color?: {
      dark?: string
      light?: string
    }
  }
  isAutoGenerated?: boolean
}

export default class GenerateTableQRCode implements JobContract {
  public key = 'GenerateTableQRCode'
  public retries = 3
  public backoff = 'exponential'

  /**
   * Handle the job
   */
  public async handle(payload: GenerateTableQRCodePayload) {
    const { lotId, vendorId, options = {}, isAutoGenerated = false } = payload

    try {
      Logger.info(`Starting QR code generation for lot ${lotId}`)

      // Load lot with all necessary relationships
      const lot = await Lot.query()
        .where('id', lotId)
        .preload('section', (sectionQuery) => {
          sectionQuery.preload('branch')
        })
        .firstOrFail()

      // Prepare table data for QR code generation
      const tableData = {
        vendorId: lot.section.branch.vendorId,
        branchId: lot.section.branchId,
        sectionId: lot.sectionId,
        lotId: lot.id,
        tableNumber: lot.name
      }

      // Generate QR code using the service
      const qrService = new QRCodeGenerationService()
      
      // Add isAutoGenerated flag to options for duplicate handling
      const enhancedOptions = {
        ...options,
        isAutoGenerated
      }

      const qrCode = await qrService.generateTableQRCode(tableData, enhancedOptions)

      Logger.info(`Successfully generated QR code ${qrCode.id} for lot ${lotId} (table: ${lot.name})`)

      // Emit success event for notifications
      Event.emit('qr:generated', {
        lotId,
        qrCodeId: qrCode.id,
        tableNumber: lot.name,
        vendorId,
        success: true,
        isAutoGenerated
      })

    } catch (error) {
      Logger.error(`Failed to generate QR code for lot ${lotId}: ${error.message}`, {
        lotId,
        vendorId,
        error: error.message,
        stack: error.stack,
        isAutoGenerated
      })

      // Emit failure event for notifications
      Event.emit('qr:generated', {
        lotId,
        vendorId,
        success: false,
        error: error.message,
        isAutoGenerated
      })

      // Re-throw error to trigger retry mechanism
      throw error
    }
  }

  /**
   * Handle job failure after all retries
   */
  public async failed(payload: GenerateTableQRCodePayload, error: Error) {
    const { lotId, vendorId, isAutoGenerated } = payload

    Logger.error(`QR code generation job failed permanently for lot ${lotId}`, {
      lotId,
      vendorId,
      error: error.message,
      isAutoGenerated,
      retries: this.retries
    })

    // Emit final failure event
    Event.emit('qr:generation:failed', {
      lotId,
      vendorId,
      error: error.message,
      isAutoGenerated,
      finalFailure: true
    })
  }
}
