import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
import ProcessKaa from 'App/Helpers/ProcessKaa'
import ProcessKplc from 'App/Helpers/ProcessKplc'
import Lot from 'App/Models/Lot'
import Order from 'App/Models/Order'
import User from 'App/Models/User'
import CustomerNewOrder from 'App/Notifications/Customer/CustomerNewOrder'
import StaffNewOrder from 'App/Notifications/Staff/StaffNewOrder'
import Hotelplus from 'App/Services/Hotelplus'

export type ProcessOrderPayload = {
  orderId: string
}

export default class ProcessOrder implements JobHandlerContract {
  constructor(public job: Job) {
    this.job = job
  }

  public async notifyCustomer(order: Order) {
    await order.customer.notify(new CustomerNewOrder(order))
  }

  public async notifyStaff(order: Order) {
    const staff = order.lotId
      ? await Lot.findOrFail(order.lotId).then(
          async (lot) => await lot.related('staff').query().orderBy('created_at', 'desc')
        )
      : await User.query().whereHas('employers', (q) => {
          q.where('branch_id', order.branchId)
          q.wherePivot('online', true)
        })

    staff.map((x) => x.notify(new StaffNewOrder(order)))
  }

  /**
   * Base Entry point
   */
  public async handle(payload: ProcessOrderPayload) {
    const { orderId } = payload
    const order = await Order.query()
      .where('id', orderId)
      .preload('customer')
      .preload('vendor')
      .preload('branch')
      .preload('items', (itemsQuery) => {
        itemsQuery.preload('product')
      })
      .firstOrFail()

    switch (order.vendor?.slug) {
      case 'kplc':
        await this.notifyCustomer(order)
        order.items.forEach((item) => {
          if (item.product) {
            ProcessKplc.process(item.product, order)
          }
        })

        await this.notifyStaff(order)

        break
      case 'kaa':
        await this.notifyCustomer(order)
        order.items.forEach(async (item) => {
          if (item.product) {
            const apiRes = await ProcessKaa.process(item.product, order)

            if (apiRes) {
              await order.merge({ meta: { ...order.meta, ...{ apiRes } } }).save()
            }
          }
        })

        await this.notifyStaff(order)

        break

      default:
        await this.notifyCustomer(order)

        const settings = await order.branch
          ?.related('settings')
          .query()
          .where('name', 'hotelplus')
          .first()

        if (settings) {
          const hotelplus = new Hotelplus(order.branchId, settings.options)

          await hotelplus.postOrder(order)

          console.log('Syncing Orders', order.id, order.branchId, settings.options)
        }

        await this.notifyStaff(order)
    }
  }

  /**
   * This is an optional method that gets called if it exists when the retries has exceeded and is marked failed.
   */
  public async failed() {}
}
