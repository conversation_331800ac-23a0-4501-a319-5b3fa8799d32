import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
import Booking, { BookingStatus } from 'App/Models/Booking'
import BookingReminderNotification from 'App/Notifications/Booking/BookingReminderNotification'
import { DateTime } from 'luxon'

export type SendBookingReminderPayload = {
  bookingId: string
  reminderType: '24_hour' | '2_hour' | '30_minute'
}

export default class SendBookingReminder implements JobHandlerContract {
  constructor(public job: Job) {
    this.job = job
  }

  /**
   * Base Entry point
   */
  public async handle(payload: SendBookingReminderPayload) {
    const { bookingId, reminderType } = payload

    try {
      console.log(`📅 Processing ${reminderType} reminder for booking ${bookingId}`)

      // Load booking with relationships
      const booking = await Booking.query()
        .where('id', bookingId)
        .preload('customer')
        .preload('product')
        .preload('vendor')
        .preload('branch')
        .first()

      if (!booking) {
        console.warn(`⚠️ Booking ${bookingId} not found for reminder`)
        return
      }

      // Check if booking is still valid for reminders
      if (!this.shouldSendReminder(booking, reminderType)) {
        console.log(`⏭️ Skipping ${reminderType} reminder for booking ${bookingId} - not eligible`)
        return
      }

      // Send reminder notification to customer
      if (booking.customer) {
        await booking.customer.notify(new BookingReminderNotification(booking, reminderType))
        
        // Update booking to track that reminder was sent
        await this.updateReminderStatus(booking, reminderType)
        
        console.log(`✅ ${reminderType} reminder sent for booking ${bookingId}`)
      } else {
        console.warn(`⚠️ No customer found for booking ${bookingId}`)
      }

    } catch (error) {
      console.error(`❌ Failed to send ${reminderType} reminder for booking ${bookingId}:`, error.message)
      throw error // Re-throw to trigger job retry
    }
  }

  /**
   * Check if reminder should be sent
   */
  private shouldSendReminder(booking: Booking, reminderType: string): boolean {
    // Don't send reminders for cancelled, completed, or no-show bookings
    if ([BookingStatus.CANCELLED, BookingStatus.COMPLETED, BookingStatus.NO_SHOW].includes(booking.status)) {
      return false
    }

    // Only send reminders for confirmed bookings
    if (booking.status !== BookingStatus.CONFIRMED) {
      return false
    }

    // Check if appointment is in the future
    const now = DateTime.now()
    if (booking.appointmentStart <= now) {
      return false
    }

    // Check if reminder hasn't been sent already
    const remindersSent = booking.meta?.reminders_sent || []
    if (remindersSent.includes(reminderType)) {
      return false
    }

    // Check timing is appropriate for the reminder type
    const timeUntilAppointment = booking.appointmentStart.diff(now, 'minutes').minutes

    switch (reminderType) {
      case '24_hour':
        // Send if between 23-25 hours before appointment
        return timeUntilAppointment >= 23 * 60 && timeUntilAppointment <= 25 * 60
      
      case '2_hour':
        // Send if between 1.5-2.5 hours before appointment
        return timeUntilAppointment >= 90 && timeUntilAppointment <= 150
      
      case '30_minute':
        // Send if between 25-35 minutes before appointment
        return timeUntilAppointment >= 25 && timeUntilAppointment <= 35
      
      default:
        return false
    }
  }

  /**
   * Update booking to track that reminder was sent
   */
  private async updateReminderStatus(booking: Booking, reminderType: string): Promise<void> {
    try {
      const currentMeta = booking.meta || {}
      const remindersSent = currentMeta.reminders_sent || []
      
      // Add this reminder type to the sent list
      if (!remindersSent.includes(reminderType)) {
        remindersSent.push(reminderType)
      }

      // Update the meta field
      booking.meta = {
        ...currentMeta,
        reminders_sent: remindersSent,
        [`${reminderType}_sent_at`]: DateTime.now().toISO()
      }

      await booking.save()

    } catch (error) {
      console.error(`❌ Failed to update reminder status for booking ${booking.id}:`, error.message)
      // Don't throw here as the reminder was sent successfully
    }
  }

  /**
   * This method is called when the job fails
   */
  public async failed(payload: SendBookingReminderPayload, error: Error) {
    const { bookingId, reminderType } = payload
    
    console.error(`❌ Booking reminder job failed for booking ${bookingId} (${reminderType}):`, error.message)

    try {
      // Load booking to log failure
      const booking = await Booking.find(bookingId)
      if (booking) {
        const currentMeta = booking.meta || {}
        const failedReminders = currentMeta.failed_reminders || []
        
        failedReminders.push({
          type: reminderType,
          failed_at: DateTime.now().toISO(),
          error: error.message
        })

        booking.meta = {
          ...currentMeta,
          failed_reminders: failedReminders
        }

        await booking.save()
      }

      // TODO: Send alert to admin about failed reminder
      console.error(`📧 Admin alert needed: Reminder failed for booking ${bookingId}`)

    } catch (metaError) {
      console.error(`❌ Failed to log reminder failure for booking ${bookingId}:`, metaError.message)
    }
  }

  /**
   * This method is called when the job is retried
   */
  public async retrying(payload: SendBookingReminderPayload, error: Error) {
    const { bookingId, reminderType } = payload
    console.log(`🔄 Retrying ${reminderType} reminder for booking ${bookingId}. Error: ${error.message}`)
  }
}
