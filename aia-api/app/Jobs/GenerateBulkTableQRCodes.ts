import { JobContract } from '@ioc:Adonis/Addons/Queue'
import Queue from '@ioc:Adonis/Addons/Queue'
import Event from '@ioc:Adonis/Core/Event'
import Logger from '@ioc:Adonis/Core/Logger'
import Lot from 'App/Models/Lot'
import Vendor from 'App/Models/Vendor'

interface GenerateBulkTableQRCodesPayload {
  vendorId: string
  sectionIds?: string[]
  branchIds?: string[]
  lotIds?: string[]
  options?: {
    width?: number
    errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
    color?: {
      dark?: string
      light?: string
    }
  }
}

export default class GenerateBulkTableQRCodes implements JobContract {
  public key = 'GenerateBulkTableQRCodes'
  public retries = 2
  public backoff = 'exponential'

  /**
   * Handle the bulk QR code generation job
   */
  public async handle(payload: GenerateBulkTableQRCodesPayload) {
    const { vendorId, sectionIds, branchIds, lotIds, options } = payload

    try {
      Logger.info(`Starting bulk QR code generation for vendor ${vendorId}`)

      // Get vendor QR preferences
      const vendor = await Vendor.findOrFail(vendorId)
      const qrOptions = options || vendor.qrCodePreferences?.defaultOptions || this.getDefaultQROptions()

      // Build query to get lots based on provided filters
      let lotsQuery = Lot.query()
        .preload('section', (sectionQuery) => {
          sectionQuery.preload('branch')
        })

      // Apply filters
      if (lotIds && lotIds.length > 0) {
        lotsQuery = lotsQuery.whereIn('id', lotIds)
      } else {
        // Filter by sections or branches
        if (sectionIds && sectionIds.length > 0) {
          lotsQuery = lotsQuery.whereIn('sectionId', sectionIds)
        } else if (branchIds && branchIds.length > 0) {
          lotsQuery = lotsQuery.whereHas('section', (sectionQuery) => {
            sectionQuery.whereIn('branchId', branchIds)
          })
        } else {
          // No specific filters, get all lots for vendor
          lotsQuery = lotsQuery.whereHas('section', (sectionQuery) => {
            sectionQuery.whereHas('branch', (branchQuery) => {
              branchQuery.where('vendorId', vendorId)
            })
          })
        }
      }

      const lots = await lotsQuery

      Logger.info(`Found ${lots.length} lots for bulk QR code generation`)

      if (lots.length === 0) {
        Logger.warn(`No lots found for bulk QR generation with provided filters`)
        return
      }

      // Process lots in batches to prevent overwhelming the queue
      const batchSize = 10
      let processedCount = 0
      let successCount = 0
      let errorCount = 0

      for (let i = 0; i < lots.length; i += batchSize) {
        const batch = lots.slice(i, i + batchSize)
        
        // Process batch concurrently
        const batchPromises = batch.map(async (lot) => {
          try {
            await Queue.dispatch('App/Jobs/GenerateTableQRCode', {
              lotId: lot.id,
              vendorId,
              options: qrOptions,
              isAutoGenerated: false // Bulk generation is manual
            })
            successCount++
            return { success: true, lotId: lot.id }
          } catch (error) {
            errorCount++
            Logger.error(`Failed to queue QR generation for lot ${lot.id}: ${error.message}`)
            return { success: false, lotId: lot.id, error: error.message }
          }
        })

        await Promise.all(batchPromises)
        processedCount += batch.length

        Logger.info(`Processed batch ${Math.ceil((i + batchSize) / batchSize)} of ${Math.ceil(lots.length / batchSize)} (${processedCount}/${lots.length} lots)`)

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < lots.length) {
          await this.delay(100)
        }
      }

      Logger.info(`Bulk QR code generation completed for vendor ${vendorId}`, {
        totalLots: lots.length,
        successCount,
        errorCount,
        processedCount
      })

      // Emit completion event
      Event.emit('qr:bulk:completed', {
        vendorId,
        totalLots: lots.length,
        successCount,
        errorCount,
        sectionIds,
        branchIds,
        lotIds
      })

    } catch (error) {
      Logger.error(`Bulk QR code generation failed for vendor ${vendorId}: ${error.message}`, {
        vendorId,
        error: error.message,
        stack: error.stack,
        sectionIds,
        branchIds,
        lotIds
      })

      // Emit failure event
      Event.emit('qr:bulk:failed', {
        vendorId,
        error: error.message,
        sectionIds,
        branchIds,
        lotIds
      })

      throw error
    }
  }

  /**
   * Handle job failure after all retries
   */
  public async failed(payload: GenerateBulkTableQRCodesPayload, error: Error) {
    const { vendorId } = payload

    Logger.error(`Bulk QR code generation job failed permanently for vendor ${vendorId}`, {
      vendorId,
      error: error.message,
      retries: this.retries
    })

    Event.emit('qr:bulk:failed', {
      vendorId,
      error: error.message,
      finalFailure: true
    })
  }

  /**
   * Get default QR code options
   */
  private getDefaultQROptions() {
    return {
      width: 300,
      errorCorrectionLevel: 'M' as const,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    }
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
