import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
import Drive from '@ioc:Adonis/Core/Drive'
import csv from 'csv-parser'
import Product from 'App/Models/Product'
import ProductCategory from 'App/Models/ProductCategory'
import User from 'App/Models/User'
// Import notification classes if you intend to use them
// import Notification from '@ioc:Verful/Notification';
// import VendorProductUploadComplete from 'App/Notifications/VendorProductUploadComplete'
// import VendorProductUploadFailed from 'App/Notifications/VendorProductUploadFailed'
// import VendorProductUploadFailedPermanently from 'App/Notifications/VendorProductUploadFailedPermanently';
import { string } from '@ioc:Adonis/Core/Helpers' // Keep for header normalization
import { schema, rules, validator } from '@ioc:Adonis/Core/Validator'


// Define the expected payload structure for the job
export type ProcessProductCsvUploadPayload = {
  s3ObjectKey: string; // Path/Key of the object in the S3 bucket
  diskName: 's3';    // Explicitly type the disk name for clarity
  vendorId: string;
  branchId?: string; // Optional, depends on your business logic
  userId: string;    // ID of the user who initiated the upload
}

// Define expected CSV Headers (using category slug now)
const EXPECTED_HEADERS = ['name', 'product_category_slug', 'price', 'details', 'stock', 'sku_ref']

// Interface for the combined category and service info stored in the map
interface CategoryServiceInfo {
    categoryId: string;
    serviceId: string | null; // Service ID can be null if relationship is optional/broken
}

export default class ProcessProductCsvUpload implements JobHandlerContract {
  // Using standard Job type now
  constructor(public job: Job) {}

  /**
   * Base Entry point for the job. Handles the CSV processing using category slugs.
   */
  public async handle(payload: ProcessProductCsvUploadPayload): Promise<any> { // Returns report object
    const { s3ObjectKey, diskName, vendorId, branchId, userId } = payload;
    console.log(`[Job ${this.job.id}] Starting processing for S3 key: ${s3ObjectKey}, Disk: ${diskName}, Vendor: ${vendorId}`);

    const results: any[] = []; // Holds raw data from CSV rows
    const errors: { row: number; errors: any; data: any }[] = []; // Holds processing errors per row
    let processedRowCount = 0;
    let successCount = 0;
    let errorCount = 0;
    let headersVerified = false;
    let fileStream: NodeJS.ReadableStream;

    // 1. Get the file stream from S3
    try {
      fileStream = await Drive.use(diskName).getStream(s3ObjectKey);
      console.log(`[Job ${this.job.id}] Successfully obtained stream for S3 object ${s3ObjectKey}.`);
    } catch (driveError) {
      console.error(`[Job ${this.job.id}] FATAL: Failed to get stream for S3 object ${s3ObjectKey}:`, driveError);
      // Notify about the specific S3 error
      await this.cleanupAndNotify(userId, diskName, s3ObjectKey, 0, 0, [{row: 0, errors: { file: ['S3 object not found or inaccessible.', driveError.message]}, data:{}}]);
      // Remove the problematic moveToFailed call
      // await this.job.moveToFailed(new Error(`S3 object stream error: ${driveError.message}`), true);
      // Re-throw the error to signal failure to BullMQ
      throw new Error(`Failed to process job due to S3 stream error: ${driveError.message}`);
      // return; // Unreachable after throw
    }

    // Flag to prevent end handler running after rejection
    let promiseRejected = false;

    // 2. Process the stream using a Promise
    return new Promise(async (resolve, reject) => {
        const rejectAndFlag = (err) => { // Helper to set flag and reject
            if (promiseRejected) return; // Avoid double rejection
            promiseRejected = true;
            reject(err);
        };

        fileStream
            .pipe(csv({
                mapHeaders: ({ header }) => string.snakeCase(header.trim()) // Normalize headers
            }))
            .on('headers', (headers: string[]) => {
                console.log(`[Job ${this.job.id}] CSV Headers Found:`, headers);
                const lowerCaseHeaders = headers.map(h => h.toLowerCase());
                // Check if all *required* headers are present
                const missingHeaders = EXPECTED_HEADERS.filter(
                     (h) => !lowerCaseHeaders.includes(h) && !['sku_ref', 'details', 'stock'].includes(h) // Optional headers excluded
                );
                if (missingHeaders.length > 0) {
                    const errorMsg = `Missing required CSV headers: ${missingHeaders.join(', ')}`;
                    console.error(`[Job ${this.job.id}] ${errorMsg}`);
                    rejectAndFlag(new Error(errorMsg)); // Use helper
                    return; // Keep return to exit the handler early
                }
                headersVerified = true;
                console.log(`[Job ${this.job.id}] CSV headers verified successfully.`);
            })
            .on('data', (data) => results.push(data)) // Collect row data
            .on('end', async () => {
                 if (promiseRejected) { // Check the flag first
                     console.log(`[Job ${this.job.id}] CSV processing aborted due to earlier error. Skipping 'end' processing.`);
                     // Do not resolve or call cleanup - the rejection handles the failure.
                     return;
                 }

                 console.log(`[Job ${this.job.id}] CSV file reading finished. Found ${results.length} data rows.`);

                  // Check if headers failed verification (e.g., empty file after headers)
                  if (!headersVerified) {
                    const headerErrorMsg = 'CSV file is empty or header validation failed without specific error.'; // Adjusted message
                    console.error(`[Job ${this.job.id}] ${headerErrorMsg}`);
                    // Report 0 success/0 failed rows, but with a file-level error
                    const report = await this.cleanupAndNotify(userId, diskName, s3ObjectKey, 0, 0, [{row: 0, errors: { file: [headerErrorMsg]}, data: {}}]);
                    resolve(report); // Resolve with this specific non-row-processing failure report
                    return;
                  }

                  processedRowCount = 0;

                  // --- Category Preloading using SLUG ---
                  // Extract unique, non-empty, trimmed slugs from the CSV
                  const categorySlugsFromCsv = [...new Set(results.map(row => row.product_category_slug?.trim()).filter(Boolean))];
                  // Map stores <slug, {categoryId, serviceId}>
                  const categoriesInfoMap = new Map<string, CategoryServiceInfo>();

                  if (categorySlugsFromCsv.length > 0) {
                      try {
                          console.log(`[Job ${this.job.id}] Preloading categories and services for slugs:`, categorySlugsFromCsv);

                          // Fetch Categories by SLUG with their ProductType and Service
                          const categoriesWithService = await ProductCategory.query()
                              .whereIn('slug', categorySlugsFromCsv) // <<< QUERY BY SLUG
                              // Add scoping WHERE clauses here if slugs are not globally unique (unlikely but possible)
                              // Example: .where('vendor_id', vendorId)
                              .preload('productType', (typeQuery) => {
                                  typeQuery.select(['id', 'serviceId'])
                                           .preload('service', (serviceQuery) => { serviceQuery.select(['id']); });
                              })
                              .debug(false); // Turn off debug logging for this query unless needed

                          console.log(`[Job ${this.job.id}] Fetched ${categoriesWithService.length} category records using SLUG IN query.`);

                          // Build the map using slug as the key
                          categoriesWithService.forEach(cat => {
                              const slug = cat.slug; // Slug should be reliable key
                              const serviceId = cat.productType?.service?.id || null;
                              if (!categoriesInfoMap.has(slug)) {
                                  categoriesInfoMap.set(slug, { categoryId: cat.id, serviceId: serviceId });
                              } else {
                                  // Should not happen if slug has a unique constraint
                                  console.warn(`[Job ${this.job.id}] Warning: Duplicate category slug '${slug}' encountered during map build.`);
                              }
                          });
                          console.log(`[Job ${this.job.id}] Built map with ${categoriesInfoMap.size} category infos using slug keys.`);

                          // Verify if all requested slugs were found
                           let foundCount = 0;
                           categorySlugsFromCsv.forEach(slug => { if (categoriesInfoMap.has(slug)) foundCount++; });
                           if(foundCount !== categorySlugsFromCsv.length){
                                const missingSlugs = categorySlugsFromCsv.filter(slug => !categoriesInfoMap.has(slug));
                                console.warn(`[Job ${this.job.id}] Mismatch after map build: Requested ${categorySlugsFromCsv.length} slugs, mapped ${foundCount}. Missing map entries for slugs:`, missingSlugs);
                           }

                      } catch(dbError){
                           console.error(`[Job ${this.job.id}] Error during category lookup by slug:`, dbError);
                      }
                  }
                  // --- End Category Preloading ---


                  // --- Process Rows ---
                  for (const rowData of results) {
                      processedRowCount++; const currentRowNum = processedRowCount;
                      try {
                          // --- Validation Schema (using slug) ---
                          const productSchema = schema.create({
                              name: schema.string({ trim: true }),
                              // Validate slug presence and check existence in DB
                              product_category_slug: schema.string({ trim: true }, [
                                  rules.exists({ table: 'product_categories', column: 'slug'})
                                  // You could add more specific slug format rules if desired
                              ]),
                              price: schema.number([rules.unsigned()]),
                              details: schema.string.optional({ trim: true }),
                              stock: schema.number.optional([rules.unsigned()]),
                              sku_ref: schema.string.optional({ trim: true }),
                          });
                          // --- ---

                          // Validate the row
                          const validatedData = await validator.validate({
                              schema: productSchema,
                              data: rowData,
                              messages: {
                                  'product_category_slug.required': 'Product category slug is required.',
                                  'product_category_slug.exists': 'Product category slug not found in database.', // Added specific message
                                  'price.required': 'Price is required.',
                                  'price.number': 'Price must be a valid number.',
                                  'price.unsigned': 'Price cannot be negative.',
                                  'stock.number': 'Stock must be a valid number if provided.',
                                  'stock.unsigned': 'Stock cannot be negative.',
                              }
                           });

                          // --- Lookup Category & Service Info using SLUG ---
                          const requestedSlug = validatedData.product_category_slug; // Already validated to exist
                          console.log(`[Job ${this.job.id}] Row ${currentRowNum}: Looking up category slug: '${requestedSlug}'`);

                          const categoryInfo = categoriesInfoMap.get(requestedSlug); // Lookup in map

                          // This check should ideally not fail if the exists rule passed, but acts as a safeguard
                          if (!categoryInfo) {
                              console.error(`[Job ${this.job.id}] Row ${currentRowNum}: FAILED lookup for validated slug '${requestedSlug}'. Map keys:`, Array.from(categoriesInfoMap.keys()));
                              throw new Error(`Category slug '${requestedSlug}' was validated but not found in preloaded map. Potential data inconsistency.`);
                          } else {
                               console.log(`[Job ${this.job.id}] Row ${currentRowNum}: Found category info: ID='${categoryInfo.categoryId}', ServiceID='${categoryInfo.serviceId}'`);
                          }
                          // Optional: Check if serviceId is mandatory
                          if (!categoryInfo.serviceId) {
                              console.warn(`[Job ${this.job.id}] Row ${currentRowNum}: Category slug '${requestedSlug}' has no service associated.`);
                              // Uncomment below if serviceId is required on Product model
                              // throw new Error(`Category slug '${requestedSlug}' is not associated with a service.`);
                          }
                          // --- End Lookup ---

                          // Handle stock default value (-1 for unlimited)
                          const stockValue = (validatedData.stock === undefined || validatedData.stock === null) ? -1 : validatedData.stock;

                          // Prepare product data for DB operation
                          const productData = {
                              name: validatedData.name,
                              details: validatedData.details || undefined,
                              price: validatedData.price,
                              stock: stockValue,
                              productCategoryId: categoryInfo.categoryId, // Use ID from map
                              serviceId: categoryInfo.serviceId ?? undefined, // Convert null to undefined
                              vendorId: vendorId,
                              ...(branchId && { branchId: branchId }), // Conditionally add branch ID
                              userId: userId, // Assign the user who uploaded
                              status: 'Published' as const, // Default status
                              active: true, // Default active state
                              ref: validatedData.sku_ref || undefined // Use sku_ref for ref column
                          };

                          // Create or Update Product logic
                          if (validatedData.sku_ref) {
                              await Product.updateOrCreate(
                                  // Find criteria: ref + vendor + branch (if provided)
                                  { ref: validatedData.sku_ref, vendorId: vendorId, ...(branchId && { branchId: branchId }) },
                                  // Data to update/create with
                                  productData // serviceId is now string | undefined
                              );
                          } else {
                              // Create new product. `ref` is already correctly `string | undefined` in productData.
                              await Product.create(productData); // Pass productData directly
                          }
                          successCount++;

                      } catch (error) {
                          errorCount++;
                          const errorMessages = error.messages || { general: error.message };
                          console.error(`[Job ${this.job.id}] Error processing row ${currentRowNum}:`, JSON.stringify(errorMessages), 'Data:', rowData);
                          errors.push({ row: currentRowNum, errors: errorMessages, data: rowData });
                      }
                      // Update job progress
                      await this.job.updateProgress(Math.round((processedRowCount / results.length) * 100));
                  } // End for loop

                  console.log(`[Job ${this.job.id}] Finished processing all rows. Success: ${successCount}, Failed: ${errorCount}`);
                  // Cleanup and resolve the promise with the final report
                  const report = await this.cleanupAndNotify(userId, diskName, s3ObjectKey, successCount, errorCount, errors);
                  resolve(report);

            }) // End 'on.end'
            .on('error', async (error) => {
                // Catches critical stream errors (parsing errors, etc.)
                console.error(`[Job ${this.job.id}] Critical CSV Stream Error:`, error);
                rejectAndFlag(error); // Reject the promise via helper, BullMQ moves job to failed
            });
    }); // End Promise wrapper
  } // End handle method

  /**
   * Helper function to generate report, notify user, and clean up the S3 object.
   */
  private async cleanupAndNotify(userId: string, diskName: 's3', s3ObjectKey: string, successCount: number, errorCount: number, errors: any[]) {
    const report = {
        totalRows: successCount + errorCount,
        successfulRows: successCount,
        failedRows: errorCount,
        errors: errors,
        timestamp: new Date().toISOString(),
    };

    // --- Notify User ---
    const user = await User.find(userId);
    if (user) {
       try {
           // Replace with your actual Notification logic
           console.log(`[Job ${this.job.id}] Simulated notification sent to user ${userId}. Success: ${successCount}, Failed: ${errorCount}`);
       } catch (notifyError) {
            console.error(`[Job ${this.job.id}] Failed to send notification to user ${userId}:`, notifyError);
       }
    } else {
        console.warn(`[Job ${this.job.id}] User ${userId} not found for notification.`);
    }
    // --- ---

    // --- Clean up S3 object ---
    try {
        console.log(`[Job ${this.job.id}] Attempting to delete S3 object: ${s3ObjectKey} from disk ${diskName}`);
        const exists = await Drive.use(diskName).exists(s3ObjectKey);
        if (exists) {
            await Drive.use(diskName).delete(s3ObjectKey);
            console.log(`[Job ${this.job.id}] S3 object ${s3ObjectKey} deleted successfully.`);
        } else {
             console.warn(`[Job ${this.job.id}] S3 object ${s3ObjectKey} not found for deletion (already deleted or never existed?).`);
        }
    } catch (deleteError) {
        console.error(`[Job ${this.job.id}] Failed to delete S3 object ${s3ObjectKey}:`, deleteError);
        if (deleteError.code !== 'NoSuchKey') {
             console.error(`[Job ${this.job.id}] Critical error during S3 delete (not NoSuchKey):`, deleteError.code);
        }
    }
    // --- ---

    return report; // Return report for job.returnvalue
  }

  /**
   * Called when the job fails after all retry attempts.
   */
  public async failed() {
    const payload = this.job.data as ProcessProductCsvUploadPayload | undefined;

    // Defensive check for payload integrity
    if (!payload || typeof payload.s3ObjectKey !== 'string' || typeof payload.diskName !== 'string') {
        console.error(`[Job ${this.job.id}] ProcessProductCsvUpload FAILED permanently, but payload or required keys are missing/invalid.`);
        console.error(`[Job ${this.job.id}] Failure Reason:`, this.job.failedReason);
        return;
    }

    console.error(`[Job ${this.job.id}] ProcessProductCsvUpload FAILED permanently for vendor ${payload.vendorId}, S3 key: ${payload.s3ObjectKey}`);
    console.error(`[Job ${this.job.id}] Failure Reason:`, this.job.failedReason);

    // --- Notify User of permanent failure ---
    const user = await User.find(payload.userId);
    if (user) {
        try {
            // Replace with your actual "permanent failure" notification logic
            console.log(`[Job ${this.job.id}] Simulated PERMANENT FAILURE notification sent to user ${payload.userId}.`);
        } catch (notifyError) {
             console.error(`[Job ${this.job.id}] Failed to send permanent failure notification to user ${payload.userId}:`, notifyError);
        }
    } else {
        console.warn(`[Job ${this.job.id}] User ${payload.userId} not found for permanent failure notification.`);
    }
    // --- ---

    // --- Attempt S3 cleanup on permanent failure ---
    try {
       console.log(`[Job ${this.job.id}] Attempting cleanup of S3 object ${payload.s3ObjectKey} after permanent failure.`);
        const exists = await Drive.use(payload.diskName).exists(payload.s3ObjectKey);
        if (exists) {
            await Drive.use(payload.diskName).delete(payload.s3ObjectKey);
            console.log(`[Job ${this.job.id}] S3 object ${payload.s3ObjectKey} deleted after permanent job failure.`);
        } else {
             console.warn(`[Job ${this.job.id}] S3 object ${payload.s3ObjectKey} not found for deletion during permanent failure cleanup.`);
        }
    } catch (deleteError) {
        console.error(`[Job ${this.job.id}] Failed to delete S3 object ${payload.s3ObjectKey} after permanent job failure:`, deleteError);
         if (deleteError.code !== 'NoSuchKey') { /* Log critical */ }
    }
    // --- ---
  }
}