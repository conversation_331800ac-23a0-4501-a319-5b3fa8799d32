import Env from '@ioc:Adonis/Core/Env'
import axios from 'axios'
import { NotifiableModel } from '@ioc:Verful/Notification'
import { NotificationChannelContract } from '@ioc:Verful/Notification'
import NotificationTracking from 'App/Services/NotificationTracking'
import User from 'App/Models/User'

interface SmsMessageContract {
  text: string
  phone: string
}

export default class SmsChannel implements NotificationChannelContract {
  private http = axios.create({
    baseURL: 'https://quicksms.advantasms.com/api/',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  })

  public async send(notification: SmsMessageContract, notifiable: NotifiableModel) {
    try {
      // Track notification attempt
      await NotificationTracking.track(
        notification as any,
        notifiable as User,
        'sent',
        'sms',
        { phone: notification.phone }
      )

      const payload = {
        mobile: notification.phone.replace(/^\+/, ''),
        message: notification.text,
        apikey: Env.get('SMS_API_KEY'),
        partnerID: Env.get('SMS_PARTNER_ID'),
        shortcode: Env.get('SMS_SHORT_CODE'),
      }

      const { data } = await this.http.post('services/sendotp', payload)

      // Track successful delivery
      await NotificationTracking.track(
        notification as any,
        notifiable as User,
        'delivered',
        'sms',
        { 
          phone: notification.phone,
          response: data
        }
      )

      return data
    } catch (error) {
      console.error(error?.response?.data)
      
      // Track failed delivery
      await NotificationTracking.track(
        notification as any,
        notifiable as User,
        'failed',
        'sms',
        { 
          phone: notification.phone,
          error: error?.response?.data || error.message
        }
      )

      return 'Unable to send message'
    }
  }
}
