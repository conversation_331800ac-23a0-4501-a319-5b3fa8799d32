import { initializeApp, ServiceAccount, getApps } from 'firebase-admin/app'
import { Message, getMessaging } from 'firebase-admin/messaging'
import { credential } from 'firebase-admin'
import User from 'App/Models/User'
import { NotificationChannelContract } from '@ioc:Verful/Notification'
import { ChainableContract } from '@ioc:Adonis/Lucid/Database'
import { NotificationMessagePayload } from 'App/Interfaces/NotificationMessagePayload'
import NotificationTracking from 'App/Services/NotificationTracking'

// Try to import service credentials, but make it optional
let serviceCredential: ServiceAccount | null = null
try {
  serviceCredential = require('../../google-services-user.json')
} catch (error) {
  console.warn('Firebase service credentials not found. FCM notifications will be disabled.')
}

export default class FcChannel implements NotificationChannelContract {
  public async send(notification: NotificationMessagePayload, notifiable: User) {
    try {
      // Check if Firebase credentials are available
      if (!serviceCredential) {
        console.warn('FCM notification skipped: Firebase credentials not configured')
        return
      }

      const app = getApps().length
        ? getApps()[0]
        : initializeApp({
            credential: credential.cert(serviceCredential),
          })

      notification.body = notification.body?.replace('{firstName}', notifiable.firstName)
      notification.body = notification.body?.replace('{lastName}', notifiable.lastName)

      await notifiable.load('devices', (dq: ChainableContract) => {
        dq.whereNotNull('token')
      })

      const defaultImageUrl =
        'https://aia-website.vercel.app/_next/image?url=/_next/static/media/logo.9d956f51.png&w=96&q=75'

      // Track notification attempt
      await NotificationTracking.track(notification as any, notifiable, 'sent', 'fcm', {
        deviceCount: notifiable.devices.length,
      })

      const results = await Promise.allSettled(
        notifiable.devices.map(async ({ token }) => {
          const payload: Message = {
            notification: {
              title: notification.title || 'Notification',
              body: notification.body || 'Notification',
            },
            token,
            android: {
              priority: 'high',
              notification: {
                sound: 'default',
                imageUrl: notification.imageUrl || defaultImageUrl,
              },
            },
            apns: {
              payload: {
                aps: {
                  mutableContent: true,
                  contentAvailable: true,
                  sound: 'default',
                },
              },
              fcmOptions: {
                imageUrl: notification.imageUrl || defaultImageUrl,
              },
            },
            webpush: {
              notification: {
                imageUrl: notification.imageUrl || defaultImageUrl,
              },
            },
            data: {
              title: notification.title || 'Notification',
              body: notification.body || 'Notification',
              link: this.generateLinkFromActions(notification.actions),
            },
          }

          return getMessaging(app).send(payload)
        })
      )

      // Track successful and failed deliveries
      const successfulDeliveries = results.filter((result) => result.status === 'fulfilled').length

      const failedDeliveries = results.filter((result) => result.status === 'rejected').length

      if (successfulDeliveries > 0) {
        await NotificationTracking.track(notification as any, notifiable, 'delivered', 'fcm', {
          successfulDeliveries,
        })
      }

      if (failedDeliveries > 0) {
        await NotificationTracking.track(notification as any, notifiable, 'failed', 'fcm', {
          failedDeliveries,
        })
      }
    } catch (error) {
      console.error(error)
      // Track failed notification attempt
      await NotificationTracking.track(notification as any, notifiable, 'failed', 'fcm', {
        error: error.message,
      })
    }
  }

  /**
   * Generate link from notification actions (supports both new and legacy formats)
   */
  private generateLinkFromActions(actions?: any[]): string {
    if (!actions || actions.length === 0) return ''

    const firstAction = actions[0]

    // Handle new format (type/context)
    if (firstAction.type && firstAction.context) {
      const context = firstAction.context

      // Generate link based on action type
      switch (firstAction.type) {
        case 'VIEW_ORDER':
          return context.orderId ? `/orders/${context.orderId}` : ''
        case 'VIEW_TEMP_ORDER':
          return context.tempOrderId ? `/temp-orders/${context.tempOrderId}` : ''
        case 'TRACK_ORDER':
          return context.orderId ? `/orders/${context.orderId}/track` : ''
        case 'VIEW_PRODUCT_DETAILS':
          return context.productId ? `/products/${context.productId}` : ''
        case 'VIEW_NOTIFICATIONS':
          return '/notifications'
        default:
          return ''
      }
    }

    // Handle legacy format (screen/args) for backward compatibility
    if (firstAction.screen && firstAction.args) {
      return Object.keys(firstAction.args).reduce(
        (acc, key) => acc.replace(`[${key}]`, firstAction.args[key]),
        firstAction.screen
      )
    }

    return ''
  }
}
