import { NotificationChannelContract } from '@ioc:Verful/Notification'
import { NotifiableModel } from '@ioc:Verful/Notification'
import User from 'App/Models/User'
import NotificationTracking from 'App/Services/NotificationTracking'

export default class DatabaseChannel implements NotificationChannelContract {
  public async send(notification: any, notifiable: NotifiableModel) {
    try {
      // Track notification attempt
      await NotificationTracking.track(
        notification,
        notifiable as User,
        'sent',
        'database',
        { type: notification.constructor.name }
      )

      // Store the notification in the database
      const data = notification.toDatabase ? notification.toDatabase(notifiable) : notification
      await notifiable.related('notifications').create({ data })

      // Track successful delivery
      await NotificationTracking.track(
        notification,
        notifiable as User,
        'delivered',
        'database',
        { type: notification.constructor.name }
      )
    } catch (error) {
      console.error('Failed to store notification:', error)
      
      // Track failed delivery
      await NotificationTracking.track(
        notification,
        notifiable as User,
        'failed',
        'database',
        { 
          type: notification.constructor.name,
          error: error.message
        }
      )

      throw error
    }
  }
} 