import { EventsList } from '@ioc:Adonis/Core/Event'
import Lot from 'App/Models/Lot'
import Vendor from 'App/Models/Vendor'
import Logger from '@ioc:Adonis/Core/Logger'

// Conditional Queue import to handle cases where queue is not configured
let Queue: any = null
try {
  Queue = require('@ioc:Adonis/Addons/Queue').default
} catch (error) {
  Logger.warn('Queue not available, auto QR generation will be disabled')
}

/**
 * Event listener for lot creation to handle automatic QR code generation
 */
export default class LotQRCodeListener {
  /**
   * Handle lot creation event
   */
  public async onLotCreated(data: EventsList['lot:created']) {
    const { lot } = data

    try {
      // Load lot with all necessary relationships
      const lotWithRelations = await Lot.query()
        .where('id', lot.id)
        .preload('section', (sectionQuery) => {
          sectionQuery.preload('branch', (branchQuery) => {
            branchQuery.preload('vendor')
          })
        })
        .firstOrFail()

      const vendor = lotWithRelations.section.branch.vendor

      // Check if vendor has QR code preferences
      const qrPreferences = vendor.qrCodePreferences

      // Skip if auto-generation is not enabled
      if (
        !qrPreferences?.autoGeneration?.enabled ||
        !qrPreferences?.autoGeneration?.onLotCreation
      ) {
        Logger.info(`Auto QR generation disabled for vendor ${vendor.id}, skipping lot ${lot.id}`)
        return
      }

      // Skip if Queue is not available
      if (!Queue) {
        Logger.warn(`Queue not available, cannot auto-generate QR code for lot ${lot.id}`)
        return
      }

      // Dispatch background job for QR code generation
      const jobPayload = {
        lotId: lot.id,
        vendorId: vendor.id,
        options: qrPreferences.defaultOptions || this.getDefaultQROptions(),
        isAutoGenerated: true,
      }

      await Queue.dispatch('App/Jobs/GenerateTableQRCode', jobPayload)

      Logger.info(`Queued QR code generation for lot ${lot.id} (table: ${lot.name})`)
    } catch (error) {
      Logger.error(`Failed to process lot creation for QR generation: ${error.message}`, {
        lotId: lot.id,
        error: error.message,
        stack: error.stack,
      })
    }
  }

  /**
   * Get default QR code options
   */
  private getDefaultQROptions() {
    return {
      width: 300,
      errorCorrectionLevel: 'M' as const,
      color: {
        dark: '#000000',
        light: '#ffffff',
      },
    }
  }
}
