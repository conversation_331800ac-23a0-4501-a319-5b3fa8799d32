import { BaseCommand } from '@adonisjs/core/build/standalone'
import { NotificationMigrationHelper } from 'App/Helpers/NotificationMigrationHelper'
import Database from '@ioc:Adonis/Lucid/Database'

export default class ValidateNotifications extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'validate:notifications'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Validate existing notifications for contract compliance and migrate if needed'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest` 
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call 
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info('Starting notification validation...')

    try {
      // Get all notifications from database
      const notifications = await Database
        .from('notifications')
        .select('*')
        .limit(100) // Process in batches

      if (notifications.length === 0) {
        this.logger.info('No notifications found in database')
        return
      }

      this.logger.info(`Found ${notifications.length} notifications to validate`)

      let compliantCount = 0
      let nonCompliantCount = 0
      let migratedCount = 0

      for (const notification of notifications) {
        try {
          const data = JSON.parse(notification.data)
          
          // Validate current format
          const validation = NotificationMigrationHelper.validateContractCompliance(data)
          
          if (validation.isCompliant) {
            compliantCount++
            this.logger.success(`✓ Notification ${notification.id} is contract-compliant`)
            
            if (validation.warnings.length > 0) {
              validation.warnings.forEach(warning => {
                this.logger.warning(`  Warning: ${warning}`)
              })
            }
          } else {
            nonCompliantCount++
            this.logger.error(`✗ Notification ${notification.id} is NOT contract-compliant`)
            
            validation.errors.forEach(error => {
              this.logger.error(`  Error: ${error}`)
            })

            // Attempt to migrate if it's a legacy format
            if (this.hasLegacyFormat(data)) {
              try {
                const notificationClass = this.inferNotificationClass(notification)
                const migratedData = NotificationMigrationHelper.convertLegacyNotification(
                  data,
                  notificationClass
                )

                // Validate migrated data
                const migratedValidation = NotificationMigrationHelper.validateContractCompliance(migratedData)
                
                if (migratedValidation.isCompliant) {
                  // Update notification in database
                  await Database
                    .from('notifications')
                    .where('id', notification.id)
                    .update({
                      data: JSON.stringify(migratedData),
                      updated_at: new Date()
                    })

                  migratedCount++
                  this.logger.success(`  ✓ Successfully migrated notification ${notification.id}`)
                } else {
                  this.logger.error(`  ✗ Migration failed for notification ${notification.id}`)
                  migratedValidation.errors.forEach(error => {
                    this.logger.error(`    Error: ${error}`)
                  })
                }
              } catch (migrationError) {
                this.logger.error(`  ✗ Migration error for notification ${notification.id}: ${migrationError.message}`)
              }
            }
          }
        } catch (parseError) {
          this.logger.error(`✗ Failed to parse notification ${notification.id}: ${parseError.message}`)
          nonCompliantCount++
        }
      }

      // Summary
      this.logger.info('\n=== Validation Summary ===')
      this.logger.info(`Total notifications processed: ${notifications.length}`)
      this.logger.success(`Contract-compliant: ${compliantCount}`)
      this.logger.error(`Non-compliant: ${nonCompliantCount}`)
      this.logger.info(`Successfully migrated: ${migratedCount}`)

      if (nonCompliantCount > migratedCount) {
        this.logger.warning(`${nonCompliantCount - migratedCount} notifications still need manual attention`)
      }

    } catch (error) {
      this.logger.error(`Validation failed: ${error.message}`)
      this.exitCode = 1
    }
  }

  /**
   * Check if notification has legacy format
   */
  private hasLegacyFormat(data: any): boolean {
    // Check for legacy action format
    if (data.actions && Array.isArray(data.actions)) {
      return data.actions.some(action => action.screen && !action.type)
    }

    // Check for missing meta structure
    if (!data.meta || !data.meta.category || !data.meta.notificationType) {
      return true
    }

    return false
  }

  /**
   * Infer notification class from database record
   */
  private inferNotificationClass(notification: any): string {
    // Try to get from type field if available
    if (notification.type) {
      return notification.type
    }

    // Infer from data content
    const data = JSON.parse(notification.data)
    
    if (data.title) {
      const title = data.title.toLowerCase()
      
      if (title.includes('order')) {
        if (title.includes('status')) return 'CustomerOrderStatusChange'
        if (title.includes('new')) return 'CustomerNewOrder'
        return 'CustomerNewOrder'
      }
      
      if (title.includes('payment')) {
        if (title.includes('received')) return 'CustomerPaymentReceived'
        if (title.includes('failed')) return 'CustomerPaymentFailure'
        return 'CustomerPaymentReceived'
      }
      
      if (title.includes('invoice')) return 'NewInvoice'
      if (title.includes('statement')) return 'StatementNotification'
      if (title.includes('bill')) return 'PendingBillNotification'
      if (title.includes('welcome')) return 'Welcome'
      if (title.includes('subscription')) return 'CustomerNewSubscription'
      if (title.includes('broadcast')) return 'CustomerBroadcast'
    }

    return 'UnknownNotification'
  }
}
