import { BaseCommand } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import { NotificationMigrationHelper } from 'App/Helpers/NotificationMigrationHelper'

export default class MigrateNotifications extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'migrate:notifications'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Migrate legacy notifications to standardized format'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest` 
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call 
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info('Starting notification migration...')

    try {
      // Get all notifications with legacy format
      const legacyNotifications = await Database
        .from('notifications')
        .whereRaw("JSON_EXTRACT(data, '$.actions[0].screen') IS NOT NULL")
        .orWhereRaw("JSON_EXTRACT(data, '$.meta.orderId') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(data, '$.meta')) = 1")

      this.logger.info(`Found ${legacyNotifications.length} legacy notifications to migrate`)

      let migrated = 0
      let errors = 0

      for (const notification of legacyNotifications) {
        try {
          const data = JSON.parse(notification.data)
          
          // Determine notification class from data patterns
          const notificationClass = this.inferNotificationClass(data)
          
          // Convert to new format
          const migratedData = NotificationMigrationHelper.convertLegacyNotification(
            data, 
            notificationClass
          )

          // Update in database
          await Database
            .from('notifications')
            .where('id', notification.id)
            .update({
              data: JSON.stringify(migratedData),
              updated_at: new Date()
            })

          migrated++
          
          if (migrated % 100 === 0) {
            this.logger.info(`Migrated ${migrated} notifications...`)
          }

        } catch (error) {
          errors++
          this.logger.error(`Error migrating notification ${notification.id}: ${error.message}`)
        }
      }

      this.logger.success(`Migration completed! Migrated: ${migrated}, Errors: ${errors}`)

      // Generate migration report
      await this.generateMigrationReport(migrated, errors, legacyNotifications.length)

    } catch (error) {
      this.logger.error(`Migration failed: ${error.message}`)
      process.exit(1)
    }
  }

  /**
   * Infer notification class from data patterns
   */
  private inferNotificationClass(data: any): string {
    if (data.title?.includes('Order') && data.title?.includes('Confirmed')) {
      return 'CustomerNewOrder'
    }
    if (data.title?.includes('Order') && data.title?.includes('Status')) {
      return 'CustomerOrderStatusChange'
    }
    if (data.title?.includes('New Order')) {
      return 'StaffNewOrder'
    }
    if (data.title?.includes('request') || data.title?.includes('Request')) {
      return 'CustomerNewTempOrder'
    }
    if (data.body?.includes('Off') || data.body?.includes('%')) {
      return 'BroadcastBuyNotification'
    }
    
    return 'GenericNotification'
  }

  /**
   * Generate migration report
   */
  private async generateMigrationReport(migrated: number, errors: number, total: number) {
    const report = {
      timestamp: new Date().toISOString(),
      total_notifications: total,
      successfully_migrated: migrated,
      errors: errors,
      success_rate: ((migrated / total) * 100).toFixed(2) + '%',
      remaining_legacy: await this.countRemainingLegacy()
    }

    this.logger.info('Migration Report:')
    this.logger.info(JSON.stringify(report, null, 2))
  }

  /**
   * Count remaining legacy notifications
   */
  private async countRemainingLegacy(): Promise<number> {
    const result = await Database
      .from('notifications')
      .whereRaw("JSON_EXTRACT(data, '$.actions[0].screen') IS NOT NULL")
      .orWhereRaw("JSON_EXTRACT(data, '$.meta.orderId') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(data, '$.meta')) = 1")
      .count('* as total')

    return result[0].total
  }
}
