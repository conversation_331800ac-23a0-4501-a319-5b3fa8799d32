import { BaseCommand } from '@adonisjs/core/build/standalone'
import Order from 'App/Models/Order'
import Database from '@ioc:Adonis/Lucid/Database'

export default class MigrateTempOrderItems extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'migrate:temp-order-items'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Migrate existing temp orders from meta.temp_items to OrderItems table'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest` 
     * after creating the command to update the manifest file.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call 
     * `node ace generate:manifest` after creating the command to update the manifest file.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info('🔄 Starting migration of temp order items...')

    try {
      // Find all orders with temp_items in meta
      const ordersWithTempItems = await Order.query()
        .whereRaw("meta->>'temp_items' IS NOT NULL")
        .whereRaw("json_typeof(meta->'temp_items') = 'object'")
        .whereRaw("json_object_keys(meta->'temp_items') != '[]'")

      this.logger.info(`Found ${ordersWithTempItems.length} orders with temp_items to migrate`)

      if (ordersWithTempItems.length === 0) {
        this.logger.success('✅ No orders need migration')
        return
      }

      let migratedCount = 0
      let errorCount = 0
      const errors: string[] = []

      for (const order of ordersWithTempItems) {
        try {
          await this.migrateOrderItems(order)
          migratedCount++
          this.logger.info(`✅ Migrated order ${order.id} (${migratedCount}/${ordersWithTempItems.length})`)
        } catch (error) {
          errorCount++
          const errorMsg = `❌ Failed to migrate order ${order.id}: ${error.message}`
          this.logger.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      this.logger.info('\n📊 MIGRATION SUMMARY:')
      this.logger.info(`Total orders processed: ${ordersWithTempItems.length}`)
      this.logger.info(`Successfully migrated: ${migratedCount}`)
      this.logger.info(`Errors: ${errorCount}`)

      if (errors.length > 0) {
        this.logger.info('\n❌ ERRORS:')
        errors.forEach(error => this.logger.error(error))
      }

      if (migratedCount > 0) {
        this.logger.success(`✅ Successfully migrated ${migratedCount} temp orders to use OrderItems`)
      }

    } catch (error) {
      this.logger.error(`❌ Migration failed: ${error.message}`)
      throw error
    }
  }

  private async migrateOrderItems(order: Order): Promise<void> {
    const transaction = await Database.transaction()

    try {
      // Load existing items to check if already migrated
      await order.load('items')

      // If order already has OrderItems, skip migration
      if (order.items && order.items.length > 0) {
        this.logger.info(`⏭️ Order ${order.id} already has OrderItems, skipping`)
        await transaction.commit()
        return
      }

      const tempItems = order.meta?.temp_items || {}
      const tempItemKeys = Object.keys(tempItems)

      if (tempItemKeys.length === 0) {
        this.logger.info(`⏭️ Order ${order.id} has no temp_items, skipping`)
        await transaction.commit()
        return
      }

      this.logger.info(`🔄 Migrating ${tempItemKeys.length} items for order ${order.id}`)

      // Create OrderItems from temp_items
      let itemsCreated = 0
      for (const [productId, itemData] of Object.entries(tempItems)) {
        try {
          // Handle different item data formats
          let quantity = 1
          if (typeof itemData === 'object' && itemData !== null) {
            quantity = (itemData as any).quantity || 1
          } else if (typeof itemData === 'number') {
            quantity = itemData
          }

          // Validate quantity
          if (!quantity || quantity <= 0) {
            this.logger.warn(`⚠️ Skipping item ${productId} with invalid quantity: ${quantity}`)
            continue
          }

          // Create order item
          await order.related('items').create({
            productId: productId,
            quantity: quantity,
          }, { client: transaction })

          itemsCreated++
          this.logger.info(`  ✅ Created OrderItem for product ${productId} (qty: ${quantity})`)

        } catch (itemError) {
          this.logger.error(`  ❌ Failed to create item for product ${productId}: ${itemError.message}`)
          throw itemError
        }
      }

      if (itemsCreated === 0) {
        throw new Error('No valid items were created from temp_items')
      }

      // Remove temp_items from meta after successful migration
      const newMeta = { ...order.meta }
      delete newMeta.temp_items
      
      await order
        .merge({ meta: newMeta })
        .save({ client: transaction })

      await transaction.commit()
      this.logger.info(`✅ Successfully migrated ${itemsCreated} items for order ${order.id}`)

    } catch (error) {
      await transaction.rollback()
      throw error
    }
  }
}
