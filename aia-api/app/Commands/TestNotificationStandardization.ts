import { BaseCommand } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import { NotificationMigrationHelper } from 'App/Helpers/NotificationMigrationHelper'

export default class TestNotificationStandardization extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'test:notifications'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Test notification standardization and identify format inconsistencies'

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  public async run() {
    this.logger.info('Testing notification standardization...')

    try {
      // Analyze current notification formats
      await this.analyzeNotificationFormats()
      
      // Test legacy format detection
      await this.testLegacyFormatDetection()
      
      // Test migration helper
      await this.testMigrationHelper()
      
      // Generate recommendations
      await this.generateRecommendations()

    } catch (error) {
      this.logger.error(`Testing failed: ${error.message}`)
      process.exit(1)
    }
  }

  /**
   * Analyze current notification formats in database
   */
  private async analyzeNotificationFormats() {
    this.logger.info('Analyzing notification formats...')

    // Count total notifications
    const totalCount = await Database.from('notifications').count('* as total')
    const total = totalCount[0].total

    // Count legacy format notifications (screen/args)
    const legacyCount = await Database
      .from('notifications')
      .whereRaw("JSON_EXTRACT(data, '$.actions[0].screen') IS NOT NULL")
      .count('* as total')
    const legacy = legacyCount[0].total

    // Count new format notifications (type/context)
    const newCount = await Database
      .from('notifications')
      .whereRaw("JSON_EXTRACT(data, '$.actions[0].type') IS NOT NULL")
      .count('* as total')
    const newFormat = newCount[0].total

    // Count notifications with minimal meta
    const minimalMetaCount = await Database
      .from('notifications')
      .whereRaw("JSON_EXTRACT(data, '$.meta.orderId') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(data, '$.meta')) = 1")
      .count('* as total')
    const minimalMeta = minimalMetaCount[0].total

    this.logger.info('Format Analysis Results:')
    this.logger.info(`Total notifications: ${total}`)
    this.logger.info(`Legacy format (screen/args): ${legacy} (${((legacy/total)*100).toFixed(1)}%)`)
    this.logger.info(`New format (type/context): ${newFormat} (${((newFormat/total)*100).toFixed(1)}%)`)
    this.logger.info(`Minimal metadata: ${minimalMeta} (${((minimalMeta/total)*100).toFixed(1)}%)`)
  }

  /**
   * Test legacy format detection
   */
  private async testLegacyFormatDetection() {
    this.logger.info('Testing legacy format detection...')

    // Get sample legacy notifications
    const legacyNotifications = await Database
      .from('notifications')
      .whereRaw("JSON_EXTRACT(data, '$.actions[0].screen') IS NOT NULL")
      .limit(5)

    for (const notification of legacyNotifications) {
      const data = JSON.parse(notification.data)
      const validation = NotificationMigrationHelper.validateContractCompliance(data)
      
      this.logger.info(`Notification ${notification.id}:`)
      this.logger.info(`  Compliant: ${validation.isCompliant}`)
      this.logger.info(`  Errors: ${validation.errors.length}`)
      this.logger.info(`  Warnings: ${validation.warnings.length}`)
      
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          this.logger.warning(`    ${warning}`)
        })
      }
    }
  }

  /**
   * Test migration helper functionality
   */
  private async testMigrationHelper() {
    this.logger.info('Testing migration helper...')

    // Test legacy notification conversion
    const legacyNotification = {
      title: 'New Order',
      body: 'Hello Gideon. Your order from O\'Sinkirri Bar & Restaurant is Pending',
      actions: [
        {
          screen: '/orders/[orderId]',
          label: 'View Order',
          args: { orderId: '01jyvtn0crpxx14qc9j1kzhy3f' }
        }
      ],
      meta: {
        orderId: '01jyvtn0crpxx14qc9j1kzhy3f'
      }
    }

    const converted = NotificationMigrationHelper.convertLegacyNotification(
      legacyNotification, 
      'CustomerNewOrder'
    )

    this.logger.info('Migration Test Results:')
    this.logger.info('Original format:')
    this.logger.info(JSON.stringify(legacyNotification, null, 2))
    this.logger.info('Converted format:')
    this.logger.info(JSON.stringify(converted, null, 2))

    // Validate converted notification
    const validation = NotificationMigrationHelper.validateContractCompliance(converted)
    this.logger.info(`Converted notification compliant: ${validation.isCompliant}`)
    
    if (!validation.isCompliant) {
      validation.errors.forEach(error => {
        this.logger.error(`  Error: ${error}`)
      })
    }
  }

  /**
   * Generate recommendations for standardization
   */
  private async generateRecommendations() {
    this.logger.info('Generating recommendations...')

    const recommendations = [
      '1. Run migration command: node ace migrate:notifications',
      '2. Update notification classes to use NotificationHelper consistently',
      '3. Add NotificationValidation middleware to prevent future inconsistencies',
      '4. Update frontend to handle new notification format',
      '5. Monitor notification creation to ensure compliance'
    ]

    this.logger.info('Standardization Recommendations:')
    recommendations.forEach(rec => {
      this.logger.info(`  ${rec}`)
    })

    // Check for specific problematic patterns
    const problematicNotifications = await Database
      .from('notifications')
      .whereRaw("JSON_EXTRACT(data, '$.actions[0].screen') IS NOT NULL")
      .whereRaw("created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)")
      .count('* as total')

    const recentProblematic = problematicNotifications[0].total

    if (recentProblematic > 0) {
      this.logger.warning(`Found ${recentProblematic} recent notifications using legacy format!`)
      this.logger.warning('This indicates ongoing creation of non-standardized notifications.')
    }
  }
}
