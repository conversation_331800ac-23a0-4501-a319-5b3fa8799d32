import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'

/**
 * Middleware to validate notification format consistency
 * Ensures all notifications follow the standardized format
 */
export default class NotificationValidation {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {
    // Only validate notification-related requests
    if (!this.isNotificationRequest(request)) {
      await next()
      return
    }

    try {
      const body = request.body()
      
      // Validate notification data if present
      if (body.data && this.hasNotificationData(body.data)) {
        this.validateNotificationFormat(body.data)
      }

      await next()
    } catch (error) {
      return response.status(422).json({
        error: 'Invalid notification format',
        details: error.message,
        standardFormat: this.getStandardFormatExample()
      })
    }
  }

  /**
   * Check if this is a notification-related request
   */
  private isNotificationRequest(request): boolean {
    const url = request.url()
    return url.includes('/notifications') || 
           url.includes('/notify') || 
           request.header('x-notification-request') === 'true'
  }

  /**
   * Check if the data contains notification structure
   */
  private hasNotificationData(data: any): boolean {
    return data && (data.title || data.body || data.actions || data.meta)
  }

  /**
   * Validate notification format against standard
   */
  private validateNotificationFormat(data: any): void {
    // Check for legacy format indicators
    if (data.actions && Array.isArray(data.actions)) {
      for (const action of data.actions) {
        if (action.screen && action.args) {
          throw new Error(
            `Legacy notification format detected. Use 'type' and 'context' instead of 'screen' and 'args'. ` +
            `Found: {screen: "${action.screen}", args: {...}}. ` +
            `Expected: {type: "ACTION_TYPE", context: {...}}`
          )
        }
        
        if (!action.type) {
          throw new Error(
            `Missing 'type' field in notification action. ` +
            `Each action must have a semantic type like 'VIEW_ORDER', 'TRACK_ORDER', etc.`
          )
        }
      }
    }

    // Check for minimal meta (legacy indicator)
    if (data.meta && Object.keys(data.meta).length === 1 && data.meta.orderId) {
      console.warn(
        'Minimal notification metadata detected. Consider using NotificationHelper.createNotificationData() ' +
        'for richer metadata including category, notificationType, priority, etc.'
      )
    }

    // Validate required fields for new format
    if (data.meta && data.meta.category && !data.meta.notificationType) {
      throw new Error('Missing notificationType in notification metadata')
    }
  }

  /**
   * Provide example of standard format
   */
  private getStandardFormatExample(): object {
    return {
      title: 'Order Confirmed',
      body: 'Your order has been confirmed',
      actions: [
        {
          type: 'VIEW_ORDER',
          label: 'View Order',
          context: {
            orderId: '01jz3hwxd8hm2s1tc3x7fg8b0j',
            status: 'Pending',
            vendorName: 'Restaurant Name'
          }
        }
      ],
      meta: {
        category: 'ORDER',
        notificationType: 'ORDER_CREATED',
        priority: 'MEDIUM',
        entityId: '01jz3hwxd8hm2s1tc3x7fg8b0j',
        entityType: 'order'
      },
      icon: 'https://cdn.verful.com/icons/order-confirmed-icon.png'
    }
  }
}
