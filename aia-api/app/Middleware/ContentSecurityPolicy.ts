import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class ContentSecurityPolicy {
  public async handle(
    { response, request }: HttpContextContract,
    next: () => Promise<void>
  ) {
    // Check if this is a mobile app route
    const isMobileAppRoute = request.url().includes('/mobile-app');
    
    // Define common sources
    const self = "'self'";
    const unsafeInline = "'unsafe-inline'"; // Needed for some UI libraries
    const unsafeEval = "'unsafe-eval'";     // Needed by some JS libraries
    const blob = 'blob:';                   // For workers and file previews
    const data = 'data:';                   // For inline images/fonts

    // Script sources - includes Firebase, Google services, and CDNs
    const scriptSrc = [
      self,
      unsafeInline,
      unsafeEval,
      'https://www.googletagmanager.com',
      'https://*.googleapis.com',
      'https://fcm.googleapis.com',
      'https://cdnjs.cloudflare.com',
      'https://www.gstatic.com',
    ].join(' ');

    // Style sources - includes CDNs and Google Fonts
    const styleSrc = [
      self,
      unsafeInline,
      'https://cdnjs.cloudflare.com',
      'https://fonts.googleapis.com',
    ].join(' ');

    // Image sources - includes S3, Firebase Storage, and data URIs
    const imgSrc = [
      self,
      data,
      'https://*.s3.amazonaws.com',
      'https://*.s3.*.amazonaws.com',
      'https://firebasestorage.googleapis.com',
      'https://www.gravatar.com',
    ].join(' ');

    // Font sources - includes CDNs and Google Fonts
    const fontSrc = [
      self,
      data,
      'https://cdnjs.cloudflare.com',
      'https://fonts.gstatic.com',
    ].join(' ');

    // Connect sources - includes APIs and WebSocket
    const connectSrc = [
      self,
      'https://uat-api.appinapp.ke',
      'wss:',
      'https://*.googleapis.com',
      'https://fcm.googleapis.com',
      'https://www.googletagmanager.com',
      'https://*.s3.amazonaws.com',
      'https://*.s3.*.amazonaws.com',
      'https://firestore.googleapis.com',
      'https://firebase.googleapis.com',
      'https://firebaseinstallations.googleapis.com',
      'https://dev-aia-bucket.s3.eu-north-1.amazonaws.com',
      'https://uat-aia-bucket.s3.eu-north-1.amazonaws.com',
      'https://stag-aia-bucket.s3.eu-north-1.amazonaws.com',
      'https://prod-aia-bucket.s3.eu-north-1.amazonaws.com'
    ].join(' ');

    // Worker sources - for web workers
    const workerSrc = [
      self,
      blob,
      'https://www.gstatic.com',
    ].join(' ');

    // Frame sources - for iframes
    const frameSrc = [
      self,
      'https://www.google.com', // For Google services like Maps
    ].join(' ');

    // Media sources - for audio/video
    const mediaSrc = [
      self,
      'https://*.s3.amazonaws.com',
      'https://*.s3.*.amazonaws.com',
    ].join(' ');

    // Combine all directives
    let cspDirectives = [
      `default-src ${self}`,
      `script-src ${scriptSrc}`,
      `style-src ${styleSrc}`,
      `img-src ${imgSrc}`,
      `font-src ${fontSrc}`,
      `connect-src ${connectSrc}`,
      `worker-src ${workerSrc}`,
      `frame-src ${frameSrc}`,
      `media-src ${mediaSrc}`,
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "manifest-src 'self'",
      "upgrade-insecure-requests",
    ].join('; ');

    // If this is a mobile app route, we might need a more permissive policy
    if (isMobileAppRoute) {
      // You can customize this for mobile app routes if needed
      // For example, you might need to add additional domains or be more permissive
      // with certain directives
      console.log('Applying mobile app CSP configuration');
      
      // Example: If mobile app needs more permissive connect-src
      const mobileConnectSrc = [
        connectSrc,
        'https://additional-mobile-api.example.com',
      ].join(' ');
      
      // Replace the connect-src directive with the mobile version
      cspDirectives = cspDirectives.replace(
        `connect-src ${connectSrc}`,
        `connect-src ${mobileConnectSrc}`
      );
    }

    // Set security headers
    response.header('Content-Security-Policy', cspDirectives);
    response.header('X-Content-Type-Options', 'nosniff');
    response.header('X-Frame-Options', 'DENY');
    response.header('X-XSS-Protection', '1; mode=block');
    response.header('Referrer-Policy', 'strict-origin-when-cross-origin');

    await next()
  }
}