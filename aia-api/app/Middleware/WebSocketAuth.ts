import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import jwt from 'jsonwebtoken'
import Env from '@ioc:Adonis/Core/Env'
import User from '../Models/User'

/**
 * Middleware for WebSocket authentication and authorization
 * This middleware can be used for HTTP endpoints that need WebSocket-compatible auth
 */
export default class WebSocketAuth {
  /**
   * Handle WebSocket authentication for HTTP endpoints
   */
  public async handle({ request, response, auth }: HttpContextContract, next: () => Promise<void>) {
    try {
      // Check if user is already authenticated via regular auth
      if (auth.isAuthenticated) {
        return await next()
      }

      // Try WebSocket-style token authentication
      const token = this.extractToken(request)

      if (!token) {
        return response.unauthorized({
          error: 'Authentication token required',
          message: 'Provide token in Authorization header or as query parameter',
        })
      }

      // Verify JWT token
      const decoded = jwt.verify(token, Env.get('APP_KEY')) as any

      // Get user from database
      const user = await User.find(decoded.sub || decoded.id)
      if (!user) {
        return response.unauthorized({
          error: 'Invalid token',
          message: 'User not found',
        })
      }

      // Load user relationships for WebSocket context
      await user.load('roles')
      await user.load('employers')

      // Set user in auth context
      auth.user = user

      await next()
    } catch (error) {
      console.error('WebSocket auth middleware error:', error.message)

      if (error.name === 'JsonWebTokenError') {
        return response.unauthorized({
          error: 'Invalid token',
          message: 'Token verification failed',
        })
      }

      if (error.name === 'TokenExpiredError') {
        return response.unauthorized({
          error: 'Token expired',
          message: 'Please refresh your token',
        })
      }

      return response.status(500).json({
        error: 'Authentication error',
        message: 'Internal authentication error',
      })
    }
  }

  /**
   * Extract token from request
   */
  private extractToken(request: any): string | null {
    // Try Authorization header first
    const authHeader = request.header('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7)
    }

    // Try query parameter
    const tokenParam = request.input('token')
    if (tokenParam) {
      return tokenParam
    }

    // Try WebSocket-style auth parameter
    const wsToken = request.input('auth_token')
    if (wsToken) {
      return wsToken
    }

    return null
  }

  /**
   * Validate WebSocket subscription permissions
   */
  public static validateSubscriptionPermissions(user: any, room: string): boolean {
    // Parse room format: type:id:subtype
    const [type, id, subType] = room.split(':')

    // Load user roles if not already loaded
    const userRoles = user.roles?.map((role) => role.name) || []

    // Admin can access everything
    if (userRoles.includes('admin')) {
      return true
    }

    switch (type) {
      case 'user':
        // Users can only access their own channels
        return id === user.id

      case 'vendor':
        // Must be associated with the vendor
        const userVendorId = user.employers?.[0]?.vendorId
        return userVendorId === id

      case 'branch':
        // Must be associated with the branch
        const userBranchId = user.employers?.[0]?.branchId
        return userBranchId === id

      case 'department':
        // Department staff or managers can access
        const isDepartmentStaff = user.departmentIds?.includes(id)
        const isManager = userRoles.includes('manager')
        return isDepartmentStaff || isManager

      case 'order':
        // Order access depends on user type and role
        if (userRoles.includes('customer')) {
          // Customers can only access their own orders
          return subType === 'customer' && user.id === id
        } else {
          // Staff can access orders from their vendor/branch
          return userRoles.some((role) =>
            [
              'waiter',
              'chef',
              'bartender',
              'barista',
              'cashier',
              'rider',
              'manager',
              'delivery',
            ].includes(role)
          )
        }

      case 'customer':
        // Only the customer themselves can access
        return userRoles.includes('customer') && id === user.id

      case 'admin':
        // Only admins can access admin channels
        return userRoles.includes('admin')

      default:
        return false
    }
  }

  /**
   * Get user type based on roles
   */
  public static getUserType(user: any): 'customer' | 'staff' | 'manager' | 'admin' {
    const userRoles = user.roles?.map((role) => role.name) || []

    if (userRoles.includes('admin')) {
      return 'admin'
    } else if (userRoles.includes('manager')) {
      return 'manager'
    } else if (userRoles.some((role) => ['waiter', 'chef', 'cashier', 'delivery'].includes(role))) {
      return 'staff'
    } else {
      return 'customer'
    }
  }

  /**
   * Get auto-subscription rooms for user
   */
  public static getAutoSubscriptionRooms(user: any): string[] {
    const rooms: string[] = []
    const userType = this.getUserType(user)
    const userRoles = user.roles?.map((role) => role.name) || []

    // User-specific channel
    rooms.push(`user:${user.id}`)

    // Type-specific channels
    switch (userType) {
      case 'admin':
        rooms.push('admin:global')
        break

      case 'manager':
        if (user.employers?.[0]?.vendorId) {
          rooms.push(`vendor:${user.employers[0].vendorId}:manager`)
          if (user.employers[0].branchId) {
            rooms.push(`branch:${user.employers[0].branchId}:manager`)
          }
        }
        break

      case 'staff':
        if (user.employers?.[0]?.vendorId) {
          rooms.push(`vendor:${user.employers[0].vendorId}:staff`)
          if (user.employers[0].branchId) {
            rooms.push(`branch:${user.employers[0].branchId}:staff`)
          }
        }
        // Add department-specific channels
        user.departmentIds?.forEach((deptId) => {
          rooms.push(`department:${deptId}`)
        })
        break

      case 'customer':
        rooms.push(`customer:${user.id}:orders`)
        break
    }

    return rooms
  }

  /**
   * Check if user can perform WebSocket action
   */
  public static canPerformAction(user: any, action: string, target?: any): boolean {
    const userType = this.getUserType(user)
    const userRoles = user.roles?.map((role) => role.name) || []

    switch (action) {
      case 'update_order_status':
        return userType !== 'customer'

      case 'update_item_status':
        return userType !== 'customer'

      case 'assign_staff':
        return userRoles.includes('manager') || userRoles.includes('admin')

      case 'broadcast_message':
        return userRoles.includes('admin')

      case 'view_analytics':
        return userType === 'manager' || userType === 'admin'

      case 'force_completion':
        return userRoles.includes('manager') || userRoles.includes('admin')

      default:
        return false
    }
  }
}
