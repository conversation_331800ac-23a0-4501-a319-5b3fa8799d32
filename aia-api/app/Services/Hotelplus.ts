import Order from 'App/Models/Order'
// Updated: Removed TempOrder import - now using unified Order model
import axios from 'axios'
import Redis from '@ioc:Adonis/Addons/Redis'
import Product from 'App/Models/Product'

export default class Hotelplus {
  public http = axios.create({
    baseURL: 'https://hotelplus.onrender.com/v1',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  })

  constructor(
    public vendorId: string,
    public credentials?: Record<string, any>,
    public token = ''
  ) {
    this.http.interceptors.request.use((config) => {
      if (this.token) {
        config.headers.Authorization = `Bearer ${this.token}`
      }

      // console.log('Request', config)

      return config
    })

    this.http.interceptors.response.use((response) => {
      // console.log('Response', response)

      return response
    })
  }

  public authorize = async () => {
    if (!this.credentials) return

    const { username, password } = this.credentials

    const res = await this.http.post('auth/login', {
      username,
      password,
    })

    if (res.status === 200) {
      this.token = res.data.token
    }
  }

  public syncProducts = async (from = 1) => {
    try {
      await this.authorize()

      const { data, status } = await this.http.get(`items?page=${from}&per=20`)

      if (status === 200) {
        const { data: products, meta } = data

        if (meta.currentPage === meta.lastPage) {
          await Redis.set(`lastProductSaved${this.vendorId}`, 1)
        }

        const mapped = products?.map((product) => ({
          //     "itemCode": 1,
          ref: product.itemCode,
          // "itemName": "FRESH FRUIT SALAD",
          name: product.itemName,
          // "sourceCat": "N/A",
          // "itemSource": "N/A",
          // "prodGr": "DESSERTS",
          category: product.prodGr,
          // "postTo": "FOOD",
          // "qnty": 0,
          stock: product.qnty === 0 ? -1 : product.qnty,
          // "units": "PC",
          unit: product.units.toLowerCase(),
          // "ucost": 0,
          // "qPurch": 0,
          // "purchDate": null,
          // "qIdealLev": 0,
          // "ppu": 250,
          price: product.ppu,
          // "qstart": 0,
          // "discrep": null,
          // "discrepQ": 0,
          // "comntDiscrep": null,
          // "qtyTotSales": 0,
          // "pictureCode": 0,
          // "accompany": "NO",
          // "descrip": "NO",
          // "stockable": "NO",
          // "qtyStore": 1,
          // "nameTake": "FRESH FRUIT SALAD",
          // "qadded": 0,
          // "qsold": 0,
          // "qpending": 0,
          // "qbal": 0,
          // "buttonCode": 0,
          // "spoilages": 0,
          // "bom": 0,
          // "phyBal": 0,
          // "active": "YES",
          active: product.active === 'YES',
          // "costCode": "0",
          // "taxType": 1,
          // "discType": 0,
          // "discPerc": 0,
          // "storeType": "0",
          // "multiStore": "NO",
          // "editSale": "NO",
          // "salesCom": 0,
          // "deleted": "NO",
          // "convToCode": 0,
          // "convValue": 0,
          // "ppu1": 250,
          // "ppu2": 250,
          // "ppu3": 250,
          // "ppu4": 250,
          // "ppu5": 250,
          // "selfService": "NO",
          // "itemDesc": "FRESH FRUIT SALAD",
          details: product.itemDesc,
          // "quickMenu": "NO",
          // "periodCount": 0,
          // "periodSales": 0,
          // "monthlyCount": 0,
          // "monthlySales": 0,
          // "yearlyCount": 0,
          // "yearlySales": 0,
          // "periodPerc": 0,
          // "monthlyPerc": 0,
          // "yearlyPerc": 0,
          // "scannerCode": "1",
          // "externalPrice": 250,
          // "currPrice1": 0,
          // "currPrice2": 0,
          // "currPrice3": 0,
          // "currPrice4": 0,
          // "currPrice5": 0,
          // "reorderQty": 0,
          // "sysBalBak": 0,
          // "incomeAcc": "0000-000",
          // "iRefNumId": "0-000",
          // "multiPrice": 0,
          // "trackPortions": "NO",
          // "iPeek": "NO",
          // "sadFate": "NO",
          // "storeName": null,
          // "iCopied": 0,
          // "iCopied1": 0,
          // "iStockBalQty": 0,
          // "wholeSalePrice": 0,
          // "wholeSaleQty": 0,
          // "minQty": 0,
          // "mrQty": 1,
          // "atZero": "NO",
          // "trackKitPortions": "NO",
          // "marginRangeFrom": 0,
          // "marginRangeTo": 0,
          // "hsCode": null,
          // "userId": "01hqz8q5jckcee0tp98fvd4jp0",
          // "createdAt": "2024-03-02T09:46:12.931+00:00",
          // "updatedAt": "2024-03-02T09:46:12.931+00:00"
        }))

        return mapped
      }

      return data
    } catch (error) {}
  }

  public syncOrders = async () => {
    const res = await this.http.get(`orders`, {
      headers: {},
    })

    if (res.status === 200) {
      return res.data
    }

    return []
  }

  public postOrder = async (order: Order) => {
    await this.authorize()
    await order.load('items')

    const res = await this.http.post(`orders`, {
      id: order.id,
      amount: order.total,
      staffId: order.staffId,
      vendorId: order.vendorId,
      items: order.items,
    })

    if (res.status === 200) {
      return res.data
    }

    return {}
  }

  // Updated: Accept unified Order model and detect temp orders
  public postTempOrder = async (order: Order) => {
    try {
      // Validate that this is actually a temp order
      if (order.status !== 'Pending') {
        console.warn('Hotelplus.postTempOrder received non-temp order:', order.id, order.status)
        return {}
      }

      await this.authorize()

      // Calculate total for temp order
      const total = await order.calculateTempOrderTotal()

      const { data: sale, status } = await this.http.post(
        `sales`,
        {
          IBLno: order.id,
          WTnox: '',
          TBno: order.lotId,
          Waiter: order.staffId,
          IBdate: order.createdAt.toISODate(),
          IBtime: order.createdAt.toISOTime(),
          Amount: total,
          DiscPercent: '',
          CASH: '',
          Clear: '',
          PinCode: '',
          PayMode: '',
          Remark: '',
          GuestName: '',
          RoomNum: '',
          OpenBills: '',
          FinalBills: '',
          KitOrds: '',
          NoPers: '',
          ShiftNoX: '',
          ShiftNo: '',
          KO: '',
          BO: '',
          OB: '',
          FB: '',
          FP: '',
          BillTypeID: '',
          RoomService: '',
          AccPeriod: '',
          CashierName: '',
          BarCode: '',
          Discounted: '',
          DiscName: '',
          AccNo: '',
          G_Card: '',
          OrderConcurr: '',
          DiscComment: '',
          DiscAmount: '',
          Covers: '',
          ServicePeriod: '',
          RevCenterName: '',
          CreatedBy: order.staff?.firstName || '',
          ClearTime: '',
          CurrCode: '',
          BillCreateTime: '',
          SalePointName: '',
          Bill_OrderStatus: '',
          Bill_Total: total,
          user_Ref: order.userId,
          Picked: '',
          BatchNo: '',
          EntryNo: '',
          GuestAccMap: '',
          RefNo: order.id,
          BillpcName: '',
          BillRef: order.id,
          ApprovedRate: '',
          ServedAt: '',
          BillComment: '',
        },
        {
          headers: { Authorization: `Bearer ${this.token}` },
        }
      )

      console.log('sale', sale)

      if (status !== 200) {
        return sale
      } else {
        // Updated: Get temp items from meta.temp_items
        const tempItems = order.getTempItems()
        const productIds = Object.keys(tempItems)

        const items = await Product.query().preload('category').whereIn('id', productIds).exec()

        items.map(async (item) => {
          const saleItem = {
            IBLno: sale.ibLno,
            TBno: order.lot?.name,
            Waiter: (order.staff?.name || '').toUpperCase(),
            IBtime: order.createdAt.toISODate(),
            IBdate: order.createdAt.toISODate(),
            ItemCode: item.ref,
            ItemName: item.name,
            ITtime: item.createdAt.toISODate(),
            // Updated: Get quantity from temp_items
            Qnty: tempItems[item.id]?.quantity || 0,
            PPU: item.price,
            ProdGrp: item.category.name.toUpperCase(),
            WrEnt: '',
            SourceCat: '',
            ItemSource: '',
            PostTo: '',
            GuestName: '',
            RoomNum: '',
            Paymode: '',
            ShiftNo: '',
            Ordered: '',
            StoreCode: '',
            CostCode: '',
            UnitCost: '',
            Units: '',
            TaxType: '',
            DiscType: '',
            DiscPerc: '',
            StoreType: '',
            AccPeriod: '',
            iRestTxnID: '',
            iPayTxnID: '',
            BarCode: '',
            ExactQty: '',
            iDiscType: '',
            DiscAmt: '',
            OrderStatus: '',
            AliasName: '',
            ServicePeriod: '',
            ServiceTime: '',
            isHappyHour: '',
            TaxA: '',
            TaxB: '',
            TaxC: '',
            TaxAmount: '',
            Department: '',
            OrderNum: '',
            TempPPU: '',
            PosttoAccMap: '',
            iRefNumID: item.ref,
            PostPcName: '',
            // Updated: Get quantity from temp_items
            CountQty: tempItems[item.id]?.quantity || 0,
          }

          console.log('sale item', item.id, saleItem)
          await this.http.post(`sale-items`, saleItem)
        })
      }

      return {}
    } catch (error) {}
  }

  public syncOrder = async (orderId: string) => {
    const res = await this.http.get(`vendors/orders/${orderId}`)

    if (res.status === 200) {
      return res.data
    }

    return {}
  }

  public syncCustomers = async () => {
    const res = await this.http.get(`vendors/customers`)

    if (res.status === 200) {
      return res.data
    }

    return []
  }

  public syncCustomer = async (customerId: string) => {
    const res = await this.http.get(`vendors/customers/${customerId}`)

    if (res.status === 200) {
      return res.data
    }

    return {}
  }

  public syncStaff = async (from = 1) => {
    try {
      await this.authorize()

      const { data, status } = await this.http.get(`waiters?page=${from}&per=20`)

      if (status === 200) {
        const { data: waiters, meta } = data

        if (meta.currentPage === meta.lastPage) {
          await Redis.set(`lastUserSaved${this.vendorId}`, 1)
        }

        const mapped = waiters?.map((waiter) => ({
          email: `${waiter.name.replace(' ', '').toLowerCase()}@${waiter.userId}.com`,
          phone: `${waiter.wTno}${waiter.passwd}`,
          firstName: waiter.name.toLowerCase(),
          lastName: '',
          // name: 'SAIDA',
          gender: waiter.gender,
          // gender: null,
          // idNum: null,
          idpass: waiter.idNum,
          // amount: null,
          // qty: null,
          // dateOfDep: '2023-10-31T00:00:00.000+00:00',
          password: waiter.passwd,
          // passwd: '849675',
          // allowAll: 'YES',
          // printOrder: 'YES',
          // printBill: 'YES',
          // clearBill: 'YES',
          // voidBill: 'NO',
          // mergeBill: 'NO',
          // splitBill: 'NO',
          // editBill: 'NO',
          // cardNo: '0',
          // maxPending: 0,
          // comPerc: 0,
          // numOfBills: 0,
          // addToPrinted: 'NO',
          // barCode: 0,
          // allowAddAll: 'NO',
          // accessFunctions: 'NO',
          // inOutStatus: 'OUT',
          // inOutTime: '5/31/2017 4:47:35 PM @STATION coffeeshop',
          // accessCreateBill: 'YES',
          // accessInterim: 'YES',
          // accessOrderStatus: 'NO',
          // accessDiscounts: 'NO',
          // selWaiters: 'NO',
          // switchSPoints: 'NO',
          // minimumSales: 0,
          // switchCurr: 'NO',
          // activeStatus: 'YES',
          // seeItemBal: 'NO',
          // allowCancel: 'NO',
          // createdBy: 'NORMAL',
          // lastActive: '2024-04-16T20:48:26.000+00:00',
          // openKey: 'NO',
          // revertBill: 'NO',
          // orderMessage: 'YES',
          // allowExitButton: 'YES',
          // createdAt: '2024-04-17T20:28:31.993+00:00',
          // updatedAt: '2024-04-17T20:28:31.993+00:00',
          // userId: waiter.userId,
          meta: { ref: waiter.wTno },
        }))

        return mapped
      }

      return data
    } catch (error) {}
  }
}
