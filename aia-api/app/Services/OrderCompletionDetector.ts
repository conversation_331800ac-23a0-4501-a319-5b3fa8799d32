import Order from '../Models/Order'
import OrderItem from '../Models/OrderItem'
import OrderItemModifier from '../Models/OrderItemModifier'
import { DateTime } from 'luxon'
import Event from '@ioc:Adonis/Core/Event'

export interface CompletionCheckResult {
  orderCompleted: boolean
  itemsCompleted: boolean
  modifiersCompleted: boolean
  statusChanged: boolean
  previousStatus: string
  newStatus: string
  completionTime?: DateTime
  pendingItems: number
  readyItems: number
  totalItems: number
  departmentBreakdown: Record<string, any>
}

/**
 * Service for detecting and managing order completion based on item and modifier status
 */
export default class OrderCompletionDetector {
  
  /**
   * Check if an order should be marked as completed and update status accordingly
   */
  public static async checkOrderCompletion(orderId: string): Promise<CompletionCheckResult> {
    const order = await Order.find(orderId)
    if (!order) {
      throw new Error(`Order ${orderId} not found`)
    }

    // Load all necessary relationships
    await order.load('items', (itemQuery) => {
      itemQuery.preload('modifiers')
      itemQuery.preload('department')
    })

    const previousStatus = order.status
    let statusChanged = false
    let newStatus = order.status

    // Analyze completion status
    const analysis = this.analyzeOrderCompletion(order)

    // Determine if order should be updated
    if (analysis.shouldMarkAsReady && order.status !== 'Ready') {
      newStatus = 'Ready'
      order.status = 'Ready'
      statusChanged = true
      
      // Set completion time
      if (!order.meta?.completion_time) {
        const meta = order.meta || {}
        meta.completion_time = DateTime.now().toISO()
        order.meta = meta
      }

      await order.save()

      // Trigger completion events
      await this.triggerOrderCompletionEvents(order, analysis)
    }

    return {
      orderCompleted: analysis.shouldMarkAsReady,
      itemsCompleted: analysis.allItemsReady,
      modifiersCompleted: analysis.allModifiersCompleted,
      statusChanged,
      previousStatus,
      newStatus,
      completionTime: statusChanged ? DateTime.now() : undefined,
      pendingItems: analysis.pendingItems,
      readyItems: analysis.readyItems,
      totalItems: analysis.totalItems,
      departmentBreakdown: analysis.departmentBreakdown
    }
  }

  /**
   * Check completion for a specific order item and cascade to order level
   */
  public static async checkItemCompletion(itemId: number): Promise<CompletionCheckResult> {
    const orderItem = await OrderItem.find(itemId)
    if (!orderItem) {
      throw new Error(`Order item ${itemId} not found`)
    }

    // Load modifiers to check completion
    await orderItem.load('modifiers')

    // Check if all modifiers are completed
    const allModifiersCompleted = orderItem.modifiers.every(modifier => 
      ['completed', 'skipped'].includes(modifier.status)
    )

    // If item is preparing and all modifiers are done, mark item as ready
    if (orderItem.status === 'preparing' && allModifiersCompleted) {
      await orderItem.completePreparation()
    }

    // Check parent order completion
    return await this.checkOrderCompletion(orderItem.orderId)
  }

  /**
   * Check completion for a specific modifier and cascade up
   */
  public static async checkModifierCompletion(modifierId: number): Promise<CompletionCheckResult> {
    const modifier = await OrderItemModifier.find(modifierId)
    if (!modifier) {
      throw new Error(`Order item modifier ${modifierId} not found`)
    }

    await modifier.load('orderItem')
    
    // Check parent item completion
    return await this.checkItemCompletion(modifier.orderItemId)
  }

  /**
   * Analyze order completion status
   */
  private static analyzeOrderCompletion(order: Order): {
    shouldMarkAsReady: boolean
    allItemsReady: boolean
    allModifiersCompleted: boolean
    pendingItems: number
    readyItems: number
    totalItems: number
    departmentBreakdown: Record<string, any>
  } {
    const totalItems = order.items.length
    let readyItems = 0
    let pendingItems = 0
    const departmentBreakdown = {}

    let allItemsReady = true
    let allModifiersCompleted = true

    for (const item of order.items) {
      // Count items by status
      if (['ready', 'served'].includes(item.status)) {
        readyItems++
      } else if (item.status === 'pending') {
        pendingItems++
        allItemsReady = false
      } else {
        allItemsReady = false
      }

      // Check modifier completion
      const itemModifiersCompleted = item.modifiers.every(modifier => 
        ['completed', 'skipped'].includes(modifier.status)
      )
      
      if (!itemModifiersCompleted) {
        allModifiersCompleted = false
      }

      // Department breakdown
      if (item.department) {
        const deptName = item.department.name
        if (!departmentBreakdown[deptName]) {
          departmentBreakdown[deptName] = {
            total: 0,
            ready: 0,
            pending: 0,
            preparing: 0
          }
        }
        departmentBreakdown[deptName].total++
        departmentBreakdown[deptName][item.status]++
      }
    }

    // Order should be marked as ready if all items are ready/served and all modifiers completed
    const shouldMarkAsReady = totalItems > 0 && 
      order.items.every(item => 
        ['ready', 'served'].includes(item.status) && 
        item.modifiers.every(modifier => ['completed', 'skipped'].includes(modifier.status))
      )

    return {
      shouldMarkAsReady,
      allItemsReady,
      allModifiersCompleted,
      pendingItems,
      readyItems,
      totalItems,
      departmentBreakdown
    }
  }

  /**
   * Trigger events when order is completed
   */
  private static async triggerOrderCompletionEvents(order: Order, analysis: any): Promise<void> {
    // Emit order completion event
    Event.emit('order:completed', {
      order,
      completionTime: DateTime.now(),
      analysis
    })

    // Emit customer notification event
    Event.emit('customer:order:ready', {
      orderId: order.id,
      customerId: order.userId,
      orderType: order.type,
      delivery: order.delivery,
      estimatedReadyTime: DateTime.now()
    })

    // Emit staff notification event
    Event.emit('staff:order:ready', {
      orderId: order.id,
      vendorId: order.vendorId,
      branchId: order.branchId,
      orderType: order.type,
      delivery: order.delivery
    })

    // Log completion for analytics
    console.log(`Order ${order.id} marked as ready at ${DateTime.now().toISO()}`, {
      totalItems: analysis.totalItems,
      departmentBreakdown: analysis.departmentBreakdown
    })
  }

  /**
   * Bulk check completion for multiple orders
   */
  public static async bulkCheckOrderCompletion(orderIds: string[]): Promise<CompletionCheckResult[]> {
    const results = []
    
    for (const orderId of orderIds) {
      try {
        const result = await this.checkOrderCompletion(orderId)
        results.push(result)
      } catch (error) {
        console.error(`Error checking completion for order ${orderId}:`, error.message)
        results.push({
          orderCompleted: false,
          itemsCompleted: false,
          modifiersCompleted: false,
          statusChanged: false,
          previousStatus: 'unknown',
          newStatus: 'unknown',
          pendingItems: 0,
          readyItems: 0,
          totalItems: 0,
          departmentBreakdown: {},
          error: error.message
        })
      }
    }

    return results
  }

  /**
   * Get orders that are ready for completion check
   */
  public static async getOrdersReadyForCompletion(): Promise<Order[]> {
    return await Order.query()
      .whereIn('status', ['Processing', 'Accepted'])
      .whereHas('items', (itemQuery) => {
        itemQuery.whereIn('status', ['ready', 'served'])
      })
      .preload('items', (itemQuery) => {
        itemQuery.preload('modifiers')
        itemQuery.preload('department')
      })
      .exec()
  }

  /**
   * Scheduled task to check all pending orders for completion
   */
  public static async scheduledCompletionCheck(): Promise<{
    checked: number
    completed: number
    errors: number
  }> {
    const orders = await this.getOrdersReadyForCompletion()
    let completed = 0
    let errors = 0

    for (const order of orders) {
      try {
        const result = await this.checkOrderCompletion(order.id)
        if (result.statusChanged) {
          completed++
        }
      } catch (error) {
        console.error(`Scheduled completion check error for order ${order.id}:`, error.message)
        errors++
      }
    }

    console.log(`Scheduled completion check: ${orders.length} checked, ${completed} completed, ${errors} errors`)

    return {
      checked: orders.length,
      completed,
      errors
    }
  }

  /**
   * Get completion statistics for analytics
   */
  public static async getCompletionStatistics(vendorId?: string, startDate?: DateTime, endDate?: DateTime): Promise<{
    totalOrders: number
    completedOrders: number
    averageCompletionTime: number
    departmentPerformance: Record<string, any>
  }> {
    let query = Order.query()
      .preload('items', (itemQuery) => {
        itemQuery.preload('department')
      })

    if (vendorId) {
      query = query.where('vendor_id', vendorId)
    }

    if (startDate) {
      query = query.where('created_at', '>=', startDate.toSQL())
    }

    if (endDate) {
      query = query.where('created_at', '<=', endDate.toSQL())
    }

    const orders = await query.exec()

    const totalOrders = orders.length
    const completedOrders = orders.filter(order => order.status === 'Ready').length

    // Calculate average completion time
    const completedOrdersWithTime = orders.filter(order => 
      order.status === 'Ready' && order.meta?.completion_time
    )

    let averageCompletionTime = 0
    if (completedOrdersWithTime.length > 0) {
      const totalCompletionTime = completedOrdersWithTime.reduce((sum, order) => {
        const completionTime = DateTime.fromISO(order.meta.completion_time)
        const orderTime = order.createdAt
        return sum + completionTime.diff(orderTime, 'minutes').minutes
      }, 0)
      
      averageCompletionTime = totalCompletionTime / completedOrdersWithTime.length
    }

    // Department performance analysis
    const departmentPerformance = {}
    orders.forEach(order => {
      order.items.forEach(item => {
        if (item.department) {
          const deptName = item.department.name
          if (!departmentPerformance[deptName]) {
            departmentPerformance[deptName] = {
              totalItems: 0,
              completedItems: 0,
              averageTime: 0
            }
          }
          
          departmentPerformance[deptName].totalItems++
          if (['ready', 'served'].includes(item.status)) {
            departmentPerformance[deptName].completedItems++
          }
        }
      })
    })

    return {
      totalOrders,
      completedOrders,
      averageCompletionTime: Math.round(averageCompletionTime * 100) / 100,
      departmentPerformance
    }
  }
}
