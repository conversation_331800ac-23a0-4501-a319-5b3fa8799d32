import { DateTime } from 'luxon'
import Logger from '@ioc:Adonis/Core/Logger'
import Database from '@ioc:Adonis/Lucid/Database'
import User from 'App/Models/User'
import SubscriptionPlan from 'App/Models/SubscriptionPlan'
import CustomerSubscription from 'App/Models/CustomerSubscription'
import CustomerUsage from 'App/Models/CustomerUsage'
import FeatureFlagService from './FeatureFlagService'

export default class SubscriptionService {
  /**
   * Check if billing is enabled
   */
  private static async isBillingEnabled(): Promise<boolean> {
    return await FeatureFlagService.isEnabled('billing_system')
  }

  /**
   * Check if specific billing type is enabled
   */
  private static async isBillingTypeEnabled(type: 'subscription' | 'usage' | 'campaign' | 'notification' | 'vendor'): Promise<boolean> {
    const mainBillingEnabled = await this.isBillingEnabled()
    if (!mainBillingEnabled) return false

    return await FeatureFlagService.isEnabled(`${type}_billing`)
  }

  /**
   * Create a new subscription for a customer
   */
  public static async createSubscription(
    customer: User,
    plan: SubscriptionPlan,
    options: {
      startDate?: DateTime
      endDate?: DateTime
      autoRenew?: boolean
      paymentMethodId?: string
    } = {}
  ): Promise<CustomerSubscription> {
    // Check if subscription billing is enabled
    if (!await this.isBillingTypeEnabled('subscription')) {
      throw new Error('Subscription billing is currently disabled')
    }

    const trx = await Database.transaction()

    try {
      // Validate required plan properties
      if (!plan.billingCycle) {
        throw new Error('Plan billing cycle is required')
      }
      if (plan.amount === null) {
        throw new Error('Plan amount is required')
      }
      if (!plan.currency) {
        throw new Error('Plan currency is required')
      }

      const startDate = options.startDate || DateTime.now()
      const endDate = options.endDate || this.calculateEndDate(startDate, plan.billingCycle)

      const subscription = await CustomerSubscription.create({
        customerId: customer.id,
        planId: plan.id,
        status: 'active',
        startDate,
        endDate,
        billingCycle: plan.billingCycle,
        amount: plan.amount,
        currency: plan.currency,
        autoRenew: options.autoRenew ?? true,
        paymentMethodId: options.paymentMethodId,
        nextBillingAt: endDate,
      })

      await trx.commit()

      Logger.info('Subscription created', {
        customer_id: customer.id,
        plan_id: plan.id,
        subscription_id: subscription.id,
      })

      return subscription
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to create subscription', {
        error,
        customer_id: customer.id,
        plan_id: plan.id,
      })
      throw error
    }
  }

  /**
   * Cancel a subscription
   */
  public static async cancelSubscription(
    subscription: CustomerSubscription,
    options: { immediate?: boolean } = {}
  ): Promise<void> {
    const trx = await Database.transaction()

    try {
      if (options.immediate) {
        subscription.status = 'cancelled'
        subscription.endDate = DateTime.now()
        subscription.autoRenew = false
      } else {
        subscription.status = 'cancelled'
        subscription.autoRenew = false
      }

      await subscription.save()
      await trx.commit()

      Logger.info('Subscription cancelled', {
        subscription_id: subscription.id,
        immediate: options.immediate,
      })
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to cancel subscription', {
        error,
        subscription_id: subscription.id,
      })
      throw error
    }
  }

  /**
   * Record usage for a customer
   */
  public static async recordUsage(
    customer: User,
    type: 'order' | 'feature' | 'notification',
    options: {
      subscriptionId?: number
      featureCode?: string
      quantity?: number
      amount?: number
      currency?: string
      referenceId?: string
      referenceType?: string
      meta?: Record<string, any>
    } = {}
  ): Promise<CustomerUsage | null> {
    // Check if usage billing is enabled
    if (!await this.isBillingTypeEnabled('usage')) {
      Logger.info('Usage billing is disabled, skipping usage recording', {
        customerId: customer.id,
        type,
        options
      })
      return null
    }

    const trx = await Database.transaction()

    try {
      const usage = await CustomerUsage.create({
        customerId: customer.id,
        subscriptionId: options.subscriptionId,
        type,
        featureCode: options.featureCode,
        quantity: options.quantity || 1,
        amount: options.amount || 0,
        currency: options.currency || 'KES',
        billingPeriod: DateTime.now().toFormat('yyyy-MM'),
        status: 'pending',
        meta: options.meta,
      })

      await trx.commit()

      Logger.info('Usage recorded', {
        customer_id: customer.id,
        type,
        usage_id: usage.id,
      })

      return usage
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to record usage', {
        error,
        customer_id: customer.id,
        type,
      })
      throw error
    }
  }

  /**
   * Get customer's current subscription
   */
  public static async getCurrentSubscription(customer: User): Promise<CustomerSubscription | null> {
    // Check if subscription billing is enabled
    if (!await this.isBillingTypeEnabled('subscription')) {
      return null
    }

    return await CustomerSubscription.query()
      .where('customer_id', customer.id)
      .where('status', 'active')
      .where('end_date', '>', DateTime.now().toSQL())
      .preload('plan')
      .first()
  }

  /**
   * Get customer's usage for a billing period
   */
  public static async getCustomerUsage(
    customer: User,
    billingPeriod: string = DateTime.now().toFormat('yyyy-MM')
  ): Promise<{
    total: number
    byType: Record<string, number>
    items: CustomerUsage[]
  }> {
    // Check if usage billing is enabled
    if (!await this.isBillingTypeEnabled('usage')) {
      return {
        total: 0,
        byType: {},
        items: [],
      }
    }

    const usages = await CustomerUsage.query()
      .where('customer_id', customer.id)
      .where('billing_period', billingPeriod)
      .orderBy('created_at', 'asc')

    const byType = usages.reduce((acc, usage) => {
      acc[usage.type] = (acc[usage.type] || 0) + usage.amount
      return acc
    }, {} as Record<string, number>)

    const total = usages.reduce((sum, usage) => sum + usage.amount, 0)

    return {
      total,
      byType,
      items: usages,
    }
  }

  /**
   * Calculate end date based on billing cycle
   */
  private static calculateEndDate(startDate: DateTime, billingCycle: 'monthly' | 'yearly'): DateTime {
    if (billingCycle === 'monthly') {
      return startDate.plus({ months: 1 })
    }
    return startDate.plus({ years: 1 })
  }
}