import { DateTime } from 'luxon'
import Logger from '@ioc:Adonis/Core/Logger'
import Campaign from 'App/Models/Campaign'
import Database from '@ioc:Adonis/Lucid/Database'
import ChargeConfiguration from 'App/Models/ChargeConfiguration'

export default class CampaignBillingService {
  /**
   * Create or update campaign charge configuration
   */
  public static async setupCampaignCharges(campaign: Campaign): Promise<void> {
    const trx = await Database.transaction()

    try {
      // Create or update charge configuration
      const chargeConfig = await ChargeConfiguration.query({ client: trx })
        .where('code', `campaign_${campaign.id}`)
        .first()

      if (chargeConfig) {
        // Update existing configuration
        chargeConfig.merge({
          name: `Campaign: ${campaign.name}`,
          description: `Billing configuration for campaign: ${campaign.name}`,
          type: campaign.billingType,
          amount: campaign.billingAmount,
          percentageRate: campaign.billingType === 'percentage' ? campaign.billingAmount : null,
          vendorId: campaign.vendorId,
          branchId: campaign.branchId,
          isActive: true,
          currency: campaign.currency,
          meta: {
            campaign_id: campaign.id,
            display_duration: campaign.displayDuration,
            position: campaign.position,
          },
        })
        await chargeConfig.save()
      } else {
        // Create new configuration
        await ChargeConfiguration.create({
          name: `Campaign: ${campaign.name}`,
          code: `campaign_${campaign.id}`,
          description: `Billing configuration for campaign: ${campaign.name}`,
          type: campaign.billingType,
          amount: campaign.billingAmount,
          percentageRate: campaign.billingType === 'percentage' ? campaign.billingAmount : null,
          vendorId: campaign.vendorId,
          branchId: campaign.branchId,
          isActive: true,
          currency: campaign.currency,
          meta: {
            campaign_id: campaign.id,
            display_duration: campaign.displayDuration,
            position: campaign.position,
          },
        })
      }

      Logger.info('Campaign charge configuration created/updated', {
        campaign_id: campaign.id,
      })

      await trx.commit()
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to setup campaign charges', {
        error,
        campaign_id: campaign.id,
      })
      throw error
    }
  }

  /**
   * Record campaign charge
   */
  public static async recordCharge(campaign: Campaign): Promise<void> {
    const trx = await Database.transaction()

    try {
      // Get campaign charge configuration
      const chargeConfig = await ChargeConfiguration.query({ client: trx })
        .where('code', `campaign_${campaign.id}`)
        .first()

      if (!chargeConfig) {
        throw new Error(`No charge configuration found for campaign ${campaign.id}`)
      }

      // Calculate charge amount
      const amount = chargeConfig.type === 'fixed' 
        ? chargeConfig.amount 
        : (campaign.billingAmount * (chargeConfig.percentageRate ?? 0)) / 100

      // Create charge record
      await Database.table('charges').insert({
        vendor_id: campaign.vendorId,
        type: chargeConfig.type,
        amount,
        currency: campaign.currency,
        status: 'Pending',
        meta: {
          campaign_id: campaign.id,
          charge_config_id: chargeConfig.id,
          display_duration: campaign.displayDuration,
          position: campaign.position,
        },
        created_at: new Date(),
        updated_at: new Date(),
      })

      Logger.info('Campaign charge recorded', {
        campaign_id: campaign.id,
        amount,
        currency: campaign.currency,
      })

      await trx.commit()
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to record campaign charge', {
        error,
        campaign_id: campaign.id,
      })
      throw error
    }
  }

  /**
   * Generate campaign bills for a period
   */
  public static async generateBills(billingPeriod: string = DateTime.now().toFormat('yyyy-MM')): Promise<void> {
    const trx = await Database.transaction()

    try {
      // Get all approved campaigns for the period
      const campaigns = await Campaign.query({ client: trx })
        .where('status', 'Approved')
        .whereBetween('startDate', [
          DateTime.fromFormat(billingPeriod, 'yyyy-MM').startOf('month').toSQL(),
          DateTime.fromFormat(billingPeriod, 'yyyy-MM').endOf('month').toSQL(),
        ])

      for (const campaign of campaigns) {
        await this.recordCharge(campaign)
      }

      Logger.info('Campaign bills generated', {
        billing_period: billingPeriod,
        campaign_count: campaigns.length,
      })

      await trx.commit()
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to generate campaign bills', {
        error,
        billing_period: billingPeriod,
      })
      throw error
    }
  }
} 