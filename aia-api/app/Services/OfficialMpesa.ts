import Payment from 'App/Models/Payment'
import MpesaConfigValidator from './MpesaConfigValidator'

/**
 * Official Safaricom M-Pesa Service
 * 
 * This service uses the official Safaricom mpesa-node library
 * for better reliability, comprehensive API coverage, and official support.
 */
export default class OfficialMpesaService {
  private mpesaApi: any
  private baseUrl: string

  constructor(_payment?: Payment) {
    // Validate configuration first
    console.log('=== OFFICIAL MPESA INITIALIZATION ===')
    MpesaConfigValidator.logConfigStatus()
    MpesaConfigValidator.validateOrThrow()

    this.baseUrl = process.env.MPESA_CALLBACK_DOMAIN || process.env.APP_URL || 'https://uat-api.appinapp.ke'
    
    // Import the official library dynamically
    const Mpesa = require('mpesa-node')
    
    // Initialize with official library configuration
    this.mpesaApi = new Mpesa({
      consumerKey: process.env.MPESA_CONSUMER_KEY!,
      consumerSecret: process.env.MPESA_CONSUMER_SECRET!,
      environment: this.mapEnvironment(process.env.MPESA_ENVIRONMENT!),
      shortCode: process.env.MPESA_SHORTCODE!,
      initiatorName: process.env.MPESA_INITIATOR_NAME || 'testapi',
      lipaNaMpesaShortCode: process.env.MPESA_LIPA_NA_MPESA_SHORTCODE || process.env.MPESA_SHORTCODE!,
      lipaNaMpesaShortPass: process.env.MPESA_PASSKEY!,
      securityCredential: process.env.MPESA_SECURITY_CREDENTIAL || '',
      certPath: process.env.MPESA_CERT_PATH || undefined
    })

    console.log('Official M-Pesa API initialized successfully')
    console.log('Environment:', this.mapEnvironment(process.env.MPESA_ENVIRONMENT!))
    console.log('Base URL:', this.baseUrl)
    console.log('Short Code:', process.env.MPESA_SHORTCODE)
  }

  /**
   * Map environment variable to official library format
   */
  private mapEnvironment(env: string): string {
    return env === 'live' ? 'production' : 'sandbox'
  }

  /**
   * Initiate STK Push (Lipa Na M-Pesa Online)
   */
  public async stkPush(
    phoneNumber: string, 
    amount: string, 
    accountReference: string, 
    transactionDescription: string = 'Payment'
  ) {
    console.log('=== OFFICIAL MPESA STK PUSH REQUEST ===')
    console.log('Request Details:', {
      phone: phoneNumber.replace(/(\d{3})\d{6}(\d{3})/, '$1******$2'), // Mask phone for security
      amount,
      accountReference,
      transactionDescription,
      environment: process.env.MPESA_ENVIRONMENT,
      timestamp: new Date().toISOString()
    })

    // Validate inputs
    this.validateStkPushInputs(phoneNumber, amount, accountReference)

    try {
      // Construct callback URL
      const callbackUrl = `${this.baseUrl}/v1/mpsa/e59ed6a68b83/confirm`
      
      console.log('Callback URL:', callbackUrl)

      // Call official library method
      const response = await this.mpesaApi.lipaNaMpesaOnline(
        parseInt(phoneNumber), // Official library expects number
        parseInt(amount),
        callbackUrl,
        accountReference,
        transactionDescription
      )

      console.log('=== OFFICIAL MPESA STK PUSH RESPONSE ===')
      console.log('Response Status:', response ? 'SUCCESS' : 'ERROR')
      console.log('Response:', JSON.stringify(response, null, 2))

      // Transform response to match current interface
      return {
        data: response,
        error: null
      }

    } catch (error) {
      console.error('=== OFFICIAL MPESA STK PUSH ERROR ===')
      console.error('Error Type:', error.constructor.name)
      console.error('Error Message:', error.message)
      console.error('Error Details:', error)

      return {
        data: null,
        error: {
          message: error.message,
          details: error
        }
      }
    }
  }

  /**
   * Query STK Push transaction status
   */
  public async querySTKPushStatus(checkoutRequestId: string) {
    console.log('=== MPESA STK PUSH STATUS QUERY ===')
    console.log('Checkout Request ID:', checkoutRequestId)

    try {
      const response = await this.mpesaApi.lipaNaMpesaQuery(checkoutRequestId)
      
      console.log('=== STK PUSH STATUS RESPONSE ===')
      console.log('Response:', JSON.stringify(response, null, 2))

      return {
        data: response,
        error: null
      }

    } catch (error) {
      console.error('=== STK PUSH STATUS QUERY ERROR ===')
      console.error('Error:', error.message)

      return {
        data: null,
        error: {
          message: error.message,
          details: error
        }
      }
    }
  }

  /**
   * Register C2B URLs
   */
  public async registerC2BUrls() {
    console.log('=== REGISTERING C2B URLS ===')

    const confirmationUrl = `${this.baseUrl}/v1/mpsa/e59ed6a68b83/confirm`
    const validationUrl = `${this.baseUrl}/v1/mpsa/e59ed6a68b83/validate`

    console.log('Confirmation URL:', confirmationUrl)
    console.log('Validation URL:', validationUrl)

    try {
      const response = await this.mpesaApi.c2bRegister(confirmationUrl, validationUrl)
      
      console.log('=== C2B REGISTRATION RESPONSE ===')
      console.log('Response:', JSON.stringify(response, null, 2))

      return {
        data: response,
        error: null
      }

    } catch (error) {
      console.error('=== C2B REGISTRATION ERROR ===')
      console.error('Error:', error.message)

      return {
        data: null,
        error: {
          message: error.message,
          details: error
        }
      }
    }
  }

  /**
   * Simulate C2B transaction (for testing)
   */
  public async simulateC2B(phoneNumber: string, amount: string, billRefNumber: string) {
    console.log('=== SIMULATING C2B TRANSACTION ===')
    console.log('Phone:', phoneNumber.replace(/(\d{3})\d{6}(\d{3})/, '$1******$2'))
    console.log('Amount:', amount)
    console.log('Bill Ref:', billRefNumber)

    try {
      const response = await this.mpesaApi.c2bSimulate(
        parseInt(phoneNumber),
        parseInt(amount),
        billRefNumber
      )

      console.log('=== C2B SIMULATION RESPONSE ===')
      console.log('Response:', JSON.stringify(response, null, 2))

      return {
        data: response,
        error: null
      }

    } catch (error) {
      console.error('=== C2B SIMULATION ERROR ===')
      console.error('Error:', error.message)

      return {
        data: null,
        error: {
          message: error.message,
          details: error
        }
      }
    }
  }

  /**
   * Check account balance
   */
  public async checkAccountBalance() {
    console.log('=== CHECKING ACCOUNT BALANCE ===')

    const queueUrl = `${this.baseUrl}/v1/mpsa/e59ed6a68b83/timeout`
    const resultUrl = `${this.baseUrl}/v1/mpsa/e59ed6a68b83/result`

    try {
      const response = await this.mpesaApi.accountBalance(
        process.env.MPESA_SHORTCODE!,
        4, // Organization identifier type
        queueUrl,
        resultUrl
      )

      console.log('=== ACCOUNT BALANCE RESPONSE ===')
      console.log('Response:', JSON.stringify(response, null, 2))

      return {
        data: response,
        error: null
      }

    } catch (error) {
      console.error('=== ACCOUNT BALANCE ERROR ===')
      console.error('Error:', error.message)

      return {
        data: null,
        error: {
          message: error.message,
          details: error
        }
      }
    }
  }

  /**
   * Validate STK Push inputs
   */
  private validateStkPushInputs(phoneNumber: string, amount: string, accountReference: string) {
    if (!phoneNumber || !amount || !accountReference) {
      throw new Error('Missing required parameters for STK Push')
    }

    // Validate phone number format (Kenyan format)
    const phoneRegex = /^254\d{9}$/
    if (!phoneRegex.test(phoneNumber)) {
      throw new Error('Invalid phone number format. Expected format: 254XXXXXXXXX')
    }

    // Validate amount
    const numericAmount = parseInt(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      throw new Error('Invalid amount. Amount must be a positive number')
    }

    // Validate account reference
    if (accountReference.length < 1 || accountReference.length > 12) {
      throw new Error('Account reference must be between 1 and 12 characters')
    }
  }

  /**
   * Get service configuration summary
   */
  public getConfigSummary() {
    return {
      library: 'official-safaricom-mpesa-node',
      environment: this.mapEnvironment(process.env.MPESA_ENVIRONMENT!),
      shortCode: process.env.MPESA_SHORTCODE,
      baseUrl: this.baseUrl,
      callbackUrls: {
        confirmation: `${this.baseUrl}/v1/mpsa/e59ed6a68b83/confirm`,
        validation: `${this.baseUrl}/v1/mpsa/e59ed6a68b83/validate`,
        timeout: `${this.baseUrl}/v1/mpsa/e59ed6a68b83/timeout`,
        result: `${this.baseUrl}/v1/mpsa/e59ed6a68b83/result`
      },
      timestamp: new Date().toISOString()
    }
  }
}
