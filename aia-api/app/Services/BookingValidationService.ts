import { DateTime } from 'luxon'
import Booking, { BookingStatus, SelectedServiceOption, StaffAssignment, EquipmentReservation } from 'App/Models/Booking'
import Product from 'App/Models/Product'
import Branch from 'App/Models/Branch'
import User from 'App/Models/User'
import OptionResolutionService from './OptionResolutionService'

export interface BookingValidationRequest {
  productId: string
  branchId: string
  customerId: string
  appointmentStart: DateTime
  appointmentEnd: DateTime
  selectedServiceOptions: SelectedServiceOption[]
  staffAssignments?: StaffAssignment[]
  equipmentReservations?: EquipmentReservation[]
  excludeBookingId?: string // For updates
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  conflicts?: {
    timeConflicts: Booking[]
    staffConflicts: string[]
    equipmentConflicts: string[]
  }
}

export default class BookingValidationService {
  /**
   * Comprehensive validation of a booking request
   */
  public static async validateBookingRequest(request: BookingValidationRequest): Promise<ValidationResult> {
    const errors: string[] = []
    const warnings: string[] = []
    const conflicts = {
      timeConflicts: [] as Booking[],
      staffConflicts: [] as string[],
      equipmentConflicts: [] as string[]
    }

    try {
      // Basic validation
      await this.validateBasicRequirements(request, errors)
      
      // Time validation
      await this.validateAppointmentTimes(request, errors, warnings)
      
      // Product and service options validation
      await this.validateProductAndOptions(request, errors, warnings)
      
      // Resource availability validation
      const resourceConflicts = await this.validateResourceAvailability(request, errors)
      conflicts.timeConflicts = resourceConflicts.timeConflicts
      conflicts.staffConflicts = resourceConflicts.staffConflicts
      conflicts.equipmentConflicts = resourceConflicts.equipmentConflicts
      
      // Business rules validation
      await this.validateBusinessRules(request, errors, warnings)

    } catch (error) {
      errors.push(`Validation error: ${error.message}`)
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      conflicts: errors.length > 0 ? conflicts : undefined
    }
  }

  /**
   * Validate basic requirements
   */
  private static async validateBasicRequirements(request: BookingValidationRequest, errors: string[]): Promise<void> {
    // Check required fields
    if (!request.productId) {
      errors.push('Product ID is required')
    }
    
    if (!request.branchId) {
      errors.push('Branch ID is required')
    }
    
    if (!request.customerId) {
      errors.push('Customer ID is required')
    }
    
    if (!request.appointmentStart) {
      errors.push('Appointment start time is required')
    }
    
    if (!request.appointmentEnd) {
      errors.push('Appointment end time is required')
    }

    // Verify entities exist
    try {
      await Product.findOrFail(request.productId)
    } catch {
      errors.push('Product not found')
    }

    try {
      await Branch.findOrFail(request.branchId)
    } catch {
      errors.push('Branch not found')
    }

    try {
      await User.findOrFail(request.customerId)
    } catch {
      errors.push('Customer not found')
    }
  }

  /**
   * Validate appointment times
   */
  private static async validateAppointmentTimes(
    request: BookingValidationRequest, 
    errors: string[], 
    warnings: string[]
  ): Promise<void> {
    const { appointmentStart, appointmentEnd } = request

    // Basic time validation
    if (appointmentStart >= appointmentEnd) {
      errors.push('Appointment start time must be before end time')
      return
    }

    // Check if appointment is in the past
    const now = DateTime.now()
    if (appointmentStart <= now) {
      errors.push('Cannot book appointments in the past')
    }

    // Check minimum advance booking time (default 2 hours)
    const minAdvanceTime = now.plus({ hours: 2 })
    if (appointmentStart < minAdvanceTime) {
      errors.push('Appointments must be booked at least 2 hours in advance')
    }

    // Check maximum advance booking time (default 30 days)
    const maxAdvanceTime = now.plus({ days: 30 })
    if (appointmentStart > maxAdvanceTime) {
      warnings.push('Booking is more than 30 days in advance')
    }

    // Validate appointment duration
    const durationMinutes = appointmentEnd.diff(appointmentStart, 'minutes').minutes
    if (durationMinutes < 15) {
      errors.push('Appointment duration must be at least 15 minutes')
    }
    
    if (durationMinutes > 480) { // 8 hours
      warnings.push('Appointment duration is longer than 8 hours')
    }
  }

  /**
   * Validate product and service options
   */
  private static async validateProductAndOptions(
    request: BookingValidationRequest,
    errors: string[],
    warnings: string[]
  ): Promise<void> {
    try {
      // Load product with service options
      const resolvedOptions = await OptionResolutionService.resolveProductOptions(request.productId)
      
      // Validate selected options exist and are valid
      const selectedOptionIds = request.selectedServiceOptions.map(opt => opt.id)
      const validOptionIds = resolvedOptions.allOptions.map(opt => opt.id)
      
      const invalidOptions = selectedOptionIds.filter(id => !validOptionIds.includes(id))
      if (invalidOptions.length > 0) {
        errors.push(`Invalid service options: ${invalidOptions.join(', ')}`)
      }

      // Validate option constraints and dependencies
      const selectedResolvedOptions = resolvedOptions.allOptions.filter(opt => 
        selectedOptionIds.includes(opt.id)
      )
      
      const optionValidation = OptionResolutionService.validateOptionSelection(
        selectedResolvedOptions,
        resolvedOptions.allOptions
      )
      
      errors.push(...optionValidation.errors)
      warnings.push(...optionValidation.warnings)

      // Validate duration matches selected duration options
      const durationOptions = selectedResolvedOptions.filter(opt => opt.type === 'duration')
      if (durationOptions.length > 0) {
        const expectedDuration = durationOptions.reduce((total, opt) => 
          total + (opt.duration?.minutes || 0), 0
        )
        
        const actualDuration = request.appointmentEnd.diff(request.appointmentStart, 'minutes').minutes
        const tolerance = 5 // 5-minute tolerance
        
        if (Math.abs(actualDuration - expectedDuration) > tolerance) {
          errors.push(`Appointment duration (${actualDuration} min) does not match selected duration options (${expectedDuration} min)`)
        }
      }

    } catch (error) {
      errors.push(`Failed to validate service options: ${error.message}`)
    }
  }

  /**
   * Validate resource availability (time slots, staff, equipment)
   */
  private static async validateResourceAvailability(
    request: BookingValidationRequest,
    errors: string[]
  ): Promise<{
    timeConflicts: Booking[]
    staffConflicts: string[]
    equipmentConflicts: string[]
  }> {
    const conflicts = {
      timeConflicts: [] as Booking[],
      staffConflicts: [] as string[],
      equipmentConflicts: [] as string[]
    }

    // Check for time conflicts with existing bookings
    const timeConflicts = await this.checkTimeConflicts(request)
    if (timeConflicts.length > 0) {
      conflicts.timeConflicts = timeConflicts
      errors.push(`Time slot conflicts with ${timeConflicts.length} existing booking(s)`)
    }

    // Check staff availability
    if (request.staffAssignments && request.staffAssignments.length > 0) {
      const staffConflicts = await this.checkStaffAvailability(request)
      if (staffConflicts.length > 0) {
        conflicts.staffConflicts = staffConflicts
        errors.push(`Staff conflicts: ${staffConflicts.join(', ')}`)
      }
    }

    // Check equipment availability
    if (request.equipmentReservations && request.equipmentReservations.length > 0) {
      const equipmentConflicts = await this.checkEquipmentAvailability(request)
      if (equipmentConflicts.length > 0) {
        conflicts.equipmentConflicts = equipmentConflicts
        errors.push(`Equipment conflicts: ${equipmentConflicts.join(', ')}`)
      }
    }

    return conflicts
  }

  /**
   * Check for time conflicts with existing bookings
   */
  private static async checkTimeConflicts(request: BookingValidationRequest): Promise<Booking[]> {
    const query = Booking.query()
      .where('branchId', request.branchId)
      .whereNotIn('status', [BookingStatus.CANCELLED, BookingStatus.NO_SHOW])
      .where((builder) => {
        // Check for overlapping time ranges
        builder
          .whereBetween('appointmentStart', [request.appointmentStart.toISO(), request.appointmentEnd.toISO()])
          .orWhereBetween('appointmentEnd', [request.appointmentStart.toISO(), request.appointmentEnd.toISO()])
          .orWhere((subQuery) => {
            subQuery
              .where('appointmentStart', '<=', request.appointmentStart.toISO())
              .where('appointmentEnd', '>=', request.appointmentEnd.toISO())
          })
      })

    // Exclude current booking if this is an update
    if (request.excludeBookingId) {
      query.where('id', '!=', request.excludeBookingId)
    }

    return await query
  }

  /**
   * Check staff availability for the requested time
   */
  private static async checkStaffAvailability(request: BookingValidationRequest): Promise<string[]> {
    const conflicts: string[] = []
    
    if (!request.staffAssignments) return conflicts

    for (const assignment of request.staffAssignments) {
      // Check if staff member has conflicting bookings
      const staffConflicts = await Booking.query()
        .whereNotIn('status', [BookingStatus.CANCELLED, BookingStatus.NO_SHOW])
        .whereRaw("JSON_CONTAINS(staff_assignments, JSON_OBJECT('userId', ?))", [assignment.userId])
        .where((builder) => {
          builder
            .whereBetween('appointmentStart', [request.appointmentStart.toISO(), request.appointmentEnd.toISO()])
            .orWhereBetween('appointmentEnd', [request.appointmentStart.toISO(), request.appointmentEnd.toISO()])
            .orWhere((subQuery) => {
              subQuery
                .where('appointmentStart', '<=', request.appointmentStart.toISO())
                .where('appointmentEnd', '>=', request.appointmentEnd.toISO())
            })
        })

      if (request.excludeBookingId) {
        staffConflicts.where('id', '!=', request.excludeBookingId)
      }

      const conflictCount = await staffConflicts.getCount()
      if (conflictCount > 0) {
        conflicts.push(assignment.userId)
      }
    }

    return conflicts
  }

  /**
   * Check equipment availability for the requested time
   */
  private static async checkEquipmentAvailability(request: BookingValidationRequest): Promise<string[]> {
    const conflicts: string[] = []
    
    if (!request.equipmentReservations) return conflicts

    for (const reservation of request.equipmentReservations) {
      // Check if equipment has conflicting bookings
      const equipmentConflicts = await Booking.query()
        .whereNotIn('status', [BookingStatus.CANCELLED, BookingStatus.NO_SHOW])
        .whereRaw("JSON_CONTAINS(equipment_reservations, JSON_OBJECT('equipmentId', ?))", [reservation.equipmentId])
        .where((builder) => {
          builder
            .whereBetween('appointmentStart', [request.appointmentStart.toISO(), request.appointmentEnd.toISO()])
            .orWhereBetween('appointmentEnd', [request.appointmentStart.toISO(), request.appointmentEnd.toISO()])
            .orWhere((subQuery) => {
              subQuery
                .where('appointmentStart', '<=', request.appointmentStart.toISO())
                .where('appointmentEnd', '>=', request.appointmentEnd.toISO())
            })
        })

      if (request.excludeBookingId) {
        equipmentConflicts.where('id', '!=', request.excludeBookingId)
      }

      const conflictCount = await equipmentConflicts.getCount()
      if (conflictCount > 0) {
        conflicts.push(reservation.equipmentId)
      }
    }

    return conflicts
  }

  /**
   * Validate business rules
   */
  private static async validateBusinessRules(
    request: BookingValidationRequest,
    errors: string[],
    warnings: string[]
  ): Promise<void> {
    // Check branch operating hours
    // TODO: Implement branch hours validation
    
    // Check customer booking limits
    // TODO: Implement customer booking limits
    
    // Check blackout dates
    // TODO: Implement blackout date validation
    
    // Add any other business-specific validation rules
  }
}
