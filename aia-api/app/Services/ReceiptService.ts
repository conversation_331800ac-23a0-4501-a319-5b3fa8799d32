import Receipt from 'App/Models/Receipt'
import Payment from 'App/Models/Payment'
import User from 'App/Models/User'
import View from '@ioc:Adonis/Core/View'
import Drive from '@ioc:Adonis/Core/Drive'
import Logger from '@ioc:Adonis/Core/Logger'
import { DateTime } from 'luxon'
import puppeteer from 'puppeteer'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import Mail from '@ioc:Adonis/Addons/Mail'

export default class ReceiptService {
  /**
   * Generate a receipt for a payment
   */
  public async generateReceipt(paymentId: string): Promise<Receipt> {
    try {
      Logger.info(`Starting receipt generation for payment ${paymentId}`)

      // Load payment with all necessary relationships
      const payment = await Payment.query()
        .where('id', paymentId)
        .preload('customer')
        .preload('vendor', (vendorQuery) => {
          vendorQuery.preload('branches')
        })
        .preload('invoice', (invoiceQuery) => {
          invoiceQuery.preload('order', (orderQuery) => {
            orderQuery.preload('branch')
          })
        })
        .firstOrFail()

      // SECURITY: Only generate receipts for successful payments
      if (payment.status !== 'Success') {
        throw new Error(
          `Cannot generate receipt for payment with status: ${payment.status}. Only successful payments can have receipts.`
        )
      }

      // Check if receipt already exists
      let receipt = await Receipt.query().where('payment_id', paymentId).first()

      if (!receipt) {
        // Create new receipt record
        receipt = await Receipt.create({
          userId: payment.userId,
          paymentId: payment.id,
          status: 'pending',
        })
      }

      // Generate receipt HTML
      const receiptHtml = await this.generateReceiptHtml(payment, receipt)

      // Generate PDF from HTML
      const pdfBuffer = await this.generatePdfFromHtml(receiptHtml)

      // Upload PDF to S3
      const filePath = `receipts/${receipt.id}/${receipt.number}.pdf`
      await Drive.use('s3').put(filePath, pdfBuffer, {
        contentType: 'application/pdf',
        visibility: 'private',
      })

      // Get signed URL for the file
      const fileUrl = await Drive.use('s3').getSignedUrl(filePath, {
        expiresIn: '7 days',
      })

      // Update receipt with file information
      receipt.merge({
        filePath,
        fileUrl,
        status: 'generated',
        metadata: {
          fileSize: pdfBuffer.length,
          generatedAt: DateTime.now().toISO(),
          paymentMethod: payment.method,
          amount: payment.amount,
        },
      })

      await receipt.save()

      Logger.info(`Receipt generated successfully for payment ${paymentId}`)
      return receipt
    } catch (error) {
      Logger.error(`Failed to generate receipt for payment ${paymentId}: ${error.message}`)

      // Update receipt status to failed if it exists
      const receipt = await Receipt.query().where('payment_id', paymentId).first()

      if (receipt) {
        receipt.merge({
          status: 'failed',
          metadata: {
            error: error.message,
            failedAt: DateTime.now().toISO(),
          },
        })
        await receipt.save()
      }

      throw error
    }
  }

  /**
   * Generate receipt HTML from template
   */
  private async generateReceiptHtml(payment: Payment, receipt: Receipt): Promise<string> {
    const order = payment.invoice?.order
    const customer = payment.customer
    const vendor = payment.vendor
    const branch = order?.branch || vendor?.branches?.[0]

    // Prepare template data
    // Calculate pricing from order data
    const orderPricing = order.meta?.pricing || {}
    const subtotal = orderPricing.subtotal || 0
    const chargesTotal = orderPricing.chargesTotal || 0
    const total = orderPricing.total || payment.amount

    const templateData = {
      receipt,
      payment,
      customer,
      vendor,
      branch,
      order,
      orderItems: [], // Will be populated from order items if available
      currency: 'KES',
      subtotal: Number(subtotal).toFixed(2),
      serviceFee: this.calculateServiceFee(order),
      deliveryFee: this.calculateDeliveryFee(order),
      tax: this.calculateTax(order),
      total: Number(total).toFixed(2),
      // Add all charges breakdown for transparency
      allCharges: this.getAllCharges(order),
      chargesTotal: Number(chargesTotal).toFixed(2),
    }

    // Render the receipt template
    const html = await View.render('receipts/receipt', templateData)
    return html
  }

  /**
   * Generate PDF from HTML using Puppeteer
   */
  private async generatePdfFromHtml(html: string): Promise<Buffer> {
    let browser
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      const page = await browser.newPage()
      await page.setContent(html, { waitUntil: 'networkidle0' })

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px',
        },
      })

      return Buffer.from(pdfBuffer)
    } finally {
      if (browser) {
        await browser.close()
      }
    }
  }

  /**
   * Send receipt via email
   */
  public async sendReceiptByEmail(receiptId: string): Promise<void> {
    const receipt = await Receipt.query()
      .where('id', receiptId)
      .preload('user')
      .preload('payment', (paymentQuery) => {
        paymentQuery.preload('vendor')
      })
      .firstOrFail()

    if (!receipt.user.email) {
      throw new Error('Customer does not have an email address')
    }

    if (!receipt.fileUrl) {
      throw new Error('Receipt PDF has not been generated')
    }

    try {
      // Send email with receipt attachment
      await Mail.send((message) => {
        const mailer = new CustomerMailer(receipt.user, 'mails/receipt/email', {
          greeting: `Hello ${receipt.user.firstName}!`,
          subject: `Receipt ${receipt.number} - ${receipt.payment.vendor?.name}`,
          intro: `Thank you for your purchase! Please find your receipt attached.`,
          receiptNumber: receipt.number,
          vendorName: receipt.payment.vendor?.name,
          amount: receipt.payment.amount,
          receiptUrl: receipt.fileUrl,
          downloadText: 'Download Receipt',
        })
        mailer.prepare(message)
      })

      // Update receipt delivery status
      receipt.merge({
        status: 'delivered',
        deliveryMethod: 'email',
        deliveredAt: DateTime.now(),
      })
      await receipt.save()

      Logger.info(`Receipt ${receipt.number} sent via email to ${receipt.user.email}`)
    } catch (error) {
      Logger.error(`Failed to send receipt ${receipt.number} via email: ${error.message}`)
      throw error
    }
  }

  /**
   * Get receipt download URL with fresh signed URL
   */
  public async getReceiptDownloadUrl(receiptId: string, userId?: string): Promise<string> {
    let query = Receipt.query().where('id', receiptId)

    if (userId) {
      query = query.where('user_id', userId)
    }

    const receipt = await query.firstOrFail()

    if (!receipt.filePath) {
      throw new Error('Receipt file not found')
    }

    // Generate fresh signed URL
    const signedUrl = await Drive.use('s3').getSignedUrl(receipt.filePath, {
      expiresIn: '1 hour',
    })

    return signedUrl
  }

  /**
   * Regenerate receipt (useful for failed generations or updates)
   */
  public async regenerateReceipt(receiptId: string): Promise<Receipt> {
    const receipt = await Receipt.query().where('id', receiptId).preload('payment').firstOrFail()

    // Reset receipt status
    receipt.merge({
      status: 'pending',
      filePath: null,
      fileUrl: null,
      deliveredAt: null,
      metadata: null,
    })
    await receipt.save()

    // Regenerate receipt
    return this.generateReceipt(receipt.paymentId)
  }

  /**
   * Calculate subtotal from order items
   */
  private calculateSubtotal(items: any[]): string {
    if (!items || items.length === 0) {
      return '0.00'
    }
    const subtotal = items.reduce((sum, item) => {
      return sum + parseFloat(item.price || 0) * (item.quantity || 1)
    }, 0)
    return subtotal.toFixed(2)
  }

  /**
   * Calculate service fee
   */
  private calculateServiceFee(order: any): string {
    // Calculate service fee from order charges
    if (order.meta && order.meta.charges) {
      const serviceFee = order.meta.charges['Service Charge'] || 0
      return Number(serviceFee).toFixed(2)
    }
    return '0.00'
  }

  /**
   * Calculate delivery fee
   */
  private calculateDeliveryFee(order: any): string {
    // Calculate delivery fee from order charges
    if (order.meta && order.meta.charges) {
      // Look for delivery-related charges
      const deliveryFee =
        order.meta.charges['Delivery Fee'] || order.meta.charges['Delivery Charge'] || 0
      return Number(deliveryFee).toFixed(2)
    }
    return order?.delivery?.fee || '0.00'
  }

  /**
   * Calculate tax
   */
  private calculateTax(order: any): string {
    // Calculate tax from order charges (if any tax-related charges exist)
    if (order.meta && order.meta.charges) {
      const tax = order.meta.charges['Tax'] || order.meta.charges['VAT'] || 0
      return Number(tax).toFixed(2)
    }
    return '0.00'
  }

  /**
   * Get all charges breakdown for receipt display
   * Combines AppInApp charges under single "Service Charge" label
   */
  private getAllCharges(order: any): Array<{ name: string; amount: string }> {
    const charges = []

    if (order.meta && order.meta.charges) {
      let appInAppChargesTotal = 0
      const otherCharges: Array<{ name: string; amount: string }> = []

      // Separate AppInApp charges from vendor charges
      for (const [chargeName, chargeAmount] of Object.entries(order.meta.charges)) {
        const amount = Number(chargeAmount) || 0

        // AppInApp charges to combine under "Service Charge"
        if (
          chargeName.includes('Customer Surcharge') ||
          chargeName.includes('Preorder Surcharge') ||
          chargeName.includes('Digital Menu') ||
          chargeName.includes('Digital Waiter') ||
          chargeName === 'Service Charge'
        ) {
          appInAppChargesTotal += amount
        } else {
          // Keep vendor charges separate (delivery, packaging, etc.)
          otherCharges.push({
            name: chargeName,
            amount: amount.toFixed(2),
          })
        }
      }

      // Add combined AppInApp charges as single "Service Charge"
      if (appInAppChargesTotal > 0) {
        charges.push({
          name: 'Service Charge',
          amount: appInAppChargesTotal.toFixed(2),
        })
      }

      // Add other charges separately
      charges.push(...otherCharges)
    }

    return charges
  }

  /**
   * List receipts for a user
   */
  public async listUserReceipts(
    userId: string,
    page: number = 1,
    perPage: number = 20
  ): Promise<{ receipts: Receipt[]; pagination: any }> {
    const receiptsQuery = Receipt.query()
      .where('user_id', userId)
      .preload('payment', (paymentQuery) => {
        paymentQuery.preload('vendor')
      })
      .orderBy('created_at', 'desc')

    const receipts = await receiptsQuery.paginate(page, perPage)

    return {
      receipts: receipts.all(),
      pagination: receipts.getMeta(),
    }
  }

  /**
   * List all receipts (admin only)
   */
  public async listAllReceipts(
    page: number = 1,
    perPage: number = 20
  ): Promise<{ receipts: Receipt[]; pagination: any }> {
    const receiptsQuery = Receipt.query()
      .preload('payment', (paymentQuery) => {
        paymentQuery.preload('vendor')
      })
      .orderBy('created_at', 'desc')

    const receipts = await receiptsQuery.paginate(page, perPage)

    return {
      receipts: receipts.all(),
      pagination: receipts.getMeta(),
    }
  }
}
