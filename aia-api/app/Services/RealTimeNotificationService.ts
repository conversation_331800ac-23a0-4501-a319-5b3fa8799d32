import WebSocketManager from './WebSocketManager'
import { DateTime } from 'luxon'
import User from '../Models/User'
import Order from '../Models/Order'
import OrderItem from '../Models/OrderItem'
import Department from '../Models/Department'

export interface NotificationData {
  id: string
  type: NotificationType
  priority: NotificationPriority
  title: string
  message: string
  data: Record<string, any>
  created_at: DateTime
  expires_at?: DateTime
  action_url?: string
  action_label?: string
  requires_acknowledgment?: boolean
}

export type NotificationType =
  | 'order_status_change'
  | 'item_status_change'
  | 'order_completion'
  | 'overdue_alert'
  | 'staff_assignment'
  | 'department_workload'
  | 'system_alert'
  | 'customer_notification'
  | 'manager_alert'
  | 'booking_created'
  | 'booking_confirmed'
  | 'booking_reminder'
  | 'booking_cancelled'
  | 'booking_modified'
  | 'booking_completed'
  | 'booking_no_show'
  | 'vendor_booking_alert'

export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical'

export interface NotificationTarget {
  type: 'user' | 'role' | 'department' | 'vendor' | 'branch'
  id: string
  filters?: Record<string, any>
}

export interface NotificationTemplate {
  type: NotificationType
  priority: NotificationPriority
  title_template: string
  message_template: string
  action_url_template?: string
  action_label?: string
  expires_in_minutes?: number
  requires_acknowledgment?: boolean
  target_roles?: string[]
}

/**
 * Service for managing real-time notifications across the fulfillment system
 */
export default class RealTimeNotificationService {
  private static notificationTemplates: Map<string, NotificationTemplate> = new Map()
  private static activeNotifications: Map<string, NotificationData> = new Map()

  /**
   * Initialize notification templates
   */
  public static initialize(): void {
    this.setupNotificationTemplates()
    console.log('Real-time notification service initialized')
  }

  /**
   * Send notification to specific targets
   */
  public static async sendNotification(
    type: NotificationType,
    targets: NotificationTarget[],
    data: Record<string, any>,
    customTemplate?: Partial<NotificationTemplate>
  ): Promise<string> {
    try {
      // Get or create notification template
      const template = customTemplate ?
        { ...this.getTemplate(type), ...customTemplate } :
        this.getTemplate(type)

      // Generate notification
      const notification = this.generateNotification(template, data)

      // Store active notification
      this.activeNotifications.set(notification.id, notification)

      // Send to targets
      for (const target of targets) {
        await this.sendToTarget(target, notification)
      }

      // Set expiration cleanup if needed
      if (notification.expires_at) {
        this.scheduleNotificationCleanup(notification.id, notification.expires_at)
      }

      return notification.id

    } catch (error) {
      console.error('Error sending notification:', error)
      throw error
    }
  }

  /**
   * Send order status change notification
   */
  public static async notifyOrderStatusChange(
    order: Order,
    previousStatus: string,
    updatedBy?: string
  ): Promise<void> {
    const data = {
      order_id: order.id,
      order_ref: order.ref,
      status: order.status,
      previous_status: previousStatus,
      updated_by: updatedBy,
      customer_name: order.customer?.name,
      delivery_type: order.delivery,
      order_type: order.type
    }

    const targets: NotificationTarget[] = []

    // Notify customer
    if (order.userId) {
      targets.push({
        type: 'user',
        id: order.userId
      })
    }

    // Notify vendor staff
    targets.push({
      type: 'vendor',
      id: order.vendorId,
      filters: { roles: ['staff', 'manager'] }
    })

    // Notify branch staff if applicable
    if (order.branchId) {
      targets.push({
        type: 'branch',
        id: order.branchId,
        filters: { roles: ['staff', 'manager'] }
      })
    }

    await this.sendNotification('order_status_change', targets, data)
  }

  /**
   * Send item status change notification
   */
  public static async notifyItemStatusChange(
    orderItem: OrderItem,
    previousStatus: string,
    updatedBy?: string
  ): Promise<void> {
    await orderItem.load('order', (orderQuery) => {
      orderQuery.preload('customer')
    })
    await orderItem.load('product')
    await orderItem.load('department')
    await orderItem.load('assignedStaff')

    const data = {
      order_id: orderItem.orderId,
      item_id: orderItem.id,
      product_name: orderItem.product?.name,
      status: orderItem.status,
      previous_status: previousStatus,
      updated_by: updatedBy,
      department_name: orderItem.department?.name,
      assigned_staff_name: orderItem.assignedStaff?.name,
      estimated_time: orderItem.estimatedPreparationTime,
      is_overdue: orderItem.isOverdue
    }

    const targets: NotificationTarget[] = []

    // Notify department staff
    if (orderItem.departmentId) {
      targets.push({
        type: 'department',
        id: orderItem.departmentId
      })
    }

    // Notify assigned staff
    if (orderItem.assignedStaffId) {
      targets.push({
        type: 'user',
        id: orderItem.assignedStaffId
      })
    }

    // Notify managers
    targets.push({
      type: 'vendor',
      id: orderItem.order.vendorId,
      filters: { roles: ['manager'] }
    })

    await this.sendNotification('item_status_change', targets, data)
  }

  /**
   * Send order completion notification
   */
  public static async notifyOrderCompletion(
    order: Order,
    completionData: Record<string, any>
  ): Promise<void> {
    const data = {
      order_id: order.id,
      order_ref: order.ref,
      customer_name: order.customer?.name,
      delivery_type: order.delivery,
      completion_time: completionData.completion_time,
      total_items: completionData.total_items,
      preparation_duration: completionData.preparation_duration
    }

    const targets: NotificationTarget[] = []

    // Notify customer
    if (order.userId) {
      targets.push({
        type: 'user',
        id: order.userId
      })
    }

    // Notify relevant staff based on delivery type
    const staffRoles = this.getRelevantStaffRoles(order.delivery)
    targets.push({
      type: 'vendor',
      id: order.vendorId,
      filters: { roles: staffRoles }
    })

    await this.sendNotification('order_completion', targets, data)
  }

  /**
   * Send overdue alert notification
   */
  public static async notifyOverdueItem(
    orderItem: OrderItem,
    overdueMinutes: number,
    priorityLevel: 'low' | 'medium' | 'high' | 'critical'
  ): Promise<void> {
    await orderItem.load('order')
    await orderItem.load('product')
    await orderItem.load('department')
    await orderItem.load('assignedStaff')

    const data = {
      order_id: orderItem.orderId,
      item_id: orderItem.id,
      product_name: orderItem.product?.name,
      overdue_minutes: overdueMinutes,
      priority_level: priorityLevel,
      department_name: orderItem.department?.name,
      assigned_staff_name: orderItem.assignedStaff?.name,
      estimated_time: orderItem.estimatedPreparationTime
    }

    const targets: NotificationTarget[] = []

    // Notify assigned staff with high priority
    if (orderItem.assignedStaffId) {
      targets.push({
        type: 'user',
        id: orderItem.assignedStaffId
      })
    }

    // Notify department staff
    if (orderItem.departmentId) {
      targets.push({
        type: 'department',
        id: orderItem.departmentId
      })
    }

    // Notify managers with critical priority for high/critical overdue items
    if (['high', 'critical'].includes(priorityLevel)) {
      targets.push({
        type: 'vendor',
        id: orderItem.order.vendorId,
        filters: { roles: ['manager'] }
      })
    }

    // Use custom template with higher priority for overdue items
    const customTemplate: Partial<NotificationTemplate> = {
      priority: priorityLevel === 'critical' ? 'critical' : 'high',
      requires_acknowledgment: ['high', 'critical'].includes(priorityLevel)
    }

    await this.sendNotification('overdue_alert', targets, data, customTemplate)
  }

  /**
   * Send staff assignment notification
   */
  public static async notifyStaffAssignment(
    orderItem: OrderItem,
    assignedStaffId: string,
    assignedBy: string
  ): Promise<void> {
    await orderItem.load('order')
    await orderItem.load('product')
    await orderItem.load('department')

    const data = {
      order_id: orderItem.orderId,
      item_id: orderItem.id,
      product_name: orderItem.product?.name,
      assigned_by: assignedBy,
      department_name: orderItem.department?.name,
      estimated_time: orderItem.estimatedPreparationTime,
      priority_level: orderItem.priorityLevel,
      special_instructions: orderItem.specialInstructions
    }

    const targets: NotificationTarget[] = [
      {
        type: 'user',
        id: assignedStaffId
      }
    ]

    await this.sendNotification('staff_assignment', targets, data)
  }

  /**
   * Send department workload notification
   */
  public static async notifyDepartmentWorkload(
    departmentId: string,
    workloadData: Record<string, any>,
    alertType: 'capacity_warning' | 'capacity_critical' | 'workload_update'
  ): Promise<void> {
    const department = await Department.find(departmentId)
    if (!department) return

    const data = {
      department_id: departmentId,
      department_name: department.name,
      alert_type: alertType,
      ...workloadData
    }

    const targets: NotificationTarget[] = [
      {
        type: 'department',
        id: departmentId
      },
      {
        type: 'vendor',
        id: department.vendorId,
        filters: { roles: ['manager'] }
      }
    ]

    const priority: NotificationPriority = alertType === 'capacity_critical' ? 'critical' :
                                          alertType === 'capacity_warning' ? 'high' : 'medium'

    const customTemplate: Partial<NotificationTemplate> = {
      priority,
      requires_acknowledgment: alertType === 'capacity_critical'
    }

    await this.sendNotification('department_workload', targets, data, customTemplate)
  }

  /**
   * Send to specific target
   */
  private static async sendToTarget(target: NotificationTarget, notification: NotificationData): Promise<void> {
    try {
      switch (target.type) {
        case 'user':
          WebSocketManager.sendToUser(target.id, 'notification', notification)
          break

        case 'department':
          WebSocketManager.broadcastToRoom(`department:${target.id}`, 'notification', notification)
          break

        case 'vendor':
          const vendorRoom = target.filters?.roles ?
            `vendor:${target.id}:${target.filters.roles.join(',')}` :
            `vendor:${target.id}:staff`
          WebSocketManager.broadcastToRoom(vendorRoom, 'notification', notification)
          break

        case 'branch':
          const branchRoom = target.filters?.roles ?
            `branch:${target.id}:${target.filters.roles.join(',')}` :
            `branch:${target.id}:staff`
          WebSocketManager.broadcastToRoom(branchRoom, 'notification', notification)
          break

        case 'role':
          // This would require a more complex implementation to find users by role
          console.log(`Role-based notification not yet implemented: ${target.id}`)
          break
      }
    } catch (error) {
      console.error(`Error sending notification to ${target.type}:${target.id}:`, error)
    }
  }

  /**
   * Generate notification from template and data
   */
  private static generateNotification(template: NotificationTemplate, data: Record<string, any>): NotificationData {
    const id = this.generateNotificationId()
    const now = DateTime.now()

    return {
      id,
      type: template.type,
      priority: template.priority,
      title: this.interpolateTemplate(template.title_template, data),
      message: this.interpolateTemplate(template.message_template, data),
      data,
      created_at: now,
      expires_at: template.expires_in_minutes ? now.plus({ minutes: template.expires_in_minutes }) : undefined,
      action_url: template.action_url_template ? this.interpolateTemplate(template.action_url_template, data) : undefined,
      action_label: template.action_label,
      requires_acknowledgment: template.requires_acknowledgment
    }
  }

  /**
   * Interpolate template with data
   */
  private static interpolateTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match
    })
  }

  /**
   * Get notification template
   */
  private static getTemplate(type: NotificationType): NotificationTemplate {
    const template = this.notificationTemplates.get(type)
    if (!template) {
      throw new Error(`No template found for notification type: ${type}`)
    }
    return template
  }

  /**
   * Generate unique notification ID
   */
  private static generateNotificationId(): string {
    return `notif_${DateTime.now().toMillis()}_${Math.random().toString(36).substring(2, 8)}`
  }

  /**
   * Get relevant staff roles for delivery type
   */
  private static getRelevantStaffRoles(deliveryType: string): string[] {
    switch (deliveryType) {
      case 'Pickup':
        return ['cashier', 'front_of_house', 'manager']
      case 'Delivery':
        return ['delivery_coordinator', 'delivery_driver', 'manager']
      case 'Dine-in':
        return ['waiter', 'server', 'front_of_house', 'manager']
      default:
        return ['manager']
    }
  }

  /**
   * Schedule notification cleanup
   */
  private static scheduleNotificationCleanup(notificationId: string, expiresAt: DateTime): void {
    const delay = expiresAt.diff(DateTime.now(), 'milliseconds').milliseconds

    if (delay > 0) {
      setTimeout(() => {
        this.activeNotifications.delete(notificationId)
      }, delay)
    }
  }

  /**
   * Setup notification templates
   */
  private static setupNotificationTemplates(): void {
    // Order status change template
    this.notificationTemplates.set('order_status_change', {
      type: 'order_status_change',
      priority: 'medium',
      title_template: 'Order {{order_ref}} Status Updated',
      message_template: 'Order {{order_ref}} status changed from {{previous_status}} to {{status}}',
      action_url_template: '/orders/{{order_id}}',
      action_label: 'View Order',
      expires_in_minutes: 60,
      target_roles: ['customer', 'staff', 'manager']
    })

    // Item status change template
    this.notificationTemplates.set('item_status_change', {
      type: 'item_status_change',
      priority: 'low',
      title_template: 'Item Status Updated',
      message_template: '{{product_name}} status changed to {{status}}',
      action_url_template: '/orders/{{order_id}}/items/{{item_id}}',
      action_label: 'View Item',
      expires_in_minutes: 30,
      target_roles: ['staff', 'manager']
    })

    // Order completion template
    this.notificationTemplates.set('order_completion', {
      type: 'order_completion',
      priority: 'high',
      title_template: 'Order {{order_ref}} Ready!',
      message_template: 'Your order {{order_ref}} is ready for {{delivery_type}}',
      action_url_template: '/orders/{{order_id}}',
      action_label: 'View Order',
      expires_in_minutes: 120,
      target_roles: ['customer', 'staff']
    })

    // Overdue alert template
    this.notificationTemplates.set('overdue_alert', {
      type: 'overdue_alert',
      priority: 'high',
      title_template: 'Overdue Item Alert',
      message_template: '{{product_name}} is {{overdue_minutes}} minutes overdue',
      action_url_template: '/orders/{{order_id}}/items/{{item_id}}',
      action_label: 'Check Item',
      requires_acknowledgment: true,
      target_roles: ['staff', 'manager']
    })

    // Staff assignment template
    this.notificationTemplates.set('staff_assignment', {
      type: 'staff_assignment',
      priority: 'medium',
      title_template: 'New Item Assigned',
      message_template: 'You have been assigned {{product_name}} (Est. {{estimated_time}} min)',
      action_url_template: '/orders/{{order_id}}/items/{{item_id}}',
      action_label: 'Start Preparation',
      expires_in_minutes: 30,
      target_roles: ['staff']
    })

    // Department workload template
    this.notificationTemplates.set('department_workload', {
      type: 'department_workload',
      priority: 'medium',
      title_template: 'Department Workload Alert',
      message_template: '{{department_name}} workload: {{alert_type}}',
      action_url_template: '/departments/{{department_id}}/workload',
      action_label: 'View Workload',
      expires_in_minutes: 15,
      target_roles: ['manager']
    })

    // Booking created template
    this.notificationTemplates.set('booking_created', {
      type: 'booking_created',
      priority: 'high',
      title_template: 'New Booking Request',
      message_template: 'New booking for {{service_name}} on {{appointment_date}} at {{appointment_time}}',
      action_url_template: '/bookings/{{booking_id}}',
      action_label: 'View Booking',
      expires_in_minutes: 60,
      target_roles: ['customer', 'manager', 'staff']
    })

    // Booking confirmed template
    this.notificationTemplates.set('booking_confirmed', {
      type: 'booking_confirmed',
      priority: 'high',
      title_template: 'Booking Confirmed',
      message_template: 'Your booking for {{service_name}} on {{appointment_date}} at {{appointment_time}} is confirmed',
      action_url_template: '/bookings/{{booking_id}}',
      action_label: 'View Booking',
      expires_in_minutes: 120,
      target_roles: ['customer', 'manager', 'staff']
    })

    // Booking cancelled template
    this.notificationTemplates.set('booking_cancelled', {
      type: 'booking_cancelled',
      priority: 'high',
      title_template: 'Booking Cancelled',
      message_template: 'Booking for {{service_name}} on {{appointment_date}} has been cancelled',
      action_url_template: '/bookings/{{booking_id}}',
      action_label: 'View Details',
      expires_in_minutes: 180,
      target_roles: ['customer', 'manager', 'staff']
    })

    // Booking modified template
    this.notificationTemplates.set('booking_modified', {
      type: 'booking_modified',
      priority: 'medium',
      title_template: 'Booking Updated',
      message_template: 'Your booking for {{service_name}} has been updated',
      action_url_template: '/bookings/{{booking_id}}',
      action_label: 'View Changes',
      expires_in_minutes: 120,
      target_roles: ['customer', 'manager', 'staff']
    })
  }

  /**
   * Send booking created notification
   */
  public static async notifyBookingCreated(
    customerId: string,
    vendorId: string,
    bookingData: Record<string, any>
  ): Promise<void> {
    const data = {
      customer_id: customerId,
      vendor_id: vendorId,
      ...bookingData
    }

    const targets = [
      { type: 'user' as const, id: customerId },
      { type: 'vendor' as const, id: vendorId, filters: { roles: ['manager', 'staff'] } }
    ]

    await this.sendNotification('booking_created', targets, data)
  }

  /**
   * Send booking confirmed notification
   */
  public static async notifyBookingConfirmed(
    customerId: string,
    vendorId: string,
    bookingData: Record<string, any>
  ): Promise<void> {
    const data = {
      customer_id: customerId,
      vendor_id: vendorId,
      ...bookingData
    }

    const targets = [
      { type: 'user' as const, id: customerId },
      { type: 'vendor' as const, id: vendorId, filters: { roles: ['manager', 'staff'] } }
    ]

    await this.sendNotification('booking_confirmed', targets, data)
  }

  /**
   * Send booking cancelled notification
   */
  public static async notifyBookingCancelled(
    customerId: string,
    vendorId: string,
    bookingData: Record<string, any>
  ): Promise<void> {
    const data = {
      customer_id: customerId,
      vendor_id: vendorId,
      ...bookingData
    }

    const targets = [
      { type: 'user' as const, id: customerId },
      { type: 'vendor' as const, id: vendorId, filters: { roles: ['manager', 'staff'] } }
    ]

    await this.sendNotification('booking_cancelled', targets, data)
  }

  /**
   * Send booking modified notification
   */
  public static async notifyBookingModified(
    customerId: string,
    vendorId: string,
    bookingData: Record<string, any>
  ): Promise<void> {
    const data = {
      customer_id: customerId,
      vendor_id: vendorId,
      ...bookingData
    }

    const targets = [
      { type: 'user' as const, id: customerId },
      { type: 'vendor' as const, id: vendorId, filters: { roles: ['manager', 'staff'] } }
    ]

    await this.sendNotification('booking_modified', targets, data)
  }

  /**
   * Get active notifications for user
   */
  public static getActiveNotifications(userId: string): NotificationData[] {
    // This would typically query a database for user-specific notifications
    // For now, return all active notifications (in a real implementation, filter by user)
    return Array.from(this.activeNotifications.values())
  }

  /**
   * Mark notification as acknowledged
   */
  public static acknowledgeNotification(notificationId: string, userId: string): boolean {
    const notification = this.activeNotifications.get(notificationId)
    if (notification && notification.requires_acknowledgment) {
      // In a real implementation, this would update the database
      console.log(`Notification ${notificationId} acknowledged by user ${userId}`)
      return true
    }
    return false
  }

  /**
   * Get notification statistics
   */
  public static getStatistics(): {
    total_active: number
    by_type: Record<string, number>
    by_priority: Record<string, number>
    requiring_acknowledgment: number
  } {
    const stats = {
      total_active: this.activeNotifications.size,
      by_type: {},
      by_priority: {},
      requiring_acknowledgment: 0
    }

    this.activeNotifications.forEach(notification => {
      stats.by_type[notification.type] = (stats.by_type[notification.type] || 0) + 1
      stats.by_priority[notification.priority] = (stats.by_priority[notification.priority] || 0) + 1

      if (notification.requires_acknowledgment) {
        stats.requiring_acknowledgment++
      }
    })

    return stats
  }
}
