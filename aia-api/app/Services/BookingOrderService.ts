import { DateTime } from 'luxon'
import Order from 'App/Models/Order'
import Booking, { BookingStatus, SelectedServiceOption, StaffAssignment, EquipmentReservation } from 'App/Models/Booking'
import BookingValidationService from './BookingValidationService'
import OptionResolutionService from './OptionResolutionService'
import Database from '@ioc:Adonis/Lucid/Database'

export interface BookingOrderRequest {
  customerId: string
  vendorId: string
  branchId: string
  productId: string
  appointmentStart: DateTime
  appointmentEnd: DateTime
  selectedServiceOptions: SelectedServiceOption[]
  staffAssignments?: StaffAssignment[]
  equipmentReservations?: EquipmentReservation[]
  bookingNotes?: Record<string, any>
  sectionId?: string
  lotId?: string
  staffId?: string
}

export interface BookingOrderResponse {
  order: Order
  booking: Booking
  totalPrice: number
  confirmationCode: string
}

export default class BookingOrderService {
  /**
   * Create a complete booking with associated order
   */
  public static async createBookingOrder(request: BookingOrderRequest): Promise<BookingOrderResponse> {
    const trx = await Database.transaction()

    try {
      // Validate the booking request
      const validation = await BookingValidationService.validateBookingRequest({
        productId: request.productId,
        branchId: request.branchId,
        customerId: request.customerId,
        appointmentStart: request.appointmentStart,
        appointmentEnd: request.appointmentEnd,
        selectedServiceOptions: request.selectedServiceOptions,
        staffAssignments: request.staffAssignments,
        equipmentReservations: request.equipmentReservations
      })

      if (!validation.valid) {
        await trx.rollback()
        throw new Error(`Booking validation failed: ${validation.errors.join(', ')}`)
      }

      // Calculate pricing
      const resolvedOptions = await OptionResolutionService.resolveProductOptions(request.productId)
      const selectedOptions = resolvedOptions.allOptions.filter(option => 
        request.selectedServiceOptions.some(selected => selected.id === option.id)
      )
      
      const basePrice = resolvedOptions.basePrice
      const optionsTotal = OptionResolutionService.calculateTotalPrice(0, selectedOptions)
      const totalPrice = basePrice + optionsTotal

      // Calculate duration
      const durationMinutes = request.appointmentEnd.diff(request.appointmentStart, 'minutes').minutes
      const bufferMinutes = selectedOptions
        .filter(opt => opt.type === 'duration')
        .reduce((max, opt) => Math.max(max, opt.duration?.bufferMinutes || 0), 15)

      // Create the order first
      const order = await Order.create({
        userId: request.customerId,
        staffId: request.staffId || null,
        vendorId: request.vendorId,
        branchId: request.branchId,
        sectionId: request.sectionId || null,
        lotId: request.lotId || null,
        action: 'Booking',
        type: 'Preorder',
        delivery: 'Dinein', // Default for bookings
        status: 'Pending',
        total: totalPrice,
        meta: {
          booking_type: 'appointment',
          appointment_start: request.appointmentStart.toISO(),
          appointment_end: request.appointmentEnd.toISO(),
          selected_service_options: request.selectedServiceOptions,
          staff_assignments: request.staffAssignments,
          equipment_reservations: request.equipmentReservations,
          booking_notes: request.bookingNotes
        },
        items: {
          [request.productId]: {
            quantity: 1,
            selected_service_options: request.selectedServiceOptions,
            base_price: basePrice,
            options_total: optionsTotal,
            total_price: totalPrice
          }
        }
      }, { client: trx })

      // Create the booking
      const booking = await Booking.create({
        orderId: order.id,
        customerId: request.customerId,
        vendorId: request.vendorId,
        branchId: request.branchId,
        productId: request.productId,
        appointmentStart: request.appointmentStart,
        appointmentEnd: request.appointmentEnd,
        durationMinutes,
        bufferMinutes,
        status: BookingStatus.PENDING,
        staffAssignments: request.staffAssignments || [],
        equipmentReservations: request.equipmentReservations || [],
        selectedServiceOptions: request.selectedServiceOptions,
        basePrice,
        optionsTotal,
        totalPrice,
        bookingNotes: request.bookingNotes || null
      }, { client: trx })

      await trx.commit()

      // Load relationships for response
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await booking.load('customer')
      await booking.load('product')
      await booking.load('branch')
      await booking.load('vendor')

      return {
        order,
        booking,
        totalPrice,
        confirmationCode: booking.confirmationCode!
      }

    } catch (error) {
      await trx.rollback()
      throw error
    }
  }

  /**
   * Update a booking and its associated order
   */
  public static async updateBookingOrder(
    bookingId: string,
    updates: Partial<BookingOrderRequest>
  ): Promise<BookingOrderResponse> {
    const trx = await Database.transaction()

    try {
      // Load existing booking with order
      const booking = await Booking.query({ client: trx })
        .where('id', bookingId)
        .preload('order')
        .firstOrFail()

      if (!booking.canBeModified()) {
        throw new Error('Booking cannot be modified in its current state')
      }

      const order = booking.order

      // Validate updates if appointment times are changing
      if (updates.appointmentStart || updates.appointmentEnd) {
        const newStartTime = updates.appointmentStart || booking.appointmentStart
        const newEndTime = updates.appointmentEnd || booking.appointmentEnd

        const validation = await BookingValidationService.validateBookingRequest({
          productId: booking.productId,
          branchId: booking.branchId,
          customerId: booking.customerId,
          appointmentStart: newStartTime,
          appointmentEnd: newEndTime,
          selectedServiceOptions: updates.selectedServiceOptions || booking.selectedServiceOptions,
          staffAssignments: updates.staffAssignments || booking.staffAssignments,
          equipmentReservations: updates.equipmentReservations || booking.equipmentReservations,
          excludeBookingId: booking.id
        })

        if (!validation.valid) {
          await trx.rollback()
          throw new Error(`Booking validation failed: ${validation.errors.join(', ')}`)
        }

        booking.appointmentStart = newStartTime
        booking.appointmentEnd = newEndTime
        booking.durationMinutes = newEndTime.diff(newStartTime, 'minutes').minutes

        // Update order meta
        order.meta = {
          ...order.meta,
          appointment_start: newStartTime.toISO(),
          appointment_end: newEndTime.toISO()
        }
      }

      // Update service options and recalculate pricing if needed
      if (updates.selectedServiceOptions) {
        const resolvedOptions = await OptionResolutionService.resolveProductOptions(booking.productId)
        const selectedOptions = resolvedOptions.allOptions.filter(option => 
          updates.selectedServiceOptions!.some(selected => selected.id === option.id)
        )
        
        const optionsTotal = OptionResolutionService.calculateTotalPrice(0, selectedOptions)
        const totalPrice = booking.basePrice + optionsTotal

        booking.selectedServiceOptions = updates.selectedServiceOptions
        booking.optionsTotal = optionsTotal
        booking.totalPrice = totalPrice

        // Update order
        order.total = totalPrice
        order.meta = {
          ...order.meta,
          selected_service_options: updates.selectedServiceOptions
        }
        order.items = {
          [booking.productId]: {
            quantity: 1,
            selected_service_options: updates.selectedServiceOptions,
            base_price: booking.basePrice,
            options_total: optionsTotal,
            total_price: totalPrice
          }
        }
      }

      // Update other fields
      if (updates.staffAssignments) {
        booking.staffAssignments = updates.staffAssignments
        order.meta = {
          ...order.meta,
          staff_assignments: updates.staffAssignments
        }
      }

      if (updates.equipmentReservations) {
        booking.equipmentReservations = updates.equipmentReservations
        order.meta = {
          ...order.meta,
          equipment_reservations: updates.equipmentReservations
        }
      }

      if (updates.bookingNotes) {
        booking.bookingNotes = updates.bookingNotes
        order.meta = {
          ...order.meta,
          booking_notes: updates.bookingNotes
        }
      }

      // Save both models
      await booking.save({ client: trx })
      await order.save({ client: trx })

      await trx.commit()

      // Load relationships for response
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await booking.load('customer')
      await booking.load('product')
      await booking.load('branch')
      await booking.load('vendor')

      return {
        order,
        booking,
        totalPrice: booking.totalPrice,
        confirmationCode: booking.confirmationCode!
      }

    } catch (error) {
      await trx.rollback()
      throw error
    }
  }

  /**
   * Cancel a booking and its associated order
   */
  public static async cancelBookingOrder(
    bookingId: string,
    cancelledBy: string,
    reason?: string
  ): Promise<{ booking: Booking; order: Order }> {
    const trx = await Database.transaction()

    try {
      // Load booking with order
      const booking = await Booking.query({ client: trx })
        .where('id', bookingId)
        .preload('order')
        .firstOrFail()

      if (!booking.canBeCancelled()) {
        throw new Error('Booking cannot be cancelled in its current state')
      }

      const order = booking.order

      // Cancel booking
      await booking.cancel(cancelledBy, reason)

      // Update order status
      order.status = 'Cancelled'
      await order.save({ client: trx })

      await trx.commit()

      return { booking, order }

    } catch (error) {
      await trx.rollback()
      throw error
    }
  }

  /**
   * Confirm a booking and update order status
   */
  public static async confirmBookingOrder(bookingId: string): Promise<{ booking: Booking; order: Order }> {
    const trx = await Database.transaction()

    try {
      // Load booking with order
      const booking = await Booking.query({ client: trx })
        .where('id', bookingId)
        .preload('order')
        .firstOrFail()

      const order = booking.order

      // Confirm booking
      await booking.confirm()

      // Update order status
      order.status = 'Placed'
      await order.save({ client: trx })

      await trx.commit()

      return { booking, order }

    } catch (error) {
      await trx.rollback()
      throw error
    }
  }

  /**
   * Get booking with order details
   */
  public static async getBookingWithOrder(bookingId: string): Promise<{ booking: Booking; order: Order }> {
    const booking = await Booking.query()
      .where('id', bookingId)
      .preload('order')
      .preload('customer')
      .preload('product')
      .preload('branch')
      .preload('vendor')
      .firstOrFail()

    const order = booking.order
    await order.load('customer')
    await order.load('vendor')
    await order.load('branch')

    return { booking, order }
  }
}
