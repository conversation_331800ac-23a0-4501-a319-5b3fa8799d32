import Logger from '@ioc:Adonis/Core/Logger'
import FeatureFlag from 'App/Models/FeatureFlag'

export default class FeatureFlagService {
  private static cache: Map<string, FeatureFlag> = new Map()

  /**
   * Check if a feature is enabled
   */
  public static async isEnabled(
    code: string,
    context: {
      scopeId?: string | null
      conditions?: Record<string, any>
    } = {}
  ): Promise<boolean> {
    try {
      const flag = await this.getFlag(code)
      if (!flag) return false

      if (!flag.isEnabledForScope(context.scopeId)) {
        return false
      }

      if (context.conditions && !flag.meetsConditions(context.conditions)) {
        return false
      }

      return true
    } catch (error) {
      Logger.error('Error checking feature flag', {
        error,
        code,
        context,
      })
      return false
    }
  }

  /**
   * Get a feature flag by code
   */
  public static async getFlag(code: string): Promise<FeatureFlag | null> {
    // Check cache first
    if (this.cache.has(code)) {
      return this.cache.get(code)!
    }

    // Get from database
    const flag = await FeatureFlag.findBy('code', code)
    if (flag) {
      this.cache.set(code, flag)
    }

    return flag
  }

  /**
   * Enable a feature flag
   */
  public static async enableFlag(
    code: string,
    options: {
      scope?: 'global' | 'vendor' | 'branch' | 'customer'
      scopeId?: string | null
      conditions?: Record<string, any>
    } = {}
  ): Promise<FeatureFlag> {
    const flag = await FeatureFlag.firstOrCreate(
      { code },
      {
        name: code,
        isEnabled: true,
        scope: options.scope || 'global',
        scopeId: options.scopeId,
        conditions: options.conditions || {},
      }
    )

    flag.isEnabled = true
    await flag.save()

    // Update cache
    this.cache.set(code, flag)

    return flag
  }

  /**
   * Disable a feature flag
   */
  public static async disableFlag(code: string): Promise<FeatureFlag> {
    const flag = await FeatureFlag.findByOrFail('code', code)
    flag.isEnabled = false
    await flag.save()

    // Update cache
    this.cache.set(code, flag)

    return flag
  }

  /**
   * Clear the feature flag cache
   */
  public static clearCache(): void {
    this.cache.clear()
  }
}