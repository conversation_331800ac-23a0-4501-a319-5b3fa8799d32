import QRCode from 'qrcode'
import { DateTime } from 'luxon'
import { ulid } from 'ulidx'
import TableQRCode from 'App/Models/TableQRCode'
import Lot from 'App/Models/Lot'
import Section from 'App/Models/Section'
import Branch from 'App/Models/Branch'
import Vendor from 'App/Models/Vendor'
import Env from '@ioc:Adonis/Core/Env'

interface TableQRData {
  vendorId: string
  branchId: string
  sectionId: string
  lotId: string
  tableNumber: string
}

interface QRCodeOptions {
  width?: number
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
  color?: {
    dark?: string
    light?: string
  }
  isAutoGenerated?: boolean
}

export default class QRCodeGenerationService {
  private baseUrl: string

  constructor() {
    this.baseUrl = Env.get('APP_URL', 'https://app.appinapp.ke')
  }

  /**
   * Generate QR code for a single table
   */
  public async generateTableQRCode(
    tableData: TableQRData,
    options: QRCodeOptions = {}
  ): Promise<TableQRCode> {
    // Check if QR code already exists for this table
    const existingQRCode = await TableQRCode.query().where('lotId', tableData.lotId).first()

    if (existingQRCode) {
      // For auto-generation, return existing QR code silently
      if (options.isAutoGenerated) {
        return existingQRCode
      }
      // For manual generation, throw error
      throw new Error(`QR code already exists for table ${tableData.tableNumber}`)
    }

    // Generate unique ID for QR code
    const qrCodeId = ulid().toLowerCase()

    // Generate QR code URL and image first
    const qrCodeUrl = `${this.baseUrl}/table/${qrCodeId}`
    const qrCodeImage = await this.generateQRCodeImage(qrCodeUrl, options)

    // Create new QR code record with all required fields
    const tableQRCode = await TableQRCode.create({
      id: qrCodeId,
      vendorId: tableData.vendorId,
      branchId: tableData.branchId,
      sectionId: tableData.sectionId,
      lotId: tableData.lotId,
      tableNumber: tableData.tableNumber,
      qrCodeUrl: qrCodeUrl,
      qrCodeImage: qrCodeImage,
      generatedAt: DateTime.now(),
      isActive: true,
      scanCount: 0,
    })

    return tableQRCode
  }

  /**
   * Generate QR codes for all tables in a section
   */
  public async generateSectionQRCodes(
    sectionId: string,
    options: QRCodeOptions = {}
  ): Promise<TableQRCode[]> {
    // Get section with all related data
    const section = await Section.query()
      .where('id', sectionId)
      .preload('branch', (branchQuery) => {
        branchQuery.preload('vendor')
      })
      .preload('lots')
      .firstOrFail()

    const generatedQRCodes: TableQRCode[] = []

    // Generate QR code for each table (lot) in the section
    for (const lot of section.lots) {
      try {
        const tableData: TableQRData = {
          vendorId: section.branch.vendor.id,
          branchId: section.branch.id,
          sectionId: section.id,
          lotId: lot.id,
          tableNumber: lot.name, // Assuming lot.name contains table number
        }

        const qrCode = await this.generateTableQRCode(tableData, options)
        generatedQRCodes.push(qrCode)
      } catch (error) {
        // Skip if QR code already exists, but log other errors
        if (!error.message.includes('already exists')) {
          console.error(`Failed to generate QR code for table ${lot.name}:`, error)
        }
      }
    }

    return generatedQRCodes
  }

  /**
   * Generate QR codes for all tables in a branch
   */
  public async generateBranchQRCodes(
    branchId: string,
    options: QRCodeOptions = {}
  ): Promise<TableQRCode[]> {
    // Get branch with all sections and lots
    const branch = await Branch.query()
      .where('id', branchId)
      .preload('vendor')
      .preload('sections', (sectionQuery) => {
        sectionQuery.preload('lots')
      })
      .firstOrFail()

    const generatedQRCodes: TableQRCode[] = []

    // Generate QR codes for all sections
    for (const section of branch.sections) {
      const sectionQRCodes = await this.generateSectionQRCodes(section.id, options)
      generatedQRCodes.push(...sectionQRCodes)
    }

    return generatedQRCodes
  }

  /**
   * Regenerate QR code for existing table
   */
  public async regenerateQRCode(
    qrCodeId: string,
    options: QRCodeOptions = {}
  ): Promise<TableQRCode> {
    const tableQRCode = await TableQRCode.findOrFail(qrCodeId)

    // Generate new QR code URL and image
    const qrCodeUrl = `${this.baseUrl}/table/${tableQRCode.id}`
    const qrCodeImage = await this.generateQRCodeImage(qrCodeUrl, options)

    // Update QR code
    tableQRCode.qrCodeUrl = qrCodeUrl
    tableQRCode.qrCodeImage = qrCodeImage
    tableQRCode.generatedAt = DateTime.now()
    await tableQRCode.save()

    return tableQRCode
  }

  /**
   * Generate QR code image as base64 string
   */
  private async generateQRCodeImage(url: string, options: QRCodeOptions = {}): Promise<string> {
    const defaultOptions = {
      width: 300,
      errorCorrectionLevel: 'M' as const,
      color: {
        dark: '#000000',
        light: '#ffffff',
      },
    }

    const qrOptions = { ...defaultOptions, ...options }

    try {
      const qrCodeDataURL = await QRCode.toDataURL(url, qrOptions)
      return qrCodeDataURL
    } catch (error) {
      throw new Error(`Failed to generate QR code image: ${error.message}`)
    }
  }

  /**
   * Deactivate QR code
   */
  public async deactivateQRCode(qrCodeId: string): Promise<void> {
    const tableQRCode = await TableQRCode.findOrFail(qrCodeId)
    await tableQRCode.deactivate()
  }

  /**
   * Activate QR code
   */
  public async activateQRCode(qrCodeId: string): Promise<void> {
    const tableQRCode = await TableQRCode.findOrFail(qrCodeId)
    await tableQRCode.activate()
  }

  /**
   * Get QR code analytics for a vendor
   */
  public async getQRCodeAnalytics(vendorId: string) {
    const qrCodes = await TableQRCode.query()
      .where('vendorId', vendorId)
      .preload('section')
      .preload('lot')

    const totalQRCodes = qrCodes.length
    const activeQRCodes = qrCodes.filter((qr) => qr.isActive).length
    const totalScans = qrCodes.reduce((sum, qr) => sum + qr.scanCount, 0)
    const averageScansPerTable = totalQRCodes > 0 ? totalScans / totalQRCodes : 0

    const sectionStats = qrCodes.reduce(
      (stats, qr) => {
        const sectionName = qr.section?.name || 'Unknown'
        if (!stats[sectionName]) {
          stats[sectionName] = { tables: 0, scans: 0 }
        }
        stats[sectionName].tables += 1
        stats[sectionName].scans += qr.scanCount
        return stats
      },
      {} as Record<string, { tables: number; scans: number }>
    )

    return {
      totalQRCodes,
      activeQRCodes,
      inactiveQRCodes: totalQRCodes - activeQRCodes,
      totalScans,
      averageScansPerTable: Math.round(averageScansPerTable * 100) / 100,
      sectionStats,
    }
  }
}
