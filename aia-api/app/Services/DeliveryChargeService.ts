import Order from 'App/Models/Order'
import Vendor from 'App/Models/Vendor'
import Branch from 'App/Models/Branch'
import { DeliveryPricingCalculator } from 'App/Utils/DeliveryPricingCalculator'
import { CentralizedDeliveryPricingCalculator } from 'App/Utils/CentralizedDeliveryPricingCalculator'

export default class DeliveryChargeService {
  /**
   * Calculate and apply delivery charges for delivery orders
   */
  public static async applyDeliveryCharges(order: Order): Promise<void> {
    // Only apply delivery charges for delivery orders
    if (order.delivery !== 'Delivery') {
      return
    }

    // Check if delivery address is provided in meta
    const deliveryAddress = order.meta?.deliveryAddress
    if (!deliveryAddress || !deliveryAddress.coordinates) {
      console.log(`⚠️ No delivery address found for delivery order ${order.id}`)
      return
    }

    try {
      // Load vendor and branch for location and pricing information
      await order.load('vendor')
      await order.load('branch')

      if (!order.vendor || !order.branch) {
        console.error(`❌ Vendor or branch not found for order ${order.id}`)
        return
      }

      // Get vendor location from branch
      const vendorLocation = {
        lat: parseFloat(order.branch.location?.coordinates?.lat || '0'),
        lng: parseFloat(order.branch.location?.coordinates?.lng || '0'),
      }

      // Get customer location from order meta
      const customerLocation = {
        lat: parseFloat(deliveryAddress.coordinates.lat),
        lng: parseFloat(deliveryAddress.coordinates.lng),
      }

      // Validate coordinates
      if (
        !vendorLocation.lat ||
        !vendorLocation.lng ||
        !customerLocation.lat ||
        !customerLocation.lng
      ) {
        console.error(`❌ Invalid coordinates for delivery calculation on order ${order.id}`)
        return
      }

      // Calculate order value for delivery fee calculation
      const orderValue = await order.calculateBaseAmount()

      // Try centralized pricing first, fallback to vendor pricing
      const pricingResult = await CentralizedDeliveryPricingCalculator.calculateWithFallback(
        vendorLocation,
        customerLocation,
        order.vendor.pricingStructure,
        orderValue
      )

      // Check if delivery is available
      if (pricingResult.errors.length > 0 || pricingResult.deliveryOptions.length === 0) {
        console.error(`❌ Delivery calculation failed for order ${order.id}:`, pricingResult.errors)
        return
      }

      // Use the first available delivery option (highest priority)
      const selectedDelivery =
        pricingResult.deliveryOptions.find((option) => option.isAvailable) ||
        pricingResult.deliveryOptions[0]

      if (!selectedDelivery) {
        console.error(`❌ No delivery options available for order ${order.id}`)
        return
      }

      // Apply delivery charges to order meta
      const currentMeta = order.meta || {}
      const currentCharges = currentMeta.charges || {}

      // Add delivery fee to charges
      currentCharges['Delivery Fee'] = selectedDelivery.fee

      // Update order with delivery information
      const updatedMeta = {
        ...currentMeta,
        charges: currentCharges,
        deliveryCalculation: {
          distance: pricingResult.distance,
          estimatedTime: selectedDelivery.estimatedTime,
          vehicleType: selectedDelivery.vehicleType,
          priceBreakdown: selectedDelivery.priceBreakdown,
          pricingSystem: pricingResult.fallbackUsed ? 'vendor_fallback' : 'centralized',
          optionUsed: selectedDelivery.name,
          configId: selectedDelivery.configId,
        },
      }

      // Update order fields
      order.meta = updatedMeta
      order.deliveryFee = selectedDelivery.fee
      order.deliveryDistance = pricingResult.distance
      order.estimatedDeliveryTime = selectedDelivery.estimatedTime

      await order.save()

      console.log(
        `✅ Delivery charges applied to order ${order.id}: KES ${selectedDelivery.fee} for ${pricingResult.distance}km (${selectedDelivery.name})`
      )
    } catch (error) {
      console.error(`❌ Error applying delivery charges to order ${order.id}:`, error)
    }
  }

  /**
   * Calculate delivery charges without applying them (for estimation)
   */
  public static async calculateDeliveryCharges(order: Order): Promise<{
    deliveryFee: number
    distance: number
    estimatedTime: number
    isAvailable: boolean
    errors: string[]
  }> {
    const result = {
      deliveryFee: 0,
      distance: 0,
      estimatedTime: 0,
      isAvailable: false,
      errors: [],
    }

    // Only calculate for delivery orders
    if (order.delivery !== 'Delivery') {
      result.errors.push('Not a delivery order')
      return result
    }

    // Check if delivery address is provided
    const deliveryAddress = order.meta?.deliveryAddress
    if (!deliveryAddress || !deliveryAddress.coordinates) {
      result.errors.push('No delivery address provided')
      return result
    }

    try {
      // Load vendor and branch
      await order.load('vendor')
      await order.load('branch')

      if (!order.vendor || !order.branch) {
        result.errors.push('Vendor or branch not found')
        return result
      }

      // Get locations
      const vendorLocation = {
        lat: parseFloat(order.branch.location?.coordinates?.lat || '0'),
        lng: parseFloat(order.branch.location?.coordinates?.lng || '0'),
      }

      const customerLocation = {
        lat: parseFloat(deliveryAddress.coordinates.lat),
        lng: parseFloat(deliveryAddress.coordinates.lng),
      }

      // Calculate order value
      const orderValue = await order.calculateBaseAmount()

      // Parse vendor pricing
      const vendorPricing = DeliveryPricingCalculator.parseVendorPricing(
        order.vendor.pricingStructure
      )

      // Calculate delivery pricing
      const pricingResult = DeliveryPricingCalculator.calculateDeliveryPricing(
        vendorLocation,
        customerLocation,
        vendorPricing,
        orderValue
      )

      if (pricingResult.errors.length > 0) {
        result.errors = pricingResult.errors
        return result
      }

      const selectedDelivery =
        pricingResult.deliveryOptions.find((option) => option.isAvailable) ||
        pricingResult.deliveryOptions[0]

      if (selectedDelivery) {
        result.deliveryFee = selectedDelivery.fee
        result.distance = pricingResult.distance
        result.estimatedTime = selectedDelivery.estimatedTime
        result.isAvailable = true
      } else {
        result.errors.push('No delivery options available')
      }
    } catch (error) {
      result.errors.push(`Calculation error: ${error.message}`)
    }

    return result
  }
}
