import WebSocketManager from './WebSocketManager'
import { SocketUser, WebSocketConnection } from './WebSocketServer'
import { DateTime } from 'luxon'
import User from '../Models/User'
import Order from '../Models/Order'
import Department from '../Models/Department'

export interface SubscriptionRequest {
  type: 'order' | 'department' | 'vendor' | 'branch' | 'staff' | 'customer' | 'admin'
  target_id: string
  sub_type?: string
  filters?: Record<string, any>
  metadata?: Record<string, any>
}

export interface SubscriptionInfo {
  id: string
  socket_id: string
  user_id: string
  type: string
  target_id: string
  sub_type?: string
  filters?: Record<string, any>
  subscribed_at: DateTime
  last_activity: DateTime
  metadata?: Record<string, any>
}

export interface SubscriptionValidationResult {
  allowed: boolean
  reason?: string
  required_permissions?: string[]
  suggested_alternatives?: string[]
}

/**
 * Service for managing WebSocket subscriptions with authentication and authorization
 */
export default class WebSocketSubscriptionManager {
  private static subscriptions: Map<string, SubscriptionInfo> = new Map()
  private static userSubscriptions: Map<string, Set<string>> = new Map() // userId -> subscription IDs
  private static channelSubscriptions: Map<string, Set<string>> = new Map() // channel -> subscription IDs

  /**
   * Subscribe user to a specific channel with validation
   */
  public static async subscribe(
    socketId: string,
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<{
    success: boolean
    subscription_id?: string
    channel?: string
    error?: string
    validation_result?: SubscriptionValidationResult
  }> {
    try {
      // Validate subscription request
      const validation = await this.validateSubscription(user, request)
      
      if (!validation.allowed) {
        return {
          success: false,
          error: validation.reason || 'Subscription not allowed',
          validation_result: validation
        }
      }

      // Generate subscription ID and channel name
      const subscriptionId = this.generateSubscriptionId(user.id, request)
      const channel = this.buildChannelName(request)

      // Create subscription info
      const subscription: SubscriptionInfo = {
        id: subscriptionId,
        socket_id: socketId,
        user_id: user.id,
        type: request.type,
        target_id: request.target_id,
        sub_type: request.sub_type,
        filters: request.filters,
        subscribed_at: DateTime.now(),
        last_activity: DateTime.now(),
        metadata: request.metadata
      }

      // Store subscription
      this.subscriptions.set(subscriptionId, subscription)

      // Track user subscriptions
      if (!this.userSubscriptions.has(user.id)) {
        this.userSubscriptions.set(user.id, new Set())
      }
      this.userSubscriptions.get(user.id)!.add(subscriptionId)

      // Track channel subscriptions
      if (!this.channelSubscriptions.has(channel)) {
        this.channelSubscriptions.set(channel, new Set())
      }
      this.channelSubscriptions.get(channel)!.add(subscriptionId)

      // Subscribe to WebSocket channel
      const wsServer = WebSocketManager.getServer()
      const socket = wsServer.getIO().sockets.sockets.get(socketId)
      if (socket) {
        socket.join(channel)
      }

      console.log(`User ${user.name} subscribed to ${channel} (${subscriptionId})`)

      return {
        success: true,
        subscription_id: subscriptionId,
        channel
      }

    } catch (error) {
      console.error('Error creating subscription:', error)
      return {
        success: false,
        error: 'Failed to create subscription'
      }
    }
  }

  /**
   * Unsubscribe from a specific subscription
   */
  public static async unsubscribe(
    socketId: string,
    subscriptionId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const subscription = this.subscriptions.get(subscriptionId)
      if (!subscription) {
        return { success: false, error: 'Subscription not found' }
      }

      // Verify socket ownership
      if (subscription.socket_id !== socketId) {
        return { success: false, error: 'Unauthorized to unsubscribe' }
      }

      // Build channel name for unsubscription
      const channel = this.buildChannelName({
        type: subscription.type as any,
        target_id: subscription.target_id,
        sub_type: subscription.sub_type
      })

      // Remove from WebSocket channel
      const wsServer = WebSocketManager.getServer()
      const socket = wsServer.getIO().sockets.sockets.get(socketId)
      if (socket) {
        socket.leave(channel)
      }

      // Remove from tracking
      this.subscriptions.delete(subscriptionId)
      
      const userSubs = this.userSubscriptions.get(subscription.user_id)
      if (userSubs) {
        userSubs.delete(subscriptionId)
        if (userSubs.size === 0) {
          this.userSubscriptions.delete(subscription.user_id)
        }
      }

      const channelSubs = this.channelSubscriptions.get(channel)
      if (channelSubs) {
        channelSubs.delete(subscriptionId)
        if (channelSubs.size === 0) {
          this.channelSubscriptions.delete(channel)
        }
      }

      console.log(`Unsubscribed from ${channel} (${subscriptionId})`)

      return { success: true }

    } catch (error) {
      console.error('Error unsubscribing:', error)
      return { success: false, error: 'Failed to unsubscribe' }
    }
  }

  /**
   * Unsubscribe all subscriptions for a socket
   */
  public static async unsubscribeAll(socketId: string): Promise<void> {
    const subscriptionsToRemove = Array.from(this.subscriptions.values())
      .filter(sub => sub.socket_id === socketId)

    for (const subscription of subscriptionsToRemove) {
      await this.unsubscribe(socketId, subscription.id)
    }
  }

  /**
   * Get all subscriptions for a user
   */
  public static getUserSubscriptions(userId: string): SubscriptionInfo[] {
    const userSubIds = this.userSubscriptions.get(userId)
    if (!userSubIds) return []

    return Array.from(userSubIds)
      .map(id => this.subscriptions.get(id))
      .filter(Boolean) as SubscriptionInfo[]
  }

  /**
   * Get all subscriptions for a channel
   */
  public static getChannelSubscriptions(channel: string): SubscriptionInfo[] {
    const channelSubIds = this.channelSubscriptions.get(channel)
    if (!channelSubIds) return []

    return Array.from(channelSubIds)
      .map(id => this.subscriptions.get(id))
      .filter(Boolean) as SubscriptionInfo[]
  }

  /**
   * Update subscription activity
   */
  public static updateSubscriptionActivity(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId)
    if (subscription) {
      subscription.last_activity = DateTime.now()
    }
  }

  /**
   * Validate subscription request
   */
  private static async validateSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    const userRoles = user.roles || []

    // Admin can subscribe to anything
    if (userRoles.includes('admin')) {
      return { allowed: true }
    }

    switch (request.type) {
      case 'order':
        return await this.validateOrderSubscription(user, request)
      
      case 'department':
        return await this.validateDepartmentSubscription(user, request)
      
      case 'vendor':
        return await this.validateVendorSubscription(user, request)
      
      case 'branch':
        return await this.validateBranchSubscription(user, request)
      
      case 'staff':
        return await this.validateStaffSubscription(user, request)
      
      case 'customer':
        return await this.validateCustomerSubscription(user, request)
      
      default:
        return {
          allowed: false,
          reason: `Unknown subscription type: ${request.type}`
        }
    }
  }

  /**
   * Validate order subscription
   */
  private static async validateOrderSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    try {
      const order = await Order.find(request.target_id)
      if (!order) {
        return {
          allowed: false,
          reason: 'Order not found'
        }
      }

      // Customer can only subscribe to their own orders
      if (user.roles.includes('customer')) {
        if (order.userId !== user.id) {
          return {
            allowed: false,
            reason: 'Can only subscribe to your own orders'
          }
        }
        return { allowed: true }
      }

      // Staff can subscribe to orders from their vendor/branch
      if (user.roles.some(role => ['waiter', 'chef', 'manager', 'cashier'].includes(role))) {
        if (user.vendorId && order.vendorId === user.vendorId) {
          return { allowed: true }
        }
        return {
          allowed: false,
          reason: 'Can only subscribe to orders from your vendor'
        }
      }

      return {
        allowed: false,
        reason: 'Insufficient permissions for order subscription'
      }

    } catch (error) {
      return {
        allowed: false,
        reason: 'Error validating order subscription'
      }
    }
  }

  /**
   * Validate department subscription
   */
  private static async validateDepartmentSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    try {
      const department = await Department.find(request.target_id)
      if (!department) {
        return {
          allowed: false,
          reason: 'Department not found'
        }
      }

      // Check if user is assigned to this department or is a manager
      if (user.roles.includes('manager') && user.vendorId === department.vendorId) {
        return { allowed: true }
      }

      if (user.departmentIds?.includes(request.target_id)) {
        return { allowed: true }
      }

      return {
        allowed: false,
        reason: 'Not assigned to this department',
        suggested_alternatives: [`vendor:${department.vendorId}:staff`]
      }

    } catch (error) {
      return {
        allowed: false,
        reason: 'Error validating department subscription'
      }
    }
  }

  /**
   * Validate vendor subscription
   */
  private static async validateVendorSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    if (user.vendorId === request.target_id) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason: 'Can only subscribe to your own vendor'
    }
  }

  /**
   * Validate branch subscription
   */
  private static async validateBranchSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    if (user.branchId === request.target_id) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason: 'Can only subscribe to your own branch'
    }
  }

  /**
   * Validate staff subscription
   */
  private static async validateStaffSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    // Users can subscribe to their own staff updates
    if (request.target_id === user.id) {
      return { allowed: true }
    }

    // Managers can subscribe to their staff
    if (user.roles.includes('manager')) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason: 'Can only subscribe to your own staff updates or manage staff as a manager'
    }
  }

  /**
   * Validate customer subscription
   */
  private static async validateCustomerSubscription(
    user: SocketUser,
    request: SubscriptionRequest
  ): Promise<SubscriptionValidationResult> {
    // Users can only subscribe to their own customer updates
    if (request.target_id === user.id && user.roles.includes('customer')) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason: 'Can only subscribe to your own customer updates'
    }
  }

  /**
   * Build channel name from subscription request
   */
  private static buildChannelName(request: SubscriptionRequest): string {
    let channel = `${request.type}:${request.target_id}`
    
    if (request.sub_type) {
      channel += `:${request.sub_type}`
    }

    return channel
  }

  /**
   * Generate unique subscription ID
   */
  private static generateSubscriptionId(userId: string, request: SubscriptionRequest): string {
    const timestamp = DateTime.now().toMillis()
    const hash = Buffer.from(`${userId}:${request.type}:${request.target_id}:${timestamp}`)
      .toString('base64')
      .substring(0, 16)
    
    return `sub_${hash}`
  }

  /**
   * Get subscription statistics
   */
  public static getStatistics(): {
    total_subscriptions: number
    active_users: number
    active_channels: number
    subscriptions_by_type: Record<string, number>
    subscriptions_by_channel: Record<string, number>
  } {
    const subscriptionsByType = {}
    const subscriptionsByChannel = {}

    this.subscriptions.forEach(sub => {
      subscriptionsByType[sub.type] = (subscriptionsByType[sub.type] || 0) + 1
    })

    this.channelSubscriptions.forEach((subs, channel) => {
      subscriptionsByChannel[channel] = subs.size
    })

    return {
      total_subscriptions: this.subscriptions.size,
      active_users: this.userSubscriptions.size,
      active_channels: this.channelSubscriptions.size,
      subscriptions_by_type: subscriptionsByType,
      subscriptions_by_channel: subscriptionsByChannel
    }
  }

  /**
   * Cleanup stale subscriptions
   */
  public static cleanupStaleSubscriptions(maxInactiveMinutes: number = 30): number {
    const cutoff = DateTime.now().minus({ minutes: maxInactiveMinutes })
    let cleaned = 0

    this.subscriptions.forEach((subscription, id) => {
      if (subscription.last_activity < cutoff) {
        this.unsubscribe(subscription.socket_id, id)
        cleaned++
      }
    })

    return cleaned
  }
}
