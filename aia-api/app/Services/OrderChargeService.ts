import Order from 'App/Models/Order'
import ChargeConfiguration from 'App/Models/ChargeConfiguration'

export default class OrderChargeService {
  /**
   * Calculate all applicable charges for an order
   */
  public static async calculateCharges(order: Order): Promise<{
    charges: Array<{
      configurationId: number
      name: string
      type: 'fixed' | 'percentage'
      amount: number
      isTax: boolean
      isMandatory: boolean
    }>
    totalCharges: number
    finalAmount: number
  }> {
    // Get base amount from order items
    const baseAmount = await order.calculateBaseAmount()

    // Get all applicable charge configurations
    const configurations = await ChargeConfiguration.query()
      .where('is_active', true)
      .orderBy('priority', 'desc')
      .orderBy('sort_order', 'asc')
      .exec()

    const charges: Array<{
      configurationId: number
      name: string
      type: 'fixed' | 'percentage'
      amount: number
      isTax: boolean
      isMandatory: boolean
    }> = []
    let totalCharges = 0

    // Apply each charge configuration
    for (const config of configurations) {
      const isApplicable = await config.isApplicable(order, baseAmount)
      if (isApplicable) {
        const chargeAmount = config.calculateCharge(baseAmount)
        charges.push({
          configurationId: config.id,
          name: config.name,
          type: config.type,
          amount: chargeAmount,
          isTax: config.isTax,
          isMandatory: config.isMandatory,
        })
        totalCharges += chargeAmount
      }
    }

    return {
      charges,
      totalCharges,
      finalAmount: baseAmount + totalCharges,
    }
  }

  /**
   * Apply charges to an order and save them in the meta field
   */
  public static async applyChargesToOrder(order: Order): Promise<void> {
    const { charges, totalCharges, finalAmount } = await this.calculateCharges(order)

    // Convert charges array to simple key-value format expected by the system
    const chargesObject: Record<string, number> = {}
    charges.forEach((charge) => {
      chargesObject[charge.name] = Number(charge.amount) // Ensure numeric values
    })

    // Update order meta with charges in the correct format
    const meta = order.meta || {}
    meta.charges = chargesObject // Store as simple object, not array
    meta.totalCharges = Number(totalCharges) // Ensure numeric
    meta.finalAmount = Number(finalAmount) // Ensure numeric

    order.meta = meta
    await order.save()
  }

  /**
   * Get a breakdown of charges for an order
   */
  public static async getChargeBreakdown(order: Order): Promise<{
    baseAmount: number
    charges: Array<{
      configurationId: number
      name: string
      type: 'fixed' | 'percentage'
      amount: number
      isTax: boolean
      isMandatory: boolean
    }>
    totalCharges: number
    finalAmount: number
  }> {
    const baseAmount = await order.calculateBaseAmount()
    const { charges, totalCharges, finalAmount } = await this.calculateCharges(order)

    return {
      baseAmount,
      charges,
      totalCharges,
      finalAmount,
    }
  }
}
