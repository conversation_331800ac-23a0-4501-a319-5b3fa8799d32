import Env from '@ioc:Adonis/Core/Env'

/**
 * M-Pesa Configuration Validator
 * 
 * This service validates that all required M-Pesa environment variables
 * are properly configured and provides helpful error messages for missing
 * or invalid configurations.
 */
export default class MpesaConfigValidator {
  private static requiredVars = [
    'MPESA_CONSUMER_KEY',
    'MPESA_CONSUMER_SECRET',
    'MPESA_ENVIRONMENT',
    'MPESA_STORE',
    'MPESA_SHORTCODE',
    'MPESA_PASSKEY'
  ]

  private static optionalVars = [
    'MPESA_INITIATOR_NAME',
    'MPESA_INITIATOR_PASSWORD',
    'MPESA_CALLBACK_DOMAIN',
    'MPESA_VALIDATION_URL',
    'MPESA_CONFIRMATION_URL',
    'MPESA_LIPA_NA_MPESA_SHORTCODE',
    'MPESA_SECURITY_CREDENTIAL',
    'MPESA_CERT_PATH'
  ]

  /**
   * Validate all M-Pesa configuration
   */
  public static validate(): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    // Check required variables
    for (const varName of this.requiredVars) {
      const value = Env.get(varName)
      if (!value) {
        errors.push(`Missing required environment variable: ${varName}`)
      } else {
        // Validate specific formats
        switch (varName) {
          case 'MPESA_ENVIRONMENT':
            if (!['sandbox', 'live'].includes(value)) {
              errors.push(`Invalid MPESA_ENVIRONMENT: ${value}. Must be 'sandbox' or 'live'`)
            }
            break
          case 'MPESA_STORE':
          case 'MPESA_SHORTCODE':
            if (isNaN(Number(value))) {
              errors.push(`Invalid ${varName}: ${value}. Must be a number`)
            }
            break
          case 'MPESA_PASSKEY':
            if (value.length < 10) {
              warnings.push(`${varName} seems too short. Verify it's correct.`)
            }
            break
        }
      }
    }

    // Check optional variables and provide warnings
    for (const varName of this.optionalVars) {
      const value = Env.get(varName)
      if (!value) {
        warnings.push(`Optional environment variable not set: ${varName}`)
      }
    }

    // Validate callback domain
    const callbackDomain = Env.get('MPESA_CALLBACK_DOMAIN') || Env.get('APP_URL')
    if (!callbackDomain) {
      errors.push('No callback domain configured. Set MPESA_CALLBACK_DOMAIN or APP_URL')
    } else if (!callbackDomain.startsWith('https://')) {
      if (Env.get('MPESA_ENVIRONMENT') === 'live') {
        errors.push('Callback domain must use HTTPS in live environment')
      } else {
        warnings.push('Callback domain should use HTTPS, even in sandbox')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Get M-Pesa configuration summary
   */
  public static getConfigSummary(): Record<string, any> {
    return {
      environment: Env.get('MPESA_ENVIRONMENT'),
      store: Env.get('MPESA_STORE'),
      shortcode: Env.get('MPESA_SHORTCODE'),
      callbackDomain: Env.get('MPESA_CALLBACK_DOMAIN') || Env.get('APP_URL'),
      hasInitiatorCredentials: !!(Env.get('MPESA_INITIATOR_NAME') && Env.get('MPESA_INITIATOR_PASSWORD')),
      configuredAt: new Date().toISOString()
    }
  }

  /**
   * Log configuration status
   */
  public static logConfigStatus(): void {
    const validation = this.validate()
    const summary = this.getConfigSummary()

    console.log('=== M-PESA CONFIGURATION STATUS ===')
    console.log('Configuration Summary:', JSON.stringify(summary, null, 2))
    
    if (validation.isValid) {
      console.log('✅ M-Pesa configuration is valid')
    } else {
      console.log('❌ M-Pesa configuration has errors')
    }

    if (validation.errors.length > 0) {
      console.log('🚨 Configuration Errors:')
      validation.errors.forEach(error => console.log(`  - ${error}`))
    }

    if (validation.warnings.length > 0) {
      console.log('⚠️  Configuration Warnings:')
      validation.warnings.forEach(warning => console.log(`  - ${warning}`))
    }

    console.log('=====================================')
  }

  /**
   * Validate configuration and throw error if invalid
   */
  public static validateOrThrow(): void {
    const validation = this.validate()
    
    if (!validation.isValid) {
      const errorMessage = [
        'M-Pesa configuration is invalid:',
        ...validation.errors.map(error => `  - ${error}`)
      ].join('\n')
      
      throw new Error(errorMessage)
    }
  }
}
