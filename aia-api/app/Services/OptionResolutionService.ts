import Product from 'App/Models/Product'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption from 'App/Models/ServiceConfigurationOption'
import ServiceOption from 'App/Models/ServiceOption'
import ProductServiceOption from 'App/Models/ProductServiceOption'
import Duration from 'App/Models/Duration'
import Redis from '@ioc:Adonis/Addons/Redis'

export interface ResolvedOption {
  id: string
  name: string
  type: string
  description: string | null
  priceAdjustment: number
  isDefault: boolean
  sortOrder: number
  source: 'configuration' | 'direct'
  
  // Calendar integration data
  durationId?: string | null
  duration?: Duration | null
  calendarBlockMinutes?: number | null
  
  // Constraints and validation
  constraints: Record<string, any>
  canCombineWith?: string[]
  requiredOptions?: string[]
}

export interface ResolvedOptionSet {
  productId: string
  productName: string
  basePrice: number
  
  // Options grouped by type
  optionsByType: Record<string, ResolvedOption[]>
  
  // All options flat
  allOptions: ResolvedOption[]
  
  // Default selections
  defaultOptions: ResolvedOption[]
  
  // Duration options with calendar data
  durationOptions: ResolvedOption[]
  
  // Metadata
  totalOptions: number
  hasCalendarIntegration: boolean
  cacheKey: string
  resolvedAt: Date
}

export default class OptionResolutionService {
  private static CACHE_TTL = 300 // 5 minutes

  /**
   * Resolve all options for a product (configuration + direct options)
   */
  public static async resolveProductOptions(productId: string, useCache: boolean = true): Promise<ResolvedOptionSet> {
    const cacheKey = `product_options:${productId}`
    
    // Try cache first
    if (useCache) {
      const cached = await this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }

    // Load product with relationships
    const product = await Product.query()
      .where('id', productId)
      .preload('serviceConfiguration', (query) => {
        query.preload('options', (optQuery) => {
          optQuery.where('active', true).preload('duration').orderBy('sortOrder')
        })
      })
      .preload('directServiceOptions', (query) => {
        query.preload('duration').pivotColumns(['price_adjustment_override', 'is_default', 'sort_order'])
      })
      .firstOrFail()

    const resolvedOptions: ResolvedOption[] = []

    // Resolve configuration options
    if (product.serviceConfiguration) {
      const configOptions = await this.resolveConfigurationOptions(product.serviceConfiguration)
      resolvedOptions.push(...configOptions)
    }

    // Resolve direct service options
    const directOptions = await this.resolveDirectOptions(product)
    resolvedOptions.push(...directOptions)

    // Group and organize options
    const optionSet = this.organizeOptions(product, resolvedOptions, cacheKey)

    // Cache the result
    if (useCache) {
      await this.setCache(cacheKey, optionSet)
    }

    return optionSet
  }

  /**
   * Resolve options from a service configuration
   */
  private static async resolveConfigurationOptions(configuration: ServiceConfiguration): Promise<ResolvedOption[]> {
    const options: ResolvedOption[] = []

    for (const option of configuration.options) {
      const resolvedOption: ResolvedOption = {
        id: option.id,
        name: option.name,
        type: option.type,
        description: option.description,
        priceAdjustment: option.priceAdjustment,
        isDefault: option.isDefault,
        sortOrder: option.sortOrder,
        source: 'configuration',
        durationId: option.durationId,
        duration: option.duration,
        calendarBlockMinutes: option.duration?.calendarBlockMinutes || null,
        constraints: option.constraints,
        requiredOptions: option.getRequiredOptions()
      }

      options.push(resolvedOption)
    }

    return options
  }

  /**
   * Resolve direct service options attached to a product
   */
  private static async resolveDirectOptions(product: Product): Promise<ResolvedOption[]> {
    const options: ResolvedOption[] = []

    for (const serviceOption of product.directServiceOptions) {
      const pivot = serviceOption.$pivot as ProductServiceOption

      const resolvedOption: ResolvedOption = {
        id: serviceOption.id,
        name: serviceOption.name,
        type: serviceOption.type,
        description: serviceOption.description,
        priceAdjustment: pivot.priceAdjustmentOverride ?? serviceOption.defaultPriceAdjustment,
        isDefault: pivot.isDefault,
        sortOrder: pivot.sortOrder,
        source: 'direct',
        durationId: serviceOption.durationId,
        duration: serviceOption.duration,
        calendarBlockMinutes: serviceOption.duration?.calendarBlockMinutes || null,
        constraints: serviceOption.constraints,
        requiredOptions: serviceOption.getRequiredOptions()
      }

      options.push(resolvedOption)
    }

    return options
  }

  /**
   * Organize resolved options into structured format
   */
  private static organizeOptions(product: Product, options: ResolvedOption[], cacheKey: string): ResolvedOptionSet {
    // Group by type
    const optionsByType = options.reduce((grouped, option) => {
      if (!grouped[option.type]) {
        grouped[option.type] = []
      }
      grouped[option.type].push(option)
      return grouped
    }, {} as Record<string, ResolvedOption[]>)

    // Sort each type by sort order
    Object.keys(optionsByType).forEach(type => {
      optionsByType[type].sort((a, b) => a.sortOrder - b.sortOrder)
    })

    // Get default options
    const defaultOptions = options.filter(option => option.isDefault)

    // Get duration options
    const durationOptions = options.filter(option => option.type === 'duration')

    // Check for calendar integration
    const hasCalendarIntegration = durationOptions.some(option => option.calendarBlockMinutes !== null)

    return {
      productId: product.id,
      productName: product.name,
      basePrice: product.price,
      optionsByType,
      allOptions: options.sort((a, b) => a.sortOrder - b.sortOrder),
      defaultOptions,
      durationOptions,
      totalOptions: options.length,
      hasCalendarIntegration,
      cacheKey,
      resolvedAt: new Date()
    }
  }

  /**
   * Calculate total price for selected options
   */
  public static calculateTotalPrice(basePrice: number, selectedOptions: ResolvedOption[]): number {
    const adjustments = selectedOptions.reduce((total, option) => {
      return total + option.priceAdjustment
    }, 0)

    return basePrice + adjustments
  }

  /**
   * Validate option selection (check constraints and dependencies)
   */
  public static validateOptionSelection(
    selectedOptions: ResolvedOption[],
    allOptions: ResolvedOption[]
  ): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []
    const selectedIds = selectedOptions.map(opt => opt.id)

    // Check for conflicting options
    for (const option of selectedOptions) {
      for (const otherOption of selectedOptions) {
        if (option.id !== otherOption.id) {
          // Check if options can be combined
          if (option.constraints.excludes?.includes(otherOption.id) || 
              option.constraints.excludes?.includes(otherOption.type)) {
            errors.push(`Option '${option.name}' cannot be combined with '${otherOption.name}'`)
          }
        }
      }

      // Check required dependencies
      if (option.requiredOptions && option.requiredOptions.length > 0) {
        const missingRequired = option.requiredOptions.filter(reqId => !selectedIds.includes(reqId))
        if (missingRequired.length > 0) {
          const missingNames = allOptions
            .filter(opt => missingRequired.includes(opt.id))
            .map(opt => opt.name)
          errors.push(`Option '${option.name}' requires: ${missingNames.join(', ')}`)
        }
      }
    }

    // Check for multiple options of same type (except add-ons)
    const typeGroups = selectedOptions.reduce((groups, option) => {
      if (!groups[option.type]) {
        groups[option.type] = []
      }
      groups[option.type].push(option)
      return groups
    }, {} as Record<string, ResolvedOption[]>)

    Object.entries(typeGroups).forEach(([type, options]) => {
      if (type !== 'add_on' && options.length > 1) {
        warnings.push(`Multiple ${type} options selected: ${options.map(o => o.name).join(', ')}`)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Get calendar requirements for selected options
   */
  public static getCalendarRequirements(selectedOptions: ResolvedOption[]): {
    totalCalendarMinutes: number
    durationBreakdown: { name: string; minutes: number }[]
    requiresBranchHours: boolean
    requiredStaff: number
    requiredEquipment: string[]
  } {
    const durationOptions = selectedOptions.filter(opt => opt.duration)
    
    let totalMinutes = 0
    let requiresBranchHours = false
    let requiredStaff = 0
    const requiredEquipment: string[] = []
    const durationBreakdown: { name: string; minutes: number }[] = []

    for (const option of durationOptions) {
      if (option.duration) {
        totalMinutes += option.duration.calendarBlockMinutes
        durationBreakdown.push({
          name: option.name,
          minutes: option.duration.calendarBlockMinutes
        })

        if (option.duration.shouldRespectBranchHours()) {
          requiresBranchHours = true
        }

        requiredStaff = Math.max(requiredStaff, option.duration.getRequiredStaff())
        requiredEquipment.push(...option.duration.getRequiredEquipment())
      }
    }

    return {
      totalCalendarMinutes: totalMinutes,
      durationBreakdown,
      requiresBranchHours,
      requiredStaff,
      requiredEquipment: [...new Set(requiredEquipment)] // Remove duplicates
    }
  }

  /**
   * Clear cache for a product
   */
  public static async clearProductCache(productId: string): Promise<void> {
    const cacheKey = `product_options:${productId}`
    await Redis.del(cacheKey)
  }

  /**
   * Clear all option resolution cache
   */
  public static async clearAllCache(): Promise<void> {
    const keys = await Redis.keys('product_options:*')
    if (keys.length > 0) {
      await Redis.del(...keys)
    }
  }

  /**
   * Get data from cache
   */
  private static async getFromCache(key: string): Promise<ResolvedOptionSet | null> {
    try {
      const cached = await Redis.get(key)
      if (cached) {
        const parsed = JSON.parse(cached)
        // Convert date strings back to Date objects
        parsed.resolvedAt = new Date(parsed.resolvedAt)
        return parsed
      }
    } catch (error) {
      // Cache miss or error, continue without cache
    }
    return null
  }

  /**
   * Set data in cache
   */
  private static async setCache(key: string, data: ResolvedOptionSet): Promise<void> {
    try {
      await Redis.setex(key, this.CACHE_TTL, JSON.stringify(data))
    } catch (error) {
      // Cache error, continue without caching
    }
  }
}
