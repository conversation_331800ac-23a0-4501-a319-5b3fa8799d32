import TableQRCode from 'App/Models/TableQRCode'
import { DateTime } from 'luxon'

interface TableContext {
  qrCodeId: string
  tableNumber: string
  vendor: {
    id: string
    name: string
    details?: string
    logo?: any
  }
  branch: {
    id: string
    name: string
    location?: string
    details?: string
  }
  section: {
    id: string
    name: string
    details?: string
  }
  lot: {
    id: string
    name: string
    details?: string
  }
  isActive: boolean
  scanMetadata: {
    scanCount: number
    lastScannedAt: DateTime | null
    scannedAt: DateTime
  }
}

export default class QRCodeProcessingService {
  /**
   * Process QR code scan and return table context
   */
  public async processQRCodeScan(qrCodeId: string): Promise<TableContext> {
    // Find and validate QR code
    const tableQRCode = await this.validateAndGetQRCode(qrCodeId)

    // Record the scan
    await this.trackScan(qrCodeId)

    // Return complete table context
    return this.buildTableContext(tableQRCode)
  }

  /**
   * Validate QR code exists and is active
   */
  public async validateQRCode(qrCodeId: string): Promise<boolean> {
    try {
      const tableQRCode = await TableQRCode.query()
        .where('id', qrCodeId)
        .where('isActive', true)
        .first()

      return !!tableQRCode
    } catch (error) {
      return false
    }
  }

  /**
   * Track QR code scan
   */
  public async trackScan(qrCodeId: string): Promise<void> {
    const tableQRCode = await TableQRCode.findOrFail(qrCodeId)
    await tableQRCode.recordScan()
  }

  /**
   * Get table context without recording scan (for preview)
   */
  public async getTableContext(qrCodeId: string): Promise<TableContext> {
    const tableQRCode = await this.validateAndGetQRCode(qrCodeId)
    return this.buildTableContext(tableQRCode, false)
  }

  /**
   * Extract QR code ID from URL
   */
  public extractQRCodeId(qrCodeUrl: string): string | null {
    try {
      // Handle different URL formats:
      // https://app.appinapp.ke/table/01jrqr123abc456def
      // /table/01jrqr123abc456def
      // 01jrqr123abc456def

      const url = new URL(qrCodeUrl.startsWith('http') ? qrCodeUrl : `https://app.appinapp.ke${qrCodeUrl}`)
      const pathParts = url.pathname.split('/')
      
      // Find 'table' in path and get the next part
      const tableIndex = pathParts.indexOf('table')
      if (tableIndex !== -1 && pathParts[tableIndex + 1]) {
        return pathParts[tableIndex + 1]
      }

      return null
    } catch (error) {
      // If URL parsing fails, assume it's just the ID
      if (qrCodeUrl.match(/^[a-z0-9]{26}$/)) {
        return qrCodeUrl
      }
      return null
    }
  }

  /**
   * Get QR code usage statistics
   */
  public async getQRCodeStats(qrCodeId: string) {
    const tableQRCode = await TableQRCode.query()
      .where('id', qrCodeId)
      .preload('vendor')
      .preload('section')
      .firstOrFail()

    const recentScans = await this.getRecentScanCount(qrCodeId, 24) // Last 24 hours
    const weeklyScans = await this.getRecentScanCount(qrCodeId, 168) // Last 7 days

    return {
      totalScans: tableQRCode.scanCount,
      recentScans,
      weeklyScans,
      lastScannedAt: tableQRCode.lastScannedAt,
      usageFrequency: tableQRCode.usageFrequency,
      isRecentlyScanned: tableQRCode.isRecentlyScanned,
      tableInfo: {
        number: tableQRCode.tableNumber,
        section: tableQRCode.section?.name,
        restaurant: tableQRCode.vendor?.name,
      },
    }
  }

  /**
   * Validate and get QR code with all relationships
   */
  private async validateAndGetQRCode(qrCodeId: string): Promise<TableQRCode> {
    const tableQRCode = await TableQRCode.query()
      .where('id', qrCodeId)
      .preload('vendor')
      .preload('branch')
      .preload('section')
      .preload('lot')
      .first()

    if (!tableQRCode) {
      throw new Error('QR code not found')
    }

    if (!tableQRCode.isActive) {
      throw new Error('QR code is inactive')
    }

    return tableQRCode
  }

  /**
   * Build complete table context from QR code
   */
  private buildTableContext(tableQRCode: TableQRCode, recordScan: boolean = true): TableContext {
    return {
      qrCodeId: tableQRCode.id,
      tableNumber: tableQRCode.tableNumber,
      vendor: {
        id: tableQRCode.vendor.id,
        name: tableQRCode.vendor.name,
        details: tableQRCode.vendor.details,
        logo: tableQRCode.vendor.logo,
      },
      branch: {
        id: tableQRCode.branch.id,
        name: tableQRCode.branch.name,
        location: tableQRCode.branch.location,
        details: tableQRCode.branch.details,
      },
      section: {
        id: tableQRCode.section.id,
        name: tableQRCode.section.name,
        details: tableQRCode.section.details,
      },
      lot: {
        id: tableQRCode.lot.id,
        name: tableQRCode.lot.name,
        details: tableQRCode.lot.details,
      },
      isActive: tableQRCode.isActive,
      scanMetadata: {
        scanCount: tableQRCode.scanCount,
        lastScannedAt: tableQRCode.lastScannedAt,
        scannedAt: recordScan ? DateTime.now() : DateTime.now(),
      },
    }
  }

  /**
   * Get scan count for recent period
   */
  private async getRecentScanCount(qrCodeId: string, hours: number): Promise<number> {
    // This is a simplified implementation
    // In a real system, you might want to track individual scans in a separate table
    const tableQRCode = await TableQRCode.find(qrCodeId)
    
    if (!tableQRCode?.lastScannedAt) {
      return 0
    }

    const cutoff = DateTime.now().minus({ hours })
    if (tableQRCode.lastScannedAt > cutoff) {
      // Estimate based on total scans and time since creation
      const daysSinceCreation = DateTime.now().diff(tableQRCode.generatedAt, 'days').days
      const averageScansPerDay = tableQRCode.scanCount / Math.max(daysSinceCreation, 1)
      return Math.round(averageScansPerDay * (hours / 24))
    }

    return 0
  }

  /**
   * Bulk validate multiple QR codes
   */
  public async validateMultipleQRCodes(qrCodeIds: string[]): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {}

    const validQRCodes = await TableQRCode.query()
      .whereIn('id', qrCodeIds)
      .where('isActive', true)
      .select('id')

    const validIds = validQRCodes.map(qr => qr.id)

    for (const id of qrCodeIds) {
      results[id] = validIds.includes(id)
    }

    return results
  }
}
