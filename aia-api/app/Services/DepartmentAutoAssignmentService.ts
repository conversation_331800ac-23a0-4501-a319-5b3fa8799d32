import Department from 'App/Models/Department'
import OrderItem from 'App/Models/OrderItem'
import Product from 'App/Models/Product'
import ProductCategory from 'App/Models/ProductCategory'

export default class DepartmentAutoAssignmentService {
  /**
   * Auto-assign order items to appropriate departments
   */
  public static async assignOrderItemsToDepartments(orderId: string): Promise<{
    assigned: number
    failed: number
    details: Array<{
      itemId: number
      productName: string
      departmentName?: string
      error?: string
    }>
  }> {
    const result = {
      assigned: 0,
      failed: 0,
      details: []
    }

    // Get all order items for this order
    const orderItems = await OrderItem.query()
      .where('order_id', orderId)
      .preload('product', (productQuery) => {
        productQuery.preload('category')
      })
      .where('department_id', null) // Only unassigned items

    for (const item of orderItems) {
      try {
        const department = await this.findBestDepartmentForProduct(item.product)
        
        if (department) {
          await item.merge({ departmentId: department.id }).save()
          result.assigned++
          result.details.push({
            itemId: item.id,
            productName: item.product.name,
            departmentName: department.name
          })
        } else {
          result.failed++
          result.details.push({
            itemId: item.id,
            productName: item.product.name,
            error: 'No suitable department found'
          })
        }
      } catch (error) {
        result.failed++
        result.details.push({
          itemId: item.id,
          productName: item.product.name,
          error: error.message
        })
      }
    }

    return result
  }

  /**
   * Find the best department for a specific product
   */
  public static async findBestDepartmentForProduct(product: Product): Promise<Department | null> {
    // Get departments for this vendor/branch
    const departments = await Department.query()
      .where('vendor_id', product.vendorId)
      .where('branch_id', product.branchId)
      .where('active', true)
      .orderBy('priority_level', 'asc')

    if (departments.length === 0) {
      return null
    }

    // Strategy 1: Category mapping
    if (product.category) {
      const categoryDepartment = await this.findDepartmentByCategory(departments, product.category)
      if (categoryDepartment) {
        return categoryDepartment
      }
    }

    // Strategy 2: Product name keywords
    const keywordDepartment = await this.findDepartmentByKeywords(departments, product.name)
    if (keywordDepartment) {
      return keywordDepartment
    }

    // Strategy 3: Fallback to Kitchen or highest priority department
    const fallbackDepartment = departments.find(dept => 
      dept.meta?.auto_assignment_rules?.fallback_department === true
    )
    
    if (fallbackDepartment) {
      return fallbackDepartment
    }

    // Strategy 4: Return highest priority department
    return departments[0]
  }

  /**
   * Find department by product category mapping
   */
  private static async findDepartmentByCategory(
    departments: Department[], 
    category: ProductCategory
  ): Promise<Department | null> {
    for (const department of departments) {
      const categoryMappings = department.meta?.category_mappings || []
      if (categoryMappings.includes(category.id)) {
        return department
      }
    }
    return null
  }

  /**
   * Find department by product name keywords
   */
  private static async findDepartmentByKeywords(
    departments: Department[], 
    productName: string
  ): Promise<Department | null> {
    const productNameLower = productName.toLowerCase()

    // Score each department based on keyword matches
    const departmentScores = departments.map(department => {
      const keywords = department.meta?.category_keywords || []
      let score = 0

      for (const keyword of keywords) {
        if (productNameLower.includes(keyword.toLowerCase())) {
          score += 1
        }
      }

      return { department, score }
    })

    // Sort by score (highest first) and priority level (lowest first)
    departmentScores.sort((a, b) => {
      if (a.score !== b.score) {
        return b.score - a.score // Higher score first
      }
      return a.department.priorityLevel - b.department.priorityLevel // Lower priority level first
    })

    // Return department with highest score (if any matches)
    const bestMatch = departmentScores[0]
    return bestMatch && bestMatch.score > 0 ? bestMatch.department : null
  }

  /**
   * Get department workload for load balancing
   */
  public static async getDepartmentWorkload(departmentId: string): Promise<{
    currentOrders: number
    maxCapacity: number
    utilizationPercentage: number
    canAcceptMore: boolean
  }> {
    const department = await Department.findOrFail(departmentId)
    
    // Count current active orders in this department
    const currentOrders = await OrderItem.query()
      .where('department_id', departmentId)
      .whereIn('status', ['pending', 'preparing'])
      .count('* as total')

    const currentCount = parseInt(currentOrders[0].$extras.total)
    const maxCapacity = department.maxConcurrentOrders || 10
    const utilizationPercentage = Math.round((currentCount / maxCapacity) * 100)

    return {
      currentOrders: currentCount,
      maxCapacity,
      utilizationPercentage,
      canAcceptMore: currentCount < maxCapacity
    }
  }

  /**
   * Reassign items from overloaded departments
   */
  public static async balanceDepartmentWorkload(vendorId: string, branchId: string): Promise<{
    reassigned: number
    details: Array<{
      itemId: number
      fromDepartment: string
      toDepartment: string
    }>
  }> {
    const result = {
      reassigned: 0,
      details: []
    }

    // Get all departments for this vendor/branch
    const departments = await Department.query()
      .where('vendor_id', vendorId)
      .where('branch_id', branchId)
      .where('active', true)

    // Check workload for each department
    const workloads = await Promise.all(
      departments.map(async (dept) => ({
        department: dept,
        workload: await this.getDepartmentWorkload(dept.id)
      }))
    )

    // Find overloaded departments (>80% capacity)
    const overloaded = workloads.filter(w => w.workload.utilizationPercentage > 80)
    const underutilized = workloads.filter(w => w.workload.utilizationPercentage < 60)

    // Reassign items from overloaded to underutilized departments
    for (const overloadedDept of overloaded) {
      const itemsToReassign = await OrderItem.query()
        .where('department_id', overloadedDept.department.id)
        .where('status', 'pending') // Only pending items can be reassigned
        .limit(2) // Reassign max 2 items at a time

      for (const item of itemsToReassign) {
        const targetDept = underutilized.find(u => u.workload.canAcceptMore)
        
        if (targetDept) {
          await item.merge({ departmentId: targetDept.department.id }).save()
          result.reassigned++
          result.details.push({
            itemId: item.id,
            fromDepartment: overloadedDept.department.name,
            toDepartment: targetDept.department.name
          })

          // Update workload tracking
          targetDept.workload.currentOrders++
          targetDept.workload.canAcceptMore = targetDept.workload.currentOrders < targetDept.workload.maxCapacity
        }
      }
    }

    return result
  }
}
