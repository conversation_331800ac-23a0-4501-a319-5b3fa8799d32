import axios, { AxiosInstance } from 'axios'
import https from 'https'

export default class Kplc {
  public http: AxiosInstance = axios.create({
    baseURL: 'https://197.248.29.94:8144',
    httpsAgent: new https.Agent({ rejectUnauthorized: false }),
  })

  constructor(public id = 'kplc') {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
  }

  public authorize = async () => {
    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
    const data = new URLSearchParams()
    data.append('grant_type', 'password')
    data.append('username', 'kplepicapp')
    data.append('password', 'test123')
    data.append('scope', 'apim:subscribe')

    const authCredentials = {
      username: 'ZL3ZGzpPnHVayZF4CYbQnOh7mfca',
      password: 'Kmyodc1tqihHqaGCopEpzcp1XgMa',
    }

    try {
      const response = await this.http.post('token', data, {
        headers: headers,
        auth: authCredentials,
        httpsAgent: new https.Agent({ rejectUnauthorized: false }),
      })

      if (response.status === 200) {
        return response.data
      } else {
        return false
      }
    } catch (error) {
      return error
    }
  }

  // # Staff identification
  public verifyStaffIdentity = async (payload: any) => {
    const { nationalId, staffNumber } = payload
    if (!staffNumber) {
      return { error: 'staffNumber if required in the request query parameters.' }
    }

    if (!nationalId) {
      return { error: 'nationalId is required in the request query parameters.' }
    }

    try {
      const { access_token: accessToken } = await this.authorize()
      const response = await this.http.get(
        `juaforsure/3.0.1/juaforsureKplc?staffNumber=${staffNumber}&nationalId=${nationalId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      )
      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  // # contractor identification
  public async verifyContractorIdentity(payload: any) {
    const { staffNumber } = payload

    if (!staffNumber) {
      return { error: 'staffNumber if required in the request query parameters.' }
    }

    try {
      const { access_token: accessToken } = await this.authorize()
      const response = await this.http.get(
        `juaforsure/3.0.1/juaforsureContractor?staff_number=${staffNumber}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      )

      console.log(response)

      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async getIdAccount(payload: any) {
    const { accountReference } = payload

    if (!accountReference) {
      return { error: 'account_reference is required in the request query parameters' }
    }

    try {
      const { access_token: accessToken } = await this.authorize()
      const response = await this.http.get('sectorSupplies/3.0.1/', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })

      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async getStatementPdf(payload: any) {
    const { idPaymentForm, fromDate, toDate } = payload

    if (!idPaymentForm || !fromDate || !toDate) {
      return {
        error: 'idPaymentForm, fromDate and toDate are required',
      }
    }

    try {
      const { access_token: accessToken } = await this.authorize()
      const { data } = await this.http.get(
        `balancePdf/2.0.1/?encoded=false&fromDate=${fromDate}&toDate=${toDate}&idPaymentForm=${idPaymentForm}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      )

      return data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async getAccountBalance(payload: any) {
    const { accountNumber } = payload
    if (!accountNumber) {
      return { error: 'Account number is required in the request data.' }
    }

    const accessToken = await this.authorize()

    const soapBody = `
      <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:inc="http://incms.indra.es/IncmsAccount/">
          <soapenv:Header/>
          <soapenv:Body>
              <inc:AccountBalance>
                  <Account>{account_number}</Account>
              </inc:AccountBalance>
          </soapenv:Body>
      </soapenv:Envelope>
    `
    const soapRequestBody = soapBody.replace('{account_number}', accountNumber)

    try {
      const response = await this.http.post('incmsAccountBalance/2.0.1', soapRequestBody, {
        headers: {
          'Content-Type': 'text/xml',
          'Authorization': `Bearer ${accessToken}`,
          'SOAPAction': '""',
        },
      })

      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async getListOfBills(payload: any) {
    const { idAccount, fromDateDue, fromDateEmi, lastPeriod, toDateDue, toDateEmi } = payload
    if (!idAccount || !fromDateDue || !fromDateEmi || !lastPeriod || !toDateDue || !toDateEmi) {
      return {
        error:
          'All id_account, from_date_due, from_date_emi, last_period, to_date_due, to_date_emi are required in the request query parameters.',
      }
    }

    const { access_token: accessToken } = await this.authorize()

    try {
      const response = await this.http.get(
        `accounts/3.0.1/${idAccount}/documents?fromDateDue=${fromDateDue}&fromDateEmi=${fromDateEmi}&lastPeriod=${lastPeriod}&toDateDue=${toDateDue}&toDateEmi=${toDateEmi}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      )

      console.log(response)

      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async getIdSectorSupply(payload: any) {
    return payload
  }

  public async getSpecificBillText(payload: any) {
    const { idDocument } = payload

    if (!idDocument) {
      return { error: 'id_document is required in the request query parameters.' }
    }

    try {
      const { access_token: accessToken } = await this.authorize()
      const response = await this.http.get(`documents/3.0.1/${idDocument}/bills`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })

      console.log(response)

      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async getSpecificBillPdf(payload: any) {
    const { idDocument } = payload

    if (!idDocument) {
      return { error: 'id_document is required in the request query parameters.' }
    }

    try {
      const { access_token: accessToken } = await this.authorize()
      const response = await this.http.get(`documents/3.0.1/${idDocument}/pdf`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })

      console.log(response)

      return response.data
    } catch (error) {
      return { error: `Failed to send the request. ${error}` }
    }
  }

  public async idAccount(payload: any) {
    const { accountReference } = payload

    if (!accountReference) {
      return { error: 'account_reference is required' }
    }

    const result = await this.getIdSectorSupply({ accountReference })

    if (result) {
      return { idAccount: result }
    }

    return { error: 'id sector supply does not exist' }
  }

  // # 1.    Account Number Authentication
  public async accountNumberAuthentication(payload: any) {
    const { accountNumber } = payload

    if (!accountNumber) {
      return { error: 'accountNumber is required.' }
    }

    return this.getAccountBalance({ accountNumber })
  }

  // # 2.    Meter Number Authentication
  public async meterNumberAuthentication(payload: any) {
    const { serialNumberMeter } = payload

    if (!serialNumberMeter) {
      return { error: 'serial_number_meter is required.' }
    }

    return this.meterNumberAuthentication({ serialNumberMeter })
  }

  public async billQuery(payload: any) {
    const { accountReference, fromDateDue, fromDateEmi, lastPeriod, toDateDue, toDateEmi } = payload

    if (
      !accountReference ||
      !fromDateDue ||
      !fromDateEmi ||
      !lastPeriod ||
      !toDateDue ||
      !toDateEmi
    ) {
      return {
        error:
          'All account_reference, from_date_due, from_date_emi, last_period, to_date_due, to_date_emi are required.',
      }
    }

    const response = await this.getIdAccount({ accountReference })

    if (response.status === 200) {
      const idAccount = response.data.data[response.data.data.length - 1].idAccount

      const billsResponse = await this.getListOfBills({
        idAccount,
        fromDateDue,
        fromDateEmi,
        lastPeriod,
        toDateDue,
        toDateEmi,
      })

      if (billsResponse.status === 200) {
        const idDocument = billsResponse.data.data[billsResponse.data.data.length - 1].idDocument
        const specificBillTextResponse = await this.getSpecificBillText({ idDocument })

        return specificBillTextResponse
      }

      return billsResponse
    }

    return response
  }

  // # 8.    Self-Meter Reading
  public async checkSelfReadingEligibility(payload: any) {
    const { idAccount } = payload

    if (!idAccount) {
      return { error: 'id_account is required' }
    }

    const isSelfReadingLegible = await this.checkSelfReadingEligibility({ idAccount })

    if (isSelfReadingLegible) {
      return { isSelfReadingLegible }
    }

    return { isSelfReadingLegible }
  }

  public async registerSelfReading(payload: any) {
    const { accountReference, phoneNumber } = payload

    if (!accountReference || !phoneNumber) {
      return { detail: 'all account_reference and phone_number are required.' }
    }

    const idAccount = await this.getIdAccount({ accountReference })

    if (!idAccount) {
      return { detail: 'no id_account associated with specified account_reference' }
    }

    const isSelfReadingLegible = await this.checkSelfReadingEligibility({ idAccount })

    if (!isSelfReadingLegible) {
      return { detail: 'this account is not eligible for self reading' }
    }

    const idSectorSupply = await this.getIdSectorSupply({ accountReference })

    if (!idSectorSupply) {
      return { detail: 'no id_sector_supply associated with specified account_reference' }
    }

    const response = await this.registerSelfReading({ idSectorSupply, phoneNumber })

    if (!response) {
      return { detail: 'failed to register for self reading', response }
    }

    return { detail: 'registered successfuly for self reading', response }
  }

  public async getMetersAttachedToAccount(payload: any) {
    return payload
  }

  public async submitMeterReadings(payload: any) {
    return payload
  }

  public async submitSelfReading(payload: any) {
    const { accountReference, readingValue } = payload

    if (!accountReference) {
      return { detail: 'account_reference is required' }
    }

    const idAccount = await this.getIdAccount({ accountReference })

    if (!idAccount) {
      return { detail: 'id account for the specified account_reference does not exist' }
    }

    const metersAttachedToAccount = await this.getMetersAttachedToAccount({ idAccount })

    if (!metersAttachedToAccount) {
      return { detail: 'there are no meters attached to specified account' }
    }

    const serialNumber = metersAttachedToAccount[0].numMeter
    const consumType = metersAttachedToAccount[0].consumType
    const idService = metersAttachedToAccount[0].idService
    // const readingValue = 1

    const result = await this.submitMeterReadings({
      serialNumber,
      consumType,
      readingValue,
      idService,
      idAccount,
    })

    if (!result) {
      return { detail: 'failed to submit meter readings' }
    }

    return { detail: 'meter readings submitted succesfuly', response: result }
  }

  public async deregisterUserAsSelfReader(payload: any) {
    return payload
  }

  public async deregisterSelfReading(payload: any) {
    const { accountReference } = payload

    if (!accountReference) {
      return { detail: 'account_reference is required' }
    }

    const idSectorSupply = await this.getIdSectorSupply({ accountReference })

    if (!idSectorSupply) {
      return { detail: 'there is no id_sector_supply associated with specified account_reference' }
    }

    const result = await this.deregisterUserAsSelfReader({ idSectorSupply })

    if (!result) {
      return { detail: 'failed to deregister from self reading', response: result }
    }

    return { detail: 'deregistered successfuly from self reading', response: result }
  }
}
