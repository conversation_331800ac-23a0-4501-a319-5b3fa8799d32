import { DateTime } from 'luxon'

export interface PricingCalculation {
  subtotal: number
  modifiersTotal: number
  chargesTotal: number
  total: number
  invoiceAmount: number
  currency: string
}

export interface OrderWithPricing {
  id: string
  items?: any[]
  meta?: {
    charges?: Record<string, any>
  }
  invoices?: any[]
  pricing: PricingCalculation
}

export default class OrderPricingService {
  /**
   * Calculate comprehensive pricing information for an order
   */
  public static calculateOrderPricing(order: any): PricingCalculation {
    let subtotal = 0
    let modifiersTotal = 0

    // Calculate subtotal from items and their modifiers
    if (order.items && order.items.length > 0) {
      order.items.forEach((item) => {
        // Add item price * quantity
        if (item.product) {
          subtotal += (item.product.price || 0) * (item.quantity || 0)
        }

        // Add modifier costs
        if (item.modifiers && item.modifiers.length > 0) {
          item.modifiers.forEach((modifier) => {
            modifiersTotal += (modifier.priceAtTimeOfOrder || 0) * (modifier.quantity || 0)
          })
        }
      })
    }

    // Calculate charges from meta
    let chargesTotal = 0
    if (order.meta && order.meta.charges) {
      try {
        chargesTotal = Object.values(order.meta.charges).reduce(
          (acc, charge) => acc + (Number(charge) || 0),
          0
        )
      } catch (error) {
        console.warn('Error calculating charges total:', error)
        chargesTotal = 0
      }
    }

    // BUSINESS RULE: Preorder surcharge is now handled by ChargeConfiguration system
    // The preorder surcharge should be configured as a charge configuration with
    // conditions: { "order_types": ["Preorder"] } and percentage_rate: 5.00
    // This ensures it's configurable rather than hardcoded

    // Get latest invoice amount
    let invoiceAmount = 0
    if (order.invoices && order.invoices.length > 0) {
      try {
        // Sort by creation date and get the most recent
        const latestInvoice = order.invoices.sort((a, b) => {
          const dateA = new Date(b.createdAt).getTime()
          const dateB = new Date(a.createdAt).getTime()
          return dateA - dateB
        })[0]
        invoiceAmount = latestInvoice.amount || 0
      } catch (error) {
        console.warn('Error calculating invoice amount:', error)
        invoiceAmount = 0
      }
    }

    // Calculate total
    const total = subtotal + modifiersTotal + chargesTotal

    return {
      subtotal: Number(subtotal.toFixed(2)),
      modifiersTotal: Number(modifiersTotal.toFixed(2)),
      chargesTotal: Number(chargesTotal.toFixed(2)),
      total: Number(total.toFixed(2)),
      invoiceAmount: Number(invoiceAmount.toFixed(2)),
      currency: 'KES',
    }
  }

  /**
   * Add pricing information to a single order object
   */
  public static addPricingToOrder(order: any): any {
    const pricing = this.calculateOrderPricing(order)
    return {
      ...order,
      pricing,
    }
  }

  /**
   * Add pricing information to multiple orders
   */
  public static addPricingToOrders(orders: any[]): any[] {
    return orders.map((order) => this.addPricingToOrder(order))
  }

  /**
   * Add pricing information to paginated order results
   */
  public static addPricingToPaginatedOrders(paginatedOrders: any): any {
    const serializedOrders = paginatedOrders.serialize()

    return {
      ...serializedOrders,
      data: this.addPricingToOrders(serializedOrders.data),
    }
  }

  /**
   * Validate pricing calculation consistency
   */
  public static validatePricingConsistency(order: any): {
    isValid: boolean
    errors: string[]
    calculatedTotal: number
    invoiceTotal: number
  } {
    const errors: string[] = []
    const pricing = this.calculateOrderPricing(order)

    // Check if calculated total matches invoice amount (with tolerance for rounding)
    const tolerance = 0.01
    const totalDifference = Math.abs(pricing.total - pricing.invoiceAmount)

    if (pricing.invoiceAmount > 0 && totalDifference > tolerance) {
      errors.push(`Total mismatch: calculated ${pricing.total}, invoice ${pricing.invoiceAmount}`)
    }

    // Check for negative values
    if (pricing.subtotal < 0) {
      errors.push('Negative subtotal detected')
    }

    if (pricing.total < 0) {
      errors.push('Negative total detected')
    }

    // Check for missing items
    if (!order.items || order.items.length === 0) {
      errors.push('Order has no items')
    }

    return {
      isValid: errors.length === 0,
      errors,
      calculatedTotal: pricing.total,
      invoiceTotal: pricing.invoiceAmount,
    }
  }

  /**
   * Calculate estimated preparation time based on items
   */
  public static calculateEstimatedPreparationTime(order: any): number {
    if (!order.items || order.items.length === 0) {
      return 0
    }

    let totalTime = 0

    order.items.forEach((item) => {
      // Base preparation time (can be enhanced with product-specific times)
      const baseTime = 5 // 5 minutes per item
      const quantity = item.quantity || 1

      // Add time for modifiers
      let modifierTime = 0
      if (item.modifiers && item.modifiers.length > 0) {
        modifierTime = item.modifiers.length * 2 // 2 minutes per modifier
      }

      totalTime += (baseTime + modifierTime) * quantity
    })

    return Math.ceil(totalTime)
  }

  /**
   * Get order complexity score (for department assignment)
   */
  public static getOrderComplexityScore(order: any): number {
    if (!order.items || order.items.length === 0) {
      return 0
    }

    let complexityScore = 0

    order.items.forEach((item) => {
      // Base complexity
      complexityScore += 1

      // Add complexity for modifiers
      if (item.modifiers && item.modifiers.length > 0) {
        complexityScore += item.modifiers.length * 0.5
      }

      // Add complexity for quantity
      complexityScore += (item.quantity || 1) * 0.2
    })

    return Math.round(complexityScore * 10) / 10
  }
}
