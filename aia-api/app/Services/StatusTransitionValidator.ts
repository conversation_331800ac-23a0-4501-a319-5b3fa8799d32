import OrderItem from '../Models/OrderItem'
import OrderItemModifier from '../Models/OrderItemModifier'
import Order from '../Models/Order'
import Department from '../Models/Department'
import User from '../Models/User'
import { DateTime } from 'luxon'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  requiredFields?: string[]
  businessRules?: string[]
}

export interface TransitionContext {
  staffId?: string
  departmentId?: string
  notes?: string
  priority?: number
  orderType?: string
  isManagerOverride?: boolean
}

/**
 * Comprehensive status transition validation service
 * Handles business rules for order items, modifiers, and department-specific workflows
 */
export default class StatusTransitionValidator {
  /**
   * Validate order item status transition
   */
  public static async validateItemTransition(
    orderItem: OrderItem,
    newStatus: string,
    context: TransitionContext = {}
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      requiredFields: [],
      businessRules: [],
    }

    // Load necessary relationships
    await orderItem.load('order')
    await orderItem.load('department')
    await orderItem.load('assignedStaff')
    await orderItem.load('modifiers')

    // Basic transition validation
    const basicValidation = this.validateBasicItemTransition(orderItem.status, newStatus)
    if (!basicValidation.isValid) {
      result.errors.push(...basicValidation.errors)
      result.isValid = false
    }

    // Order type specific validation
    const orderTypeValidation = await this.validateOrderTypeRules(orderItem, newStatus, context)
    this.mergeValidationResults(result, orderTypeValidation)

    // Department specific validation
    if (orderItem.department) {
      const deptValidation = await this.validateDepartmentRules(orderItem, newStatus, context)
      this.mergeValidationResults(result, deptValidation)
    }

    // Staff assignment validation
    const staffValidation = await this.validateStaffAssignmentRules(orderItem, newStatus, context)
    this.mergeValidationResults(result, staffValidation)

    // Time-based validation
    const timeValidation = this.validateTimeBasedRules(orderItem, newStatus, context)
    this.mergeValidationResults(result, timeValidation)

    // Modifier dependency validation
    const modifierValidation = await this.validateModifierDependencies(orderItem, newStatus)
    this.mergeValidationResults(result, modifierValidation)

    // Business hours validation
    const businessHoursValidation = await this.validateBusinessHours(orderItem, newStatus)
    this.mergeValidationResults(result, businessHoursValidation)

    return result
  }

  /**
   * Validate modifier status transition
   */
  public static async validateModifierTransition(
    modifier: OrderItemModifier,
    newStatus: string,
    context: TransitionContext = {}
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      requiredFields: [],
      businessRules: [],
    }

    // Load necessary relationships
    await modifier.load('orderItem', (itemQuery) => {
      itemQuery.preload('order').preload('department')
    })

    // Basic transition validation
    const basicValidation = this.validateBasicModifierTransition(modifier.status, newStatus)
    if (!basicValidation.isValid) {
      result.errors.push(...basicValidation.errors)
      result.isValid = false
    }

    // Parent item status validation
    const parentValidation = this.validateParentItemStatus(modifier, newStatus)
    this.mergeValidationResults(result, parentValidation)

    // Complexity and skill validation
    const skillValidation = await this.validateSkillRequirements(modifier, newStatus, context)
    this.mergeValidationResults(result, skillValidation)

    // Allergen safety validation
    const allergenValidation = this.validateAllergenSafety(modifier, newStatus, context)
    this.mergeValidationResults(result, allergenValidation)

    // Retry attempt validation
    const retryValidation = this.validateRetryAttempts(modifier, newStatus)
    this.mergeValidationResults(result, retryValidation)

    return result
  }

  /**
   * Validate order status transition based on item completion
   */
  public static async validateOrderTransition(
    order: Order,
    newStatus: string
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    await order.load('items', (itemsQuery) => {
      itemsQuery.preload('modifiers')
    })

    // Check if all items are ready for order completion
    if (newStatus === 'Ready') {
      const allItemsReady = order.items.every(
        (item) => ['ready', 'served'].includes(item.status) && item.allModifiersCompleted
      )

      if (!allItemsReady) {
        result.errors.push('Cannot mark order as ready - not all items are completed')
        result.isValid = false
      }
    }

    // Validate payment requirements for certain transitions
    if (['Ready', 'Completed'].includes(newStatus)) {
      const paymentValidation = this.validatePaymentRequirements(order, newStatus)
      this.mergeValidationResults(result, paymentValidation)
    }

    return result
  }

  // Private validation methods
  private static validateBasicItemTransition(
    currentStatus: string,
    newStatus: string
  ): ValidationResult {
    const validTransitions = {
      pending: ['placed', 'cancelled', 'on_hold'],
      placed: ['ready', 'cancelled', 'on_hold'],
      preparing: ['ready', 'cancelled', 'on_hold', 'delayed'], // Keep for backward compatibility
      ready: ['served'], // Ready items can only be served, not cancelled
      served: [], // Terminal state
      cancelled: [], // Terminal state
      on_hold: ['pending', 'placed', 'cancelled'],
      delayed: ['placed', 'cancelled', 'on_hold'],
    }

    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    }

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      result.isValid = false
      result.errors.push(`Invalid transition from ${currentStatus} to ${newStatus}`)
    }

    return result
  }

  private static validateBasicModifierTransition(
    currentStatus: string,
    newStatus: string
  ): ValidationResult {
    const validTransitions = {
      pending: ['preparing', 'skipped', 'cancelled', 'on_hold'],
      preparing: ['completed', 'failed', 'cancelled', 'on_hold'],
      completed: [], // Terminal state
      skipped: [], // Terminal state
      cancelled: [], // Terminal state
      failed: ['pending'], // Can retry
      on_hold: ['pending', 'preparing', 'cancelled'],
    }

    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    }

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      result.isValid = false
      result.errors.push(`Invalid modifier transition from ${currentStatus} to ${newStatus}`)
    }

    return result
  }

  private static async validateOrderTypeRules(
    orderItem: OrderItem,
    newStatus: string,
    context: TransitionContext
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    const orderType = orderItem.order.type

    // Preorder specific rules
    if (orderType === 'Preorder') {
      const scheduledTime = orderItem.order.meta?.scheduled_time
      if (scheduledTime && newStatus === 'preparing') {
        const scheduledDateTime = DateTime.fromISO(scheduledTime)
        const now = DateTime.now()

        if (now < scheduledDateTime.minus({ minutes: 30 })) {
          result.warnings.push('Starting preparation more than 30 minutes before scheduled time')
          result.businessRules.push('preorder_early_preparation')
        }
      }
    }

    // Instant order rules
    if (orderType === 'Instant') {
      if (newStatus === 'preparing' && !context.staffId) {
        result.errors.push('Staff assignment required for instant order preparation')
        result.requiredFields = ['staffId']
        result.isValid = false
      }
    }

    // Delivery order rules
    if (orderItem.order.delivery === 'Delivery') {
      if (newStatus === 'ready') {
        // Check if delivery is assigned
        const hasDeliveryAssignment = orderItem.order.meta?.delivery_staff_id
        if (!hasDeliveryAssignment) {
          result.warnings.push('No delivery staff assigned for delivery order')
          result.businessRules.push('delivery_assignment_required')
        }
      }
    }

    return result
  }

  private static async validateDepartmentRules(
    orderItem: OrderItem,
    newStatus: string,
    context: TransitionContext
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    const department = orderItem.department

    // Ensure department exists
    if (!department) {
      if (newStatus === 'preparing') {
        result.errors.push('Department assignment required for preparation')
        result.isValid = false
      }
      return result
    }

    // Load staff relationship for operational status check with error handling
    try {
      if (!department.staff) {
        await department.load('staff')
      }
    } catch (error) {
      // If staff loading fails, allow operation but add warning
      result.warnings.push('Unable to verify department staff status')
      result.businessRules.push('staff_verification_failed')
    }

    // Check department operational status with manager override support
    if (newStatus === 'preparing') {
      const isOperational = await this.checkDepartmentOperationalStatus(department)

      if (!isOperational && !context.isManagerOverride) {
        result.errors.push(`Department ${department.name} is not operational`)
        result.isValid = false
      } else if (!isOperational && context.isManagerOverride) {
        result.warnings.push(`Manager override: Department ${department.name} is not operational`)
        result.businessRules.push('operational_status_override')
      }
    }

    // Check department capacity
    if (newStatus === 'preparing' && department.isAtCapacity) {
      if (!context.isManagerOverride) {
        result.errors.push(`Department ${department.name} is at capacity`)
        result.isValid = false
      } else {
        result.warnings.push('Manager override: Department at capacity')
        result.businessRules.push('capacity_override')
      }
    }

    // Check working hours
    if (newStatus === 'preparing' && !department.isWithinWorkingHours) {
      result.warnings.push(`Department ${department.name} is outside working hours`)
      result.businessRules.push('outside_working_hours')
    }

    // Department-specific workflow rules
    const workflowSettings = department.workflowSettings || {}

    if (workflowSettings.requireQualityCheck && newStatus === 'ready') {
      if (!orderItem.qualityCheckedBy) {
        result.errors.push('Quality check required before marking as ready')
        result.requiredFields = ['qualityCheckedBy']
        result.isValid = false
      }
    }

    if (!workflowSettings.allowParallelPreparation && newStatus === 'preparing') {
      // Check if staff member is already preparing another item
      const staffWorkload = await this.getStaffCurrentWorkload(context.staffId, department.id)
      if (staffWorkload > 0) {
        result.errors.push('Parallel preparation not allowed in this department')
        result.isValid = false
      }
    }

    return result
  }

  private static async validateStaffAssignmentRules(
    orderItem: OrderItem,
    newStatus: string,
    context: TransitionContext
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    if (newStatus === 'preparing') {
      if (!context.staffId) {
        result.errors.push('Staff assignment required for preparation')
        result.requiredFields = ['staffId']
        result.isValid = false
        return result
      }

      // Validate staff exists and is active
      const staff = await User.find(context.staffId)
      if (!staff) {
        result.errors.push('Invalid staff member')
        result.isValid = false
        return result
      }

      // Check if staff is assigned to department
      if (orderItem.departmentId) {
        const department = await Department.find(orderItem.departmentId)
        await department?.load('staff')

        const isAssigned = department?.staff.some(
          (s) => s.id === context.staffId && s.$pivot?.active
        )

        if (!isAssigned) {
          result.warnings.push('Staff member not assigned to this department')
          result.businessRules.push('cross_department_assignment')
        }
      }

      // Check staff workload
      const currentWorkload = await this.getStaffCurrentWorkload(context.staffId)
      const maxWorkload = 5 // This could come from department settings

      if (currentWorkload >= maxWorkload) {
        result.warnings.push(`Staff member at maximum workload (${currentWorkload}/${maxWorkload})`)
        result.businessRules.push('staff_overload')
      }

      // Check skill requirements for complex items
      if (orderItem.requiresSpecialAttention) {
        const staffSkillLevel = await this.getStaffSkillLevel(
          context.staffId,
          orderItem.departmentId
        )
        if (staffSkillLevel < 3) {
          result.warnings.push('Complex item assigned to junior staff member')
          result.businessRules.push('skill_level_mismatch')
        }
      }
    }

    return result
  }

  // Helper methods for validation
  private static mergeValidationResults(target: ValidationResult, source: ValidationResult): void {
    if (!source.isValid) {
      target.isValid = false
    }
    target.errors.push(...source.errors)
    target.warnings.push(...source.warnings)
    if (source.requiredFields) {
      target.requiredFields = [...(target.requiredFields || []), ...source.requiredFields]
    }
    if (source.businessRules) {
      target.businessRules = [...(target.businessRules || []), ...source.businessRules]
    }
  }

  private static async getStaffCurrentWorkload(
    staffId?: string,
    departmentId?: string
  ): Promise<number> {
    if (!staffId) return 0

    let query = OrderItem.query()
      .where('assigned_staff_id', staffId)
      .whereIn('status', ['pending', 'preparing'])

    if (departmentId) {
      query = query.where('department_id', departmentId)
    }

    const items = await query.exec()
    return items.length
  }

  private static async getStaffSkillLevel(staffId: string, departmentId?: string): Promise<number> {
    if (!departmentId) return 1

    const department = await Department.find(departmentId)
    await department?.load('staff')

    const staffAssignment = department?.staff.find((s) => s.id === staffId)
    return staffAssignment?.$pivot.skill_level || 1
  }

  /**
   * Check department operational status with proper error handling
   */
  private static async checkDepartmentOperationalStatus(department: Department): Promise<boolean> {
    try {
      // Ensure staff relationship is loaded
      if (!department.staff) {
        await department.load('staff')
      }

      // Check basic operational requirements
      if (!department.active) {
        return false
      }

      // Check if department has active staff
      const activeStaffCount = department.staff?.filter((staff) => staff.$pivot?.active).length || 0
      if (activeStaffCount === 0) {
        return false
      }

      // Check working hours
      if (!department.isWithinWorkingHours) {
        return false
      }

      return true
    } catch (error) {
      // If there's an error checking operational status, default to true to avoid blocking operations
      console.warn(`Error checking department operational status for ${department.id}:`, error)
      return true
    }
  }

  private static validateTimeBasedRules(
    orderItem: OrderItem,
    newStatus: string,
    context: TransitionContext
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    const now = DateTime.now()

    // Check preparation time limits
    if (orderItem.preparationStartedAt && newStatus === 'ready') {
      const preparationTime = now.diff(orderItem.preparationStartedAt, 'minutes').minutes
      const estimatedTime = orderItem.estimatedPreparationTime || 15

      if (preparationTime > estimatedTime * 2) {
        result.warnings.push(
          `Preparation time significantly exceeded estimate (${preparationTime}min vs ${estimatedTime}min)`
        )
        result.businessRules.push('preparation_time_exceeded')
      }
    }

    // Check for rush orders
    if (orderItem.order.meta?.is_rush_order && newStatus === 'preparing') {
      if (!context.priority || context.priority > 2) {
        result.warnings.push('Rush order should have high priority')
        result.businessRules.push('rush_order_priority')
      }
    }

    // Check order age
    const orderAge = now.diff(orderItem.createdAt, 'minutes').minutes
    if (orderAge > 60 && newStatus === 'preparing') {
      result.warnings.push(`Order is ${orderAge} minutes old`)
      result.businessRules.push('old_order_preparation')
    }

    return result
  }

  private static async validateModifierDependencies(
    orderItem: OrderItem,
    newStatus: string
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    }

    if (newStatus === 'ready') {
      // Check if all modifiers are completed
      const incompleteModifiers = orderItem.modifiers.filter(
        (modifier) => !['completed', 'skipped'].includes(modifier.status)
      )

      if (incompleteModifiers.length > 0) {
        result.errors.push(`${incompleteModifiers.length} modifiers are not completed`)
        result.isValid = false
      }

      // Check for failed modifiers that haven't been retried
      const failedModifiers = orderItem.modifiers.filter((modifier) => modifier.status === 'failed')

      if (failedModifiers.length > 0) {
        result.errors.push(`${failedModifiers.length} modifiers have failed and need attention`)
        result.isValid = false
      }
    }

    return result
  }

  private static async validateBusinessHours(
    orderItem: OrderItem,
    newStatus: string
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    // This would integrate with actual business hours from vendor/branch settings
    const now = DateTime.now()
    const currentHour = now.hour

    // Example business rules
    if (currentHour < 6 || currentHour > 23) {
      if (newStatus === 'preparing') {
        result.warnings.push('Preparation outside normal business hours')
        result.businessRules.push('after_hours_preparation')
      }
    }

    return result
  }

  // Modifier-specific validation methods
  private static validateParentItemStatus(
    modifier: OrderItemModifier,
    newStatus: string
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    }

    const parentStatus = modifier.orderItem.status

    // Can't start modifier preparation if parent item isn't being prepared
    if (newStatus === 'preparing' && !['preparing', 'ready'].includes(parentStatus)) {
      result.errors.push('Cannot prepare modifier when parent item is not being prepared')
      result.isValid = false
    }

    // Warn if completing modifier when parent item is already ready
    if (newStatus === 'completed' && parentStatus === 'ready') {
      result.warnings.push('Completing modifier after parent item is already ready')
    }

    return result
  }

  private static async validateSkillRequirements(
    modifier: OrderItemModifier,
    newStatus: string,
    context: TransitionContext
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    if (newStatus === 'preparing' && modifier.requiresSpecialSkill) {
      if (!context.staffId) {
        result.errors.push('Staff assignment required for complex modifier')
        result.requiredFields = ['staffId']
        result.isValid = false
        return result
      }

      // Check staff skill level
      const departmentId = modifier.orderItem.departmentId
      if (departmentId) {
        const staffSkillLevel = await this.getStaffSkillLevel(context.staffId, departmentId)

        if (staffSkillLevel < modifier.complexityLevel) {
          result.warnings.push(
            `Modifier complexity (${modifier.complexityLevel}) exceeds staff skill level (${staffSkillLevel})`
          )
          result.businessRules.push('skill_complexity_mismatch')
        }
      }
    }

    return result
  }

  private static validateAllergenSafety(
    modifier: OrderItemModifier,
    newStatus: string,
    context: TransitionContext
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    if (modifier.affectsAllergens && newStatus === 'preparing') {
      if (!context.notes || !context.notes.toLowerCase().includes('allergen')) {
        result.warnings.push('Allergen-affecting modifier requires special handling notes')
        result.businessRules.push('allergen_safety_protocol')
      }

      // Additional safety checks could be added here
      result.businessRules.push('allergen_cross_contamination_risk')
    }

    return result
  }

  private static validateRetryAttempts(
    modifier: OrderItemModifier,
    newStatus: string
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    }

    // Limit retry attempts
    if (newStatus === 'preparing' && modifier.preparationAttempts > 3) {
      result.errors.push('Maximum retry attempts exceeded - requires manager approval')
      result.isValid = false
    }

    if (modifier.preparationAttempts > 1 && newStatus === 'preparing') {
      result.warnings.push(`This is retry attempt #${modifier.preparationAttempts}`)
    }

    return result
  }

  private static validatePaymentRequirements(order: Order, newStatus: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      businessRules: [],
    }

    // Check payment status for certain order types
    if (order.delivery === 'Delivery' && newStatus === 'Ready') {
      const paymentStatus = order.meta?.payment_status
      if (paymentStatus !== 'paid' && paymentStatus !== 'authorized') {
        result.warnings.push('Delivery order not yet paid')
        result.businessRules.push('payment_verification_required')
      }
    }

    // Preorders might require prepayment
    if (order.type === 'Preorder' && newStatus === 'Ready') {
      const requiresPrepayment = order.meta?.requires_prepayment
      const paymentStatus = order.meta?.payment_status

      if (requiresPrepayment && paymentStatus !== 'paid') {
        result.errors.push('Preorder requires payment before completion')
        result.isValid = false
      }
    }

    return result
  }

  /**
   * Get validation summary for display purposes
   */
  public static getValidationSummary(validation: ValidationResult): string {
    const parts = []

    if (!validation.isValid) {
      parts.push(`❌ ${validation.errors.length} error(s)`)
    }

    if (validation.warnings.length > 0) {
      parts.push(`⚠️ ${validation.warnings.length} warning(s)`)
    }

    if (validation.businessRules && validation.businessRules.length > 0) {
      parts.push(`📋 ${validation.businessRules.length} business rule(s)`)
    }

    return parts.length > 0 ? parts.join(', ') : '✅ Valid'
  }

  /**
   * Check if validation allows proceeding with warnings
   */
  public static canProceedWithWarnings(validation: ValidationResult): boolean {
    return validation.isValid && validation.warnings.length > 0
  }

  /**
   * Get required actions from validation result
   */
  public static getRequiredActions(validation: ValidationResult): string[] {
    const actions = []

    if (validation.requiredFields) {
      actions.push(`Provide: ${validation.requiredFields.join(', ')}`)
    }

    if (validation.businessRules?.includes('allergen_safety_protocol')) {
      actions.push('Follow allergen safety protocols')
    }

    if (validation.businessRules?.includes('skill_complexity_mismatch')) {
      actions.push('Assign to more experienced staff')
    }

    return actions
  }
}
