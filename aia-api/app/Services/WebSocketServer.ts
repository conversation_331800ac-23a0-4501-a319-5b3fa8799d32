import { Server as SocketIOServer } from 'socket.io'
import { Server as HttpServer } from 'http'
import jwt from 'jsonwebtoken'
import User from '../Models/User'
import { DateTime } from 'luxon'
import WebSocketSubscriptionManager from './WebSocketSubscriptionManager'

export interface SocketUser {
  id: string
  name: string
  email: string
  roles: string[]
  vendorId?: string
  branchId?: string
  departmentIds?: string[]
}

export interface WebSocketConnection {
  socketId: string
  userId: string
  user: SocketUser
  connectedAt: DateTime
  lastActivity: DateTime
  subscriptions: Set<string>
  userType: 'customer' | 'staff' | 'manager' | 'admin'
}

/**
 * WebSocket server for real-time communication in the fulfillment tracking system
 */
export default class WebSocketServer {
  private io: SocketIOServer
  private connections: Map<string, WebSocketConnection> = new Map()
  private userConnections: Map<string, Set<string>> = new Map() // userId -> Set of socketIds
  private roomSubscriptions: Map<string, Set<string>> = new Map() // room -> Set of socketIds

  constructor(httpServer: HttpServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || '*',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
    })

    this.setupMiddleware()
    this.setupEventHandlers()
    this.startCleanupInterval()
  }

  /**
   * Set up authentication and connection middleware
   */
  private setupMiddleware(): void {
    this.io.use(async (socket, next) => {
      try {
        const token =
          socket.handshake.auth.token ||
          socket.handshake.headers.authorization?.replace('Bearer ', '')

        if (!token) {
          return next(new Error('Authentication token required'))
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.APP_KEY!) as any

        // Get user from database
        const user = await User.find(decoded.sub || decoded.id)
        if (!user) {
          return next(new Error('User not found'))
        }

        // Load user roles and relationships
        await user.load('roles')
        await user.load('employers') // For vendor/branch associations

        // Attach user info to socket
        const socketUser: SocketUser = {
          id: user.id,
          name: user.name,
          email: user.email,
          roles: user.roles.map((role) => role.name),
          vendorId: user.employers?.[0]?.vendorId,
          branchId: user.employers?.[0]?.branchId,
          departmentIds: [], // Will be populated based on department assignments
        }

        socket.data.user = socketUser
        next()
      } catch (error) {
        console.error('WebSocket authentication error:', error.message)
        next(new Error('Authentication failed'))
      }
    })
  }

  /**
   * Set up main event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket) => {
      this.handleConnection(socket)

      socket.on('disconnect', () => {
        this.handleDisconnection(socket)
      })

      socket.on('subscribe', (data) => {
        this.handleSubscription(socket, data)
      })

      socket.on('unsubscribe', (data) => {
        this.handleUnsubscription(socket, data)
      })

      socket.on('subscribe_advanced', (data) => {
        this.handleAdvancedSubscription(socket, data)
      })

      socket.on('unsubscribe_advanced', (data) => {
        this.handleAdvancedUnsubscription(socket, data)
      })

      socket.on('get_subscriptions', () => {
        this.handleGetSubscriptions(socket)
      })

      socket.on('ping', () => {
        this.handlePing(socket)
      })

      socket.on('error', (error) => {
        console.error(`Socket error for ${socket.id}:`, error)
      })
    })
  }

  /**
   * Handle new connection
   */
  private handleConnection(socket: any): void {
    const user = socket.data.user as SocketUser
    const now = DateTime.now()

    // Determine user type based on roles
    let userType: 'customer' | 'staff' | 'manager' | 'admin' = 'customer'
    if (user.roles.includes('admin')) {
      userType = 'admin'
    } else if (user.roles.includes('manager')) {
      userType = 'manager'
    } else if (
      user.roles.some((role) => ['waiter', 'chef', 'cashier', 'delivery'].includes(role))
    ) {
      userType = 'staff'
    }

    // Create connection record
    const connection: WebSocketConnection = {
      socketId: socket.id,
      userId: user.id,
      user,
      connectedAt: now,
      lastActivity: now,
      subscriptions: new Set(),
      userType,
    }

    this.connections.set(socket.id, connection)

    // Track user connections
    if (!this.userConnections.has(user.id)) {
      this.userConnections.set(user.id, new Set())
    }
    this.userConnections.get(user.id)!.add(socket.id)

    // Auto-subscribe to relevant channels based on user type and role
    this.autoSubscribeUser(socket, connection)

    console.log(`WebSocket connected: ${user.name} (${user.id}) - ${userType}`)

    // Send connection confirmation
    socket.emit('connected', {
      message: 'Connected to fulfillment tracking system',
      user: {
        id: user.id,
        name: user.name,
        type: userType,
        roles: user.roles,
      },
      timestamp: now.toISO(),
    })
  }

  /**
   * Handle disconnection
   */
  private handleDisconnection(socket: any): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    // Cleanup advanced subscriptions
    WebSocketSubscriptionManager.unsubscribeAll(socket.id)

    // Remove from user connections
    const userSockets = this.userConnections.get(connection.userId)
    if (userSockets) {
      userSockets.delete(socket.id)
      if (userSockets.size === 0) {
        this.userConnections.delete(connection.userId)
      }
    }

    // Remove from room subscriptions
    connection.subscriptions.forEach((room) => {
      const roomSockets = this.roomSubscriptions.get(room)
      if (roomSockets) {
        roomSockets.delete(socket.id)
        if (roomSockets.size === 0) {
          this.roomSubscriptions.delete(room)
        }
      }
    })

    // Remove connection
    this.connections.delete(socket.id)

    console.log(`WebSocket disconnected: ${connection.user.name} (${connection.userId})`)
  }

  /**
   * Auto-subscribe user to relevant channels
   */
  private autoSubscribeUser(socket: any, connection: WebSocketConnection): void {
    const { user, userType } = connection

    // Subscribe to user-specific channel
    this.subscribeToRoom(socket, `user:${user.id}`)

    // Subscribe based on user type and role
    switch (userType) {
      case 'admin':
        this.subscribeToRoom(socket, 'admin:global')
        break

      case 'manager':
        if (user.vendorId) {
          this.subscribeToRoom(socket, `vendor:${user.vendorId}:manager`)
          if (user.branchId) {
            this.subscribeToRoom(socket, `branch:${user.branchId}:manager`)
          }
        }
        break

      case 'staff':
        if (user.vendorId) {
          this.subscribeToRoom(socket, `vendor:${user.vendorId}:staff`)
          if (user.branchId) {
            this.subscribeToRoom(socket, `branch:${user.branchId}:staff`)
          }
        }
        // Subscribe to department-specific channels if applicable
        user.departmentIds?.forEach((deptId) => {
          this.subscribeToRoom(socket, `department:${deptId}`)
        })
        break

      case 'customer':
        // Customers only get their own order updates
        this.subscribeToRoom(socket, `customer:${user.id}:orders`)
        break
    }
  }

  /**
   * Handle subscription requests
   */
  private handleSubscription(socket: any, data: { room: string; auth?: any }): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    const { room, auth } = data

    // Validate subscription permissions
    if (!this.canSubscribeToRoom(connection, room, auth)) {
      socket.emit('subscription_error', {
        room,
        error: 'Permission denied',
      })
      return
    }

    this.subscribeToRoom(socket, room)

    socket.emit('subscribed', {
      room,
      timestamp: DateTime.now().toISO(),
    })
  }

  /**
   * Handle unsubscription requests
   */
  private handleUnsubscription(socket: any, data: { room: string }): void {
    const { room } = data
    this.unsubscribeFromRoom(socket, room)

    socket.emit('unsubscribed', {
      room,
      timestamp: DateTime.now().toISO(),
    })
  }

  /**
   * Handle ping for connection health
   */
  private handlePing(socket: any): void {
    const connection = this.connections.get(socket.id)
    if (connection) {
      connection.lastActivity = DateTime.now()
    }
    socket.emit('pong', { timestamp: DateTime.now().toISO() })
  }

  /**
   * Subscribe socket to a room
   */
  private subscribeToRoom(socket: any, room: string): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    socket.join(room)
    connection.subscriptions.add(room)

    if (!this.roomSubscriptions.has(room)) {
      this.roomSubscriptions.set(room, new Set())
    }
    this.roomSubscriptions.get(room)!.add(socket.id)
  }

  /**
   * Unsubscribe socket from a room
   */
  private unsubscribeFromRoom(socket: any, room: string): void {
    const connection = this.connections.get(socket.id)
    if (!connection) return

    socket.leave(room)
    connection.subscriptions.delete(room)

    const roomSockets = this.roomSubscriptions.get(room)
    if (roomSockets) {
      roomSockets.delete(socket.id)
      if (roomSockets.size === 0) {
        this.roomSubscriptions.delete(room)
      }
    }
  }

  /**
   * Check if user can subscribe to a room
   */
  private canSubscribeToRoom(connection: WebSocketConnection, room: string, auth?: any): boolean {
    const { user, userType } = connection

    // Admin can subscribe to anything
    if (userType === 'admin') return true

    // Parse room format
    const [type, id, subType] = room.split(':')

    switch (type) {
      case 'user':
        return id === user.id // Users can only subscribe to their own channel

      case 'vendor':
        return user.vendorId === id // Must be associated with the vendor

      case 'branch':
        return user.branchId === id // Must be associated with the branch

      case 'department':
        return user.departmentIds?.includes(id) || userType === 'manager' // Department staff or managers

      case 'order':
        // Order-specific permissions would need additional validation
        return userType !== 'customer' || auth?.customerId === user.id

      case 'customer':
        return userType === 'customer' && id === user.id

      default:
        return false
    }
  }

  /**
   * Broadcast message to a room
   */
  public broadcastToRoom(room: string, event: string, data: any): void {
    this.io.to(room).emit(event, {
      ...data,
      timestamp: DateTime.now().toISO(),
      room,
    })
  }

  /**
   * Send message to specific user
   */
  public sendToUser(userId: string, event: string, data: any): void {
    const userSockets = this.userConnections.get(userId)
    if (userSockets) {
      userSockets.forEach((socketId) => {
        this.io.to(socketId).emit(event, {
          ...data,
          timestamp: DateTime.now().toISO(),
        })
      })
    }
  }

  /**
   * Get connection statistics
   */
  public getStats(): {
    totalConnections: number
    connectionsByType: Record<string, number>
    activeRooms: number
    connectedUsers: number
  } {
    const connectionsByType = {
      customer: 0,
      staff: 0,
      manager: 0,
      admin: 0,
    }

    this.connections.forEach((conn) => {
      connectionsByType[conn.userType]++
    })

    return {
      totalConnections: this.connections.size,
      connectionsByType,
      activeRooms: this.roomSubscriptions.size,
      connectedUsers: this.userConnections.size,
    }
  }

  /**
   * Get Socket.IO server instance
   */
  public getIO(): SocketIOServer {
    return this.io
  }

  /**
   * Close all WebSocket connections gracefully
   */
  public async close(): Promise<void> {
    console.log('🔌 Closing WebSocket server...')

    // Disconnect all clients gracefully
    this.io.sockets.sockets.forEach((socket) => {
      socket.emit('server_shutdown', {
        message: 'Server is shutting down',
        timestamp: DateTime.now().toISO(),
      })
      socket.disconnect(true)
    })

    // Clear all tracking data
    this.connections.clear()
    this.userConnections.clear()
    this.roomSubscriptions.clear()

    // Close the Socket.IO server
    this.io.close()

    console.log('✅ WebSocket server closed')
  }

  /**
   * Start cleanup interval for stale connections
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      const now = DateTime.now()
      const staleThreshold = now.minus({ minutes: 5 })

      this.connections.forEach((connection, socketId) => {
        if (connection.lastActivity < staleThreshold) {
          console.log(`Cleaning up stale connection: ${connection.user.name}`)
          this.io.sockets.sockets.get(socketId)?.disconnect(true)
        }
      })
    }, 60000) // Run every minute
  }

  /**
   * Handle advanced subscription with validation
   */
  private async handleAdvancedSubscription(socket: any, data: any): Promise<void> {
    const connection = this.connections.get(socket.id)
    if (!connection) {
      socket.emit('subscription_error', {
        error: 'Connection not found',
      })
      return
    }

    try {
      const result = await WebSocketSubscriptionManager.subscribe(socket.id, connection.user, data)

      if (result.success) {
        socket.emit('subscription_success', {
          subscription_id: result.subscription_id,
          channel: result.channel,
          type: data.type,
          target_id: data.target_id,
          timestamp: DateTime.now().toISO(),
        })
      } else {
        socket.emit('subscription_error', {
          error: result.error,
          validation_result: result.validation_result,
        })
      }
    } catch (error) {
      console.error('Error handling advanced subscription:', error)
      socket.emit('subscription_error', {
        error: 'Failed to process subscription',
      })
    }
  }

  /**
   * Handle advanced unsubscription
   */
  private async handleAdvancedUnsubscription(socket: any, data: any): Promise<void> {
    try {
      const result = await WebSocketSubscriptionManager.unsubscribe(socket.id, data.subscription_id)

      if (result.success) {
        socket.emit('unsubscription_success', {
          subscription_id: data.subscription_id,
          timestamp: DateTime.now().toISO(),
        })
      } else {
        socket.emit('unsubscription_error', {
          error: result.error,
        })
      }
    } catch (error) {
      console.error('Error handling advanced unsubscription:', error)
      socket.emit('unsubscription_error', {
        error: 'Failed to process unsubscription',
      })
    }
  }

  /**
   * Handle get subscriptions request
   */
  private handleGetSubscriptions(socket: any): void {
    const connection = this.connections.get(socket.id)
    if (!connection) {
      socket.emit('subscriptions_error', {
        error: 'Connection not found',
      })
      return
    }

    try {
      const subscriptions = WebSocketSubscriptionManager.getUserSubscriptions(connection.userId)

      socket.emit('subscriptions_list', {
        user_id: connection.userId,
        total_subscriptions: subscriptions.length,
        subscriptions: subscriptions.map((sub) => ({
          id: sub.id,
          type: sub.type,
          target_id: sub.target_id,
          sub_type: sub.sub_type,
          subscribed_at: sub.subscribed_at.toISO(),
          last_activity: sub.last_activity.toISO(),
        })),
        timestamp: DateTime.now().toISO(),
      })
    } catch (error) {
      console.error('Error getting subscriptions:', error)
      socket.emit('subscriptions_error', {
        error: 'Failed to get subscriptions',
      })
    }
  }

  /**
   * Get the Socket.IO server instance
   */
  public getIO(): SocketIOServer {
    return this.io
  }
}
