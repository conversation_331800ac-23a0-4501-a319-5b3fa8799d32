import { DateTime } from 'luxon'
import Product from 'App/Models/Product'
import Branch from 'App/Models/Branch'
import Booking from 'App/Models/Booking'
import Duration from 'App/Models/Duration'
import User from 'App/Models/User'
import OptionResolutionService, { ResolvedOption } from './OptionResolutionService'

export interface TimeSlot {
  startTime: DateTime
  endTime: DateTime
  available: boolean
  staffAssigned?: string[]
  equipmentReserved?: string[]
  conflictReason?: string
}

export interface AvailabilityRequest {
  productId: string
  branchId: string
  selectedOptions: string[]
  preferredDate: DateTime
  customerId?: string
}

export interface AvailabilityResponse {
  date: string
  availableSlots: TimeSlot[]
  constraints: {
    minAdvanceHours: number
    maxAdvanceDays: number
    blackoutDays: string[]
    branchHours: {
      open: string
      close: string
    }
  }
  totalSlots: number
  availableCount: number
}

export default class BookingAvailabilityService {
  /**
   * Get available time slots for a service on a specific date
   */
  public static async getAvailableSlots(request: AvailabilityRequest): Promise<AvailabilityResponse> {
    // Load product with service options
    const product = await Product.findOrFail(request.productId)
    const branch = await Branch.findOrFail(request.branchId)
    
    // Resolve selected service options
    const resolvedOptions = await OptionResolutionService.resolveProductOptions(request.productId)
    const selectedOptions = resolvedOptions.allOptions.filter(option => 
      request.selectedOptions.includes(option.id)
    )

    // Get duration requirements
    const durationOptions = selectedOptions.filter(option => option.type === 'duration')
    const totalDurationMinutes = this.calculateTotalDuration(durationOptions)
    const bufferMinutes = this.calculateBufferTime(durationOptions)

    // Get branch operating hours
    const branchHours = await this.getBranchOperatingHours(branch, request.preferredDate)
    
    // Get scheduling constraints
    const constraints = this.getSchedulingConstraints(durationOptions, branchHours)

    // Validate date is within allowed booking window
    this.validateBookingDate(request.preferredDate, constraints)

    // Generate potential time slots
    const potentialSlots = this.generateTimeSlots(
      request.preferredDate,
      branchHours,
      totalDurationMinutes,
      bufferMinutes
    )

    // Check availability for each slot
    const checkedSlots = await Promise.all(
      potentialSlots.map(slot => this.checkSlotAvailability(
        slot,
        request.branchId,
        selectedOptions,
        request.customerId
      ))
    )

    return {
      date: request.preferredDate.toFormat('yyyy-MM-dd'),
      availableSlots: checkedSlots,
      constraints,
      totalSlots: checkedSlots.length,
      availableCount: checkedSlots.filter(slot => slot.available).length
    }
  }

  /**
   * Reserve a specific time slot temporarily
   */
  public static async reserveSlot(
    productId: string,
    branchId: string,
    startTime: DateTime,
    endTime: DateTime,
    customerId: string,
    selectedOptions: string[]
  ): Promise<{ reserved: boolean; reservationId?: string; expiresAt?: DateTime }> {
    // Check if slot is still available
    const slot: TimeSlot = {
      startTime,
      endTime,
      available: true
    }

    const checkedSlot = await this.checkSlotAvailability(
      slot,
      branchId,
      [],
      customerId
    )

    if (!checkedSlot.available) {
      return {
        reserved: false
      }
    }

    // Create temporary reservation (could be implemented with Redis or temp table)
    const reservationId = this.generateReservationId()
    const expiresAt = DateTime.now().plus({ minutes: 15 }) // 15-minute hold

    // TODO: Implement temporary reservation storage
    // await this.createTemporaryReservation(reservationId, {
    //   productId,
    //   branchId,
    //   startTime,
    //   endTime,
    //   customerId,
    //   selectedOptions,
    //   expiresAt
    // })

    return {
      reserved: true,
      reservationId,
      expiresAt
    }
  }

  /**
   * Calculate total duration from selected duration options
   */
  private static calculateTotalDuration(durationOptions: ResolvedOption[]): number {
    if (durationOptions.length === 0) {
      return 60 // Default 1 hour
    }

    // Sum up all duration options (in case multiple can be selected)
    return durationOptions.reduce((total, option) => {
      return total + (option.duration?.minutes || 60)
    }, 0)
  }

  /**
   * Calculate buffer time from duration options
   */
  private static calculateBufferTime(durationOptions: ResolvedOption[]): number {
    if (durationOptions.length === 0) {
      return 15 // Default 15 minutes
    }

    // Use the maximum buffer time from selected options
    return Math.max(...durationOptions.map(option => option.duration?.bufferMinutes || 15))
  }

  /**
   * Get branch operating hours for a specific date
   */
  private static async getBranchOperatingHours(branch: Branch, date: DateTime): Promise<{ open: string; close: string }> {
    // TODO: Implement branch-specific operating hours
    // For now, return default hours
    const dayOfWeek = date.toFormat('cccc').toLowerCase()
    
    // Default hours - should be loaded from branch settings
    const defaultHours = {
      monday: { open: '09:00', close: '17:00' },
      tuesday: { open: '09:00', close: '17:00' },
      wednesday: { open: '09:00', close: '17:00' },
      thursday: { open: '09:00', close: '17:00' },
      friday: { open: '09:00', close: '17:00' },
      saturday: { open: '10:00', close: '16:00' },
      sunday: { open: '10:00', close: '16:00' }
    }

    return defaultHours[dayOfWeek] || { open: '09:00', close: '17:00' }
  }

  /**
   * Get scheduling constraints from duration options
   */
  private static getSchedulingConstraints(durationOptions: ResolvedOption[], branchHours: { open: string; close: string }) {
    const constraints = {
      minAdvanceHours: 2,
      maxAdvanceDays: 30,
      blackoutDays: [] as string[],
      branchHours
    }

    // Apply constraints from duration options
    durationOptions.forEach(option => {
      if (option.duration?.schedulingRules) {
        const rules = option.duration.schedulingRules
        constraints.minAdvanceHours = Math.max(constraints.minAdvanceHours, rules.minAdvanceHours)
        constraints.blackoutDays.push(...rules.blackoutDays)
      }
    })

    // Remove duplicates from blackout days
    constraints.blackoutDays = [...new Set(constraints.blackoutDays)]

    return constraints
  }

  /**
   * Validate that the booking date is within allowed window
   */
  private static validateBookingDate(date: DateTime, constraints: any): void {
    const now = DateTime.now()
    const minBookingTime = now.plus({ hours: constraints.minAdvanceHours })
    const maxBookingTime = now.plus({ days: constraints.maxAdvanceDays })

    if (date < minBookingTime) {
      throw new Error(`Bookings must be made at least ${constraints.minAdvanceHours} hours in advance`)
    }

    if (date > maxBookingTime) {
      throw new Error(`Bookings cannot be made more than ${constraints.maxAdvanceDays} days in advance`)
    }

    const dayOfWeek = date.toFormat('cccc').toLowerCase()
    if (constraints.blackoutDays.includes(dayOfWeek)) {
      throw new Error(`Bookings are not available on ${dayOfWeek}s`)
    }
  }

  /**
   * Generate potential time slots for a date
   */
  private static generateTimeSlots(
    date: DateTime,
    branchHours: { open: string; close: string },
    durationMinutes: number,
    bufferMinutes: number
  ): TimeSlot[] {
    const slots: TimeSlot[] = []
    const totalMinutes = durationMinutes + bufferMinutes
    
    // Parse branch hours
    const [openHour, openMinute] = branchHours.open.split(':').map(Number)
    const [closeHour, closeMinute] = branchHours.close.split(':').map(Number)
    
    let currentTime = date.set({ hour: openHour, minute: openMinute, second: 0, millisecond: 0 })
    const endOfDay = date.set({ hour: closeHour, minute: closeMinute, second: 0, millisecond: 0 })

    // Generate slots every 30 minutes
    while (currentTime.plus({ minutes: totalMinutes }) <= endOfDay) {
      const slotEnd = currentTime.plus({ minutes: durationMinutes })
      
      slots.push({
        startTime: currentTime,
        endTime: slotEnd,
        available: true
      })

      currentTime = currentTime.plus({ minutes: 30 })
    }

    return slots
  }

  /**
   * Check if a specific time slot is available
   */
  private static async checkSlotAvailability(
    slot: TimeSlot,
    branchId: string,
    selectedOptions: ResolvedOption[],
    customerId?: string
  ): Promise<TimeSlot> {
    // Check for existing bookings that conflict
    const conflictingBookings = await Booking.query()
      .where('branchId', branchId)
      .where('status', '!=', 'cancelled')
      .where((query) => {
        query
          .whereBetween('appointmentStart', [slot.startTime.toISO(), slot.endTime.toISO()])
          .orWhereBetween('appointmentEnd', [slot.startTime.toISO(), slot.endTime.toISO()])
          .orWhere((subQuery) => {
            subQuery
              .where('appointmentStart', '<=', slot.startTime.toISO())
              .where('appointmentEnd', '>=', slot.endTime.toISO())
          })
      })

    if (conflictingBookings.length > 0) {
      return {
        ...slot,
        available: false,
        conflictReason: 'Time slot already booked'
      }
    }

    // TODO: Check staff availability
    // TODO: Check equipment availability
    // TODO: Check capacity limits

    return slot
  }

  /**
   * Generate a unique reservation ID
   */
  private static generateReservationId(): string {
    return `res_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
