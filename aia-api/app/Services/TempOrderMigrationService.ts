import Order from 'App/Models/Order'
import Database from '@ioc:Adonis/Lucid/Database'

export interface MigrationResult {
  success: boolean
  orderId: string
  itemsCreated: number
  error?: string
}

export interface MigrationSummary {
  totalOrders: number
  successfulMigrations: number
  failedMigrations: number
  totalItemsCreated: number
  errors: string[]
}

export default class TempOrderMigrationService {
  /**
   * Find all orders that need migration (have temp_items but no OrderItems)
   */
  public static async findOrdersNeedingMigration(): Promise<Order[]> {
    // Get all orders with temp_items and then filter in memory
    const ordersWithTempItems = await Order.query()
      .whereRaw("meta->>'temp_items' IS NOT NULL")
      .preload('items')

    // Filter orders that actually need migration
    return ordersWithTempItems.filter((order) => {
      const tempItems = order.meta?.temp_items
      if (!tempItems || typeof tempItems !== 'object') return false

      const tempItemKeys = Object.keys(tempItems)
      if (tempItemKeys.length === 0) return false

      // Check if order already has OrderItems
      return !order.items || order.items.length === 0
    })
  }

  /**
   * Check if an order needs migration
   */
  public static async orderNeedsMigration(order: Order): Promise<boolean> {
    // Load items if not already loaded
    if (!order.items) {
      await order.load('items')
    }

    // If order already has OrderItems, no migration needed
    if (order.items && order.items.length > 0) {
      return false
    }

    // Check if order has temp_items
    const tempItems = order.meta?.temp_items || {}
    return Object.keys(tempItems).length > 0
  }

  /**
   * Migrate a single order from temp_items to OrderItems
   */
  public static async migrateOrder(order: Order): Promise<MigrationResult> {
    const transaction = await Database.transaction()

    try {
      // Check if migration is needed
      const needsMigration = await this.orderNeedsMigration(order)
      if (!needsMigration) {
        await transaction.commit()
        return {
          success: true,
          orderId: order.id,
          itemsCreated: 0,
          error: 'Order does not need migration',
        }
      }

      const tempItems = order.meta?.temp_items || {}
      let itemsCreated = 0

      // Create OrderItems from temp_items
      for (const [productId, itemData] of Object.entries(tempItems)) {
        try {
          // Handle different item data formats
          let quantity = 1
          let selectedModifiers = []

          if (typeof itemData === 'object' && itemData !== null) {
            quantity = (itemData as any).quantity || 1
            selectedModifiers = (itemData as any).selected_modifiers || []
          } else if (typeof itemData === 'number') {
            quantity = itemData
          }

          // Validate quantity
          if (!quantity || quantity <= 0) {
            console.warn(`Skipping item ${productId} with invalid quantity: ${quantity}`)
            continue
          }

          // Create order item
          const orderItem = await order.related('items').create(
            {
              productId: productId,
              quantity: quantity,
            },
            { client: transaction }
          )

          itemsCreated++

          // Handle modifiers if present
          if (selectedModifiers && selectedModifiers.length > 0) {
            for (const modifierId of selectedModifiers) {
              try {
                await orderItem.related('modifiers').create(
                  {
                    modifierId: modifierId,
                    quantity: 1,
                  },
                  { client: transaction }
                )
              } catch (modifierError) {
                console.error(`Failed to migrate modifier ${modifierId}:`, modifierError)
              }
            }
          }
        } catch (itemError) {
          console.error(`Failed to create item for product ${productId}:`, itemError)
          throw itemError
        }
      }

      if (itemsCreated === 0) {
        throw new Error('No valid items were created from temp_items')
      }

      // Remove temp_items from meta after successful migration
      const newMeta = { ...order.meta }
      delete newMeta.temp_items

      await order.merge({ meta: newMeta }).save({ client: transaction })

      await transaction.commit()

      return {
        success: true,
        orderId: order.id,
        itemsCreated,
      }
    } catch (error) {
      await transaction.rollback()
      return {
        success: false,
        orderId: order.id,
        itemsCreated: 0,
        error: error.message,
      }
    }
  }

  /**
   * Migrate all orders that need migration
   */
  public static async migrateAllOrders(): Promise<MigrationSummary> {
    const ordersToMigrate = await this.findOrdersNeedingMigration()

    const summary: MigrationSummary = {
      totalOrders: ordersToMigrate.length,
      successfulMigrations: 0,
      failedMigrations: 0,
      totalItemsCreated: 0,
      errors: [],
    }

    for (const order of ordersToMigrate) {
      const result = await this.migrateOrder(order)

      if (result.success) {
        summary.successfulMigrations++
        summary.totalItemsCreated += result.itemsCreated
      } else {
        summary.failedMigrations++
        summary.errors.push(`Order ${result.orderId}: ${result.error}`)
      }
    }

    return summary
  }

  /**
   * Get migration status for the system
   */
  public static async getMigrationStatus(): Promise<{
    ordersNeedingMigration: number
    ordersWithTempItems: number
    ordersWithOrderItems: number
  }> {
    // Count orders with temp_items (simplified query)
    const ordersWithTempItems = await Order.query()
      .whereRaw("meta->>'temp_items' IS NOT NULL")
      .count('* as total')

    // Count orders with OrderItems
    const ordersWithOrderItems = await Database.from('orders')
      .join('order_items', 'orders.id', 'order_items.order_id')
      .countDistinct('orders.id as total')

    // Find orders that actually need migration
    const ordersNeedingMigration = await this.findOrdersNeedingMigration()

    return {
      ordersNeedingMigration: ordersNeedingMigration.length,
      ordersWithTempItems: Number(ordersWithTempItems[0].total),
      ordersWithOrderItems: Number(ordersWithOrderItems[0].total),
    }
  }
}
