/**
 * Delivery pricing calculation utilities
 */

import { DistanceCalculator, Coordinates } from './DistanceCalculator'

export interface VendorPricingStructure {
  basePrice: number
  pricePerKm: number
  minimumOrderValue?: number
  deliveryRadius?: number
  freeDeliveryThreshold?: number
  maxDeliveryFee?: number
}

export interface DeliveryOption {
  id: string
  name: string
  description: string
  estimatedTime: number
  fee: number
  vehicleType: string
  isAvailable: boolean
  distance?: number
  priceBreakdown?: {
    basePrice: number
    distancePrice: number
    totalDistance: number
  }
}

export interface PricingCalculationResult {
  deliveryOptions: DeliveryOption[]
  distance: number
  isWithinRadius: boolean
  meetsMinimumOrder: boolean
  errors: string[]
}

export class DeliveryPricingCalculator {
  /**
   * Calculate delivery pricing based on vendor configuration and distance
   */
  public static calculateDeliveryPricing(
    vendorLocation: Coordinates,
    customerLocation: Coordinates,
    vendorPricing: VendorPricingStructure | null,
    orderValue: number = 0
  ): PricingCalculationResult {
    const result: PricingCalculationResult = {
      deliveryOptions: [],
      distance: 0,
      isWithinRadius: true,
      meetsMinimumOrder: true,
      errors: []
    }

    // Validate coordinates
    if (!DistanceCalculator.isValidCoordinates(vendorLocation)) {
      result.errors.push('Invalid vendor location coordinates')
      return result
    }

    if (!DistanceCalculator.isValidCoordinates(customerLocation)) {
      result.errors.push('Invalid customer location coordinates')
      return result
    }

    // Calculate distance
    result.distance = DistanceCalculator.calculateDistance(vendorLocation, customerLocation)

    // Use vendor pricing if available, otherwise use default pricing
    if (vendorPricing) {
      return this.calculateWithVendorPricing(result, vendorPricing, orderValue)
    } else {
      return this.calculateWithDefaultPricing(result)
    }
  }

  /**
   * Calculate pricing using vendor's configured pricing structure
   */
  private static calculateWithVendorPricing(
    result: PricingCalculationResult,
    pricing: VendorPricingStructure,
    orderValue: number
  ): PricingCalculationResult {
    // Check delivery radius
    if (pricing.deliveryRadius && result.distance > pricing.deliveryRadius) {
      result.isWithinRadius = false
      result.errors.push(`Delivery location is outside ${pricing.deliveryRadius}km radius`)
      return result
    }

    // Check minimum order value
    if (pricing.minimumOrderValue && orderValue < pricing.minimumOrderValue) {
      result.meetsMinimumOrder = false
      result.errors.push(`Minimum order value is KES ${pricing.minimumOrderValue}`)
    }

    // Calculate base delivery fee
    const basePrice = pricing.basePrice || 0
    const distancePrice = result.distance * (pricing.pricePerKm || 0)
    let totalFee = basePrice + distancePrice

    // Apply maximum delivery fee cap if configured
    if (pricing.maxDeliveryFee && totalFee > pricing.maxDeliveryFee) {
      totalFee = pricing.maxDeliveryFee
    }

    // Apply free delivery threshold if configured
    if (pricing.freeDeliveryThreshold && orderValue >= pricing.freeDeliveryThreshold) {
      totalFee = 0
    }

    // Create delivery options
    result.deliveryOptions = [
      {
        id: 'standard_delivery',
        name: 'Standard Delivery',
        description: `Motorcycle delivery (${result.distance}km)`,
        estimatedTime: this.calculateEstimatedTime(result.distance, 'motorcycle'),
        fee: Math.round(totalFee),
        vehicleType: 'motorcycle',
        isAvailable: result.isWithinRadius && result.meetsMinimumOrder,
        distance: result.distance,
        priceBreakdown: {
          basePrice: basePrice,
          distancePrice: Math.round(distancePrice),
          totalDistance: result.distance
        }
      },
      {
        id: 'express_delivery',
        name: 'Express Delivery',
        description: `Car delivery (${result.distance}km)`,
        estimatedTime: this.calculateEstimatedTime(result.distance, 'car'),
        fee: Math.round(totalFee * 1.5), // 50% premium for express
        vehicleType: 'car',
        isAvailable: result.isWithinRadius && result.meetsMinimumOrder,
        distance: result.distance,
        priceBreakdown: {
          basePrice: Math.round(basePrice * 1.5),
          distancePrice: Math.round(distancePrice * 1.5),
          totalDistance: result.distance
        }
      }
    ]

    return result
  }

  /**
   * Calculate pricing using default fixed pricing (fallback)
   */
  private static calculateWithDefaultPricing(result: PricingCalculationResult): PricingCalculationResult {
    result.deliveryOptions = [
      {
        id: 'standard_delivery',
        name: 'Standard Delivery',
        description: `Regular motorcycle delivery (${result.distance}km)`,
        estimatedTime: this.calculateEstimatedTime(result.distance, 'motorcycle'),
        fee: 150, // Default fixed price
        vehicleType: 'motorcycle',
        isAvailable: true,
        distance: result.distance
      },
      {
        id: 'express_delivery',
        name: 'Express Delivery',
        description: `Fast car delivery (${result.distance}km)`,
        estimatedTime: this.calculateEstimatedTime(result.distance, 'car'),
        fee: 250, // Default fixed price
        vehicleType: 'car',
        isAvailable: true,
        distance: result.distance
      }
    ]

    return result
  }

  /**
   * Calculate estimated delivery time based on distance and vehicle type
   */
  private static calculateEstimatedTime(distance: number, vehicleType: string): number {
    const baseTime = vehicleType === 'car' ? 20 : 30 // Base time in minutes
    const timePerKm = vehicleType === 'car' ? 2 : 3 // Minutes per km
    
    return Math.round(baseTime + (distance * timePerKm))
  }

  /**
   * Parse vendor pricing structure from JSON
   */
  public static parseVendorPricing(pricingJson: any): VendorPricingStructure | null {
    if (!pricingJson || typeof pricingJson !== 'object') {
      return null
    }

    return {
      basePrice: Number(pricingJson.basePrice) || 0,
      pricePerKm: Number(pricingJson.pricePerKm) || 0,
      minimumOrderValue: pricingJson.minimumOrderValue ? Number(pricingJson.minimumOrderValue) : undefined,
      deliveryRadius: pricingJson.deliveryRadius ? Number(pricingJson.deliveryRadius) : undefined,
      freeDeliveryThreshold: pricingJson.freeDeliveryThreshold ? Number(pricingJson.freeDeliveryThreshold) : undefined,
      maxDeliveryFee: pricingJson.maxDeliveryFee ? Number(pricingJson.maxDeliveryFee) : undefined
    }
  }
}
