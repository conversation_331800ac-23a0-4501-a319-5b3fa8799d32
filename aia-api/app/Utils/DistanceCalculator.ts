/**
 * Distance calculation utilities for delivery pricing
 */

export interface Coordinates {
  lat: number
  lng: number
}

export interface DistanceResult {
  distance: number // in kilometers
  isWithinRadius: boolean
  radiusLimit?: number
}

export class DistanceCalculator {
  /**
   * Calculate distance between two points using Haversine formula
   * Returns distance in kilometers
   */
  public static calculateDistance(point1: Coordinates, point2: Coordinates): number {
    const R = 6371 // Earth's radius in kilometers
    
    const lat1Rad = this.toRadians(point1.lat)
    const lat2Rad = this.toRadians(point2.lat)
    const deltaLatRad = this.toRadians(point2.lat - point1.lat)
    const deltaLngRad = this.toRadians(point2.lng - point1.lng)

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    
    const distance = R * c
    
    // Round to 2 decimal places
    return Math.round(distance * 100) / 100
  }

  /**
   * Check if delivery location is within vendor's delivery radius
   */
  public static isWithinDeliveryRadius(
    vendorLocation: Coordinates,
    customerLocation: Coordinates,
    radiusKm: number
  ): DistanceResult {
    const distance = this.calculateDistance(vendorLocation, customerLocation)
    
    return {
      distance,
      isWithinRadius: distance <= radiusKm,
      radiusLimit: radiusKm
    }
  }

  /**
   * Validate coordinates
   */
  public static isValidCoordinates(coords: Coordinates): boolean {
    return (
      coords &&
      typeof coords.lat === 'number' &&
      typeof coords.lng === 'number' &&
      coords.lat >= -90 && coords.lat <= 90 &&
      coords.lng >= -180 && coords.lng <= 180 &&
      !isNaN(coords.lat) &&
      !isNaN(coords.lng)
    )
  }

  /**
   * Convert degrees to radians
   */
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Get approximate distance for common Nairobi locations (for testing)
   */
  public static getTestDistances(): Record<string, { from: Coordinates; to: Coordinates; expectedDistance: number }> {
    return {
      'CBD_to_Westlands': {
        from: { lat: -1.2921, lng: 36.8219 }, // Nairobi CBD
        to: { lat: -1.2630, lng: 36.8063 },   // Westlands
        expectedDistance: 4.2 // approximately 4.2 km
      },
      'CBD_to_Karen': {
        from: { lat: -1.2921, lng: 36.8219 }, // Nairobi CBD
        to: { lat: -1.3197, lng: 36.6853 },   // Karen
        expectedDistance: 15.8 // approximately 15.8 km
      },
      'Same_location': {
        from: { lat: -1.2921, lng: 36.8219 },
        to: { lat: -1.2921, lng: 36.8219 },
        expectedDistance: 0 // same location
      }
    }
  }
}
