/**
 * Centralized delivery pricing calculation utilities
 * Replaces vendor-specific pricing with admin-controlled centralized pricing
 */

import { DistanceCalculator, Coordinates } from './DistanceCalculator'

export interface CentralizedDeliveryOption {
  id: string
  name: string
  description: string
  estimatedTime: number
  fee: number
  vehicleType: string
  isAvailable: boolean
  distance?: number
  priceBreakdown?: {
    basePrice: number
    distancePrice: number
    totalDistance: number
    tierUsed?: string
    appliedDiscounts?: string[]
  }
  configId: string
  priority: number
}

export interface CentralizedPricingResult {
  deliveryOptions: CentralizedDeliveryOption[]
  distance: number
  errors: string[]
  fallbackUsed: boolean
}

export class CentralizedDeliveryPricingCalculator {
  /**
   * Calculate delivery pricing using centralized admin-controlled pricing
   */
  public static async calculateCentralizedPricing(
    vendorLocation: Coordinates,
    customerLocation: Coordinates,
    orderValue: number = 0
  ): Promise<CentralizedPricingResult> {
    const result: CentralizedPricingResult = {
      deliveryOptions: [],
      distance: 0,
      errors: [],
      fallbackUsed: false,
    }

    // Validate coordinates
    if (!DistanceCalculator.isValidCoordinates(vendorLocation)) {
      result.errors.push('Invalid vendor location coordinates')
      return result
    }

    if (!DistanceCalculator.isValidCoordinates(customerLocation)) {
      result.errors.push('Invalid customer location coordinates')
      return result
    }

    // Calculate distance
    result.distance = DistanceCalculator.calculateDistance(vendorLocation, customerLocation)

    try {
      // Dynamic import to avoid IoC issues
      const { default: DeliveryPricingConfig } = await import('App/Models/DeliveryPricingConfig')

      // Get active delivery pricing configurations
      const configs = await DeliveryPricingConfig.query()
        .where('is_active', true)
        .preload('tiers', (tiersQuery) => {
          tiersQuery.where('is_active', true).orderBy('priority', 'asc')
        })
        .orderBy('priority', 'asc')

      if (configs.length === 0) {
        result.errors.push('No active delivery pricing configurations found')
        return result
      }

      // Calculate pricing for each configuration
      for (const config of configs) {
        const option = await this.calculateOptionPricing(config, result.distance, orderValue)
        if (option) {
          result.deliveryOptions.push(option)
        }
      }

      // If no options are available, add error
      if (result.deliveryOptions.length === 0) {
        result.errors.push('No delivery options available for this location and order value')
      }
    } catch (error) {
      result.errors.push(`Failed to calculate centralized pricing: ${error.message}`)
    }

    return result
  }

  /**
   * Calculate pricing for a specific delivery configuration
   */
  private static async calculateOptionPricing(
    config: DeliveryPricingConfig,
    distance: number,
    orderValue: number
  ): Promise<CentralizedDeliveryOption | null> {
    // Check if distance is within limits
    if (!config.isWithinDistanceLimit(distance)) {
      return null
    }

    // Check minimum order value
    if (!config.meetsMinimumOrderValue(orderValue)) {
      return null
    }

    // Check if configuration is available (time-based, etc.)
    if (!config.isAvailable) {
      return null
    }

    let fee = 0
    let basePrice = 0
    let distancePrice = 0
    let tierUsed = 'Base Pricing'
    const appliedDiscounts: string[] = []

    // Check if there are tiers for this configuration
    if (config.tiers && config.tiers.length > 0) {
      // Find applicable tier
      const applicableTier = config.tiers.find(
        (tier) => tier.isActive && tier.appliesToDistance(distance)
      )

      if (applicableTier) {
        fee = applicableTier.calculateFee(distance)
        tierUsed = applicableTier.tierName
        basePrice = Number(applicableTier.basePrice) // Ensure numeric conversion
        distancePrice = fee - basePrice
      } else {
        // No applicable tier found, fall back to base configuration pricing
        fee = config.calculateBaseFee(distance)
        basePrice = config.basePrice
        distancePrice = distance * config.pricePerKm
        tierUsed = 'Base Pricing (No Tier Match)'
      }
    } else {
      // Use base configuration pricing
      fee = config.calculateBaseFee(distance)
      basePrice = config.basePrice
      distancePrice = distance * config.pricePerKm
    }

    // Apply free delivery if qualified
    if (config.qualifiesForFreeDelivery(orderValue)) {
      fee = 0
      appliedDiscounts.push('Free Delivery')
    }

    // Calculate estimated time
    const estimatedTime = config.calculateEstimatedTime(distance)

    return {
      id: `centralized_${config.id}`,
      name: config.name,
      description: `${config.description || config.name} (${distance}km)`,
      estimatedTime,
      fee: Math.round(fee),
      vehicleType: config.vehicleType,
      isAvailable: true,
      distance,
      priceBreakdown: {
        basePrice,
        distancePrice: Math.round(distancePrice),
        totalDistance: distance,
        tierUsed,
        appliedDiscounts,
      },
      configId: config.id,
      priority: config.priority,
    }
  }

  /**
   * Get default delivery configuration (fallback)
   */
  public static async getDefaultConfiguration(): Promise<any | null> {
    const { default: DeliveryPricingConfig } = await import('App/Models/DeliveryPricingConfig')
    return await DeliveryPricingConfig.query()
      .where('is_active', true)
      .where('is_default', true)
      .first()
  }

  /**
   * Fallback to legacy vendor pricing if centralized pricing fails
   */
  public static async calculateWithFallback(
    vendorLocation: Coordinates,
    customerLocation: Coordinates,
    vendorPricing: any,
    orderValue: number = 0
  ): Promise<CentralizedPricingResult> {
    // Try centralized pricing first
    const centralizedResult = await this.calculateCentralizedPricing(
      vendorLocation,
      customerLocation,
      orderValue
    )

    // If centralized pricing succeeds, return it
    if (centralizedResult.deliveryOptions.length > 0) {
      return centralizedResult
    }

    // Fallback to legacy vendor pricing
    const { DeliveryPricingCalculator } = await import('./DeliveryPricingCalculator')
    const legacyResult = DeliveryPricingCalculator.calculateDeliveryPricing(
      vendorLocation,
      customerLocation,
      vendorPricing,
      orderValue
    )

    // Convert legacy result to centralized format
    const fallbackResult: CentralizedPricingResult = {
      deliveryOptions: legacyResult.deliveryOptions.map((option) => ({
        ...option,
        id: `fallback_${option.id}`,
        configId: 'legacy_fallback',
        priority: 999,
      })),
      distance: legacyResult.distance,
      errors: [...centralizedResult.errors, ...legacyResult.errors],
      fallbackUsed: true,
    }

    return fallbackResult
  }
}
