import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Branch from '../../Models/Branch'
import Product from '../../Models/Product'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class BranchProductsController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const branchQuery = Product.query()
      .where('branchId', branch.id)
      .preload('vendor')
      .preload('service')
      .withCount('forms', (query) => {
        query.as('totalForms')
      })
      .filter(filters)

    return await branchQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a branch
   * @description Create a branch with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async store({ request, response, auth }: HttpContextContract, branch: Branch) {
    const {
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type = 'Digital',
      condition = 'New',
      status = 'Draft',
      availability = 'In Stock',
      shipping = 'Free',
      unit = 'other',
      mode = 'Single',
      payment = 'Prepaid',
      visibility = 'Public',
      productCategoryId,
      vendorId,
      serviceId,
      meta = {},
      extra = {},

      forms = [],
    } = request.all()

    const product = await branch.related('products').create({
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type,
      condition,
      status,
      availability,
      shipping,
      unit,
      mode,
      visibility,
      payment,
      productCategoryId,
      userId: auth.user?.id,
      vendorId,
      serviceId,
      meta,
      extra,
    })

    console.info('product', product)

    const image = request.file('image')

    if (image) {
      await product.merge({ image: Attachment.fromFile(image) }).save()
    }

    if (forms.length > 0) {
      await product.related('forms').createMany(forms)
    }

    return response.json(product)
  }
}
