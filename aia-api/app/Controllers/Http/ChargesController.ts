import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Charge from '../../Models/Charge'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Charge management
 * @version 1.0.0
 * @description Charge management for the application
 */
export default class ChargesController {
  /**
   * @index
   * @summary List all charges
   * @description List all charges, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const chargeQuery = Charge.filter(filters)

    return await chargeQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a charge
   * @description Create a charge with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Charge>
   */
  public async store({ request, response }: HttpContextContract) {
    const { amount } = request.all()

    const charge = new Charge()

    charge.fill({ amount })

    await charge.save()

    return response.json(charge)
  }

  @bind()
  /**
   * @show
   * @summary Show a single charge
   * @description Show a charge with their details (name and details)
   * @paramPath id required number - Charge ID
   * @responseBody 200 - <Charge>
   * @response 404 - Charge not found
   */
  public async show({ response }: HttpContextContract, charge: Charge) {
    return response.json(charge)
  }

  @bind()
  /**
   * @update
   * @summary Update a charge
   * @description Update a charge with their details (name and details)
   * @paramPath id required number - Charge ID
   * @requestBody <Charge>
   * @responseBody 200 - <Charge>
   * @response 404 - Charge not found
   */
  public async update({ request, response }: HttpContextContract, charge: Charge) {
    const { amount } = request.all()
    await charge.merge({ amount }).save()

    return response.json(charge)
  }

  @bind()
  /**
   * @destroy
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, charge: Charge) {
    return await charge.delete()
  }
}
