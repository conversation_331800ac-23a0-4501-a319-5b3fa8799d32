import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import ProductRating from '../../Models/ProductRating'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name ProductRating management
 * @version 1.0.0
 * @description ProductRating management for the application
 */
export default class ProductRatingsController {
  /**
   * @index
   * @summary List all ratings
   * @description List all ratings, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const ratingQuery = ProductRating.filter(filters)

    return await ratingQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a rating
   * @description Create a rating with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <ProductRating>
   */
  public async store({ request, response }: HttpContextContract) {
    const { points, comment, productId } = request.all()
    const rating = ProductRating.create({ points, comment, productId })

    return response.json(rating)
  }

  @bind()
  /**
   * @show
   * @summary Show a single rating
   * @description Show a rating with their details (name and details)
   * @paramPath id required number - ProductRating ID
   * @responseBody 200 - <ProductRating>
   * @response 404 - ProductRating not found
   */
  public async show({ response }: HttpContextContract, rating: ProductRating) {
    return response.json(rating)
  }

  @bind()
  /**
   * @update
   * @summary Update a rating
   * @description Update a rating with their details (name and details)
   * @paramPath id required number - ProductRating ID
   * @requestBody <ProductRating>
   * @responseBody 200 - <ProductRating>
   * @response 404 - ProductRating not found
   */
  public async update({ request, response }: HttpContextContract, rating: ProductRating) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await rating.merge(input).save()

    return response.json(rating)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Product Rating
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, rating: ProductRating) {
    return await rating.delete()
  }
}
