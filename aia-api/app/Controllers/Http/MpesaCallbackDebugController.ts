import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import MpesaConfigValidator from 'App/Services/MpesaConfigValidator'

/**
 * Interface for M-Pesa callback response
 */
interface MpesaCallbackResponse {
  ResultCode?: number;
  ResultDesc?: string;
  [key: string]: any;
}

/**
 * Interface for simulation result analysis
 */
interface SimulationAnalysis {
  endpointReachable: boolean;
  validResponse: boolean;
  expectedMpesaResponse: boolean;
  recommendations: string[];
}

/**
 * M-Pesa Callback Debug Controller
 *
 * This controller helps debug M-Pesa callback issues by providing
 * tools to test callback URLs, validate configuration, and simulate callbacks.
 */
export default class MpesaCallbackDebugController {

  /**
   * Test if callback URLs are accessible
   */
  public async testCallbackUrls({ response }: HttpContextContract) {
    try {
      const baseUrl = process.env.MPESA_CALLBACK_DOMAIN || process.env.APP_URL || 'https://uat-api.appinapp.ke'
      const mpesaBasePath = '/v1/mpsa/e59ed6a68b83'

      const callbackUrls = {
        validation: `${baseUrl}${mpesaBasePath}/validate`,
        confirmation: `${baseUrl}${mpesaBasePath}/confirm`,
        timeout: `${baseUrl}${mpesaBasePath}/timeout`,
        result: `${baseUrl}${mpesaBasePath}/result`,
        health: `${baseUrl}${mpesaBasePath}/health`
      }

      console.log('=== TESTING CALLBACK URL ACCESSIBILITY ===')
      
      const results = {}
      
      // Test each URL
      for (const [name, url] of Object.entries(callbackUrls)) {
        try {
          console.log(`Testing ${name}: ${url}`)
          
          // For GET endpoints (like health), test directly
          if (name === 'health') {
            const fetch = require('node-fetch')
            const testResponse = await fetch(url, {
              method: 'GET',
              timeout: 5000
            })
            
            results[name] = {
              url,
              accessible: testResponse.ok,
              status: testResponse.status,
              statusText: testResponse.statusText
            }
          } else {
            // For POST endpoints, we'll just check if they're configured
            results[name] = {
              url,
              configured: true,
              note: 'POST endpoint - cannot test directly, but route exists'
            }
          }
        } catch (error) {
          results[name] = {
            url,
            accessible: false,
            error: error.message
          }
        }
      }

      return response.json({
        timestamp: new Date().toISOString(),
        baseUrl,
        mpesaBasePath,
        callbackUrls,
        testResults: results,
        instructions: {
          step1: 'Ensure all URLs are accessible from the internet',
          step2: 'Register these URLs in your M-Pesa Dashboard (Daraja Portal)',
          step3: 'Test with actual M-Pesa transactions',
          note: 'M-Pesa servers must be able to reach these URLs'
        }
      })

    } catch (error) {
      console.error('=== CALLBACK URL TEST ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to test callback URLs',
        message: error.message
      })
    }
  }

  /**
   * Simulate M-Pesa callback to test endpoint functionality
   */
  public async simulateCallback({ request, response }: HttpContextContract) {
    try {
      const { type, data } = request.only(['type', 'data'])

      if (!type || !data) {
        return response.badRequest({
          error: 'Missing required parameters',
          required: ['type', 'data'],
          example: {
            type: 'confirmation',
            data: {
              TransactionType: 'Pay Bill',
              TransID: 'LHG31AA5TX',
              TransTime: '**************',
              TransAmount: '10.00',
              BusinessShortCode: '600638',
              BillRefNumber: 'invoice123',
              InvoiceNumber: '',
              OrgAccountBalance: '49197.00',
              ThirdPartyTransID: '',
              MSISDN: '************',
              FirstName: 'John',
              MiddleName: '',
              LastName: 'Doe'
            }
          }
        })
      }

      console.log('=== SIMULATING MPESA CALLBACK ===')
      console.log('Type:', type)
      console.log('Data:', JSON.stringify(data, null, 2))

      const baseUrl = process.env.MPESA_CALLBACK_DOMAIN || process.env.APP_URL || 'https://uat-api.appinapp.ke'
      const mpesaBasePath = '/v1/mpsa/e59ed6a68b83'

      let targetUrl = ''
      switch (type.toLowerCase()) {
        case 'validation':
          targetUrl = `${baseUrl}${mpesaBasePath}/validate`
          break
        case 'confirmation':
          targetUrl = `${baseUrl}${mpesaBasePath}/confirm`
          break
        case 'timeout':
          targetUrl = `${baseUrl}${mpesaBasePath}/timeout`
          break
        case 'result':
          targetUrl = `${baseUrl}${mpesaBasePath}/result`
          break
        default:
          return response.badRequest({
            error: 'Invalid callback type',
            validTypes: ['validation', 'confirmation', 'timeout', 'result']
          })
      }

      // Simulate the callback by making a request to our own endpoint
      const fetch = require('node-fetch')
      
      const callbackResponse = await fetch(targetUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'M-Pesa-Simulator/1.0'
        },
        body: JSON.stringify(data),
        timeout: 10000
      })

      const responseText = await callbackResponse.text()
      let responseJson: MpesaCallbackResponse | null = null

      try {
        responseJson = JSON.parse(responseText) as MpesaCallbackResponse
      } catch (e) {
        // Response is not JSON
      }

      const analysis: SimulationAnalysis = {
        endpointReachable: callbackResponse.status !== 0,
        validResponse: callbackResponse.ok,
        expectedMpesaResponse: responseJson !== null && (responseJson.ResultCode !== undefined),
        recommendations: []
      }

      const result = {
        timestamp: new Date().toISOString(),
        simulation: {
          type,
          targetUrl,
          requestData: data
        },
        response: {
          status: callbackResponse.status,
          statusText: callbackResponse.statusText,
          headers: Object.fromEntries(callbackResponse.headers.entries()),
          body: responseJson || responseText
        },
        success: callbackResponse.ok,
        analysis
      }

      // Add recommendations based on results
      if (!callbackResponse.ok) {
        analysis.recommendations.push('Endpoint returned error status - check server logs')
      }

      if (!responseJson || responseJson.ResultCode === undefined) {
        analysis.recommendations.push('Response should include ResultCode and ResultDesc for M-Pesa compatibility')
      }

      if (callbackResponse.ok && responseJson && responseJson.ResultCode === 0) {
        analysis.recommendations.push('✅ Callback simulation successful - endpoint is working correctly')
      }

      console.log('Simulation Result:', JSON.stringify(result, null, 2))

      return response.json(result)

    } catch (error) {
      console.error('=== CALLBACK SIMULATION ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to simulate callback',
        message: error.message
      })
    }
  }

  /**
   * Get callback configuration summary
   */
  public async getCallbackConfig({ response }: HttpContextContract) {
    try {
      const validation = MpesaConfigValidator.validate()
      const baseUrl = process.env.MPESA_CALLBACK_DOMAIN || process.env.APP_URL || 'https://uat-api.appinapp.ke'
      const mpesaBasePath = '/v1/mpsa/e59ed6a68b83'

      const config = {
        timestamp: new Date().toISOString(),
        configuration: {
          baseUrl,
          mpesaBasePath,
          environment: process.env.MPESA_ENVIRONMENT,
          shortcode: process.env.MPESA_SHORTCODE,
          callbackDomain: process.env.MPESA_CALLBACK_DOMAIN,
          appUrl: process.env.APP_URL
        },
        callbackUrls: {
          validation: `${baseUrl}${mpesaBasePath}/validate`,
          confirmation: `${baseUrl}${mpesaBasePath}/confirm`,
          timeout: `${baseUrl}${mpesaBasePath}/timeout`,
          result: `${baseUrl}${mpesaBasePath}/result`,
          health: `${baseUrl}${mpesaBasePath}/health`
        },
        validation: {
          isValid: validation.isValid,
          errors: validation.errors,
          warnings: validation.warnings
        },
        troubleshooting: {
          commonIssues: [
            'Callback URLs not registered in M-Pesa Dashboard',
            'Server not accessible from internet (firewall/NAT issues)',
            'SSL certificate issues (M-Pesa requires HTTPS)',
            'Incorrect callback URL format',
            'Authentication middleware blocking M-Pesa requests'
          ],
          checkList: [
            '✓ Verify callback URLs are accessible from internet',
            '✓ Register URLs in M-Pesa Dashboard (Daraja Portal)',
            '✓ Ensure server uses HTTPS (required for production)',
            '✓ Check firewall allows incoming connections on port 443',
            '✓ Verify callback endpoints return proper M-Pesa response format',
            '✓ Test with M-Pesa simulator before going live'
          ]
        },
        nextSteps: [
          '1. Test callback URL accessibility',
          '2. Simulate callbacks to verify endpoint functionality',
          '3. Register URLs in M-Pesa Dashboard',
          '4. Test with actual M-Pesa transactions',
          '5. Monitor callback success rates'
        ]
      }

      return response.json(config)

    } catch (error) {
      console.error('=== CALLBACK CONFIG ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to get callback configuration',
        message: error.message
      })
    }
  }

  /**
   * Monitor recent callback attempts
   */
  public async monitorCallbacks({ response }: HttpContextContract) {
    // This would typically read from logs or a database
    // For now, we'll return a template for monitoring
    
    return response.json({
      timestamp: new Date().toISOString(),
      monitoring: {
        note: 'This endpoint would show recent callback attempts',
        implementation: 'Check server logs for callback requests',
        logPatterns: [
          'Look for "=== MPESA VALIDATION REQUEST ===" entries',
          'Look for "=== MPESA CONFIRMATION REQUEST ===" entries',
          'Check for "=== M-PESA CALLBACK MIDDLEWARE ===" entries'
        ]
      },
      recommendations: [
        'Implement callback logging to database for better monitoring',
        'Set up alerts for failed callbacks',
        'Track callback success rates',
        'Monitor response times'
      ]
    })
  }
}
