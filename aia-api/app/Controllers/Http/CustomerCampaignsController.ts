import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Campaign from 'App/Models/Campaign'
import { DateTime } from 'luxon'

export default class CustomerCampaignsController {
  /**
   * @index
   * @summary Get personalized campaigns for customer
   * @description Get campaigns personalized for the authenticated customer based on their preferences, order history, and location
   * @paramQuery per - Number of items per page (max 50)
   * @paramQuery page - Page number
   * @paramQuery order - Order by column (createdAt, position, startDate)
   * @paramQuery sort - Sort direction (asc or desc)
   * @paramQuery type - Campaign type filter (global, vendor, location, personalized)
   */
  public async index({ request, response, auth }: HttpContextContract) {
    const {
      per = 20,
      page = 1,
      order = 'position',
      sort = 'asc',
      type,
    } = request.qs()

    const user = auth.user!
    const perPage = Math.min(parseInt(per), 50)

    // Load user relationships for personalization
    await user.load('vendors')
    await user.load('branches')

    const campaignQuery = Campaign.query()
      .where('status', 'Approved')
      .where('startDate', '<=', DateTime.now().toSQL())
      .where('endDate', '>=', DateTime.now().toSQL())
      .preload('vendor', (vendorQuery) => {
        vendorQuery.where('active', true)
      })
      .preload('branch')

    // Apply personalization logic
    await this.applyPersonalizationFilters(campaignQuery, user, type)

    // Order by position first (for carousel ordering), then by specified order
    if (order === 'position') {
      campaignQuery.orderByRaw('position ASC NULLS LAST')
    } else {
      campaignQuery.orderBy(order, sort)
    }

    const campaigns = await campaignQuery.paginate(page, perPage)

    // Only expose customer-safe fields
    const result = campaigns.serialize({
      fields: [
        'id',
        'name',
        'details',
        'link',
        'image',
        'displayDuration',
        'position',
        'startDate',
        'endDate',
        'vendor',
        'branch',
      ],
    })

    return response.json(result)
  }

  /**
   * @recommendations
   * @summary Get campaign recommendations for customer
   * @description Get campaigns recommended based on customer's order history and preferences
   * @paramQuery per - Number of items per page (max 20)
   * @paramQuery page - Page number
   */
  public async recommendations({ request, response, auth }: HttpContextContract) {
    const { per = 10, page = 1 } = request.qs()
    const user = auth.user!
    const perPage = Math.min(parseInt(per), 20)

    // Load user order history for recommendations
    await user.load('orders', (orderQuery) => {
      orderQuery
        .where('status', 'Completed')
        .preload('vendor')
        .orderBy('createdAt', 'desc')
        .limit(50) // Last 50 orders for analysis
    })

    const campaignQuery = Campaign.query()
      .where('status', 'Approved')
      .where('startDate', '<=', DateTime.now().toSQL())
      .where('endDate', '>=', DateTime.now().toSQL())
      .preload('vendor', (vendorQuery) => {
        vendorQuery.where('active', true)
      })
      .preload('branch')

    // Get vendor IDs from user's order history
    const orderVendorIds = user.orders?.map(order => order.vendorId) || []
    const uniqueVendorIds = [...new Set(orderVendorIds)]

    if (uniqueVendorIds.length > 0) {
      // Prioritize campaigns from vendors the customer has ordered from
      campaignQuery.whereIn('vendorId', uniqueVendorIds)
    }

    campaignQuery.orderByRaw('position ASC NULLS LAST')

    const campaigns = await campaignQuery.paginate(page, perPage)

    // Only expose customer-safe fields
    const result = campaigns.serialize({
      fields: [
        'id',
        'name',
        'details',
        'link',
        'image',
        'displayDuration',
        'position',
        'startDate',
        'endDate',
        'vendor',
        'branch',
      ],
    })

    return response.json(result)
  }

  /**
   * Apply personalization filters to campaign query
   */
  private async applyPersonalizationFilters(campaignQuery: any, user: any, type?: string) {
    switch (type) {
      case 'global':
        // Global campaigns - no additional filtering needed
        break

      case 'vendor':
        // Vendor-specific campaigns from user's associated vendors
        const vendorIds = user.vendors?.map((vendor) => vendor.id) || []
        if (vendorIds.length > 0) {
          campaignQuery.whereIn('vendorId', vendorIds)
        } else {
          // No associated vendors - return empty result
          campaignQuery.where('id', 'non-existent-id')
        }
        break

      case 'location':
        // Location-based campaigns (would need user location data)
        // This would require implementing geospatial queries
        break

      case 'personalized':
        // Personalized campaigns based on order history
        await user.load('orders', (orderQuery) => {
          orderQuery
            .where('status', 'Completed')
            .select('vendorId')
            .distinct('vendorId')
            .limit(20)
        })

        const orderVendorIds = user.orders?.map(order => order.vendorId) || []
        if (orderVendorIds.length > 0) {
          campaignQuery.whereIn('vendorId', orderVendorIds)
        }
        break

      default:
        // Default: show all approved campaigns with some personalization
        // Could implement a scoring system here
        break
    }
  }

  /**
   * @interactions
   * @summary Track campaign interactions
   * @description Track when a customer views or clicks on a campaign
   * @paramPath id - Campaign ID
   * @paramBody action - Action type (view, click, dismiss)
   */
  public async trackInteraction({ params, request, response, auth }: HttpContextContract) {
    const { action } = request.only(['action'])
    const user = auth.user!

    try {
      const campaign = await Campaign.findOrFail(params.id)

      // Here you would implement campaign analytics tracking
      // For example, storing interaction data in a separate table
      // await CampaignInteraction.create({
      //   campaignId: campaign.id,
      //   userId: user.id,
      //   action: action,
      //   timestamp: DateTime.now()
      // })

      return response.json({
        message: 'Interaction tracked successfully',
        campaignId: campaign.id,
        action: action,
      })
    } catch (error) {
      return response.status(404).json({
        error: 'Campaign not found',
      })
    }
  }
}
