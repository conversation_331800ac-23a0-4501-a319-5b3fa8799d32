import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import NotificationTracking from 'App/Services/NotificationTracking'

export default class NotificationTrackingController {
  /**
   * @getStats
   * @summary Get notification statistics
   * @description Get statistics about notifications including counts by class name, channel, and status
   * @paramQuery startDate string - Start date for filtering (YYYY-MM-DD)
   * @paramQuery endDate string - End date for filtering (YYYY-MM-DD)
   * @paramQuery notificationClassName string - Filter by notification class name (e.g., CustomerPaymentReceived, CustomerNewOrder)
   * @paramQuery channel string - Filter by notification channel (fcm, sms, mail, database)
   * @paramQuery status string - Filter by notification status (sent, delivered, failed, read)
   * @responseBody 200 - { total: number, byType: object, byChannel: object, byStatus: object }
   */
  public async getStats({ request, response }: HttpContextContract) {
    const { startDate, endDate, notificationClassName, channel, status } = request.qs()

    const stats = await NotificationTracking.getStats({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      notificationClassName,
      channel,
      status,
    })

    return response.json(stats)
  }

  /**
   * @getDeliveryRates
   * @summary Get notification delivery rates
   * @description Get delivery success rates for notifications by class name and channel
   * @paramQuery startDate string - Start date for filtering (YYYY-MM-DD)
   * @paramQuery endDate string - End date for filtering (YYYY-MM-DD)
   * @paramQuery notificationClassName string - Filter by notification class name (e.g., CustomerPaymentReceived, CustomerNewOrder)
   * @paramQuery channel string - Filter by notification channel (fcm, sms, mail, database)
   * @responseBody 200 - { overall: number, byType: object, byChannel: object }
   */
  public async getDeliveryRates({ request, response }: HttpContextContract) {
    const { startDate, endDate, notificationClassName, channel } = request.qs()

    const rates = await NotificationTracking.getDeliveryRates({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      notificationClassName,
      channel,
    })

    return response.json(rates)
  }
} 