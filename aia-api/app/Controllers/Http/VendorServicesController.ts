import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Vendor from '../../Models/Vendor'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'

/**
 * @name Vendor management
 * @version 1.0.0
 * @description Vendor management for the application
 */
export default class VendorServicesController {
  /**
   * @index
   * @summary List all vendores
   * @description List all vendores, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const vendorQuery = vendor.related('services').query().filter(filters)

    return await vendorQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Attach a service to a vendor
   * @description Attach a service to a vendor with active status
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Vendor>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { services = [], active = true } = request.all()

    await vendor.related('services').sync(
      services.reduce((acc, serviceId) => {
        acc[serviceId] = { active }
        return acc
      }, {}),
      false
    )

    await vendor.save()

    return response.json(vendor)
  }

  /**
   * @update
   * @summary Update a vendor service
   * @description Update a vendor with active status
   * @requestBody <Vendor>
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  @bind()
  public async update(
    { request, response }: HttpContextContract,
    vendor: Vendor,
    service: Service
  ) {
    const { active } = request.all()
    
    await vendor.related('services').sync({ [service.id]: { active } })
    
    await vendor.save()

    return response.json(vendor)
  }

  /**
   * @destroy
   * @summary Detach a service from a vendor
   *
   */
  @bind()
  public async destroy({ response }: HttpContextContract, vendor: Vendor, service: Service) {
    await vendor.related('services').detach([service.id])
    await vendor.save()

    return response.json(vendor)
  }
}
