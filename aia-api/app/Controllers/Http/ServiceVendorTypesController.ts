import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'

export default class ServiceVendorTypesController {
  @bind()

  /**
   * @index
   * @summary Show all ServiceVendorTypes
   * @version 1.0.0
   * @description  ServiceVendorTypes management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = service.related('vendorTypes').query().filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a ServiceVendors
   * @description Create a ServiceVendors with their details (name and details)
   * @requestBody {"name": "", "details": "", "serviceId": ""}
   */
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { name, details, serviceId } = request.all()
    const type = await service.related('vendorTypes').create({ name, details, serviceId })

    const image = request.file('image')
    if (image) {
      type.merge({ image: Attachment.fromFile(image) })
    }

    await type.save()

    return response.json(type)
  }
}
