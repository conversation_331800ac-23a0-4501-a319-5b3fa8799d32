import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Product from '../../Models/Product'
import { bind } from '@adonisjs/route-model-binding'
import { ModifierType } from 'App/Enums/ModifierType'
import PackagingOption from '../../Models/PackagingOption'

/**
 * @name Product management
 * @version 1.0.0
 * @description Product management for the application
 */
export default class ProductsController {
  /**
   * @index
   * @summary List all products
   * @description List all products, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const productQuery = Product.filter(filters)
      .preload('category')
      .preload('vendor')
      .preload('branch')
      .preload('service')
      .withCount('forms', (query) => {
        query.as('totalForms')
      })

    return await productQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a product
   * @description Create a product with their details including packaging options
   * @requestBody {
   *   "name": "Product Name",
   *   "details": "Product description",
   *   "price": 1000,
   *   "vendorId": "vendor_id",
   *   "packagingOptionIds": ["pkg_opt_123", "pkg_opt_456"]
   * }
   * @responseBody 200 - <Product>
   * @response 400 - Bad Request - Invalid packaging options or validation errors
   */
  public async store({ request, response, auth }: HttpContextContract) {
    const {
      name,
      ref,
      details,
      price,
      discounted,
      stock = -1,
      active = true,
      featured = false,
      type = 'Digital',
      condition = 'New',
      status = 'Draft',
      availability = 'In Stock',
      shipping = 'Free',
      unit = 'other',
      mode = 'Single',
      payment = 'Prepaid',
      visibility = 'Public',
      productCategoryId,
      vendorId,
      branchId,
      serviceId,
      meta = {},
      extra = {},
      tagIds = [],
      accompaniments = {},
      upsells = {},
      available_modifiers = {},
      packagingOptionIds = [],
    } = request.all()

    const product = await Product.create({
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type,
      condition,
      status,
      availability,
      shipping,
      unit,
      mode,
      visibility,
      payment,
      productCategoryId,
      userId: auth.user?.id,
      vendorId,
      branchId,
      serviceId,
      meta,
      extra,
    })

    const image = request.file('image')
    if (image) {
      await product.merge({ image: Attachment.fromFile(image) }).save()
    }

    const gallery = request.files('gallery')
    if (gallery.length > 0) {
      await product.related('gallery').createMany(gallery.map((file) => Attachment.fromFile(file)))
    }

    await product.related('tags').sync(tagIds)

    Object.entries(accompaniments).forEach(([productId, value]) => {
      product.related('accompaniments').attach({
        [productId]: {
          price: (value as any).price,
        },
      })
    })

    Object.entries(upsells).forEach(([productId, value]) => {
      product.related('upsells').attach({
        [productId]: {
          price: (value as any).price,
        },
      })
    })

    // Handle modifiers
    Object.entries(available_modifiers).forEach(([modifierOptionId, value]) => {
      product.related('availableModifiers').attach({
        [modifierOptionId]: {
          price_adjustment_override: (value as any).price_adjustment_override,
          is_default: (value as any).is_default || false,
          sort_order: (value as any).sort_order || 0,
        },
      })
    })

    // Handle packaging options
    if (packagingOptionIds && packagingOptionIds.length > 0) {
      // Validate that all packaging options belong to the same vendor as the product
      const packagingOptions = await PackagingOption.query()
        .whereIn('id', packagingOptionIds)
        .where('vendorId', vendorId)
        .where('active', true)

      if (packagingOptions.length !== packagingOptionIds.length) {
        return response.badRequest({
          message: 'Some packaging options are invalid or do not belong to this vendor',
          details: 'All packaging options must belong to the same vendor as the product and be active'
        })
      }

      await product.related('packagingOptions').sync(packagingOptionIds)
    }

    return response.json(product)
  }

  @bind()
  /**
   * @show
   * @summary Show a single product
   * @description Show a product with their details (name and details)
   * @paramPath id required number - Product ID
   * @responseBody 200 - <Product>
   * @response 404 - Product not found
   */
  public async show({ response }: HttpContextContract, product: Product) {
    await product.load('vendor')
    await product.load('branch')
    await product.load('forms')
    await product.load('tags')
    await product.load('accompaniments')
    await product.load('upsells')
    await product.load('availableModifiers')
    await product.load('packagingOptions')
    await product.load('category', (cq) => cq.preload('productType'))
    await product.load('service', (sq) => sq.preload('task'))

    // Group modifiers by type
    const modifiers = product.availableModifiers.reduce((acc, modifier) => {
      const type = modifier.type
      if (!acc[type]) {
        acc[type] = []
      }
      acc[type].push({
        ...modifier.toJSON(),
        price_adjustment: modifier.$extras.price_adjustment_override ?? modifier.defaultPriceAdjustment,
        is_default: modifier.$extras.is_default ?? false,
        sort_order: modifier.$extras.sort_order ?? 0,
      })
      return acc
    }, {} as Record<ModifierType, any[]>)

    // Sort each type's modifiers by sort_order
    Object.keys(modifiers).forEach((type) => {
      modifiers[type as ModifierType].sort((a, b) => a.sort_order - b.sort_order)
    })

    const productData = product.toJSON()
    productData.modifiers = modifiers

    return response.json(productData)
  }

  @bind()
  /**
   * @update
   * @summary Update a product
   * @description Update a product with their details (name and details)
   * @paramPath id required number - Product ID
   * @requestBody {
   *   "name": "string",
   *   "details": "string",
   *   "price": "number",
   *   "discounted": "number|null",
   *   "stock": "number",
   *   "active": "boolean",
   *   "featured": "boolean",
   *   "type": "string",
   *   "condition": "string",
   *   "status": "string",
   *   "availability": "string",
   *   "shipping": "string",
   *   "unit": "string",
   *   "mode": "string",
   *   "payment": "string",
   *   "visibility": "string",
   *   "productCategoryId": "string",
   *   "vendorId": "string",
   *   "branchId": "string",
   *   "serviceId": "string",
   *   "meta": "object",
   *   "extra": "object",
   *   "tagIds": "string[]",
   *   "accompaniments": "object",
   *   "upsells": "object",
   *   "available_modifiers": {
   *     "modifier_option_id": {
   *       "price_adjustment_override": "number|null",
   *       "is_default": "boolean",
   *       "sort_order": "number"
   *     }
   *   },
   *   "packagingOptionIds": "string[]"
   * }
   * @responseBody 200 - <Product>
   * @response 404 - Product not found
   */
  public async update({ request, response }: HttpContextContract, product: Product) {
    const {
      name,
      details,
      price,
      discounted,
      payment,
      visibility,
      status,
      availability,
      shipping,
      unit,
      mode,
      stock,
      meta,
      extra,
      tagIds,
      accompaniments,
      upsells,
      featured,
      vendorId,
      branchId,
      available_modifiers = {},
      packagingOptionIds,
    } = request.all()

    await product
      .merge({
        name,
        details,
        price,
        discounted,
        payment,
        visibility,
        status,
        availability,
        shipping,
        unit,
        mode,
        stock,
        meta,
        extra,
        featured,
        vendorId,
        branchId,
      })
      .save()

    const image = request.file('image')
    if (image) {
      await product.merge({ image: Attachment.fromFile(image) }).save()
    }

    const gallery = request.files('gallery')
    if (gallery && gallery.length > 0) {
      await product.related('gallery').createMany(gallery.map((file) => Attachment.fromFile(file)))
    }
    if (tagIds) {
      await product.related('tags').sync(tagIds)
    }

    if (accompaniments) {
      await product.related('accompaniments').sync(
        Object.keys(accompaniments).reduce(
          (acc, itemId) => ({
            ...acc,
            [itemId]: accompaniments[itemId],
          }),
          {}
        )
      )
    }

    if (upsells) {
      await product
        .related('upsells')
        .sync(
          Object.keys(upsells).reduce((acc, itemId) => ({ ...acc, [itemId]: upsells[itemId] }), {})
        )
    }

    // Handle modifiers
    await product.related('availableModifiers').sync(
      Object.entries(available_modifiers).reduce((acc, [modifierOptionId, value]) => {
        acc[modifierOptionId] = {
          price_adjustment_override: (value as any).price_adjustment_override,
          is_default: (value as any).is_default || false,
          sort_order: (value as any).sort_order || 0,
        }
        return acc
      }, {})
    )

    // Handle packaging options
    if (packagingOptionIds !== undefined) {
      if (packagingOptionIds && packagingOptionIds.length > 0) {
        // Validate that all packaging options belong to the same vendor as the product
        const packagingOptions = await PackagingOption.query()
          .whereIn('id', packagingOptionIds)
          .where('vendorId', product.vendorId)
          .where('active', true)

        if (packagingOptions.length !== packagingOptionIds.length) {
          return response.badRequest({
            message: 'Some packaging options are invalid or do not belong to this vendor',
            details: 'All packaging options must belong to the same vendor as the product and be active'
          })
        }
      }

      await product.related('packagingOptions').sync(packagingOptionIds || [])
    }

    // Load the product with all its relationships
    await product.load('vendor')
    await product.load('branch')
    await product.load('forms')
    await product.load('tags')
    await product.load('accompaniments')
    await product.load('upsells')
    await product.load('availableModifiers')
    await product.load('packagingOptions')
    await product.load('category', (cq) => cq.preload('productType'))
    await product.load('service', (sq) => sq.preload('task'))

    // Group modifiers by type
    const modifiers = product.availableModifiers.reduce((acc, modifier) => {
      const type = modifier.type
      if (!acc[type]) {
        acc[type] = []
      }
      acc[type].push({
        ...modifier.toJSON(),
        price_adjustment: modifier.$extras.price_adjustment_override ?? modifier.defaultPriceAdjustment,
        is_default: modifier.$extras.is_default ?? false,
        sort_order: modifier.$extras.sort_order ?? 0,
      })
      return acc
    }, {} as Record<ModifierType, any[]>)

    // Sort each type's modifiers by sort_order
    Object.keys(modifiers).forEach((type) => {
      modifiers[type as ModifierType].sort((a, b) => a.sort_order - b.sort_order)
    })

    const productData = product.toJSON()
    productData.modifiers = modifiers

    return response.json(productData)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a product
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, product: Product) {
    return await product.delete()
  }
}
