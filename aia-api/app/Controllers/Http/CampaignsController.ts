import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Campaign from '../../Models/Campaign'
import { bind } from '@adonisjs/route-model-binding'
import CampaignStatusService from 'App/Services/CampaignStatusService'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

/**
 * @name Campaign management
 * @version 1.0.0
 * @description Campaign management for the application
 */
export default class CampaignsController {
  /**
   * @index
   * @summary List all campaigns
   * @description List all campaigns, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request, auth }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const user = auth.user!

    // SECURITY: Apply vendor-scoped role-based filtering
    await user.load('roles')
    await user.load('vendors')
    await user.load('branches')

    const campaignQuery = Campaign.filter(filters)

    // Apply vendor-scoped filtering for campaigns
    const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []

    if (
      roleNames.includes('super admin') ||
      roleNames.includes('platform admin') ||
      roleNames.includes('admin')
    ) {
      // Super admins and admins can see all campaigns
    } else if (roleNames.includes('vendor admin')) {
      // Vendor admins see campaigns for their vendors
      const vendorIds = user.vendors?.map((vendor) => vendor.id) || []
      if (vendorIds.length > 0) {
        campaignQuery.whereIn('vendorId', vendorIds)
      } else {
        // No vendors associated - return empty result
        campaignQuery.where('id', 'non-existent-id')
      }
    } else {
      // Other roles see campaigns from vendors they're associated with
      const vendorIds = user.vendors?.map((vendor) => vendor.id) || []
      const branchIds = user.branches?.map((branch) => branch.id) || []

      if (vendorIds.length > 0 || branchIds.length > 0) {
        campaignQuery.where((query) => {
          if (vendorIds.length > 0) {
            query.whereIn('vendorId', vendorIds)
          }
          if (branchIds.length > 0) {
            query.orWhereIn('branchId', branchIds)
          }
        })
      } else {
        // No vendors or branches associated - return empty result
        campaignQuery.where('id', 'non-existent-id')
      }
    }

    return await campaignQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a campaign
   * @description Create a campaign with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Campaign>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, details, vendorId, branchId, startDate, endDate, link } = request.all()

    const campaign = new Campaign()

    // Validate and set startDate
    if (!startDate || startDate === 'undefined') {
      return response.status(400).json({
        error: 'Start date is required and must be a valid timestamp',
      })
    }

    // Validate and set endDate
    if (!endDate) {
      return response.status(400).json({
        error: 'End date is required and must be a valid timestamp',
      })
    }

    campaign.fill({ name, details, vendorId, branchId, startDate, endDate, link })

    const image = request.file('image')
    if (image) {
      campaign.merge({ image: Attachment.fromFile(image) })
    }

    await campaign.save()

    return response.json(campaign)
  }

  @bind()
  /**
   * @show
   * @summary Show a single campaign
   * @description Show a campaign with their details (name and details)
   * @paramPath id required number - Campaign ID
   * @responseBody 200 - <Campaign>
   * @response 404 - Campaign not found
   */
  public async show({ response }: HttpContextContract, campaign: Campaign) {
    return response.json(campaign)
  }

  @bind()
  /**
   * @update
   * @summary Update a campaign
   * @description Update a campaign with their details (name and details)
   * @paramPath id required number - Campaign ID
   * @requestBody <Campaign>
   * @responseBody 200 - <Campaign>
   * @response 404 - Campaign not found
   */
  public async update({ request, response, auth }: HttpContextContract, campaign: Campaign) {
    const { name, details, startDate, endDate, link, status } = request.all()

    if (status && status !== campaign.status) {
      try {
        await CampaignStatusService.changeStatus(campaign, status, auth.user?.id)
      } catch (error) {
        return response.badRequest({ message: error.message })
      }
    }

    await campaign
      .merge({
        name,
        details,
        startDate,
        endDate,
        link,
      })
      .save()

    const image = request.file('image')
    if (image) {
      await campaign.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(campaign)
  }

  @bind()

  /**
   * @destroy
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, campaign: Campaign) {
    return await campaign.delete()
  }
}
