import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import MessageTemplate from '../../Models/MessageTemplate'
import { bind } from '@adonisjs/route-model-binding'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'

/**
 * @name MessageTemplate management
 * @version 1.0.0
 * @description MessageTemplate management for the application
 */
export default class MessageTemplatesController {
  /**
   * @index
   * @summary List all templates
   * @description List all templates, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const templateQuery = MessageTemplate.filter(filters)

    return await templateQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a template
   * @description Create a template with their content (name and content)
   * @requestBody {"name": "", "content": ""}
   * @responseBody 200 - <MessageTemplate>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, content, vendorId, branchId } = request.all()

    const template = await MessageTemplate.create({ name, content, vendorId, branchId })

    const image = request.file('image')

    if (image) {
      await template
        .merge({
          image: Attachment.fromFile(image),
        })
        .save()
    }

    return response.json(template)
  }

  @bind()
  /**
   * @show
   * @summary Show a single template
   * @description Show a template with their content (name and content)
   * @paramPath id required number - MessageTemplate ID
   * @responseBody 200 - <MessageTemplate>
   * @response 404 - MessageTemplate not found
   */
  public async show({ response }: HttpContextContract, template: MessageTemplate) {
    return response.json(template)
  }

  @bind()
  /**
   * @update
   * @summary Update a template
   * @description Update a template with their content (name and content)
   * @paramPath id required number - MessageTemplate ID
   * @requestBody <MessageTemplate>
   * @responseBody 200 - <MessageTemplate>
   * @response 404 - MessageTemplate not found
   */
  public async update({ request, response }: HttpContextContract, template: MessageTemplate) {
    const input = request.all()

    await template.merge(input).save()

    return response.json(template)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a template
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, template: MessageTemplate) {
    return await template.delete()
  }
}
