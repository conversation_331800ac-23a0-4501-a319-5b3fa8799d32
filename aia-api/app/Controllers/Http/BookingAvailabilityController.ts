import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { DateTime } from 'luxon'
import BookingAvailabilityService from 'App/Services/BookingAvailabilityService'
import BookingValidationService from 'App/Services/BookingValidationService'

export default class BookingAvailabilityController {
  /**
   * Get available time slots for a service on a specific date
   */
  public async getAvailableSlots({ request, response }: HttpContextContract) {
    try {
      const {
        productId,
        branchId,
        selectedOptions = [],
        preferredDate,
        customerId
      } = request.only(['productId', 'branchId', 'selectedOptions', 'preferredDate', 'customerId'])

      // Validate required parameters
      if (!productId) {
        return response.status(400).json({
          success: false,
          message: 'Product ID is required'
        })
      }

      if (!branchId) {
        return response.status(400).json({
          success: false,
          message: 'Branch ID is required'
        })
      }

      if (!preferredDate) {
        return response.status(400).json({
          success: false,
          message: 'Preferred date is required'
        })
      }

      // Parse the preferred date
      let parsedDate: DateTime
      try {
        parsedDate = DateTime.fromISO(preferredDate)
        if (!parsedDate.isValid) {
          throw new Error('Invalid date format')
        }
      } catch (error) {
        return response.status(400).json({
          success: false,
          message: 'Invalid date format. Use ISO format (YYYY-MM-DD)'
        })
      }

      // Get available slots
      const availability = await BookingAvailabilityService.getAvailableSlots({
        productId,
        branchId,
        selectedOptions,
        preferredDate: parsedDate,
        customerId
      })

      return response.json({
        success: true,
        data: availability
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get available slots',
        error: error.message
      })
    }
  }

  /**
   * Reserve a specific time slot temporarily
   */
  public async reserveSlot({ request, response }: HttpContextContract) {
    try {
      const {
        productId,
        branchId,
        startTime,
        endTime,
        customerId,
        selectedOptions = []
      } = request.only(['productId', 'branchId', 'startTime', 'endTime', 'customerId', 'selectedOptions'])

      // Validate required parameters
      if (!productId || !branchId || !startTime || !endTime || !customerId) {
        return response.status(400).json({
          success: false,
          message: 'Product ID, Branch ID, start time, end time, and customer ID are required'
        })
      }

      // Parse times
      let parsedStartTime: DateTime
      let parsedEndTime: DateTime
      
      try {
        parsedStartTime = DateTime.fromISO(startTime)
        parsedEndTime = DateTime.fromISO(endTime)
        
        if (!parsedStartTime.isValid || !parsedEndTime.isValid) {
          throw new Error('Invalid time format')
        }
      } catch (error) {
        return response.status(400).json({
          success: false,
          message: 'Invalid time format. Use ISO format'
        })
      }

      // Reserve the slot
      const reservation = await BookingAvailabilityService.reserveSlot(
        productId,
        branchId,
        parsedStartTime,
        parsedEndTime,
        customerId,
        selectedOptions
      )

      if (!reservation.reserved) {
        return response.status(409).json({
          success: false,
          message: 'Time slot is no longer available'
        })
      }

      return response.json({
        success: true,
        data: reservation
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to reserve slot',
        error: error.message
      })
    }
  }

  /**
   * Validate a booking request without creating it
   */
  public async validateBooking({ request, response }: HttpContextContract) {
    try {
      const {
        productId,
        branchId,
        customerId,
        appointmentStart,
        appointmentEnd,
        selectedServiceOptions = [],
        staffAssignments = [],
        equipmentReservations = []
      } = request.only([
        'productId',
        'branchId', 
        'customerId',
        'appointmentStart',
        'appointmentEnd',
        'selectedServiceOptions',
        'staffAssignments',
        'equipmentReservations'
      ])

      // Validate required parameters
      if (!productId || !branchId || !customerId || !appointmentStart || !appointmentEnd) {
        return response.status(400).json({
          success: false,
          message: 'Product ID, Branch ID, Customer ID, appointment start and end times are required'
        })
      }

      // Parse times
      let parsedStartTime: DateTime
      let parsedEndTime: DateTime
      
      try {
        parsedStartTime = DateTime.fromISO(appointmentStart)
        parsedEndTime = DateTime.fromISO(appointmentEnd)
        
        if (!parsedStartTime.isValid || !parsedEndTime.isValid) {
          throw new Error('Invalid time format')
        }
      } catch (error) {
        return response.status(400).json({
          success: false,
          message: 'Invalid time format. Use ISO format'
        })
      }

      // Validate the booking request
      const validation = await BookingValidationService.validateBookingRequest({
        productId,
        branchId,
        customerId,
        appointmentStart: parsedStartTime,
        appointmentEnd: parsedEndTime,
        selectedServiceOptions,
        staffAssignments,
        equipmentReservations
      })

      return response.json({
        success: true,
        data: validation
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to validate booking',
        error: error.message
      })
    }
  }

  /**
   * Get multiple days of availability
   */
  public async getMultiDayAvailability({ request, response }: HttpContextContract) {
    try {
      const {
        productId,
        branchId,
        selectedOptions = [],
        startDate,
        endDate,
        customerId
      } = request.only(['productId', 'branchId', 'selectedOptions', 'startDate', 'endDate', 'customerId'])

      // Validate required parameters
      if (!productId || !branchId || !startDate || !endDate) {
        return response.status(400).json({
          success: false,
          message: 'Product ID, Branch ID, start date, and end date are required'
        })
      }

      // Parse dates
      let parsedStartDate: DateTime
      let parsedEndDate: DateTime
      
      try {
        parsedStartDate = DateTime.fromISO(startDate)
        parsedEndDate = DateTime.fromISO(endDate)
        
        if (!parsedStartDate.isValid || !parsedEndDate.isValid) {
          throw new Error('Invalid date format')
        }
      } catch (error) {
        return response.status(400).json({
          success: false,
          message: 'Invalid date format. Use ISO format (YYYY-MM-DD)'
        })
      }

      // Validate date range
      if (parsedEndDate < parsedStartDate) {
        return response.status(400).json({
          success: false,
          message: 'End date must be after start date'
        })
      }

      const daysDiff = parsedEndDate.diff(parsedStartDate, 'days').days
      if (daysDiff > 30) {
        return response.status(400).json({
          success: false,
          message: 'Date range cannot exceed 30 days'
        })
      }

      // Get availability for each day
      const availabilityPromises: Promise<any>[] = []
      let currentDate = parsedStartDate

      while (currentDate <= parsedEndDate) {
        availabilityPromises.push(
          BookingAvailabilityService.getAvailableSlots({
            productId,
            branchId,
            selectedOptions,
            preferredDate: currentDate,
            customerId
          })
        )
        currentDate = currentDate.plus({ days: 1 })
      }

      const availabilityResults = await Promise.all(availabilityPromises)

      return response.json({
        success: true,
        data: {
          dateRange: {
            startDate: parsedStartDate.toFormat('yyyy-MM-dd'),
            endDate: parsedEndDate.toFormat('yyyy-MM-dd')
          },
          availability: availabilityResults
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get multi-day availability',
        error: error.message
      })
    }
  }
}
