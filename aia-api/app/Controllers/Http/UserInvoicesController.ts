import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Invoice from 'App/Models/Invoice'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'
import Database from '@ioc:Adonis/Lucid/Database'


export default class UserInvoicesController {
  /**
   * @index
   * @summary List all Invoices for a User
   * @description List all Invoices for a specific User, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    
    const invoiceQuery = Invoice.query()
      .whereHas('order', (query) => {
        query.where('userId', user.id)
      })
      .preload('order', (orderQuery) => {
        orderQuery.preload('customer')
          .preload('vendor')
          .preload('branch')
      })
      .preload('payments')
      .filter(filters)

    // Get status totals and pending amount
    const [statusTotals, pendingAmount] = await Promise.all([
      Database.from('invoices')
        .join('orders', 'invoices.order_id', 'orders.id')
        .where('orders.user_id', user.id)
        .select('invoices.status')
        .count('* as count')
        .groupBy('invoices.status'),
      
      Database.from('invoices')
        .join('orders', 'invoices.order_id', 'orders.id')
        .where('orders.user_id', user.id)
        .where('invoices.status', 'Pending')
        .sum('invoices.amount as total')
        .first()
    ])

    const totals = {
      Pending: 0,
      Paid: 0,
      Failed: 0,
      Cancelled: 0
    }

    statusTotals.forEach(({ status, count }) => {
      totals[status] = parseInt(count)
    })

    const paginator = await invoiceQuery.orderBy(order, sort).paginate(page, per)
    
    // Create a new meta object with the additional properties
    const meta = {
      total: paginator.total,
      per_page: paginator.perPage,
      current_page: paginator.currentPage,
      last_page: paginator.lastPage,
      totals,
      pendingAmount: Number(pendingAmount?.total || 0)
    }

    // Return a new object with the updated meta
    return {
      meta,
      data: paginator.toJSON().data
    }
  }
} 