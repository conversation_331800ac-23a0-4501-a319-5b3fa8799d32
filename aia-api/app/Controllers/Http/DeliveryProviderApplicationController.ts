import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import DeliveryProviderProfile from 'App/Models/DeliveryProviderProfile'
import DeliveryProviderApplicationValidator from 'App/Validators/DeliveryProviderApplicationValidator'
import DeliveryProviderReviewValidator from 'App/Validators/DeliveryProviderReviewValidator'
import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'

// Delivery Provider Application Notifications
import DeliveryProviderApplicationApproved from 'App/Notifications/DeliveryProvider/DeliveryProviderApplicationApproved'
import DeliveryProviderApplicationRejected from 'App/Notifications/DeliveryProvider/DeliveryProviderApplicationRejected'

/**
 * @summary Delivery Provider Application Management
 * @group Delivery Provider Applications
 * @version 1.0.0
 * @description Phase 1 & 2A: Manage delivery provider applications and verification workflow with notification system
 */
export default class DeliveryProviderApplicationController {
  /**
   * @apply
   * @summary Submit delivery provider application
   * @description Phase 1: Vendor submits application to become a delivery provider
   * @requestBody {
   *   "vehicleTypes": ["car", "motorcycle"],
   *   "maxDeliveryDistance": 10,
   *   "maxConcurrentOrders": 3,
   *   "maxOrderWeight": 25,
   *   "maxOrderVolume": 50,
   *   "workingHours": {
   *     "monday": {"start": "09:00", "end": "18:00", "active": true},
   *     "tuesday": {"start": "09:00", "end": "18:00", "active": true}
   *   },
   *   "serviceAreas": [{"name": "Downtown", "coordinates": [{"lat": 40.7128, "lng": -74.0060}]}],
   *   "emergencyContact": {"name": "John Doe", "phone": "+**********", "relationship": "spouse"}
   * }
   *
   * @responseBody 201 - {
   *   "message": "Delivery provider application submitted successfully",
   *   "profile": {
   *     "id": "string",
   *     "vendorId": "string",
   *     "verificationStatus": "pending",
   *     "vehicleTypes": ["string"],
   *     "maxDeliveryDistance": "number",
   *     "maxConcurrentOrders": "number",
   *     "createdAt": "string",
   *     "updatedAt": "string"
   *   }
   * }
   * @response 400 - {"message": "Vendor must be verified before applying for delivery services"}
   * @response 400 - {"message": "Vendor already has a delivery provider application"}
   * @response 401 - Unauthorized
   * @response 422 - Validation failed
   */
  @bind()
  public async apply({ request, response, auth }: HttpContextContract, vendor: Vendor) {
    // Ensure vendor is verified before they can apply for delivery services
    // Using existing verification logic - no changes to current vendor authorization
    if (!vendor.active || vendor.verificationStatus !== 'verified') {
      return response.badRequest({
        message: 'Vendor must be verified before applying for delivery services',
      })
    }

    // Check if vendor already has a delivery provider profile
    const existingProfile = await vendor.related('deliveryProfile').query().first()
    if (existingProfile) {
      return response.badRequest({
        message: 'Vendor already has a delivery provider application',
        profile: existingProfile,
      })
    }

    const data = await request.validate(DeliveryProviderApplicationValidator)

    const deliveryProfile = await vendor.related('deliveryProfile').create({
      verificationStatus: 'pending',
      vehicleTypes: data.vehicleTypes,
      maxDeliveryDistance: data.maxDeliveryDistance,
      maxConcurrentOrders: data.maxConcurrentOrders,
      maxOrderWeight: data.maxOrderWeight,
      maxOrderVolume: data.maxOrderVolume,
      workingHours: data.workingHours,
      breakTimes: data.breakTimes,
      autoAcceptOrders: data.autoAcceptOrders || false,
      maxOrdersPerDay: data.maxOrdersPerDay,
      preparationBufferMinutes: data.preparationBufferMinutes || 15,
      requiredDocuments: data.requiredDocuments,
      insuranceDetails: data.insuranceDetails,
      vehicleDocumentation: data.vehicleDocumentation,
    })

    return response.created({
      message: 'Delivery provider application submitted successfully',
      profile: deliveryProfile,
    })
  }

  /**
   * @show
   * @summary Get delivery provider application status
   * @description Get the current status and details of vendor's delivery provider application
   * @responseBody 200 - {"profile": {"id": "string", "vendorId": "string", "verificationStatus": "pending", "vehicleTypes": ["car", "motorcycle"], "maxDeliveryDistance": 10, "maxConcurrentOrders": 3, "active": true}}
   * @responseBody 404 - {"message": "No delivery provider application found"}
   * @responseBody 401 - Unauthorized
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor) {
    const deliveryProfile = await vendor.related('deliveryProfile').query().first()

    if (!deliveryProfile) {
      return response.notFound({
        message: 'No delivery provider application found',
      })
    }

    await deliveryProfile.load('verifierAdmin')

    return response.ok({
      profile: deliveryProfile,
    })
  }

  /**
   * @update
   * @summary Update delivery provider application
   * @description Update delivery provider application details (only allowed for pending or rejected applications)
   * @requestBody {
   *   "vehicleTypes": ["car", "motorcycle"],
   *   "maxDeliveryDistance": 15,
   *   "maxConcurrentOrders": 5,
   *   "maxOrderWeight": 30,
   *   "maxOrderVolume": 75,
   *   "workingHours": {
   *     "monday": {"start": "08:00", "end": "20:00", "active": true},
   *     "tuesday": {"start": "08:00", "end": "20:00", "active": true}
   *   },
   *   "autoAcceptOrders": true,
   *   "maxOrdersPerDay": 50,
   *   "preparationBufferMinutes": 20
   * }
   *
   * @responseBody 200 - {"message": "Delivery provider application updated successfully", "profile": {"id": "string", "vendorId": "string", "verificationStatus": "pending", "maxDeliveryDistance": 15}}
   * @responseBody 400 - {"message": "Cannot update application in current status", "currentStatus": "verified"}
   * @responseBody 404 - {"message": "No delivery provider application found"}
   * @responseBody 401 - Unauthorized
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async update({ request, response }: HttpContextContract, vendor: Vendor) {
    const deliveryProfile = await vendor.related('deliveryProfile').query().first()

    if (!deliveryProfile) {
      return response.notFound({
        message: 'No delivery provider application found',
      })
    }

    // Only allow updates if application is pending or rejected
    if (!['pending', 'rejected'].includes(deliveryProfile.verificationStatus)) {
      return response.badRequest({
        message: 'Cannot update application in current status',
        currentStatus: deliveryProfile.verificationStatus,
      })
    }

    const data = await request.validate(DeliveryProviderApplicationValidator)

    deliveryProfile.merge({
      verificationStatus: 'pending', // Reset to pending if it was rejected
      vehicleTypes: data.vehicleTypes,
      maxDeliveryDistance: data.maxDeliveryDistance,
      maxConcurrentOrders: data.maxConcurrentOrders,
      maxOrderWeight: data.maxOrderWeight,
      maxOrderVolume: data.maxOrderVolume,
      workingHours: data.workingHours,
      breakTimes: data.breakTimes,
      autoAcceptOrders: data.autoAcceptOrders || false,
      maxOrdersPerDay: data.maxOrdersPerDay,
      preparationBufferMinutes: data.preparationBufferMinutes || 15,
      requiredDocuments: data.requiredDocuments,
      insuranceDetails: data.insuranceDetails,
      vehicleDocumentation: data.vehicleDocumentation,
    })

    await deliveryProfile.save()

    return response.ok({
      message: 'Delivery provider application updated successfully',
      profile: deliveryProfile,
    })
  }

  /**
   * @withdraw
   * @summary Withdraw delivery provider application
   * @description Withdraw and delete delivery provider application (only allowed for pending or under_review applications)
   * @responseBody 200 - {"message": "Delivery provider application withdrawn successfully"}
   * @responseBody 400 - {"message": "Cannot withdraw application in current status", "currentStatus": "verified"}
   * @responseBody 404 - {"message": "No delivery provider application found"}
   * @responseBody 401 - Unauthorized
   */
  @bind()
  public async withdraw({ response }: HttpContextContract, vendor: Vendor) {
    const deliveryProfile = await vendor.related('deliveryProfile').query().first()

    if (!deliveryProfile) {
      return response.notFound({
        message: 'No delivery provider application found',
      })
    }

    // Only allow withdrawal if application is pending or under review
    if (!['pending', 'under_review'].includes(deliveryProfile.verificationStatus)) {
      return response.badRequest({
        message: 'Cannot withdraw application in current status',
        currentStatus: deliveryProfile.verificationStatus,
      })
    }

    await deliveryProfile.delete()

    return response.ok({
      message: 'Delivery provider application withdrawn successfully',
    })
  }

  /**
   * @index
   * @summary Get a list of admin (index)
   * @description Get all delivery provider applications with filtering and pagination (Admin only)
   * @tag Admin
   * @paramQuery page - Page number for pagination - @example(1)
   * @paramQuery limit - Number of items per page - @example(20)
   * @paramQuery status - Filter by verification status - @example(pending)
   * @paramQuery search - Search by vendor name, email, or phone - @example(john)
   * @responseBody 200 - {"meta": {"total": 50, "perPage": 20, "currentPage": 1}, "data": [{"id": "string", "vendorId": "string", "verificationStatus": "pending", "maxDeliveryDistance": 10}]}
   * @responseBody 403 - {"message": "Only admins can view delivery provider applications"}
   * @responseBody 401 - Unauthorized
   */
  public async index({ request, response, auth }: HttpContextContract) {
    // Ensure user is admin
    if (!auth.user?.hasRole('admin')) {
      return response.forbidden({ message: 'Only admins can view delivery provider applications' })
    }

    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const status = request.input('status')
    const search = request.input('search')

    const query = DeliveryProviderProfile.query()
      .preload('vendor')
      .preload('verifierAdmin')
      .orderBy('application_date', 'desc')

    if (status) {
      query.where('verification_status', status)
    }

    if (search) {
      query.whereHas('vendor', (vendorQuery) => {
        vendorQuery
          .where('name', 'ILIKE', `%${search}%`)
          .orWhere('email', 'ILIKE', `%${search}%`)
          .orWhere('phone', 'ILIKE', `%${search}%`)
      })
    }

    const applications = await query.paginate(page, limit)

    return response.ok(applications)
  }

  /**
   * @review
   * @summary Review delivery provider application (Admin only)
   * @description Phase 2A: Admin reviews and approves/rejects delivery provider applications. Triggers DeliveryProviderApplicationApproved or DeliveryProviderApplicationRejected notifications based on status.
   * @tag Admin - Delivery Provider Management
   * @paramPath id - Delivery provider profile ID - @example(uuid-123)
   * @requestBody {"status": "verified", "notes": "Application meets all requirements"}
   *
   * @responseBody 200 - {"message": "Delivery provider application verified successfully", "profile": {"id": "string", "vendorId": "string", "verificationStatus": "verified", "maxDeliveryDistance": 10, "active": true}}
   * @responseBody 400 - {"message": "Invalid verification status", "allowedStatuses": ["under_review", "verified", "rejected", "suspended"]}
   * @responseBody 401 - Unauthorized
   * @responseBody 403 - {"message": "Only admins can review delivery provider applications"}
   * @responseBody 404 - Delivery provider application not found
   */
  public async review({ request, response, auth, params }: HttpContextContract) {
    // Ensure user is admin
    if (!auth.user?.hasRole('admin')) {
      return response.forbidden({ message: 'Only admins can review delivery provider applications' })
    }

    const deliveryProfile = await DeliveryProviderProfile.findOrFail(params.id)
    await deliveryProfile.load('vendor')

    const data = await request.validate(DeliveryProviderReviewValidator)
    const { status, notes } = data

    const trx = await Database.transaction()

    try {
      deliveryProfile.verificationStatus = status
      deliveryProfile.verificationNotes = notes || null
      deliveryProfile.verifierAdminId = auth.user!.id

      if (status === 'verified') {
        deliveryProfile.verifiedAt = new Date()
      } else if (status === 'suspended') {
        deliveryProfile.suspendedAt = new Date()
        deliveryProfile.suspensionReason = notes || 'Suspended by admin'
      }

      await deliveryProfile.useTransaction(trx).save()

      await trx.commit()

      // Load vendor user for notifications
      await deliveryProfile.load('vendor', (vendorQuery) => {
        vendorQuery.preload('user')
      })

      // Send application status notifications
      await this.sendApplicationStatusNotifications(deliveryProfile, status, auth.user!.name, notes)

      return response.ok({
        message: `Delivery provider application ${status} successfully`,
        profile: deliveryProfile,
      })
    } catch (error) {
      await trx.rollback()
      throw error
    }
  }

  /**
   * @statistics
   * @summary Get delivery provider application statistics (Admin only)
   * @description Get comprehensive statistics about delivery provider applications including status breakdown, trends, and performance metrics
   * @tag Admin - Delivery Provider Management
   * @responseBody 200 - {"totalApplications": 150, "statusBreakdown": {"pending": 25, "verified": 85}, "averageProcessingTime": 3.5, "activeProviders": 80}
   * @responseBody 403 - {"message": "Only admins can view delivery provider statistics"}
   * @responseBody 401 - Unauthorized
   */
  public async statistics({ response, auth }: HttpContextContract) {
    // Ensure user is admin
    if (!auth.user?.hasRole('admin')) {
      return response.forbidden({ message: 'Only admins can view delivery provider statistics' })
    }

    const stats = await Database.from('delivery_provider_profiles')
      .select('verification_status')
      .count('* as total')
      .groupBy('verification_status')

    const totalApplications = await DeliveryProviderProfile.query().count('* as total')
    const activeProviders = await DeliveryProviderProfile.query()
      .where('verification_status', 'verified')
      .where('active', true)
      .count('* as total')

    return response.ok({
      totalApplications: totalApplications[0].$extras.total,
      activeProviders: activeProviders[0].$extras.total,
      statusBreakdown: stats.reduce((acc, stat) => {
        acc[stat.verification_status] = parseInt(stat.total)
        return acc
      }, {}),
    })
  }

  /**
   * Send application status notifications to vendor
   */
  private async sendApplicationStatusNotifications(
    deliveryProfile: DeliveryProviderProfile,
    status: string,
    adminName: string,
    notes?: string
  ) {
    try {
      const vendor = deliveryProfile.vendor
      if (!vendor?.user) {
        console.warn(`No user found for vendor ${vendor?.id} in delivery profile ${deliveryProfile.id}`)
        return
      }

      if (status === 'verified') {
        // Send approval notification
        const approvalDetails = {
          approvalDate: DateTime.now(),
          approvedBy: adminName,
          verificationNotes: notes || null,
          nextSteps: [
            'Set up your service areas',
            'Configure delivery preferences',
            'Start accepting delivery assignments',
          ],
          serviceAreasEnabled: [], // Will be populated when service areas are set up
          capabilitiesApproved: {
            vehicleTypes: deliveryProfile.vehicleTypes || [],
            maxDeliveryDistance: deliveryProfile.maxDeliveryDistance || 0,
            maxConcurrentOrders: deliveryProfile.maxConcurrentOrders || 1,
          },
        }

        await vendor.user.notify(
          new DeliveryProviderApplicationApproved(deliveryProfile, approvalDetails)
        )

        console.log(`Application approved notification sent to vendor ${vendor.id}`)

      } else if (status === 'rejected') {
        // Send rejection notification
        const rejectionDetails = {
          rejectionDate: DateTime.now(),
          rejectedBy: adminName,
          rejectionReasons: notes ? [notes] : ['Application did not meet requirements'],
          reapplicationAllowed: true,
          reapplicationDate: DateTime.now().plus({ days: 30 }), // Allow reapplication after 30 days
          improvementSuggestions: [
            'Ensure all required documents are valid and up-to-date',
            'Verify vehicle registration and insurance coverage',
            'Complete any missing information in your application',
          ],
        }

        await vendor.user.notify(
          new DeliveryProviderApplicationRejected(deliveryProfile, rejectionDetails)
        )

        console.log(`Application rejected notification sent to vendor ${vendor.id}`)
      }

      // Note: 'under_review' and 'suspended' status notifications can be added here if needed
      // For now, focusing on the main approval/rejection workflow

    } catch (error) {
      console.error('Failed to send application status notifications:', error)
    }
  }
}
