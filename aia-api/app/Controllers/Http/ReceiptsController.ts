import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Receipt from 'App/Models/Receipt'
import ReceiptService from 'App/Services/ReceiptService'
import CreateReceiptValidator from 'App/Validators/CreateReceiptValidator'
import UpdateReceiptValidator from 'App/Validators/UpdateReceiptValidator'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

export default class ReceiptsController {
  private receiptService = new ReceiptService()

  /**
   * Get list of receipts for the authenticated user
   * GET /receipts
   */
  public async index({ auth, request, response }: HttpContextContract) {
    try {
      const user = auth.user!
      const page = request.input('page', 1)
      const perPage = request.input('perPage', 20)

      // Load user roles (required for admin checking)
      await user.load('roles')

      // Check if user is admin (following HierarchicalAccessControlService pattern)
      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []
      const isAdmin =
        roleNames.includes('super admin') ||
        roleNames.includes('platform admin') ||
        roleNames.includes('admin')

      const result = isAdmin
        ? await this.receiptService.listAllReceipts(page, perPage)
        : await this.receiptService.listUserReceipts(user.id, page, perPage)

      return response.ok({
        status: 'success',
        message: 'Receipts retrieved successfully',
        data: result.receipts,
        pagination: result.pagination,
      })
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to retrieve receipts',
        error: error.message,
      })
    }
  }

  /**
   * Create a new receipt
   * POST /receipts
   */
  public async store({ auth, request, response }: HttpContextContract) {
    try {
      const user = auth.user!
      const payload = await request.validate(CreateReceiptValidator)

      // Use the authenticated user's ID if not provided
      const userId = payload.userId || user.id

      // Check if user can create receipt for this payment
      if (userId !== user.id) {
        // Only allow if user is admin or staff (implement role check here)
        return response.forbidden({
          status: 'error',
          message: 'You can only create receipts for your own payments',
        })
      }

      const receipt = await this.receiptService.generateReceipt(payload.paymentId)

      return response.created({
        status: 'success',
        message: 'Receipt created successfully',
        data: receipt,
      })
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to create receipt',
        error: error.message,
      })
    }
  }

  /**
   * Get a specific receipt
   * GET /receipts/:id
   */
  public async show({ auth, params, response }: HttpContextContract) {
    try {
      const user = auth.user!

      // Load user roles (required for admin checking)
      await user.load('roles')

      // Check if user is admin (following HierarchicalAccessControlService pattern)
      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []
      const isAdmin =
        roleNames.includes('super admin') ||
        roleNames.includes('platform admin') ||
        roleNames.includes('admin')

      const receiptQuery = Receipt.query()
        .where('id', params.id)
        .preload('payment', (paymentQuery) => {
          paymentQuery.preload('vendor')
        })

      // Only filter by user_id if not admin
      if (!isAdmin) {
        receiptQuery.where('user_id', user.id)
      }

      const receipt = await receiptQuery.firstOrFail()

      return response.ok({
        status: 'success',
        message: 'Receipt retrieved successfully',
        data: receipt,
      })
    } catch (error) {
      return response.notFound({
        status: 'error',
        message: 'Receipt not found',
      })
    }
  }

  /**
   * Update a receipt
   * PUT /receipts/:id
   */
  public async update({ auth, params, request, response }: HttpContextContract) {
    try {
      const user = auth.user!
      const payload = await request.validate(UpdateReceiptValidator)

      // Load user roles (required for admin checking)
      await user.load('roles')

      // Check if user is admin (following HierarchicalAccessControlService pattern)
      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []
      const isAdmin =
        roleNames.includes('super admin') ||
        roleNames.includes('platform admin') ||
        roleNames.includes('admin')

      const receiptQuery = Receipt.query().where('id', params.id)

      // Only filter by user_id if not admin
      if (!isAdmin) {
        receiptQuery.where('user_id', user.id)
      }

      const receipt = await receiptQuery.firstOrFail()

      receipt.merge(payload)
      await receipt.save()

      return response.ok({
        status: 'success',
        message: 'Receipt updated successfully',
        data: receipt,
      })
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to update receipt',
        error: error.message,
      })
    }
  }

  /**
   * Delete a receipt
   * DELETE /receipts/:id
   */
  public async destroy({ auth, params, response }: HttpContextContract) {
    try {
      const user = auth.user!

      // Load user roles (required for admin checking)
      await user.load('roles')

      // Check if user is admin (following HierarchicalAccessControlService pattern)
      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []
      const isAdmin =
        roleNames.includes('super admin') ||
        roleNames.includes('platform admin') ||
        roleNames.includes('admin')

      const receiptQuery = Receipt.query().where('id', params.id)

      // Only filter by user_id if not admin
      if (!isAdmin) {
        receiptQuery.where('user_id', user.id)
      }

      const receipt = await receiptQuery.firstOrFail()

      await receipt.delete()

      return response.ok({
        status: 'success',
        message: 'Receipt deleted successfully',
      })
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to delete receipt',
        error: error.message,
      })
    }
  }

  /**
   * Download receipt PDF
   * GET /receipts/:id/download
   */
  public async download({ auth, params, response }: HttpContextContract) {
    try {
      const user = auth.user!

      const downloadUrl = await this.receiptService.getReceiptDownloadUrl(params.id, user.id)

      return response.redirect(downloadUrl)
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to download receipt',
        error: error.message,
      })
    }
  }

  /**
   * Send receipt via email
   * POST /receipts/:id/email
   */
  public async sendEmail({ auth, params, response }: HttpContextContract) {
    try {
      const user = auth.user!

      // Load user roles (required for admin checking)
      await user.load('roles')

      // Check if user is admin (following HierarchicalAccessControlService pattern)
      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []
      const isAdmin =
        roleNames.includes('super admin') ||
        roleNames.includes('platform admin') ||
        roleNames.includes('admin')

      const receiptQuery = Receipt.query().where('id', params.id)

      // Only filter by user_id if not admin
      if (!isAdmin) {
        receiptQuery.where('user_id', user.id)
      }

      // Verify receipt exists and user has access
      await receiptQuery.firstOrFail()

      await this.receiptService.sendReceiptByEmail(params.id)

      return response.ok({
        status: 'success',
        message: 'Receipt sent via email successfully',
      })
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to send receipt via email',
        error: error.message,
      })
    }
  }

  /**
   * Regenerate receipt
   * POST /receipts/:id/regenerate
   */
  public async regenerate({ auth, params, response }: HttpContextContract) {
    try {
      const user = auth.user!

      // Load user roles (required for admin checking)
      await user.load('roles')

      // Check if user is admin (following HierarchicalAccessControlService pattern)
      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []
      const isAdmin =
        roleNames.includes('super admin') ||
        roleNames.includes('platform admin') ||
        roleNames.includes('admin')

      const receiptQuery = Receipt.query().where('id', params.id)

      // Only filter by user_id if not admin
      if (!isAdmin) {
        receiptQuery.where('user_id', user.id)
      }

      // Verify receipt exists and user has access
      await receiptQuery.firstOrFail()

      const receipt = await this.receiptService.regenerateReceipt(params.id)

      return response.ok({
        status: 'success',
        message: 'Receipt regenerated successfully',
        data: receipt,
      })
    } catch (error) {
      return response.badRequest({
        status: 'error',
        message: 'Failed to regenerate receipt',
        error: error.message,
      })
    }
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's receipts
   * Used by customer apps to ensure customers only see their own receipts
   */
  public async customerReceipts({ request, auth }: HttpContextContract) {
    const { per = 15, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const user = auth.user!

    // Customer endpoint ALWAYS shows only user's own receipts
    const receiptQuery = Receipt.query()
      .where('user_id', user.id)
      .preload('payment', (paymentQuery) => {
        paymentQuery.preload('vendor')
      })

    // Apply additional filters if any
    if (filters.status) {
      receiptQuery.where('status', filters.status)
    }
    if (filters.deliveryMethod) {
      receiptQuery.where('delivery_method', filters.deliveryMethod)
    }

    const receipts = await receiptQuery.orderBy(order, sort).paginate(page, per)
    return receipts
  }
}
