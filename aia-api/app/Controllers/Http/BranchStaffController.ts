import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Branch from '../../Models/Branch'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class BranchStaffController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request, response }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const branchQuery = branch
      .related('staff')
      .query()
      .pivotColumns(['identifier', 'online'])
      .preload('lots')
      .filter(filters)

    const staff = await branchQuery.orderBy(order, sort).paginate(page, per)

    return response.json(staff)
  }

  /**
   * @store
   * @summary Create a branch staff
   * @description Create a branch staff with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, branch: Branch) {
    const {
      vendorId,
      userId: existingUserId,
      identifier,
      firstName,
      lastName,
      phone,
      email,
      gender,
      idpass,
      dob,
      details,
      password = null,
      role = 'customer',
    } = request.all()

    let userId = existingUserId

    if (!existingUserId) {
      const user = await User.create({
        firstName,
        lastName,
        phone,
        email,
        gender,
        dob,
        idpass,
        details,
        password: password || phone,
      })

      await user.assignRole(role)

      userId = user.id
    }

    branch.related('staff').attach({ [userId]: { identifier, vendor_id: vendorId } })

    const image = request.file('image')
    if (image) {
      branch.merge({ image: Attachment.fromFile(image) })
    }

    await branch.save()

    return response.json(branch)
  }

  /**
   * @show
   *
   * @summary Fetch a branch staff
   * @description Create a branch staff with their details (name and details)
   * @paramPath id required number - Staff Id
   * @responseBody 200 - <User>
   * @responseBody 404 - Not found
   */
  @bind()
  public async show({ response }: HttpContextContract, branch: Branch, staff: User) {
    const user = await branch
      .related('staff')
      .query()
      .pivotColumns(['identifier', 'online'])
      .where('user_id', staff.id)
      .where('vendor_id', branch.vendorId)
      .firstOrFail()

    return response.json(user)
  }

  /**
   * @update
   *
   * @summary Update a branch staff
   * @description Create a branch staff with their details (name and details)
   * @requestBody {"identifier": "", "vendorId": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async update({ request, response }: HttpContextContract, branch: Branch, staff: User) {
    const { identifier, userId, vendorId, online = false } = request.all()
    const user = await branch
      .related('staff')
      .query()
      .wherePivot('user_id', staff.id)
      .wherePivot('vendor_id', vendorId)
      .wherePivot('identifier', identifier)
      .first()

    if (user) {
      await branch.related('staff').detach([user.id])
    }

    branch.related('staff').attach({ [userId]: { identifier, online, vendor_id: vendorId } })

    await branch.save()

    return response.json(user)
  }

  /**
   * @replaceRole
   * @summary Replace a staff member's roles in a branch
   * @description Replace a staff member's roles in a branch with new roles
   * @paramPath branch required string - Branch ID
   * @paramPath staff required string - Staff ID
   * @requestBody {"roles": ["string"]}
   * @responseBody 200 - Success response
   */
  @bind()
  public async replaceRole({ request, response, auth }: HttpContextContract, branch: Branch, staff: User) {
    // Authorization: Only admins, vendor admins, and branch managers can update staff roles
    const currentUser = auth.user!
    await currentUser.load('roles')
    await currentUser.load('branches')
    await currentUser.load('vendors')
    
    const roleNames = currentUser.roles.map((role) => role.name.toLowerCase())
    const isAdmin = roleNames.includes('admin') || roleNames.includes('platform admin') || roleNames.includes('super admin')
    const isVendorAdmin = roleNames.includes('vendor admin')
    const isVendor = roleNames.includes('vendor')
    const isBranchManager = roleNames.includes('branch manager')
    
    if (!isAdmin && !isVendorAdmin && !isVendor && !isBranchManager) {
      return response.forbidden({
        message: 'You are not authorized to update staff roles'
      })
    }
    
    // Check if user has access to this branch
    if (isVendorAdmin || isVendor) {
      const vendorIds = currentUser.vendors?.map((vendor) => vendor.id) || []
      if (!vendorIds.includes(branch.vendorId)) {
        return response.forbidden({
          message: 'You can only update staff in your vendor branches'
        })
      }
    } else if (isBranchManager) {
      const branchIds = currentUser.branches?.map((branch) => branch.id) || []
      if (!branchIds.includes(branch.id)) {
        return response.forbidden({
          message: 'You can only update staff in your branches'
        })
      }
    }
    
    const { roles } = request.all()

    if (!Array.isArray(roles)) {
      return response.badRequest({ message: 'Roles must be provided as an array' })
    }

    // Verify staff exists in branch
    const existingStaff = await branch
      .related('staff')
      .query()
      .where('user_id', staff.id)
      .firstOrFail()

    // Update the user's roles - assign each role individually
    // Note: AdonisJS role system will handle duplicates automatically
    for (const role of roles) {
      await staff.assignRole(role)
    }

    return response.json({
      message: 'Staff roles updated successfully',
      staff: existingStaff
    })
  }
}
