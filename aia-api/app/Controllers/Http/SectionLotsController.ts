import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Section from '../../Models/Section'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Section management
 * @version 1.0.0
 * @description Section management for the application
 */
export default class SectionLotsController {
  /**
   * @index
   * @summary List all sections
   * @description List all sections, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, section: Section) {
    const { per = 50, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const sectionQuery = section.related('lots').query().filter(filters)

    return await sectionQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a section
   * @description Create a section with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Section>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, section: Section) {
    const { name, details } = request.all()
    const lot = await section.related('lots').create({ name, details })

    const image = request.file('image')
    if (image) {
      await lot.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(section)
  }
}
