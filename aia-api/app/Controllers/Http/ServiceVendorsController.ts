import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

export default class ServiceVendorsController {
  @bind()

  /**
   * @index
   * @summary Show all ServiceVendors
   * @version 1.0.0
   * @description  ServiceVendors management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery vendorCategoryId - Filter vendors by category ID
   */
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = service.related('vendors').query().filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a ServiceVendors
   * @description Create a ServiceVendors with their details (name and details)
   * @requestBody {"firstname": "", "lastName": "", "email": "", "phone": "", "vendorCategoryId": "", "vendor":"" }
   */
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { firstName, lastName, email, phone, vendorCategoryId, vendor } = request.all()

    const user = await User.create({
      firstName,
      lastName,
      email,
      phone,
      password: phone,
    })

    const vendorQ = await user.related('vendors').create({
      name: vendor.name,
      email: vendor.email,
      phone: vendor.phone,
      reg: vendor.reg || DateTime.now().toISODate(),
      kra: vendor.kra || DateTime.now().toISODate(),
      serviceId: service.id,
    })

    const logo = request.file('vendor.logo')

    if (logo) {
      await vendorQ.merge({ logo: Attachment.fromFile(logo) }).save()
    }

    await vendorQ.related('categories').attach([vendorCategoryId])

    return response.json(vendorQ)
  }
}
