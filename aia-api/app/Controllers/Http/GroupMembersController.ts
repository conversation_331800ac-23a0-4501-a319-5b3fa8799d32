import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Group from '../../Models/Group'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Group management
 * @version 1.0.0
 * @description Group management for the application
 */
export default class GroupMembersController {
  /**
   * @index
   * @summary List all groupes
   * @description List all groupes, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, group: Group) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const groupQuery = group.related('members').query().filter(filters)

    return await groupQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a group
   * @description Create a group with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Group>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, group: Group) {
    const { vendorId, userId } = request.all()
    group.related('members').attach({ [userId]: { vendor_id: vendorId } })

    await group.save()

    return response.json(group)
  }
}
