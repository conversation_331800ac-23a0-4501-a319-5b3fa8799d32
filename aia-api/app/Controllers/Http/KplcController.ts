import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Kplc from 'App/Services/Kplc'

export default class KplcController {
  constructor(public kplc = new Kplc()) {}

  /**
   * @authorize
   *
   * @summary Authorize
   * @description Authorize the user to get token
   */
  public async authorize({ request, response }: HttpContextContract) {
    const a = await this.kplc.authorize()

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @verifyStaffIdentity
   *
   * @summary Verify Staff Identity
   * @description Verify the identity of the staff
   *
   * @paramQuery nationalId - The national ID of the staff
   * @paramQuery staffNumber - The staff number of the staff
   */
  public async verifyStaffIdentity({ request, response }: HttpContextContract) {
    const a = await this.kplc.verifyStaffIdentity(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @verifyContractorIdentity
   *
   * @summary Verify Contractor Identity
   * @description Verify the identity of the contractor
   *
   * @paramQuery staffNumber - The staff number of the contractor
   */
  public async verifyContractorIdentity({ request, response }: HttpContextContract) {
    const a = await this.kplc.verifyContractorIdentity(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @getIdAccount
   *
   * @summary Get ID Account
   * @description Get the ID Account
   *
   * @paramQuery accountReference - The account reference of the account
   */
  public async getIdAccount({ request, response }: HttpContextContract) {
    const a = await this.kplc.getIdAccount(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @getStatementPdf
   *
   * @summary Get Statement PDF
   * @description Get the statement PDF
   *
   * @paramQuery idPaymentForm - The ID Payment Form of the statement
   * @paramQuery fromDate - The from date of the statement
   * @paramQuery toDate - The to date of the statement
   */
  public async getStatementPdf({ request, response }: HttpContextContract) {
    const a = await this.kplc.getStatementPdf(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @getAccountBalance
   *
   * @summary Get Account Balance
   * @description Get the account balance
   *
   * @paramQuery accountNumber - The account number of the account
   */
  public async getAccountBalance({ request, response }: HttpContextContract) {
    const a = await this.kplc.getAccountBalance(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @getListOfBills
   *
   * @summary Get List Of Bills
   * @description Get the list of bills
   *
   * @paramQuery idAccount - The ID Account of the account
   * @paramQuery fromDateDue - The from date due of the bill
   * @paramQuery fromDateEmi - The from date emi of the bill
   * @paramQuery lastPeriod - The last period of the bill
   * @paramQuery toDateDue - The to date due of the bill
   * @paramQuery toDateEmi - The to date emi of the bill
   */
  public async getListOfBills({ request, response }: HttpContextContract) {
    const a = await this.kplc.getListOfBills(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @getSpecificBillText
   *
   * @summary Get Specific Bill Text
   * @description Get the specific bill text
   *
   * @paramQuery idDocument - The ID Document of the bill
   */
  public async getSpecificBillText({ request, response }: HttpContextContract) {
    const a = await this.kplc.getSpecificBillText(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @getSpecificBillPdf
   *
   * @summary Get Specific Bill PDF
   * @description Get the specific bill pdf
   *
   * @paramQuery idDocument - The ID Document of the bill
   */
  public async getSpecificBillPdf({ request, response }: HttpContextContract) {
    const a = await this.kplc.getSpecificBillPdf(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @authenticateAccountNumber
   *
   * @summary Authenticate Account Number
   * @description Authenticate the account number
   *
   * @paramQuery accountNumber - The account number of the account
   *
   */
  public async authenticateAccountNumber({ request, response }: HttpContextContract) {
    const a = await this.kplc.accountNumberAuthentication(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @authenticateMeterNumber
   *
   * @summary Authenticate Meter Number
   * @description Authenticate the meter number
   *
   * @paramQuery serialNumberMeter - The serial number of the meter
   *
   */
  public async authenticateMeterNumber({ request, response }: HttpContextContract) {
    const a = await this.kplc.meterNumberAuthentication(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @queryBill
   *
   * @summary Query a bill
   * @description Query a bill
   *
   * @paramQuery accountReference - The account reference
   * @paramQuery fromDateDue -
   * @paramQuery fromDateEmi -
   * @paramQuery lastPeriod -
   * @paramQuery toDateDue -
   * @paramQuery toDateEmi -
   *
   */

  public async queryBill({ request, response }: HttpContextContract) {
    const a = await this.kplc.billQuery(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @checkSelfReadingEligibility
   *
   * @summary Check Self Reading Eligibility
   * @description Check if the self reading is eligible
   *
   * @paramQuery idAccount - The ID Account of the account
   *
   */
  public async checkSelfReadingEligibility({ request, response }: HttpContextContract) {
    const a = await this.kplc.checkSelfReadingEligibility(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @registerSelfReading
   *
   * @summary Register Self Reading
   * @description Register for self reading
   *
   * @paramQuery accountReference - account reference
   * @paramQuery idSectorSupply - The ID Sector Supply of the sector supply
   * @paramQuery phoneNumber - The phone number of the user
   *
   */

  public async registerSelfReading({ request, response }: HttpContextContract) {
    const a = await this.kplc.registerSelfReading(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @submitSelfReading
   *
   * @summary Submit Self Reading
   * @description Submit for self reading
   *
   * @paramQuery accountReference - account reference
   * @paramQuery readingValue - The reading value of the meter
   *
   */

  public async submitSelfReading({ request, response }: HttpContextContract) {
    const a = await this.kplc.submitSelfReading(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }

  /**
   * @deregisterSelfReading
   *
   * @summary Deregister Self Reading
   * @description Deregister for self reading
   *
   * @paramQuery accountReference - account reference
   *
   */
  public async deregisterSelfReading({ request, response }: HttpContextContract) {
    const a = await this.kplc.deregisterSelfReading(request.all())

    return response.status(200).json({ ...a, ip: request.ip() })
  }
}
