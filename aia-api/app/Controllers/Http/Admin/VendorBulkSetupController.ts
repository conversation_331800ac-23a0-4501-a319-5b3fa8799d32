import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Drive from '@ioc:Adonis/Core/Drive'
import { Queue } from '@ioc:Rlanz/Queue'
import { cuid } from '@ioc:Adonis/Core/Helpers'


export default class VendorBulkSetupController {
  /**
   * @store
   * @summary Accept CSV file for bulk vendor setup
   * @description Accepts a CSV file upload, validates basic properties, stores it on S3, and queues a background job for processing.
   * @requestBody {
   *   "content": {
   *     "multipart/form-data": {
   *       "schema": {
   *         "type": "object",
   *         "properties": {
   *           "vendor_setup_csv": {
   *             "type": "string",
   *             "format": "binary",
   *             "description": "CSV file containing vendor data"
   *           }
   *         },
   *         "required": ["vendor_setup_csv"]
   *       }
   *     }
   *   }
   * }
   * @responseBody 202 - {"message": "CSV upload accepted...", "jobId": "string"}
   * @responseBody 422 - Validation errors (file type, size)
   * @responseBody 401 - Unauthorized
   * @responseBody 403 - Forbidden (User is not Admin)
   * @responseBody 500 - File storage or queue dispatch error
   * @tag Admin Vendor Management
   * @security BearerAuth
   */
  public async store({ request, response, auth }: HttpContextContract) {
    // 1. Validate the uploaded file
    const csvSchema = schema.create({
      vendor_setup_csv: schema.file({
        size: '10mb', // Adjust size limit as needed
        extnames: ['csv'],
      }),
    })

    let payload
    try {
      payload = await request.validate({
        schema: csvSchema,
        messages: {
          'vendor_setup_csv.required': 'CSV file is required.',
          'vendor_setup_csv.size': 'CSV file size must be under 10MB.',
          'vendor_setup_csv.extnames': 'Invalid file type. Only CSV files are allowed.',
        },
      })
    } catch (validationError) {
      return response.status(422).send(validationError.messages)
    }

    // 2. Move the file to S3
    const fileName = `${cuid()}.csv`
    const targetDisk = 's3' // Defined in config/drive.ts
    const s3Folder = 'bulk-vendor-uploads/' // Or your preferred S3 path
    const s3ObjectKey = s3Folder + fileName

    try {
      // Check if the uploaded file object state is valid
      if (!payload.vendor_setup_csv.isValid) {
        console.error(
          '[Controller] Uploaded file object state is invalid:',
          payload.vendor_setup_csv.errors
        )
        return response.badRequest({
          message: 'Uploaded file is invalid.',
          errors: payload.vendor_setup_csv.errors,
        })
      }

      console.log(
        `[Controller] Moving uploaded file to S3 disk: ${targetDisk}, Key: ${s3ObjectKey}`
      )
      await payload.vendor_setup_csv.moveToDisk(s3Folder, { name: fileName }, targetDisk)
      console.log(
        `[Controller] moveToDisk call completed for S3 key: ${s3ObjectKey}. File state: ${payload.vendor_setup_csv.state}`
      )
    } catch (moveError) {
      console.error(`[Controller] Error moving file to S3 (${s3ObjectKey}):`, moveError)
      // Ensure cleanup happens even if move fails before job dispatch
      try {
        await Drive.use(targetDisk).delete(s3ObjectKey)
        console.log(`[Controller] Cleaned up S3 object ${s3ObjectKey} after move error.`)
      } catch (cleanupError) {
        console.error(
          `[Controller] Failed to cleanup S3 object ${s3ObjectKey} after move error:`,
          cleanupError
        )
        // Log this critical failure, as manual cleanup might be required
      }
      return response.internalServerError({
        message: 'Failed to store uploaded file.',
        error: moveError.message,
      })
    }

    // 3. Dispatch Background Job
    const jobPayload = {
      s3ObjectKey: s3ObjectKey,
      diskName: targetDisk,
      userId: auth.user!.id, // Pass the initiating admin's ID
    }

    let job
    try {
      job = await Queue.dispatch('App/Jobs/ProcessVendorCsvSetup', jobPayload)
      console.log(
        `[Controller] Dispatched job ProcessVendorCsvSetup ID: ${job.id} for S3 key: ${s3ObjectKey}`
      )
    } catch (dispatchError) {
      console.error(`[Controller] Error dispatching queue job:`, dispatchError)
      // Cleanup the uploaded file if job dispatch fails
      try {
        await Drive.use(targetDisk).delete(s3ObjectKey)
        console.log(`[Controller] Cleaned up S3 object ${s3ObjectKey} after job dispatch failure.`)
      } catch (cleanupError) {
        console.error(
          `[Controller] Failed to cleanup S3 object ${s3ObjectKey} after job dispatch error:`,
          cleanupError
        )
      }
      return response.internalServerError({ message: 'Failed to queue the upload job.' })
    }

    // 4. Respond Immediately
    return response.accepted({
      message: 'CSV upload accepted and is being processed in the background.',
      jobId: job.id, // Return the BullMQ job ID
    })
  }

  /**
   * @getJobStatus
   * @summary Check the status of a bulk vendor setup job
   * @description Retrieves the current state and results (if completed or failed) of a background job.
   * @paramPath jobId - The ID of the job returned by the initial upload request.
   * @responseBody 200 - Job status object (structure defined in requirements)
   * @responseBody 404 - Job not found
   * @responseBody 401 - Unauthorized
   * @responseBody 403 - Forbidden (User is not Admin or did not initiate this job)
   * @tag Admin Vendor Management
   * @security BearerAuth
   */
  public async getJobStatus({ params, response, auth }: HttpContextContract) {
    const jobId = params.jobId

    if (!jobId) {
      return response.badRequest({ message: 'Job ID is required.' })
    }

    try {
      // Get the job from the queue
      const bullQueue = await Queue.get()
      const job = await bullQueue?.getJob(jobId)

      if (!job) {
        return response.notFound({ message: `Job with ID ${jobId} not found.` })
      }

      // Security Check: Ensure the user requesting the status is the one who initiated the job
      if (!auth.user || job.data?.userId !== auth.user.id) {
        console.warn(
          `[Status Check Denied] User ${auth.user!.id} attempted to access job ${jobId} owned by ${job.data?.userId}`
        )
        return response.forbidden({
          message: 'You do not have permission to view the status of this job.',
        })
      }

      const state = await job.getState()
      const progress = job.progress
      const returnValue = job.returnvalue

      const jobResult = {
        jobId: job.id,
        state: state,
        progress: progress,
        result: state === 'completed' ? returnValue : null,
        failedReason: state === 'failed' ? job.failedReason : null,
      }

      return response.ok(jobResult)
    } catch (error) {
      console.error(`Error fetching job status for ID ${jobId}:`, error)
      return response.internalServerError({
        message: `An error occurred while fetching status for job ${jobId}.`,
      })
    }
  }
}
