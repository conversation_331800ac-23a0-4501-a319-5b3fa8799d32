import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import DeliveryPricingConfig from 'App/Models/DeliveryPricingConfig'
import DeliveryPricingTier from 'App/Models/DeliveryPricingTier'

/**
 * @summary Admin Delivery Pricing Management
 * @group Admin Delivery Pricing
 * @version 1.0.0
 * @description Centralized delivery pricing management for super admin
 */
export default class DeliveryPricingController {
  /**
   * @index
   * @summary List all delivery pricing configurations
   * @description Get all delivery pricing configurations with their tiers
   * @tag Admin Delivery Pricing
   * @responseBody 200 - [{"id": "string", "name": "Standard Delivery", "vehicleType": "motorcycle", "isActive": true, "tiers": []}]
   */
  public async index({ response }: HttpContextContract) {
    const configs = await DeliveryPricingConfig.query()
      .preload('tiers', (tiersQuery) => {
        tiersQuery.orderBy('priority', 'asc')
      })
      .orderBy('priority', 'asc')

    return response.json(configs)
  }

  /**
   * @show
   * @summary Get specific delivery pricing configuration
   * @description Get detailed information about a delivery pricing configuration
   * @tag Admin Delivery Pricing
   * @paramPath id - Configuration ID
   * @responseBody 200 - {"id": "string", "name": "Standard Delivery", "basePrice": 200, "pricePerKm": 25, "tiers": []}
   */
  public async show({ params, response }: HttpContextContract) {
    const config = await DeliveryPricingConfig.query()
      .where('id', params.id)
      .preload('tiers', (tiersQuery) => {
        tiersQuery.orderBy('priority', 'asc')
      })
      .firstOrFail()

    return response.json(config)
  }

  /**
   * @store
   * @summary Create new delivery pricing configuration
   * @description Create a new centralized delivery pricing configuration
   * @tag Admin Delivery Pricing
   * @requestBody {
   *   "name": "Express Delivery",
   *   "description": "Fast car delivery service",
   *   "vehicleType": "car",
   *   "basePrice": 300,
   *   "pricePerKm": 35,
   *   "baseTimeMinutes": 20,
   *   "timePerKmMinutes": 2
   * }
   * @responseBody 201 - {"id": "string", "name": "Express Delivery", "vehicleType": "car"}
   */
  public async store({ request, response, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      name: schema.string({}, [rules.required(), rules.maxLength(100)]),
      description: schema.string.optional({}, [rules.maxLength(500)]),
      vehicleType: schema.enum(['motorcycle', 'car', 'bicycle', 'van']),
      isActive: schema.boolean.optional(),
      isDefault: schema.boolean.optional(),
      priority: schema.number.optional([rules.unsigned()]),

      // Pricing
      basePrice: schema.number([rules.unsigned()]),
      pricePerKm: schema.number([rules.unsigned()]),
      minimumFee: schema.number.optional([rules.unsigned()]),
      maximumFee: schema.number.optional([rules.unsigned()]),

      // Distance constraints
      maxDistanceKm: schema.number.optional([rules.unsigned()]),
      minDistanceKm: schema.number.optional([rules.unsigned()]),

      // Order constraints
      minimumOrderValue: schema.number.optional([rules.unsigned()]),
      freeDeliveryThreshold: schema.number.optional([rules.unsigned()]),

      // Time estimates
      baseTimeMinutes: schema.number([rules.unsigned()]),
      timePerKmMinutes: schema.number([rules.unsigned()]),
      preparationBufferMinutes: schema.number.optional([rules.unsigned()]),

      // Availability
      workingHours: schema.object.optional().anyMembers(),
      blackoutDates: schema.array.optional().members(schema.string()),
      availableOnHolidays: schema.boolean.optional(),

      adminNotes: schema.string.optional({}, [rules.maxLength(1000)]),
    })

    const payload = await request.validate({ schema: validationSchema })

    // If this is set as default, unset other defaults
    if (payload.isDefault) {
      await DeliveryPricingConfig.query().where('is_default', true).update({ isDefault: false })
    }

    const config = await DeliveryPricingConfig.create({
      ...payload,
      createdByAdminId: auth.user?.id,
    })

    return response.status(201).json(config)
  }

  /**
   * @update
   * @summary Update delivery pricing configuration
   * @description Update an existing delivery pricing configuration
   * @tag Admin Delivery Pricing
   * @paramPath id - Configuration ID
   * @requestBody {"name": "Updated Standard Delivery", "basePrice": 250}
   * @responseBody 200 - {"id": "string", "name": "Updated Standard Delivery", "basePrice": 250}
   */
  public async update({ params, request, response, auth }: HttpContextContract) {
    const config = await DeliveryPricingConfig.findOrFail(params.id)

    const validationSchema = schema.create({
      name: schema.string.optional({}, [rules.maxLength(100)]),
      description: schema.string.optional({}, [rules.maxLength(500)]),
      vehicleType: schema.enum.optional(['motorcycle', 'car', 'bicycle', 'van']),
      isActive: schema.boolean.optional(),
      isDefault: schema.boolean.optional(),
      priority: schema.number.optional([rules.unsigned()]),

      basePrice: schema.number.optional([rules.unsigned()]),
      pricePerKm: schema.number.optional([rules.unsigned()]),
      minimumFee: schema.number.optional([rules.unsigned()]),
      maximumFee: schema.number.optional([rules.unsigned()]),

      maxDistanceKm: schema.number.optional([rules.unsigned()]),
      minDistanceKm: schema.number.optional([rules.unsigned()]),

      minimumOrderValue: schema.number.optional([rules.unsigned()]),
      freeDeliveryThreshold: schema.number.optional([rules.unsigned()]),

      baseTimeMinutes: schema.number.optional([rules.unsigned()]),
      timePerKmMinutes: schema.number.optional([rules.unsigned()]),
      preparationBufferMinutes: schema.number.optional([rules.unsigned()]),

      workingHours: schema.object.optional().anyMembers(),
      blackoutDates: schema.array.optional().members(schema.string()),
      availableOnHolidays: schema.boolean.optional(),

      adminNotes: schema.string.optional({}, [rules.maxLength(1000)]),
    })

    const payload = await request.validate({ schema: validationSchema })

    // If this is set as default, unset other defaults
    if (payload.isDefault) {
      await DeliveryPricingConfig.query()
        .where('is_default', true)
        .whereNot('id', config.id)
        .update({ isDefault: false })
    }

    config.merge({
      ...payload,
      updatedByAdminId: auth.user?.id,
    })

    await config.save()

    return response.json(config)
  }

  /**
   * @destroy
   * @summary Delete delivery pricing configuration
   * @description Delete a delivery pricing configuration and its tiers
   * @tag Admin Delivery Pricing
   * @paramPath id - Configuration ID
   * @responseBody 200 - {"message": "Configuration deleted successfully"}
   */
  public async destroy({ params, response }: HttpContextContract) {
    const config = await DeliveryPricingConfig.findOrFail(params.id)

    // Delete associated tiers first
    await DeliveryPricingTier.query().where('delivery_pricing_config_id', config.id).delete()

    await config.delete()

    return response.json({ message: 'Configuration deleted successfully' })
  }

  /**
   * @testPricing
   * @summary Test delivery pricing calculation
   * @description Test pricing calculation for given distance and order value
   * @tag Admin Delivery Pricing
   * @requestBody {"distance": 5.5, "orderValue": 1500}
   * @responseBody 200 - {"deliveryOptions": [], "distance": 5.5, "errors": []}
   */
  public async testPricing({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      distance: schema.number([rules.unsigned()]),
      orderValue: schema.number.optional([rules.unsigned()]),
    })

    const { distance, orderValue = 0 } = await request.validate({ schema: validationSchema })

    // Mock coordinates for testing
    const vendorLocation = { lat: -1.2921, lng: 36.8219 } // Nairobi CBD
    const customerLocation = { lat: -1.263, lng: 36.8063 } // Westlands

    const { CentralizedDeliveryPricingCalculator } = await import(
      'App/Utils/CentralizedDeliveryPricingCalculator'
    )

    const result = await CentralizedDeliveryPricingCalculator.calculateCentralizedPricing(
      vendorLocation,
      customerLocation,
      orderValue
    )

    // Override distance with provided value for testing
    result.distance = distance
    result.deliveryOptions = result.deliveryOptions.map((option) => ({
      ...option,
      distance,
      fee: Math.round(
        option.priceBreakdown?.basePrice ||
          0 + (distance * (option.priceBreakdown?.distancePrice || 0)) / result.distance
      ),
    }))

    return response.json(result)
  }

  /**
   * @activate
   * @summary Activate delivery pricing configuration
   * @description Activate a delivery pricing configuration
   * @tag Admin Delivery Pricing
   * @paramPath id - Configuration ID
   * @responseBody 200 - {"id": "string", "isActive": true, "message": "Configuration activated"}
   */
  public async activate({ params, response, auth }: HttpContextContract) {
    const config = await DeliveryPricingConfig.findOrFail(params.id)

    config.isActive = true
    config.updatedByAdminId = auth.user?.id
    await config.save()

    return response.json({
      id: config.id,
      isActive: config.isActive,
      message: 'Configuration activated successfully',
    })
  }

  /**
   * @deactivate
   * @summary Deactivate delivery pricing configuration
   * @description Deactivate a delivery pricing configuration
   * @tag Admin Delivery Pricing
   * @paramPath id - Configuration ID
   * @responseBody 200 - {"id": "string", "isActive": false, "message": "Configuration deactivated"}
   */
  public async deactivate({ params, response, auth }: HttpContextContract) {
    const config = await DeliveryPricingConfig.findOrFail(params.id)

    config.isActive = false
    config.updatedByAdminId = auth.user?.id
    await config.save()

    return response.json({
      id: config.id,
      isActive: config.isActive,
      message: 'Configuration deactivated successfully',
    })
  }

  /**
   * @bulkUpdate
   * @summary Bulk update delivery pricing configurations
   * @description Update multiple configurations at once
   * @tag Admin Delivery Pricing
   * @requestBody {
   *   "updates": [
   *     {"id": "config1", "isActive": true, "priority": 1},
   *     {"id": "config2", "isActive": false, "priority": 2}
   *   ]
   * }
   * @responseBody 200 - {"updated": 2, "message": "Configurations updated successfully"}
   */
  public async bulkUpdate({ request, response, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      updates: schema.array().members(
        schema.object().members({
          id: schema.string({}, [
            rules.exists({ table: 'delivery_pricing_configs', column: 'id' }),
          ]),
          isActive: schema.boolean.optional(),
          priority: schema.number.optional([rules.unsigned()]),
          basePrice: schema.number.optional([rules.unsigned()]),
          pricePerKm: schema.number.optional([rules.unsigned()]),
        })
      ),
    })

    const { updates } = await request.validate({ schema: validationSchema })

    let updatedCount = 0

    for (const update of updates) {
      const config = await DeliveryPricingConfig.find(update.id)
      if (config) {
        config.merge({
          ...update,
          updatedByAdminId: auth.user?.id,
        })
        await config.save()
        updatedCount++
      }
    }

    return response.json({
      updated: updatedCount,
      message: `${updatedCount} configurations updated successfully`,
    })
  }

  // ==================== TIER MANAGEMENT ENDPOINTS ====================

  /**
   * @getTiers
   * @summary Get tiers for a configuration
   * @description Get all pricing tiers for a specific configuration
   * @tag Admin Delivery Pricing Tiers
   * @paramPath configId - Configuration ID
   * @responseBody 200 - [{"id": "string", "tierName": "Zone 1", "distanceFromKm": 0, "distanceToKm": 5, "basePrice": 150}]
   */
  public async getTiers({ params, response }: HttpContextContract) {
    const tiers = await DeliveryPricingTier.query()
      .where('delivery_pricing_config_id', params.configId)
      .orderBy('priority', 'asc')

    return response.json(tiers)
  }

  /**
   * @createTier
   * @summary Create new pricing tier
   * @description Create a new pricing tier for a configuration
   * @tag Admin Delivery Pricing Tiers
   * @paramPath configId - Configuration ID
   * @requestBody {
   *   "tierName": "Zone 1 (0-5km)",
   *   "distanceFromKm": 0,
   *   "distanceToKm": 5,
   *   "basePrice": 150,
   *   "pricePerKm": 20
   * }
   * @responseBody 201 - {"id": "string", "tierName": "Zone 1 (0-5km)", "basePrice": 150}
   */
  public async createTier({ params, request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      tierName: schema.string({}, [rules.required(), rules.maxLength(100)]),
      description: schema.string.optional({}, [rules.maxLength(500)]),
      distanceFromKm: schema.number([rules.unsigned()]),
      distanceToKm: schema.number.optional([rules.unsigned()]),
      basePrice: schema.number([rules.unsigned()]),
      pricePerKm: schema.number([rules.unsigned()]),
      flatRate: schema.number.optional([rules.unsigned()]),
      minimumFee: schema.number.optional([rules.unsigned()]),
      maximumFee: schema.number.optional([rules.unsigned()]),
      baseTimeMinutes: schema.number.optional([rules.unsigned()]),
      timePerKmMinutes: schema.number.optional([rules.unsigned()]),
      isActive: schema.boolean.optional(),
      priority: schema.number.optional([rules.unsigned()]),
    })

    const payload = await request.validate({ schema: validationSchema })

    // Verify the configuration exists
    await DeliveryPricingConfig.findOrFail(params.configId)

    const tier = await DeliveryPricingTier.create({
      ...payload,
      deliveryPricingConfigId: params.configId,
    })

    return response.status(201).json(tier)
  }

  /**
   * @updateTier
   * @summary Update pricing tier
   * @description Update an existing pricing tier
   * @tag Admin Delivery Pricing Tiers
   * @paramPath tierId - Tier ID
   * @requestBody {"tierName": "Updated Zone 1", "basePrice": 160}
   * @responseBody 200 - {"id": "string", "tierName": "Updated Zone 1", "basePrice": 160}
   */
  public async updateTier({ params, request, response }: HttpContextContract) {
    const tier = await DeliveryPricingTier.findOrFail(params.tierId)

    const validationSchema = schema.create({
      tierName: schema.string.optional({}, [rules.maxLength(100)]),
      description: schema.string.optional({}, [rules.maxLength(500)]),
      distanceFromKm: schema.number.optional([rules.unsigned()]),
      distanceToKm: schema.number.optional([rules.unsigned()]),
      basePrice: schema.number.optional([rules.unsigned()]),
      pricePerKm: schema.number.optional([rules.unsigned()]),
      flatRate: schema.number.optional([rules.unsigned()]),
      minimumFee: schema.number.optional([rules.unsigned()]),
      maximumFee: schema.number.optional([rules.unsigned()]),
      baseTimeMinutes: schema.number.optional([rules.unsigned()]),
      timePerKmMinutes: schema.number.optional([rules.unsigned()]),
      isActive: schema.boolean.optional(),
      priority: schema.number.optional([rules.unsigned()]),
    })

    const payload = await request.validate({ schema: validationSchema })

    tier.merge(payload)
    await tier.save()

    return response.json(tier)
  }

  /**
   * @deleteTier
   * @summary Delete pricing tier
   * @description Delete a pricing tier
   * @tag Admin Delivery Pricing Tiers
   * @paramPath tierId - Tier ID
   * @responseBody 200 - {"message": "Tier deleted successfully"}
   */
  public async deleteTier({ params, response }: HttpContextContract) {
    const tier = await DeliveryPricingTier.findOrFail(params.tierId)
    await tier.delete()

    return response.json({ message: 'Tier deleted successfully' })
  }

  /**
   * @activateTier
   * @summary Activate pricing tier
   * @description Activate a pricing tier
   * @tag Admin Delivery Pricing Tiers
   * @paramPath tierId - Tier ID
   * @responseBody 200 - {"id": "string", "isActive": true, "message": "Tier activated"}
   */
  public async activateTier({ params, response }: HttpContextContract) {
    const tier = await DeliveryPricingTier.findOrFail(params.tierId)

    tier.isActive = true
    await tier.save()

    return response.json({
      id: tier.id,
      isActive: tier.isActive,
      message: 'Tier activated successfully',
    })
  }

  /**
   * @deactivateTier
   * @summary Deactivate pricing tier
   * @description Deactivate a pricing tier
   * @tag Admin Delivery Pricing Tiers
   * @paramPath tierId - Tier ID
   * @responseBody 200 - {"id": "string", "isActive": false, "message": "Tier deactivated"}
   */
  public async deactivateTier({ params, response }: HttpContextContract) {
    const tier = await DeliveryPricingTier.findOrFail(params.tierId)

    tier.isActive = false
    await tier.save()

    return response.json({
      id: tier.id,
      isActive: tier.isActive,
      message: 'Tier deactivated successfully',
    })
  }

  /**
   * @reorderTiers
   * @summary Reorder pricing tiers
   * @description Update the priority order of pricing tiers
   * @tag Admin Delivery Pricing Tiers
   * @paramPath configId - Configuration ID
   * @requestBody {
   *   "tiers": [
   *     {"id": "tier1", "priority": 1},
   *     {"id": "tier2", "priority": 2}
   *   ]
   * }
   * @responseBody 200 - {"updated": 2, "message": "Tiers reordered successfully"}
   */
  public async reorderTiers({ params, request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      tiers: schema.array().members(
        schema.object().members({
          id: schema.string({}, [rules.exists({ table: 'delivery_pricing_tiers', column: 'id' })]),
          priority: schema.number([rules.unsigned()]),
        })
      ),
    })

    const { tiers } = await request.validate({ schema: validationSchema })

    let updatedCount = 0

    for (const tierUpdate of tiers) {
      const tier = await DeliveryPricingTier.query()
        .where('id', tierUpdate.id)
        .where('delivery_pricing_config_id', params.configId)
        .first()

      if (tier) {
        tier.priority = tierUpdate.priority
        await tier.save()
        updatedCount++
      }
    }

    return response.json({
      updated: updatedCount,
      message: `${updatedCount} tiers reordered successfully`,
    })
  }
}
