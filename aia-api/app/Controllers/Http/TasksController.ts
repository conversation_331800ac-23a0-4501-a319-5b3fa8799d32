import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Task from '../../Models/Task'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Task management
 * @version 1.0.0
 * @description Task management for the application
 */
export default class TasksController {
  /**
   * @index
   *
   * @summary List all tasks
   * @description List all tasks, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery active - Active status (1 or 0)
   * @responseBody 200 - <Task>
   * @responseBody 403 - Forbidden
   */
  public async index({ request }: HttpContextContract) {
    const { per = 20, page = 1, order = 'name', sort = 'asc', ...filters } = request.qs()
    const taskQuery = Task.filter(filters).preload('services', (sq) =>
      sq.where('active', 1).orderBy('name', 'asc')
    )

    return await taskQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   *
   * @summary Create a task
   * @description Create a task with their details (name and details)
   * @requestBody {"name": "", "details": "", active: 1}
   *
   * @responseBody 200 - <Task>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, details, active } = request.all()
    const task = new Task()

    task.fill({ name, details, active })

    const image = request.file('image')
    if (image) {
      task.merge({ image: Attachment.fromFile(image) })
    }

    await task.save()

    return response.json(task)
  }

  @bind()
  /**
   * @show
   *
   * @summary Show a single task
   * @description Show a task with their details (name and details)
   * @paramPath id required number - Task ID
   *
   * @responseBody 200 - <Task>.with(relations)
   * @response 404 - Task not found
   */
  public async show({ response }: HttpContextContract, task: Task) {
    await task.load('services', (sq) => sq.where('active', true).orderBy('name', 'asc'))
    return response.json(task)
  }

  @bind()
  /**
   * @update
   *
   * @summary Update a task
   * @description Update a task with their details (name and details)
   * @paramPath id required number - Task ID
   * @requestBody <Task>
   * @responseBody 200 - <Task>
   * @response 404 - Task not found
   */
  public async update({ request, response }: HttpContextContract, task: Task) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await task.merge(input).save()

    return response.json(task)
  }

  @bind()
  /**
   * @destroy
   *
   * @summary Delete a task
   * @description Delete a task with their details (name and details)
   * @paramPath id required number - Task ID
   *
   * @responseBody 204 - No Content
   * @response 404 - Task not found
   */
  public async destroy(_: HttpContextContract, task: Task) {
    return await task.delete()
  }
}
