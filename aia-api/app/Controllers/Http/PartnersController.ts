import { bind } from '@adonisjs/route-model-binding'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Partner from 'App/Models/Partner'

export default class PartnersController {
  /**
   * @name Partner management
   * @index
   * @summary List all Partners
   * @description List all partners, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
 
   */
  public async index({ request, response }: HttpContextContract) {
    const { page = 1, per = 10 } = request.qs()
    const partnerQuery = Partner.query()

    const partners = await partnerQuery.paginate(page, per)

    return response.json(partners)
  }

  /**
   * @store
   * @summary Create a partner
   * @description Create a partner with their details (phone and/or email address) and password
   * @requestBody {"name": "", "email": "", "phone": "254700000000", "details": "", "location": {}}
   */
  public async store({ request, response }: HttpContextContract) {
    try {
      const { name, email, phone, address } = request.all()

      const partner = await Partner.create({
        name,
        email,
        phone,
        address,
      })

      return response.json(partner)
    } catch (error) {
      // Log error by sending it to a logging service
      return response.badRequest('An error occurred while creating partner')
    }
  }

  @bind()

  /**
   * @show
   * @summary Show a single Partner
   * @description Show a Partner with their details (name and details)
   * @paramPath id required number - Partner ID
   * @responseBody 200 - <Partner>
   * @response 404 - Partner not found
   */
  public async show({ response }: HttpContextContract, partner: Partner) {
    return response.json(partner)
  }

  @bind()

  /**
   * @update
   * @summary Update a Partner
   * @description Update a Partner with their details (name and details)
   * @paramPath id required number - Partner ID
   * @requestBody <Partner>
   * @responseBody 200 - <Partner>
   * @response 404 - Partner not found
   */
  public async update({ request, response }: HttpContextContract, partner: Partner) {
    const { name, email, phone, address } = request.all()

    await partner
      .merge({
        name,
        email,
        phone,
        address,
      })
      .save()

    return response.json(partner)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Partner
   * @responseBody 204 - No content
   */
  public async destroy({ response }: HttpContextContract, partner: Partner) {
    await partner.delete()

    return response.noContent()
  }
}
