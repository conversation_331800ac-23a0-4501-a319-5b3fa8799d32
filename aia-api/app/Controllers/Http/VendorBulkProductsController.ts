import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Queue } from '@ioc:Rlanz/Queue'
import Drive from '@ioc:Adonis/Core/Drive'

import Vendor from 'App/Models/Vendor'
import { cuid } from '@ioc:Adonis/Core/Helpers'
import { schema, rules } from '@ioc:Adonis/Core/Validator'


export default class VendorBulkProductsController {

  public async store({ request, response, auth, params }: HttpContextContract) {
    // --- Authorization ---
    let vendor: Vendor;
    try {
      vendor = await Vendor.findOrFail(params.vendorId);
    } catch (error) {
      return response.notFound({ message: 'Vendor not found.' });
    }
    if (vendor.userId !== auth.user!.id) {
      // Check if user is an admin
      await auth.user!.load('roles')
      const isAdmin = auth.user!.roles.some(role => role.name.toLowerCase() === 'admin')
      
      if (!isAdmin) {
        console.warn(`[Controller] Auth failed: User ${auth.user!.id} tried upload for vendor ${vendor.id}`)
        return response.forbidden({ message: 'You are not authorized to upload products for this vendor.' })
      }
    }
    // --- ---

    // --- Validation ---
    const csvSchema = schema.create({
      product_csv: schema.file({ size: '10mb', extnames: ['csv'] }),
      branchId: schema.string.optional({}, [
        rules.exists({ table: 'branches', column: 'id', where: { vendor_id: params.vendorId } })
      ])
    });
    let payload;
    try {
        payload = await request.validate({ schema: csvSchema, messages: { /*...*/ } });
    } catch (validationError) {
         return response.status(422).send(validationError.messages);
    }
    // --- ---

    // 3. Move the file to S3
    const fileName = `${cuid()}.csv`;
    const targetDisk = 's3';
    const s3Folder = 'product-csv-uploads/';
    const s3ObjectKey = s3Folder + fileName;

    try {
      if (!payload.product_csv.isValid) {
          console.error("[Controller] Uploaded file object state is invalid:", payload.product_csv.errors);
          return response.badRequest({ message: "Uploaded file is invalid.", errors: payload.product_csv.errors });
      }

      console.log(`[Controller] Attempting to move uploaded file to S3 disk as '${s3ObjectKey}'...`);
      await payload.product_csv.moveToDisk(
        s3Folder,
        { name: fileName },
        targetDisk
      );

      // --- REMOVED INCORRECT CHECK ---
      // if (!payload.product_csv.moved()) { ... }
      // --- ---

      // Check the state property instead (optional but informative)
      if (payload.product_csv.state !== 'moved') {
          console.warn(`[Controller] File state after moveToDisk is '${payload.product_csv.state}', expected 'moved'. Errors:`, payload.product_csv.errors);
          // If state is 'moved' but errors exist, log them.
          // If state is something else, it might indicate an issue, but often moveToDisk throwing an error is the primary failure signal.
          // We'll rely on the lack of error from moveToDisk as success for now.
      }

      console.log(`[Controller] moveToDisk call completed for S3 key: ${s3ObjectKey}. File state: ${payload.product_csv.state}`);
      // Note: We cannot reliably use fs.existsSync for S3 verification here.

    } catch (moveError) {
      // This catch block is now the primary indicator of a move failure
      console.error(`[Controller] Error during moveToDisk for '${s3ObjectKey}' to disk '${targetDisk}':`, moveError);
      return response.internalServerError({ message: "Failed to store uploaded file to S3 due to a server error." });
    }

    // --- Construct the payload for the job ---
    const jobPayload = {
      s3ObjectKey: s3ObjectKey, // Use the constructed relative key
      diskName: targetDisk,
      vendorId: vendor.id,
      branchId: payload.branchId,
      userId: auth.user!.id,
    };

    // 4. Dispatch Background Job (error handling remains the same)
    let job;
    try {
      job = await Queue.dispatch('App/Jobs/ProcessProductCsvUpload', jobPayload);
      console.log(`[Controller] Dispatched job ProcessProductCsvUpload with ID: ${job.id} for vendor ${vendor.id}, using S3 key: ${jobPayload.s3ObjectKey}`);
    } catch (dispatchError) {
      console.error("[Controller] Error dispatching queue job:", dispatchError);
      try {
        await Drive.use(targetDisk).delete(s3ObjectKey); // Cleanup using the key
        console.log(`[Controller] Cleaned up S3 object ${s3ObjectKey} after job dispatch error.`);
      } catch (cleanupError) {
        console.error(`[Controller] Failed to cleanup S3 object ${s3ObjectKey} after job dispatch error:`, cleanupError);
      }
      return response.internalServerError({ message: "Failed to queue the upload job." });
    }

    // 5. Respond Immediately
    return response.accepted({
      message: 'CSV upload accepted and is being processed in the background.',
      jobId: job.id,
    });
  }

  // --- getJobStatus method remains the same ---
  public async getJobStatus({ params, response, auth }: HttpContextContract) {
    try {
      const bullQueue = await Queue.get()
      const job = await bullQueue?.getJob(params.jobId)
      if (!job) {
        return response.notFound({ message: 'Job not found.' })
      }
      if (!job.data || job.data.userId !== auth.user!.id) {
        console.warn(`Status check denied: User ${auth.user!.id} tried job ${params.jobId}`)
        return response.forbidden({ message: 'You are not authorized to view this job status.' })
      }
      const state = await job.getState()
      return response.ok({
        jobId: job.id,
        state: state,
        progress: job.progress,
        result: state === 'completed' ? job.returnvalue : null,
        failedReason: state === 'failed' ? job.failedReason : null
      })
    } catch (error) {
      console.error(`Error fetching job status for ID ${params.jobId}:`, error)
      return response.internalServerError({ message: "Failed to fetch job status." })
    }
  }
}