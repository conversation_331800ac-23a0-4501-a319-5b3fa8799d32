import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Brand from '../../Models/Brand'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Brand management
 * @version 1.0.0
 * @description Brand management for the application
 */
export default class BrandsController {
  /**
   * @index
   * @summary List all brands
   * @description List all brands, paginated
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const brandQuery = Brand.filter(filters)

    return await brandQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a brand
   * @description Create a brand with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Brand>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, details } = request.all()
    const brand = new Brand()

    brand.fill({ name, details })

    const image = request.file('image')
    if (image) {
      brand.merge({ image: Attachment.fromFile(image) })
    }

    await brand.save()

    return response.json(brand)
  }

  @bind()
  /**
   * @show
   * @summary Show a single brand
   * @description Show a brand with their details (name and details)
   * @paramPath id required number - Brand ID
   * @responseBody 200 - <Brand>
   * @response 404 - Brand not found
   */
  public async show({ response }: HttpContextContract, brand: Brand) {
    return response.json(brand)
  }

  @bind()
  /**
   * @update
   * @summary Update a brand
   * @description Update a brand with their details (name and details)
   * @paramPath id required number - Brand ID
   * @requestBody <Brand>
   * @responseBody 200 - <Brand>
   * @response 404 - Brand not found
   */
  public async update({ request, response }: HttpContextContract, brand: Brand) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await brand.merge(input).save()

    return response.json(brand)
  }

  @bind()
  public async destroy(_: HttpContextContract, brand: Brand) {
    return await brand.delete()
  }
}
