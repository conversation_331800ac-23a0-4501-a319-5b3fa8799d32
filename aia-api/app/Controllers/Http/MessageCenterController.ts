import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'
import Invoice from 'App/Models/Invoice'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

/**
 * @name Message Center management
 * @version 1.0.0
 * @description Unified Message Center API for mobile app
 */
export default class MessageCenterController {
  /**
   * @summary Get Message Center overview with counts
   * @description Returns counts for all Message Center categories with proper access control
   */
  public async overview({ auth, request, response }: HttpContextContract) {
    try {
      const user = auth.user!

      // Preload user relationships for role-based filtering
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Get access-controlled counts for each category using vendor-scoped filtering
      const [pendingRequests, pendingBills, completedOrders, statements] = await Promise.all([
        this.getPendingRequestsCount(user),
        this.getPendingBillsCount(user),
        this.getCompletedOrdersCount(user),
        this.getStatementsCount(user),
      ])

      return response.json({
        overview: {
          pendingRequests,
          pendingBills,
          completedOrders,
          statements,
        },
        lastUpdated: new Date().toISOString(),
      })
    } catch (error) {
      console.error('Message Center overview error:', error)
      return response.internalServerError({
        error: 'Failed to load Message Center overview',
        message: error.message,
      })
    }
  }

  /**
   * @summary Get Pending Requests (temp orders)
   * @description Returns pending orders with proper access control and pagination
   */
  public async pendingRequests({ auth, request, response }: HttpContextContract) {
    try {
      const { per = 20, page = 1, order = 'createdAt', sort = 'desc' } = request.qs()
      const user = auth.user!

      // Preload user relationships for role-based filtering
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Query orders with Pending status (temp orders)
      const orderQuery = Order.query()
        .where('status', 'Pending')
        .preload('customer')
        .preload('vendor')
        .preload('branch')
        .preload('staff')
        .preload('items', (itemQuery) => {
          itemQuery.preload('product')
        })

      // Apply vendor-scoped role-based filtering
      HierarchicalAccessControlService.applyVendorScopedFilter(orderQuery, user)

      const orders = await orderQuery.orderBy(order, sort).paginate(page, per)

      return response.json({
        category: 'pendingRequests',
        data: orders.serialize(),
        meta: {
          description: 'Orders awaiting processing or confirmation',
          actionRequired: 'Review and process pending orders',
        },
      })
    } catch (error) {
      console.error('Pending requests error:', error)
      return response.internalServerError({
        error: 'Failed to load pending requests',
        message: error.message,
      })
    }
  }

  /**
   * @summary Get Pending Bills (unpaid invoices)
   * @description Returns unpaid invoices with proper access control and pagination
   */
  public async pendingBills({ auth, request, response }: HttpContextContract) {
    try {
      const { per = 20, page = 1, order = 'createdAt', sort = 'desc' } = request.qs()
      const user = auth.user!

      // Preload user relationships for role-based filtering
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Query invoices with Pending status
      const invoiceQuery = Invoice.query()
        .where('status', 'Pending')
        .preload('order', (orderQuery) => {
          orderQuery.preload('customer')
          orderQuery.preload('vendor')
          orderQuery.preload('branch')
        })

      // Apply vendor-scoped role-based filtering through order relationship
      invoiceQuery.whereHas('order', (orderQuery) => {
        HierarchicalAccessControlService.applyVendorScopedFilter(orderQuery, user)
      })

      const invoices = await invoiceQuery.orderBy(order, sort).paginate(page, per)

      return response.json({
        category: 'pendingBills',
        data: invoices.serialize(),
        meta: {
          description: 'Unpaid invoices requiring payment',
          actionRequired: 'Process payments for outstanding bills',
        },
      })
    } catch (error) {
      console.error('Pending bills error:', error)
      return response.internalServerError({
        error: 'Failed to load pending bills',
        message: error.message,
      })
    }
  }

  /**
   * @summary Get Completed Orders
   * @description Returns completed orders with proper access control and pagination
   */
  public async completedOrders({ auth, request, response }: HttpContextContract) {
    try {
      const { per = 20, page = 1, order = 'createdAt', sort = 'desc' } = request.qs()
      const user = auth.user!

      // Preload user relationships for role-based filtering
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Query orders with Completed status
      const orderQuery = Order.query()
        .where('status', 'Completed')
        .preload('customer')
        .preload('vendor')
        .preload('branch')
        .preload('staff')
        .preload('invoices', (invoiceQuery) => {
          invoiceQuery.preload('payments')
        })

      // Apply vendor-scoped role-based filtering
      HierarchicalAccessControlService.applyVendorScopedFilter(orderQuery, user)

      const orders = await orderQuery.orderBy(order, sort).paginate(page, per)

      return response.json({
        category: 'completedOrders',
        data: orders.serialize(),
        meta: {
          description: 'Successfully completed and fulfilled orders',
          actionRequired: 'Review completed orders for quality assurance',
        },
      })
    } catch (error) {
      console.error('Completed orders error:', error)
      return response.internalServerError({
        error: 'Failed to load completed orders',
        message: error.message,
      })
    }
  }

  /**
   * @summary Get Statements (all invoices)
   * @description Returns all invoices with proper access control and pagination
   */
  public async statements({ auth, request, response }: HttpContextContract) {
    try {
      const { per = 20, page = 1, order = 'createdAt', sort = 'desc', status } = request.qs()
      const user = auth.user!

      // Preload user relationships for role-based filtering
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Query all invoices
      const invoiceQuery = Invoice.query()
        .preload('order', (orderQuery) => {
          orderQuery.preload('customer')
          orderQuery.preload('vendor')
          orderQuery.preload('branch')
        })
        .preload('payments')

      // Apply status filter if provided
      if (status) {
        invoiceQuery.where('status', status)
      }

      // Apply vendor-scoped role-based filtering through order relationship
      invoiceQuery.whereHas('order', (orderQuery) => {
        HierarchicalAccessControlService.applyVendorScopedFilter(orderQuery, user)
      })

      const invoices = await invoiceQuery.orderBy(order, sort).paginate(page, per)

      return response.json({
        category: 'statements',
        data: invoices.serialize(),
        meta: {
          description: 'Complete invoice history and statements',
          actionRequired: 'Review financial statements and payment history',
        },
      })
    } catch (error) {
      console.error('Statements error:', error)
      return response.internalServerError({
        error: 'Failed to load statements',
        message: error.message,
      })
    }
  }

  /**
   * Helper method to get pending requests count
   */
  private async getPendingRequestsCount(user: any): Promise<number> {
    const query = Order.query().where('status', 'Pending').where('userId', user.id)
    const result = await query.count('* as total')
    return Number(result[0].$extras.total)
  }

  /**
   * Helper method to get pending bills count
   */
  private async getPendingBillsCount(user: any): Promise<number> {
    const query = Invoice.query()
      .where('status', 'Pending')
      .whereHas('order', (orderQuery) => {
        HierarchicalAccessControlService.applyVendorScopedFilter(orderQuery, user)
      })
    const result = await query.count('* as total')
    return parseInt(result[0].$extras.total)
  }

  /**
   * Helper method to get completed orders count
   */
  private async getCompletedOrdersCount(user: any): Promise<number> {
    const query = Order.query().where('status', 'Completed').where('userId', user.id)
    const result = await query.count('* as total')
    return Number(result[0].$extras.total)
  }

  /**
   * Helper method to get statements count
   */
  private async getStatementsCount(user: any): Promise<number> {
    const query = Invoice.query().whereHas('order', (orderQuery) => {
      HierarchicalAccessControlService.applyVendorScopedFilter(orderQuery, user)
    })
    const result = await query.count('* as total')
    return parseInt(result[0].$extras.total)
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's pending requests
   * Used by customer apps to ensure customers only see their own pending orders
   * @param groupByVendor - Optional flag to group results by vendor
   */
  public async customerPendingRequests({ auth, request, response }: HttpContextContract) {
    try {
      const {
        per = 20,
        page = 1,
        order = 'createdAt',
        sort = 'desc',
        groupByVendor = 'false',
      } = request.qs()
      const user = auth.user!

      // Query orders with Pending status (temp orders)
      const orderQuery = Order.query()
        .where('status', 'Pending')
        .preload('customer')
        .preload('vendor')
        .preload('branch')
        .preload('items', (itemQuery) => {
          itemQuery.preload('product')
        })

      // Customer endpoint ALWAYS shows only user's own orders
      HierarchicalAccessControlService.applyCustomerOnlyFilter(orderQuery, user)

      const orders = await orderQuery.orderBy(order, sort).paginate(page, per)

      // Custom serialization to include unified items (OrderItems + temp_items)
      const serializedOrders = orders.serialize()
      serializedOrders.data = orders.all().map((order) => order.serializeForMessageCenter())

      // Check if vendor grouping is requested
      if (groupByVendor === 'true') {
        const groupedData = this.groupDataByVendor(serializedOrders.data)
        return response.json({
          category: 'pendingRequests',
          data: {
            ...serializedOrders,
            data: groupedData,
          },
          meta: {
            groupedByVendor: true,
            description: 'Pending requests grouped by vendor',
          },
        })
      }

      return response.json({
        category: 'pendingRequests',
        data: serializedOrders,
      })
    } catch (error) {
      console.error('Error in customerPendingRequests:', error)
      return response.status(500).json({
        error: 'Failed to fetch customer pending requests',
        details: error.message,
      })
    }
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's pending bills
   * @param groupByVendor - Optional flag to group results by vendor
   */
  public async customerPendingBills({ auth, request, response }: HttpContextContract) {
    try {
      const {
        per = 20,
        page = 1,
        order = 'createdAt',
        sort = 'desc',
        groupByVendor = 'false',
      } = request.qs()
      const user = auth.user!

      // Query unpaid invoices
      const invoiceQuery = Invoice.query()
        .where('status', 'Pending')
        .preload('order', (orderQuery) => {
          orderQuery.preload('customer')
          orderQuery.preload('vendor')
          orderQuery.preload('branch')
          orderQuery.preload('items', (itemQuery) => {
            itemQuery.preload('product')
            itemQuery.preload('modifiers')
          })
        })
        .preload('payments')

      // Customer endpoint ALWAYS shows only user's own invoices
      invoiceQuery.whereHas('order', (orderQuery) => {
        orderQuery.where('userId', user.id)
      })

      const invoices = await invoiceQuery.orderBy(order, sort).paginate(page, per)

      // Custom serialization to include unified items (OrderItems + temp_items)
      const serializedInvoices = invoices.serialize()
      serializedInvoices.data = invoices.all().map((invoice) => {
        const serializedInvoice = invoice.serialize()
        if (serializedInvoice.order) {
          serializedInvoice.order = invoice.order.serializeForMessageCenter()
        }
        return serializedInvoice
      })

      // Check if vendor grouping is requested
      if (groupByVendor === 'true') {
        const groupedData = this.groupInvoicesByVendor(serializedInvoices.data)
        return response.json({
          category: 'pendingBills',
          data: {
            ...serializedInvoices,
            data: groupedData,
          },
          meta: {
            groupedByVendor: true,
            description: 'Pending bills grouped by vendor',
          },
        })
      }

      return response.json({
        category: 'pendingBills',
        data: serializedInvoices,
      })
    } catch (error) {
      console.error('Error in customerPendingBills:', error)
      return response.status(500).json({
        error: 'Failed to fetch customer pending bills',
        details: error.message,
      })
    }
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's completed orders
   * @param groupByVendor - Optional flag to group results by vendor
   */
  public async customerCompletedOrders({ auth, request, response }: HttpContextContract) {
    try {
      const {
        per = 20,
        page = 1,
        order = 'createdAt',
        sort = 'desc',
        groupByVendor = 'false',
      } = request.qs()
      const user = auth.user!

      // Query completed orders
      const orderQuery = Order.query()
        .where('status', 'Completed')
        .preload('customer')
        .preload('vendor')
        .preload('branch')
        .preload('items', (itemQuery) => {
          itemQuery.preload('product')
          itemQuery.preload('modifiers')
        })
        .preload('invoices', (invoiceQuery) => {
          invoiceQuery.preload('payments')
        })

      // Customer endpoint ALWAYS shows only user's own orders
      HierarchicalAccessControlService.applyCustomerOnlyFilter(orderQuery, user)

      const orders = await orderQuery.orderBy(order, sort).paginate(page, per)

      // Custom serialization to include unified items (OrderItems + temp_items)
      const serializedOrders = orders.serialize()
      serializedOrders.data = orders.all().map((order) => order.serializeForMessageCenter())

      // Check if vendor grouping is requested
      if (groupByVendor === 'true') {
        const groupedData = this.groupDataByVendor(serializedOrders.data)
        return response.json({
          category: 'completedOrders',
          data: {
            ...serializedOrders,
            data: groupedData,
          },
          meta: {
            groupedByVendor: true,
            description: 'Completed orders grouped by vendor',
          },
        })
      }

      return response.json({
        category: 'completedOrders',
        data: serializedOrders,
      })
    } catch (error) {
      console.error('Error in customerCompletedOrders:', error)
      return response.status(500).json({
        error: 'Failed to fetch customer completed orders',
        details: error.message,
      })
    }
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's statements
   * @param groupByVendor - Optional flag to group results by vendor
   */
  public async customerStatements({ auth, request, response }: HttpContextContract) {
    try {
      const {
        per = 20,
        page = 1,
        order = 'createdAt',
        sort = 'desc',
        status,
        groupByVendor = 'false',
      } = request.qs()
      const user = auth.user!

      // Query all invoices
      const invoiceQuery = Invoice.query()
        .preload('order', (orderQuery) => {
          orderQuery.preload('customer')
          orderQuery.preload('vendor')
          orderQuery.preload('branch')
          orderQuery.preload('items', (itemQuery) => {
            itemQuery.preload('product')
            itemQuery.preload('modifiers')
          })
        })
        .preload('payments')

      // Apply status filter if provided
      if (status) {
        invoiceQuery.where('status', status)
      }

      // Customer endpoint ALWAYS shows only user's own invoices
      invoiceQuery.whereHas('order', (orderQuery) => {
        orderQuery.where('userId', user.id)
      })

      const invoices = await invoiceQuery.orderBy(order, sort).paginate(page, per)

      // Custom serialization to include unified items in orders
      const serializedInvoices = invoices.serialize()
      serializedInvoices.data = invoices.all().map((invoice) => {
        const serializedInvoice = invoice.serialize()
        if (invoice.order) {
          serializedInvoice.order = invoice.order.serializeForMessageCenter()
        }
        return serializedInvoice
      })

      // Check if vendor grouping is requested
      if (groupByVendor === 'true') {
        const groupedData = this.groupInvoicesByVendor(serializedInvoices.data)
        return response.json({
          category: 'statements',
          data: {
            ...serializedInvoices,
            data: groupedData,
          },
          meta: {
            groupedByVendor: true,
            description: 'Financial statements grouped by vendor',
          },
        })
      }

      return response.json({
        category: 'statements',
        data: serializedInvoices,
      })
    } catch (error) {
      console.error('Error in customerStatements:', error)
      return response.status(500).json({
        error: 'Failed to fetch customer statements',
        details: error.message,
      })
    }
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's message center overview
   */
  public async customerOverview({ auth, response }: HttpContextContract) {
    try {
      const user = auth.user!

      // Get counts for customer's own data only
      const [pendingRequests, pendingBills, completedOrders, statements] = await Promise.all([
        Order.query().where('status', 'Pending').where('userId', user.id).count('* as total'),
        Invoice.query()
          .where('status', 'Pending')
          .whereHas('order', (orderQuery) => {
            orderQuery.where('userId', user.id)
          })
          .count('* as total'),
        Order.query().where('status', 'Completed').where('userId', user.id).count('* as total'),
        Invoice.query()
          .whereHas('order', (orderQuery) => {
            orderQuery.where('userId', user.id)
          })
          .count('* as total'),
      ])

      return response.json({
        overview: {
          pendingRequests: Number(pendingRequests[0]?.total || 0),
          pendingBills: Number(pendingBills[0]?.$extras?.total || 0),
          completedOrders: Number(completedOrders[0]?.total || 0),
          statements: Number(statements[0]?.$extras?.total || 0),
        },
        lastUpdated: new Date().toISOString(),
      })
    } catch (error) {
      console.error('Error in customerOverview:', error)
      return response.status(500).json({
        error: 'Failed to fetch customer overview',
        details: error.message,
      })
    }
  }

  /**
   * Helper method to group orders by vendor
   * @param orders - Array of order objects
   * @returns Array of vendor groups with their orders
   */
  private groupDataByVendor(orders: any[]): any[] {
    const vendorGroups = new Map()

    orders.forEach((order) => {
      const vendorId = order.vendor?.id || order.vendorId
      const vendorName = order.vendor?.name || 'Unknown Vendor'
      const vendorLogo = order.vendor?.logo || null
      const vendorSlug = order.vendor?.slug || null

      if (!vendorGroups.has(vendorId)) {
        vendorGroups.set(vendorId, {
          vendor: {
            id: vendorId,
            name: vendorName,
            logo: vendorLogo,
            slug: vendorSlug,
          },
          orders: [],
          totalOrders: 0,
          totalAmount: 0,
        })
      }

      const group = vendorGroups.get(vendorId)
      group.orders.push(order)
      group.totalOrders += 1

      // Calculate total amount from order items or meta pricing
      const orderAmount = this.calculateOrderAmount(order)
      group.totalAmount += orderAmount
    })

    return Array.from(vendorGroups.values()).sort((a, b) =>
      a.vendor.name.localeCompare(b.vendor.name)
    )
  }

  /**
   * Helper method to group invoices by vendor
   * @param invoices - Array of invoice objects
   * @returns Array of vendor groups with their invoices
   */
  private groupInvoicesByVendor(invoices: any[]): any[] {
    const vendorGroups = new Map()

    invoices.forEach((invoice) => {
      const vendorId = invoice.order?.vendor?.id || invoice.order?.vendorId
      const vendorName = invoice.order?.vendor?.name || 'Unknown Vendor'
      const vendorLogo = invoice.order?.vendor?.logo || null
      const vendorSlug = invoice.order?.vendor?.slug || null

      if (!vendorGroups.has(vendorId)) {
        vendorGroups.set(vendorId, {
          vendor: {
            id: vendorId,
            name: vendorName,
            logo: vendorLogo,
            slug: vendorSlug,
          },
          invoices: [],
          totalInvoices: 0,
          totalAmount: 0,
          totalPaid: 0,
          totalPending: 0,
        })
      }

      const group = vendorGroups.get(vendorId)
      group.invoices.push(invoice)
      group.totalInvoices += 1
      group.totalAmount += invoice.amount || 0

      // Calculate paid and pending amounts
      if (invoice.status === 'Paid') {
        group.totalPaid += invoice.amount || 0
      } else if (invoice.status === 'Pending') {
        group.totalPending += invoice.amount || 0
      }
    })

    return Array.from(vendorGroups.values()).sort((a, b) =>
      a.vendor.name.localeCompare(b.vendor.name)
    )
  }

  /**
   * Helper method to calculate order amount from various sources
   * @param order - Order object
   * @returns Total order amount
   */
  private calculateOrderAmount(order: any): number {
    // Try to get amount from meta pricing first
    if (order.meta?.pricing?.total) {
      return Number(order.meta.pricing.total)
    }

    // Calculate from items if available
    if (order.items && Array.isArray(order.items)) {
      return order.items.reduce((total, item) => {
        const itemPrice = Number(item.price || 0)
        const quantity = Number(item.quantity || 1)
        return total + itemPrice * quantity
      }, 0)
    }

    // Calculate from unified items if available
    if (order.unifiedItemsForApi && Array.isArray(order.unifiedItemsForApi)) {
      return order.unifiedItemsForApi.reduce((total, item) => {
        const itemPrice = Number(item.price || 0)
        const quantity = Number(item.quantity || 1)
        return total + itemPrice * quantity
      }, 0)
    }

    return 0
  }
}
