import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor, { VendorQRPreferences } from 'App/Models/Vendor'

// Conditional Queue import to handle cases where queue is not configured
let Queue: any = null
try {
  Queue = require('@ioc:Adonis/Addons/Queue').default
} catch (error) {
  console.warn('Queue not available, bulk operations will be disabled')
}

export default class VendorQRPreferencesController {
  /**
   * Get vendor QR code preferences
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor) {
    const preferences = vendor.qrCodePreferences || this.getDefaultPreferences()

    return response.json({
      success: true,
      data: preferences,
      message: 'QR code preferences retrieved successfully',
    })
  }

  /**
   * Update vendor QR code preferences
   */
  @bind()
  public async update({ request, response }: HttpContextContract, vendor: Vendor) {
    const { autoGeneration, defaultOptions, notifications } = request.only([
      'autoGeneration',
      'defaultOptions',
      'notifications',
    ])

    // Validate the preferences structure
    const preferences: VendorQRPreferences = {
      autoGeneration: {
        enabled: autoGeneration?.enabled ?? false,
        onLotCreation: autoGeneration?.onLotCreation ?? false,
        onBulkImport: autoGeneration?.onBulkImport ?? false,
      },
      defaultOptions: {
        width: defaultOptions?.width ?? 300,
        errorCorrectionLevel: defaultOptions?.errorCorrectionLevel ?? 'M',
        color: {
          dark: defaultOptions?.color?.dark ?? '#000000',
          light: defaultOptions?.color?.light ?? '#ffffff',
        },
      },
      notifications: {
        onSuccess: notifications?.onSuccess ?? false,
        onFailure: notifications?.onFailure ?? true,
      },
    }

    // Update vendor preferences
    vendor.qrCodePreferences = preferences
    await vendor.save()

    return response.json({
      success: true,
      data: preferences,
      message: 'QR code preferences updated successfully',
    })
  }

  /**
   * Generate QR codes for existing tables (bulk operation)
   */
  @bind()
  public async generateBulk({ request, response }: HttpContextContract, vendor: Vendor) {
    const { sectionIds, branchIds, lotIds, options } = request.only([
      'sectionIds',
      'branchIds',
      'lotIds',
      'options',
    ])

    // Use vendor's default options if not provided
    const qrOptions = options || vendor.qrCodePreferences?.defaultOptions

    if (!Queue) {
      return response.status(503).json({
        success: false,
        message: 'Queue service not available. Bulk operations are disabled.',
      })
    }

    try {
      // Queue bulk generation job
      const job = await Queue.dispatch('App/Jobs/GenerateBulkTableQRCodes', {
        vendorId: vendor.id,
        sectionIds,
        branchIds,
        lotIds,
        options: qrOptions,
      })

      return response.json({
        success: true,
        data: {
          jobId: job.id,
          vendorId: vendor.id,
          filters: {
            sectionIds,
            branchIds,
            lotIds,
          },
        },
        message: 'Bulk QR code generation queued successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to queue bulk QR code generation',
        error: error.message,
      })
    }
  }

  /**
   * Get QR code generation statistics for vendor
   */
  @bind()
  public async stats({ response }: HttpContextContract, vendor: Vendor) {
    // Get basic stats from existing analytics endpoint
    const qrGenerationService = new (await import('App/Services/QRCodeGenerationService')).default()
    const analytics = await qrGenerationService.getQRCodeAnalytics(vendor.id)

    // Add auto-generation specific stats
    const preferences = vendor.qrCodePreferences
    const autoGenerationEnabled = preferences?.autoGeneration?.enabled ?? false

    return response.json({
      success: true,
      data: {
        ...analytics,
        autoGeneration: {
          enabled: autoGenerationEnabled,
          onLotCreation: preferences?.autoGeneration?.onLotCreation ?? false,
          onBulkImport: preferences?.autoGeneration?.onBulkImport ?? false,
        },
        preferences: preferences || this.getDefaultPreferences(),
      },
      message: 'QR code statistics retrieved successfully',
    })
  }

  /**
   * Reset QR code preferences to defaults
   */
  @bind()
  public async reset({ response }: HttpContextContract, vendor: Vendor) {
    vendor.qrCodePreferences = this.getDefaultPreferences()
    await vendor.save()

    return response.json({
      success: true,
      data: vendor.qrCodePreferences,
      message: 'QR code preferences reset to defaults',
    })
  }

  /**
   * Enable auto-generation for vendor
   */
  @bind()
  public async enableAutoGeneration({ response }: HttpContextContract, vendor: Vendor) {
    const currentPreferences = vendor.qrCodePreferences || this.getDefaultPreferences()

    currentPreferences.autoGeneration.enabled = true
    currentPreferences.autoGeneration.onLotCreation = true

    vendor.qrCodePreferences = currentPreferences
    await vendor.save()

    return response.json({
      success: true,
      data: vendor.qrCodePreferences,
      message: 'Auto-generation enabled successfully',
    })
  }

  /**
   * Disable auto-generation for vendor
   */
  @bind()
  public async disableAutoGeneration({ response }: HttpContextContract, vendor: Vendor) {
    const currentPreferences = vendor.qrCodePreferences || this.getDefaultPreferences()

    currentPreferences.autoGeneration.enabled = false
    currentPreferences.autoGeneration.onLotCreation = false
    currentPreferences.autoGeneration.onBulkImport = false

    vendor.qrCodePreferences = currentPreferences
    await vendor.save()

    return response.json({
      success: true,
      data: vendor.qrCodePreferences,
      message: 'Auto-generation disabled successfully',
    })
  }

  /**
   * Get default QR code preferences
   */
  private getDefaultPreferences(): VendorQRPreferences {
    return {
      autoGeneration: {
        enabled: false,
        onLotCreation: false,
        onBulkImport: false,
      },
      defaultOptions: {
        width: 300,
        errorCorrectionLevel: 'M',
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      },
      notifications: {
        onSuccess: false,
        onFailure: true,
      },
    }
  }
}
