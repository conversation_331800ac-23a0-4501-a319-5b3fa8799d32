import Database from '@ioc:Adonis/Lucid/Database'

export default class UserSearchController {
  public async search({ request, response }) {
    const { page = 1, per = 20 } = request.qs()
    const searchString = request.input('search')

    if (searchString) {
      try {
        const query = Database.from('users')
          .where('first_name', 'ilike', `%${searchString}%`)
          .orWhere('last_name', 'ilike', `%${searchString}%`)
          .orWhere('last_name', 'ilike', `%${searchString}%`)
          .orWhere('phone', 'ilike', `%${searchString}%`)
          .orWhere('email', 'ilike', `%${searchString}%`)
        const users = await query.paginate(page, per)

        // console.log("Users found: ", users)
        return response.json(users)
      } catch (error) {
        console.log('Error during search:', error)
        return response.status(500).json({ message: 'Error while searching', error: error.message })
      }
    }

    return response.json({ message: 'No search term provided' })
  }
}
