import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Department from '../../Models/Department'
import OrderItem from '../../Models/OrderItem'
import User from '../../Models/User'
import { bind } from '@adonisjs/route-model-binding'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { DateTime } from 'luxon'

/**
 * @name Department Workload Management
 * @version 1.0.0
 * @description Specialized endpoints for department workload monitoring and management
 */
export default class DepartmentWorkloadController {
  /**
   * @summary Get pending items for department
   * @description Get all pending items assigned to a specific department
   * @paramPath department_id required string - Department ID
   * @paramQuery priority - Filter by priority level
   * @paramQuery order_type - Filter by order type (Instant, Preorder)
   * @paramQuery limit - Limit number of results
   * @responseBody 200 - Pending items list
   */
  @bind()
  public async getPendingItems({ request, response }: HttpContextContract, department: Department) {
    const {
      priority,
      order_type,
      limit = 50,
      sort_by = 'created_at',
      sort_order = 'asc'
    } = request.qs()

    const pendingQuery = OrderItem.query()
      .where('department_id', department.id)
      .where('status', 'pending')
      .preload('order', (orderQuery) => {
        orderQuery.preload('customer')
      })
      .preload('product')
      .preload('modifiers')

    // Apply filters
    if (priority) {
      pendingQuery.where('priority_level', priority)
    }

    if (order_type) {
      pendingQuery.whereHas('order', (orderQuery) => {
        orderQuery.where('type', order_type)
      })
    }

    // Apply sorting
    const validSortFields = ['created_at', 'priority_level', 'estimated_preparation_time']
    const sortField = validSortFields.includes(sort_by) ? sort_by : 'created_at'

    const pendingItems = await pendingQuery
      .orderBy(sortField, sort_order)
      .limit(limit)
      .exec()

    return response.json({
      department_id: department.id,
      department_name: department.name,
      total_pending: pendingItems.length,
      items: pendingItems.map(item => ({
        ...item.serialize(),
        order_info: {
          id: item.order.id,
          type: item.order.type,
          delivery: item.order.delivery,
          customer_name: item.order.customer?.name
        },
        estimated_completion: item.estimatedCompletionTime,
        can_start: item.canBeStarted
      }))
    })
  }

  /**
   * @summary Get preparing items for department
   * @description Get all items currently being prepared in a department
   * @paramPath department_id required string - Department ID
   * @paramQuery include_overdue - Include overdue items flag
   * @paramQuery staff_id - Filter by assigned staff
   * @responseBody 200 - Preparing items list
   */
  @bind()
  public async getPreparingItems({ request, response }: HttpContextContract, department: Department) {
    const {
      include_overdue = 'true',
      staff_id,
      limit = 50
    } = request.qs()

    const preparingQuery = OrderItem.query()
      .where('department_id', department.id)
      .where('status', 'preparing')
      .preload('order')
      .preload('product')
      .preload('assignedStaff')
      .preload('modifiers', (modifierQuery) => {
        modifierQuery.preload('preparedByStaff')
      })

    if (staff_id) {
      preparingQuery.where('assigned_staff_id', staff_id)
    }

    const preparingItems = await preparingQuery
      .orderBy('preparation_started_at', 'asc')
      .limit(limit)
      .exec()

    // Filter overdue items if requested
    let filteredItems = preparingItems
    if (include_overdue === 'false') {
      filteredItems = preparingItems.filter(item => !item.isOverdue)
    }

    return response.json({
      department_id: department.id,
      department_name: department.name,
      total_preparing: filteredItems.length,
      overdue_count: preparingItems.filter(item => item.isOverdue).length,
      items: filteredItems.map(item => ({
        ...item.serialize(),
        preparation_progress: item.preparationProgress,
        is_overdue: item.isOverdue,
        assigned_staff: item.assignedStaff?.serialize(),
        estimated_completion: item.estimatedCompletionTime,
        modifier_status: {
          total: item.modifiers.length,
          completed: item.modifiers.filter(m => ['completed', 'skipped'].includes(m.status)).length,
          pending: item.modifiers.filter(m => m.status === 'pending').length,
          preparing: item.modifiers.filter(m => m.status === 'preparing').length
        }
      }))
    })
  }

  /**
   * @summary Get department statistics
   * @description Get comprehensive statistics for department performance
   * @paramPath department_id required string - Department ID
   * @paramQuery period - Time period (today, week, month)
   * @responseBody 200 - Department statistics
   */
  @bind()
  public async getDepartmentStatistics({ request, response }: HttpContextContract, department: Department) {
    const { period = 'today' } = request.qs()

    // Calculate date range based on period
    let startDate: DateTime
    switch (period) {
      case 'week':
        startDate = DateTime.now().startOf('week')
        break
      case 'month':
        startDate = DateTime.now().startOf('month')
        break
      default:
        startDate = DateTime.now().startOf('day')
    }

    // Get items for the period
    const itemsQuery = OrderItem.query()
      .where('department_id', department.id)
      .where('created_at', '>=', startDate.toSQL())
      .preload('modifiers')

    const allItems = await itemsQuery.exec()

    // Calculate statistics
    const stats = {
      department_id: department.id,
      department_name: department.name,
      period,
      date_range: {
        start: startDate.toISODate(),
        end: DateTime.now().toISODate()
      },
      totals: {
        total_items: allItems.length,
        completed_items: allItems.filter(item => ['ready', 'served'].includes(item.status)).length,
        cancelled_items: allItems.filter(item => item.status === 'cancelled').length,
        pending_items: allItems.filter(item => item.status === 'pending').length,
        preparing_items: allItems.filter(item => item.status === 'preparing').length
      },
      performance: {
        completion_rate: allItems.length > 0 ?
          (allItems.filter(item => ['ready', 'served'].includes(item.status)).length / allItems.length * 100).toFixed(2) : 0,
        average_preparation_time: this.calculateAveragePreparationTime(allItems),
        overdue_rate: allItems.length > 0 ?
          (allItems.filter(item => item.isOverdue).length / allItems.length * 100).toFixed(2) : 0
      },
      current_workload: {
        capacity_utilization: department.workloadPercentage,
        estimated_wait_time: department.estimatedWaitTime,
        is_at_capacity: department.isAtCapacity,
        can_accept_orders: department.canAcceptNewOrder()
      },
      hourly_breakdown: await this.getHourlyBreakdown(department.id, startDate)
    }

    return response.json(stats)
  }

  /**
   * @summary Get staff workload distribution
   * @description Get workload distribution across staff members in department
   * @paramPath department_id required string - Department ID
   * @paramQuery include_inactive - Include inactive staff
   * @responseBody 200 - Staff workload distribution
   */
  @bind()
  public async getStaffWorkloadDistribution({ request, response }: HttpContextContract, department: Department) {
    const { include_inactive = 'false' } = request.qs()

    // Get department staff
    await department.load('staff', (staffQuery) => {
      if (include_inactive === 'false') {
        staffQuery.wherePivot('active', true)
      }
    })

    const staffWorkloads = []

    for (const staff of department.staff) {
      // Get current assignments
      const currentItems = await OrderItem.query()
        .where('assigned_staff_id', staff.id)
        .where('department_id', department.id)
        .whereIn('status', ['pending', 'preparing', 'ready'])
        .preload('order')
        .exec()

      // Get today's completed items
      const todayCompleted = await OrderItem.query()
        .where('assigned_staff_id', staff.id)
        .where('department_id', department.id)
        .whereIn('status', ['served'])
        .where('served_at', '>=', DateTime.now().startOf('day').toSQL())
        .exec()

      const workload = {
        staff: {
          ...staff.serialize(),
          assignment: {
            role: staff.$pivot.role,
            skill_level: staff.$pivot.skill_level,
            performance_rating: staff.$pivot.performance_rating,
            is_primary_department: staff.$pivot.is_primary_department
          }
        },
        current_workload: {
          total_assigned: currentItems.length,
          pending: currentItems.filter(item => item.status === 'pending').length,
          preparing: currentItems.filter(item => item.status === 'preparing').length,
          ready: currentItems.filter(item => item.status === 'ready').length,
          overdue: currentItems.filter(item => item.isOverdue).length
        },
        performance_today: {
          completed_items: todayCompleted.length,
          average_completion_time: this.calculateAveragePreparationTime(todayCompleted),
          efficiency_score: this.calculateEfficiencyScore(todayCompleted)
        },
        availability: {
          is_available: this.isStaffAvailable(staff, currentItems.length),
          can_take_more: currentItems.length < (department.workflowSettings?.maxItemsPerStaff || 5)
        }
      }

      staffWorkloads.push(workload)
    }

    // Sort by current workload (highest first)
    staffWorkloads.sort((a, b) => b.current_workload.total_assigned - a.current_workload.total_assigned)

    return response.json({
      department_id: department.id,
      department_name: department.name,
      total_staff: staffWorkloads.length,
      available_staff: staffWorkloads.filter(s => s.availability.is_available).length,
      staff_workloads: staffWorkloads
    })
  }

  /**
   * @summary Get overdue items
   * @description Get all overdue items in department with escalation info
   * @paramPath department_id required string - Department ID
   * @responseBody 200 - Overdue items list
   */
  @bind()
  public async getOverdueItems({ response }: HttpContextContract, department: Department) {
    const overdueItems = await OrderItem.query()
      .where('department_id', department.id)
      .whereIn('status', ['preparing', 'delayed'])
      .preload('order')
      .preload('product')
      .preload('assignedStaff')
      .exec()

    const filteredOverdueItems = overdueItems.filter(item => item.isOverdue)

    return response.json({
      department_id: department.id,
      department_name: department.name,
      total_overdue: filteredOverdueItems.length,
      items: filteredOverdueItems.map(item => ({
        ...item.serialize(),
        overdue_minutes: item.preparationStartedAt ?
          DateTime.now().diff(item.preparationStartedAt, 'minutes').minutes - (item.estimatedPreparationTime || 0) : 0,
        escalation_level: this.getEscalationLevel(item),
        assigned_staff: item.assignedStaff?.serialize(),
        requires_attention: item.requiresAttention
      })).sort((a, b) => b.overdue_minutes - a.overdue_minutes)
    })
  }

  /**
   * @summary Get ready items for serving
   * @description Get all items ready for serving in department
   * @paramPath department_id required string - Department ID
   * @responseBody 200 - Ready items list
   */
  @bind()
  public async getReadyItems({ response }: HttpContextContract, department: Department) {
    const readyItems = await OrderItem.query()
      .where('department_id', department.id)
      .where('status', 'ready')
      .preload('order', (orderQuery) => {
        orderQuery.preload('customer')
      })
      .preload('product')
      .preload('modifiers')
      .orderBy('preparation_completed_at', 'asc')
      .exec()

    return response.json({
      department_id: department.id,
      department_name: department.name,
      total_ready: readyItems.length,
      items: readyItems.map(item => ({
        ...item.serialize(),
        ready_duration: item.preparationCompletedAt ?
          DateTime.now().diff(item.preparationCompletedAt, 'minutes').minutes : 0,
        order_info: {
          id: item.order.id,
          type: item.order.type,
          delivery: item.order.delivery,
          customer_name: item.order.customer?.name,
          table_number: item.order.meta?.table_number
        },
        can_be_served: item.canBeServed
      }))
    })
  }

  /**
   * @summary Optimize staff assignments
   * @description Get recommendations for optimizing staff assignments
   * @paramPath department_id required string - Department ID
   * @responseBody 200 - Optimization recommendations
   */
  @bind()
  public async getOptimizationRecommendations({ response }: HttpContextContract, department: Department) {
    await department.load('staff', (staffQuery) => {
      staffQuery.wherePivot('active', true)
    })

    // Get current workload
    const currentItems = await OrderItem.query()
      .where('department_id', department.id)
      .whereIn('status', ['pending', 'preparing'])
      .preload('assignedStaff')
      .exec()

    const recommendations = []

    // Check for unassigned items
    const unassignedItems = currentItems.filter(item => !item.assignedStaffId)
    if (unassignedItems.length > 0) {
      recommendations.push({
        type: 'unassigned_items',
        priority: 'high',
        message: `${unassignedItems.length} items need staff assignment`,
        action: 'assign_staff',
        items: unassignedItems.map(item => item.id)
      })
    }

    // Check for overloaded staff
    const staffWorkloads = new Map()
    currentItems.forEach(item => {
      if (item.assignedStaffId) {
        const current = staffWorkloads.get(item.assignedStaffId) || 0
        staffWorkloads.set(item.assignedStaffId, current + 1)
      }
    })

    const maxItemsPerStaff = department.workflowSettings?.maxItemsPerStaff || 5
    for (const [staffId, workload] of staffWorkloads) {
      if (workload > maxItemsPerStaff) {
        recommendations.push({
          type: 'overloaded_staff',
          priority: 'medium',
          message: `Staff member has ${workload} items (max: ${maxItemsPerStaff})`,
          action: 'redistribute_workload',
          staff_id: staffId,
          excess_items: workload - maxItemsPerStaff
        })
      }
    }

    return response.json({
      department_id: department.id,
      department_name: department.name,
      optimization_score: this.calculateOptimizationScore(recommendations),
      recommendations,
      current_metrics: {
        total_items: currentItems.length,
        assigned_items: currentItems.filter(item => item.assignedStaffId).length,
        unassigned_items: unassignedItems.length,
        average_workload_per_staff: department.staff.length > 0 ?
          currentItems.length / department.staff.length : 0
      }
    })
  }

  // Private helper methods
  private calculateAveragePreparationTime(items: OrderItem[]): number {
    const completedItems = items.filter(item => item.actualPreparationTime)
    if (completedItems.length === 0) return 0

    const totalTime = completedItems.reduce((sum, item) => sum + (item.actualPreparationTime || 0), 0)
    return Math.round((totalTime / completedItems.length) * 100) / 100
  }

  private calculateEfficiencyScore(items: OrderItem[]): number {
    const scoredItems = items.filter(item =>
      item.actualPreparationTime && item.estimatedPreparationTime
    )

    if (scoredItems.length === 0) return 0

    const efficiencyScores = scoredItems.map(item => {
      const efficiency = item.estimatedPreparationTime! / item.actualPreparationTime!
      return Math.min(5, Math.max(0, efficiency))
    })

    const averageEfficiency = efficiencyScores.reduce((sum, score) => sum + score, 0) / efficiencyScores.length
    return Math.round(averageEfficiency * 100) / 100
  }

  private isStaffAvailable(staff: User, currentWorkload: number): boolean {
    // Check if staff is within working hours and not overloaded
    const maxWorkload = 5 // This could come from department settings
    return currentWorkload < maxWorkload
  }

  private getEscalationLevel(item: OrderItem): 'low' | 'medium' | 'high' | 'critical' {
    if (!item.preparationStartedAt || !item.estimatedPreparationTime) return 'low'

    const overdueMinutes = DateTime.now().diff(item.preparationStartedAt, 'minutes').minutes - item.estimatedPreparationTime

    if (overdueMinutes > 30) return 'critical'
    if (overdueMinutes > 15) return 'high'
    if (overdueMinutes > 5) return 'medium'
    return 'low'
  }

  private calculateOptimizationScore(recommendations: any[]): number {
    const weights = { high: 3, medium: 2, low: 1 }
    const totalWeight = recommendations.reduce((sum, rec) => sum + weights[rec.priority], 0)

    // Score from 0-100, where 100 is perfectly optimized (no recommendations)
    return Math.max(0, 100 - totalWeight * 10)
  }

  private async getHourlyBreakdown(departmentId: string, startDate: DateTime): Promise<any[]> {
    const hours = []
    const now = DateTime.now()

    for (let hour = startDate.hour; hour <= now.hour; hour++) {
      const hourStart = startDate.set({ hour, minute: 0, second: 0 })
      const hourEnd = hourStart.plus({ hours: 1 })

      const hourItems = await OrderItem.query()
        .where('department_id', departmentId)
        .where('created_at', '>=', hourStart.toSQL())
        .where('created_at', '<', hourEnd.toSQL())
        .exec()

      hours.push({
        hour: hour.toString().padStart(2, '0') + ':00',
        total_items: hourItems.length,
        completed: hourItems.filter(item => ['ready', 'served'].includes(item.status)).length,
        pending: hourItems.filter(item => item.status === 'pending').length,
        preparing: hourItems.filter(item => item.status === 'preparing').length
      })
    }

    return hours
  }
}
