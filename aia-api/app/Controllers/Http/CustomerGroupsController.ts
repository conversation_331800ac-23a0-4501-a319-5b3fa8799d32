import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import User from '../../Models/User'

export default class CustomerGroupsController {
  @bind()
  /**
   * @index
   * @summary Show all services
   * @version 1.0.0
   * @description Service management for the application
   * @paramUse (filterable)
   * @paramPath userId required number - User ID
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  public async index({ request }: HttpContextContract, user: User) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const serviceQuery = user.related('groups').query().filter(filters)

    return await serviceQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()
  /**
   * @store
   * @summary Create a service
   * @description Create a service with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @paramPath userId required number - User ID
   * @responseBody 200 - <Service>
   */
  public async store({ request, response }: HttpContextContract, user: User) {
    const { groupId } = request.all()
    const service = await user.related('groups').attach([groupId])

    return response.json(service)
  }
}
