import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import ProductType from 'App/Models/ProductType'
import ProductCategory from 'App/Models/ProductCategory'

export default class ProductTypeCategoryProductsController {
  /**
   * @index
   * @summary Get products by product type and category
   * @description Get a limited number of products filtered by product type and category
   *
   * @paramQuery limitProduct - Number of products to return
   * @paramQuery categoryId - Category ID to filter by
   * @responseBody 200 - Array of products with basic information
   */
  @bind()
  public async index({ request, response }: HttpContextContract, type: ProductType) {
    const { limitProduct = 25, categoryId } = request.qs()

    if (!categoryId) {
      return response.badRequest({ message: 'Category ID is required' })
    }

    // Ensure limitProduct is reasonable (min 1, max 100)
    const productLimit = Math.min(Math.max(parseInt(limitProduct) || 25, 1), 100)

    // Verify the category belongs to this product type
    const category = await ProductCategory.query()
      .where('id', categoryId)
      .where('productTypeId', type.id)
      .firstOrFail()

    // Get products with basic information
    const products = await category
      .related('products')
      .query()
      .select(['id', 'name', 'price', 'image', 'vendorId'])
      .where('active', true)
      .preload('vendor', (vendorQuery) => {
        vendorQuery.select(['id', 'name'])
      })
      .limit(productLimit)

    return response.json({
      data: products,
      meta: {
        total: products.length,
        limit: productLimit,
        categoryId: categoryId,
        productTypeId: type.id
      }
    })
  }
}
