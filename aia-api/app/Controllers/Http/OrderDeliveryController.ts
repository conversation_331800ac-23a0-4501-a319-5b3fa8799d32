import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'
import Vendor from 'App/Models/Vendor'
import User from 'App/Models/User'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { bind } from '@adonisjs/route-model-binding'

import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'

// Delivery Provider Notifications
import DeliveryProviderOrderAssigned from 'App/Notifications/DeliveryProvider/DeliveryProviderOrderAssigned'
import DeliveryProviderPickupReady from 'App/Notifications/DeliveryProvider/DeliveryProviderPickupReady'

/**
 * @summary Order Delivery Management
 * @group Order Delivery
 * @version 1.0.0
 * @description Phase 1 & 2A: Manage delivery assignments, status updates, and tracking with notification workflows
 */
export default class OrderDeliveryController {
  /**
   * @assignVendor
   * @summary Assign a delivery vendor to an order
   * @description Phase 1: Assign a delivery provider to handle order delivery with estimated times and fees
   * @tag Order Delivery
   * @paramPath id - Order ID - @example(order-uuid-123)
   * @requestBody {"deliveryVendorId": "vendor-uuid-456", "deliveryBranchId": "branch-uuid-789", "estimatedDeliveryTime": 45, "deliveryFee": 5.99}
   *
   * @responseBody 200 - {"id": "string", "deliveryVendorId": "string", "deliveryStatus": "assigned", "estimatedDeliveryTime": 45, "deliveryFee": 5.99}
   * @responseBody 400 - {"message": "Failed to assign delivery vendor", "error": "string"}
   * @responseBody 404 - Order not found
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async assignVendor({ request, response }: HttpContextContract, order: Order) {
    const deliverySchema = schema.create({
      deliveryVendorId: schema.string({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      deliveryBranchId: schema.string.optional({}, [rules.exists({ table: 'branches', column: 'id' })]),
      estimatedDeliveryTime: schema.number.optional(),
      deliveryFee: schema.number.optional(),
      deliveryNotes: schema.object().anyMembers(),
    })

    const payload = await request.validate({ schema: deliverySchema })

    // Start a transaction
    const trx = await Database.transaction()

    try {
      // Update order with delivery details
      order.useTransaction(trx)
      await order.merge({
        deliveryVendorId: payload.deliveryVendorId,
        deliveryBranchId: payload.deliveryBranchId,
        deliveryStatus: 'assigned' as const,
        estimatedDeliveryTime: payload.estimatedDeliveryTime,
        deliveryFee: payload.deliveryFee,
        deliveryNotes: payload.deliveryNotes,
      }).save()

      // Update order status if needed
      if (order.status === 'Ready') {
        await order.merge({ status: 'Delivering' }).save()
      }

      await trx.commit()

      // Load relationships
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')
      await order.load('customer')
      await order.load('vendor')

      // Send delivery provider assignment notification
      await this.sendDeliveryAssignmentNotifications(order)

      return response.ok(order)
    } catch (error) {
      await trx.rollback()
      return response.badRequest({ message: 'Failed to assign delivery vendor', error })
    }
  }

  /**
   * @updateStatus
   * @summary Update delivery status
   * @description Update the delivery status of an order with tracking data and notes
   * @tag Order Delivery
   * @paramPath id - Order ID - @example(order-uuid-123)
   * @requestBody {"status": "picked_up", "trackingData": {}, "notes": "Package picked up successfully"}
   *
   * @responseBody 200 - {"id": "string", "deliveryStatus": "picked_up", "trackingData": {}, "pickupTime": "2024-01-15T14:30:00Z"}
   * @responseBody 400 - {"message": "Failed to update delivery status", "error": "string"}
   * @responseBody 404 - Order not found
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async updateStatus({ request, response }: HttpContextContract, order: Order) {
    const statusSchema = schema.create({
      status: schema.enum(['picked_up', 'in_transit', 'delivered', 'failed', 'cancelled']),
      trackingData: schema.object().anyMembers(),
      notes: schema.string.optional(),
    })

    const payload = await request.validate({ schema: statusSchema })

    // Start a transaction
    const trx = await Database.transaction()

    try {
      order.useTransaction(trx)

      // Update delivery status
      await order.merge({
        deliveryStatus: payload.status as 'picked_up' | 'in_transit' | 'delivered' | 'failed' | 'cancelled',
        trackingData: payload.trackingData,
        deliveryNotes: { ...order.deliveryNotes, statusUpdate: payload.notes },
      }).save()

      // Update timestamps based on status
      switch (payload.status) {
        case 'picked_up':
          await order.merge({ pickupTime: DateTime.now() }).save()
          break
        case 'delivered':
          await order.merge({
            actualDeliveryTime: DateTime.now(),
            status: 'Delivered'
          }).save()
          break
        case 'failed':
        case 'cancelled':
          await order.merge({ status: 'Cancelled' }).save()
          break
      }

      await trx.commit()

      // Load relationships for notifications
      await order.load('deliveryVendor')
      await order.load('customer')
      await order.load('vendor')

      // Send status-specific notifications
      await this.sendStatusUpdateNotifications(order, payload.status)

      return response.ok(order)

      await trx.commit()

      // Load relationships
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok(order)
    } catch (error) {
      await trx.rollback()
      return response.badRequest({ message: 'Failed to update delivery status', error })
    }
  }

  /**
   * @updateTracking
   * @summary Update tracking information
   * @description Update real-time tracking data and location information for an order
   * @tag Order Delivery
   * @paramPath id - Order ID - @example(order-uuid-123)
   * @requestBody {"trackingData": {}, "location": {}, "estimatedDeliveryTime": 30}
   *
   * @responseBody 200 - {"id": "string", "trackingData": {}, "estimatedDeliveryTime": 30, "deliveryVendor": {}}
   * @responseBody 400 - {"message": "Failed to update tracking information", "error": "string"}
   * @responseBody 404 - Order not found
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async updateTracking({ request, response }: HttpContextContract, order: Order) {
    const trackingSchema = schema.create({
      trackingData: schema.object().anyMembers(),
      location: schema.object().anyMembers(),
      estimatedDeliveryTime: schema.number.optional(),
    })

    const payload = await request.validate({ schema: trackingSchema })

    try {
      await order.merge({
        trackingData: payload.trackingData,
        estimatedDeliveryTime: payload.estimatedDeliveryTime,
      }).save()

      // Load relationships
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok(order)
    } catch (error) {
      return response.badRequest({ message: 'Failed to update tracking information', error })
    }
  }

  /**
   * @recordSignature
   * @summary Record delivery signature
   * @description Record customer signature and delivery confirmation notes upon successful delivery
   * @tag Order Delivery
   * @paramPath id - Order ID - @example(order-uuid-123)
   * @requestBody {
   *   "signatureImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
   *   "notes": "Package delivered to customer at front door"
   * }
   *
   * @responseBody 200 - {"id": "string", "signatureImage": "string", "deliveryNotes": {}, "status": "Delivered"}
   * @responseBody 400 - {"message": "Failed to record signature", "error": "string"}
   * @responseBody 404 - Order not found
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async recordSignature({ request, response }: HttpContextContract, order: Order) {
    const signatureSchema = schema.create({
      signatureImage: schema.string(),
      notes: schema.string.optional(),
    })

    const payload = await request.validate({ schema: signatureSchema })

    try {
      await order.merge({
        signatureImage: payload.signatureImage,
        deliveryNotes: { ...order.deliveryNotes, signatureNotes: payload.notes },
      }).save()

      return response.ok(order)
    } catch (error) {
      return response.badRequest({ message: 'Failed to record signature', error })
    }
  }

  /**
   * @getTracking
   * @summary Get delivery tracking information
   * @description Retrieve real-time tracking information and delivery status for an order
   * @tag Order Delivery
   * @paramPath id - Order ID - @example(order-uuid-123)
   * @responseBody 200 - {"orderId": "string", "trackingCode": "TRK123456", "status": "in_transit", "trackingData": {}, "estimatedDeliveryTime": 30, "pickupTime": "2024-01-15T14:30:00Z"}
   * @responseBody 400 - {"message": "Failed to get tracking information", "error": "string"}
   * @responseBody 404 - Order not found
   */
  @bind()
  public async getTracking({ response }: HttpContextContract, order: Order) {
    try {
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok({
        orderId: order.id,
        trackingCode: order.trackingCode,
        status: order.deliveryStatus,
        trackingData: order.trackingData,
        estimatedDeliveryTime: order.estimatedDeliveryTime,
        actualDeliveryTime: order.actualDeliveryTime,
        pickupTime: order.pickupTime,
        deliveryVendor: order.deliveryVendor,
        deliveryBranch: order.deliveryBranch,
      })
    } catch (error) {
      return response.badRequest({ message: 'Failed to get tracking information', error })
    }
  }

  /**
   * Send delivery assignment notifications to delivery provider and other stakeholders
   */
  private async sendDeliveryAssignmentNotifications(order: Order) {
    try {
      // Get delivery provider vendor and user
      const deliveryVendor = await Vendor.query()
        .where('id', order.deliveryVendorId!)
        .preload('user')
        .first()

      if (!deliveryVendor?.user) {
        console.warn(`No user found for delivery vendor ${order.deliveryVendorId}`)
        return
      }

      // Prepare assignment details
      const assignmentDetails = {
        pickupLocation: {
          vendorName: order.vendor.name,
          address: order.vendor.address || 'Address not available',
          coordinates: {
            lat: order.vendor.latitude || 0,
            lng: order.vendor.longitude || 0
          },
          contactPhone: order.vendor.phone || 'Phone not available',
        },
        deliveryLocation: {
          customerName: order.customer.name,
          address: order.deliveryAddress || 'Address not available',
          coordinates: {
            lat: order.deliveryLatitude || 0,
            lng: order.deliveryLongitude || 0
          },
          contactPhone: order.customer.phone || 'Phone not available',
        },
        orderDetails: {
          totalItems: order.orderItems?.length || 0,
          estimatedWeight: order.estimatedWeight || null,
          specialInstructions: order.deliveryNotes?.instructions || null,
          deliveryFee: order.deliveryFee || 0,
        },
        timing: {
          estimatedPickupTime: DateTime.now().plus({ minutes: 30 }), // Default 30 min prep time
          estimatedDeliveryTime: DateTime.now().plus({ minutes: order.estimatedDeliveryTime || 60 }),
          deliveryWindow: order.deliveryWindow || null,
        },
        assignmentExpiresAt: DateTime.now().plus({ minutes: 5 }), // 5 minute acceptance window
      }

      // Send notification to delivery provider
      await deliveryVendor.user.notify(
        new DeliveryProviderOrderAssigned(order, assignmentDetails)
      )

      console.log(`Delivery assignment notification sent to vendor ${deliveryVendor.id} for order ${order.id}`)
    } catch (error) {
      console.error('Failed to send delivery assignment notifications:', error)
    }
  }

  /**
   * Send status update notifications based on delivery status changes
   */
  private async sendStatusUpdateNotifications(order: Order, status: string) {
    try {
      // Only send pickup ready notification when order status changes to ready for pickup
      if (status === 'ready_for_pickup' || (order.status === 'Ready' && order.deliveryStatus === 'assigned')) {
        await this.sendPickupReadyNotification(order)
      }

      // Add other status-specific notifications here as needed
      // e.g., in_transit, delivered notifications to customers
    } catch (error) {
      console.error('Failed to send status update notifications:', error)
    }
  }

  /**
   * Send pickup ready notification to delivery provider
   */
  private async sendPickupReadyNotification(order: Order) {
    try {
      // Get delivery provider vendor and user
      const deliveryVendor = await Vendor.query()
        .where('id', order.deliveryVendorId!)
        .preload('user')
        .first()

      if (!deliveryVendor?.user) {
        console.warn(`No user found for delivery vendor ${order.deliveryVendorId}`)
        return
      }

      // Prepare pickup details
      const pickupDetails = {
        pickupLocation: {
          vendorName: order.vendor.name,
          address: order.vendor.address || 'Address not available',
          coordinates: {
            lat: order.vendor.latitude || 0,
            lng: order.vendor.longitude || 0
          },
          contactPhone: order.vendor.phone || 'Phone not available',
        },
        preparationCompletedAt: DateTime.now(),
        pickupDeadline: DateTime.now().plus({ hours: 1 }), // 1 hour pickup window
        specialHandlingNotes: order.deliveryNotes?.handling || null,
        verificationCode: this.generateVerificationCode(),
      }

      // Send notification to delivery provider
      await deliveryVendor.user.notify(
        new DeliveryProviderPickupReady(order, pickupDetails)
      )

      console.log(`Pickup ready notification sent to vendor ${deliveryVendor.id} for order ${order.id}`)
    } catch (error) {
      console.error('Failed to send pickup ready notification:', error)
    }
  }

  /**
   * Generate a simple verification code for pickup
   */
  private generateVerificationCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase()
  }
}