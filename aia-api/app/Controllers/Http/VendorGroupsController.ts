import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import Group from 'App/Models/Group'

/**
 * @name Vendor Group management
 * @version 1.0.0
 * @description Vendor Group management for the application
 */
export default class VendorGroupsController {
  @bind()
  /**
   * @index
   * @summary List all groups for a vendor
   * @description List all groups for a vendor, paginated
   * @paramUse (filterable)
   * @paramPath vendorId required string - Vendor ID
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @responseBody 200 - <Group>
   */
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const groupQuery = Group.filter(filters)
      .preload('members')
      .where('vendorId', vendor.id)

    return await groupQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()
  /**
   * @store
   * @summary Create a group for a vendor
   * @description Create a group with their details (name and details)
   * @paramPath vendorId required string - Vendor ID
   * @requestBody {"name": "", "details": "", "branchId": ""}
   * @responseBody 200 - <Group>
   */
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { name, details, branchId } = request.all()
    const group = await vendor.related('groups').create({ name, details, branchId })

    const image = request.file('image')
    if (image) {
      await group.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(group)
  }
} 