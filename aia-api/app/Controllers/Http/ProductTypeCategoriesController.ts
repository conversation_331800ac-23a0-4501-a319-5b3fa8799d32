import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import ProductType from 'App/Models/ProductType'
import ProductCategory from 'App/Models/ProductCategory'

export default class ProductTypeCategoriesController {
  @bind()
  /**
   * @index
   *
   * @summary List all categories
   * @description List all categories, paginated for a specific product type
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Current page
   * @paramQuery order - Order by
   * @paramQuery sort - Sort by
   * @paramQuery with - Include relationships
   *
   * @responseType <ProductCategory>
   */
  public async index({ request }: HttpContextContract, type: ProductType) {
    const { per = 20, page = 1, order = 'createdAt', sort = 'desc', productsPerCategory = 10, ...filters } = request.qs()
    const categoryQuery = ProductCategory.query().where('productTypeId', type.id).filter(filters)

    if (filters.with) {
      categoryQuery.preload('products', (pq) => {
        // Use configurable limit instead of hard-coded 5, with reasonable maximum
        const productLimit = Math.min(parseInt(productsPerCategory) || 10, 50)
        pq.limit(productLimit).preload('vendor')
      })
    }

    return await categoryQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()
  /**
   * @store
   *
   * @summary Create a category
   * @description Create a category with their details (name and details)
   * @requestBody {"name": "", "details": "", "productTypeId": ""}
   * @responseType <ProductCategory>
   */
  public async store({ request }: HttpContextContract, type: ProductType) {
    const { name, details, productTypeId } = request.all()
    const category = await type.related('categories').create({ name, details, productTypeId })

    const image = request.file('image')

    if (image) {
      category.image = Attachment.fromFile(image)
    }

    return await category.save()
  }
}
