import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Note from '../../Models/Note'
import { bind } from '@adonisjs/route-model-binding'
import CreateNoteValidator from '../../Validators/CreateNoteValidator'
import UpdateNoteValidator from '../../Validators/UpdateNoteValidator'

/**
 * @name Note management
 * @version 1.0.0
 * @description Note management for the application
 */
export default class NotesController {
  /**
   * @index
   * @summary List all notes
   * @description List all notes, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const noteQuery = Note.filter(filters).preload('product')

    return await noteQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a note
   * @description Create a note with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Note>
   */
  public async store({ request, response }: HttpContextContract) {
    // Handle bulk creation (legacy support)
    const { notes = null } = request.all()

    if (notes) {
      const createdNotes: Note[] = []
      for (const noteData of notes) {
        const validatedData = await request.validate({
          schema: new CreateNoteValidator({ request, response } as HttpContextContract).schema,
          data: {
            content: noteData.content || 'N/A',
            meta: noteData.meta,
            type: noteData.type,
            branchId: noteData.branchId,
            userId: noteData.userId || noteData.staffId,
            productId: noteData.productId
          }
        })

        const note = await Note.create(validatedData)
        await note.load('product')
        createdNotes.push(note)
      }

      return response.json({
        message: 'Notes created successfully',
        data: createdNotes
      })
    } else {
      // Handle single note creation
      const validatedData = await request.validate(CreateNoteValidator)
      const note = await Note.create(validatedData)
      await note.load('product')

      return response.json({
        message: 'Note created successfully',
        data: note
      })
    }
  }

  @bind()
  /**
   * @show
   * @summary Show a single note
   * @description Show a note with their details and relationships
   * @paramPath id required number - Note ID
   * @responseBody 200 - <Note>
   * @response 404 - Note not found
   */
  public async show({ response }: HttpContextContract, note: Note) {
    await note.load('product')
    return response.json(note)
  }

  @bind()
  /**
   * @update
   * @summary Update a note
   * @description Update a note with their details (name and details)
   * @paramPath id required number - Note ID
   * @requestBody <Note>
   * @responseBody 200 - <Note>
   * @response 404 - Note not found
   */
  public async update({ request, response }: HttpContextContract, note: Note) {
    const validatedData = await request.validate(UpdateNoteValidator)

    // Handle image upload separately since it's not in validation schema
    const upload = request.file('image')
    const updateData: any = { ...validatedData }

    if (upload) {
      updateData.image = Attachment.fromFile(upload)
    }

    await note.merge(updateData).save()
    await note.load('product')

    return response.json(note)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a Note
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, note: Note) {
    return await note.delete()
  }
}
