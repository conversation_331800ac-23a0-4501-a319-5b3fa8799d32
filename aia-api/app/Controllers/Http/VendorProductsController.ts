import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import Product from 'App/Models/Product'
import PackagingOption from 'App/Models/PackagingOption'

/**
 * @name Product management
 * @version 1.0.0
 * @description Product management for the application
 */
export default class VendorProductsController {
  /**
   * @index
   * @summary List all products
   * @description List all products, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const {
      per = 25,
      page = 1,
      order = 'createdAt',
      sort = 'asc',
      include_modifiers = 'true',
      ...filters
    } = request.qs()
    const productQuery = Product.filter(filters)
      .preload('vendor')
      .preload('category')
      .where('vendorId', vendor.id)

    // Load modifiers if requested (default: true for menu display)
    if (include_modifiers === 'true') {
      productQuery.preload('availableModifiers')
    }

    const products = await productQuery.orderBy(order, sort).paginate(page, per)

    // If modifiers are loaded, format them properly for frontend consumption
    if (include_modifiers === 'true') {
      // Convert to JSON and create a completely new response object
      const productsJson = products.toJSON()

      // Transform each product's modifiers
      const transformedData = productsJson.data.map((product) => {
        // Create a clean product object with only the necessary fields
        const transformedProduct = {
          id: product.id,
          ref: product.ref,
          name: product.name,
          details: product.details,
          price: product.price,
          discounted: product.discounted,
          stock: product.stock,
          active: product.active,
          featured: product.featured,
          type: product.type,
          condition: product.condition,
          status: product.status,
          availability: product.availability,
          shipping: product.shipping,
          unit: product.unit,
          mode: product.mode,
          payment: product.payment,
          visibility: product.visibility,
          productCategoryId: product.productCategoryId,
          userId: product.userId,
          vendorId: product.vendorId,
          branchId: product.branchId,
          serviceId: product.serviceId,
          image: product.image,
          meta: product.meta,
          extra: product.extra,
          expiresAt: product.expiresAt,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
          category: product.category,
          vendor: product.vendor,
          hasFulfillmentSettings: product.hasFulfillmentSettings,
        }

        if (product.availableModifiers && product.availableModifiers.length > 0) {
          // Group modifiers by type and format them
          const modifiers = product.availableModifiers.reduce((acc, modifier) => {
            const type = modifier.type
            if (!acc[type]) {
              acc[type] = []
            }

            // Check different possible locations for pivot data
            const pivotData = modifier.$extras || modifier.pivot || {}

            acc[type].push({
              id: modifier.id,
              name: modifier.name,
              description: modifier.description,
              type: modifier.type,
              defaultPriceAdjustment: modifier.defaultPriceAdjustment,
              price_adjustment:
                pivotData.pivot_price_adjustment_override ?? modifier.defaultPriceAdjustment,
              is_default: pivotData.pivot_is_default ?? false,
              sort_order: pivotData.pivot_sort_order ?? 0,
            })
            return acc
          }, {})

          // Sort each type's modifiers by sort_order
          Object.keys(modifiers).forEach((type) => {
            modifiers[type].sort((a, b) => a.sort_order - b.sort_order)
          })

          transformedProduct.modifiers = modifiers
        } else {
          transformedProduct.modifiers = {}
        }

        // Remove the raw availableModifiers to clean up response
        delete transformedProduct.availableModifiers
        return transformedProduct
      })

      // Return a completely new response object
      return {
        meta: productsJson.meta,
        data: transformedData,
      }
    }

    return products
  }

  /**
   * @store
   * @summary Create a product
   * @description Create a product with their details including packaging options for a specific vendor
   * @requestBody {
   *   "name": "Product Name",
   *   "details": "Product description",
   *   "price": 1000,
   *   "packagingOptionIds": ["pkg_opt_123", "pkg_opt_456"]
   * }
   * @responseBody 200 - <Product>
   * @response 400 - Bad Request - Invalid packaging options or validation errors
   */
  @bind()
  public async store({ request, response, auth }: HttpContextContract, vendor: Vendor) {
    const {
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type = 'Digital',
      condition = 'New',
      status = 'Draft',
      availability = 'In Stock',
      shipping = 'Free',
      unit = 'other',
      mode = 'Single',
      payment = 'Prepaid',
      visibility = 'Public',
      productCategoryId,
      vendorId,
      meta,
      extra,
      packagingOptionIds = [],
    } = request.all()

    const product = await vendor.related('products').create({
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type,
      condition,
      status,
      availability,
      shipping,
      unit,
      mode,
      visibility,
      payment,
      productCategoryId,
      userId: auth.user?.id,
      vendorId,
      meta,
      extra,
    })

    const image = request.file('image')

    if (image) {
      await product.merge({ image: Attachment.fromFile(image) }).save()
    }

    // Handle packaging options
    if (packagingOptionIds && packagingOptionIds.length > 0) {
      // Validate that all packaging options belong to the vendor
      const packagingOptions = await PackagingOption.query()
        .whereIn('id', packagingOptionIds)
        .where('vendorId', vendor.id)
        .where('active', true)

      if (packagingOptions.length !== packagingOptionIds.length) {
        return response.badRequest({
          message: 'Some packaging options are invalid or do not belong to this vendor',
          details: 'All packaging options must belong to this vendor and be active'
        })
      }

      await product.related('packagingOptions').sync(packagingOptionIds)
    }

    return response.json(product)
  }

  /**
   * @update
   * @summary Update a product
   * @description Update a product with their details including packaging options for a specific vendor
   * @requestBody {
   *   "name": "Updated Product Name",
   *   "details": "Updated description",
   *   "price": 1200,
   *   "packagingOptionIds": ["pkg_opt_123"]
   * }
   * @responseBody 200 - <Product>
   * @response 400 - Bad Request - Invalid packaging options or validation errors
   * @response 404 - Not Found - Product not found for this vendor
   */
  @bind()
  public async update({ request, response }: HttpContextContract, vendor: Vendor, product: Product) {
    // Ensure the product belongs to the vendor
    if (product.vendorId !== vendor.id) {
      return response.notFound({
        message: 'Product not found for this vendor'
      })
    }

    const {
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type,
      condition,
      status,
      availability,
      shipping,
      unit,
      mode,
      payment,
      visibility,
      productCategoryId,
      meta,
      extra,
      packagingOptionIds,
    } = request.all()

    try {
      await product.merge({
        ...(name !== undefined && { name }),
        ...(ref !== undefined && { ref }),
        ...(details !== undefined && { details }),
        ...(price !== undefined && { price }),
        ...(discounted !== undefined && { discounted }),
        ...(stock !== undefined && { stock }),
        ...(active !== undefined && { active }),
        ...(featured !== undefined && { featured }),
        ...(type !== undefined && { type }),
        ...(condition !== undefined && { condition }),
        ...(status !== undefined && { status }),
        ...(availability !== undefined && { availability }),
        ...(shipping !== undefined && { shipping }),
        ...(unit !== undefined && { unit }),
        ...(mode !== undefined && { mode }),
        ...(payment !== undefined && { payment }),
        ...(visibility !== undefined && { visibility }),
        ...(productCategoryId !== undefined && { productCategoryId }),
        ...(meta !== undefined && { meta }),
        ...(extra !== undefined && { extra }),
      }).save()

      const image = request.file('image')
      if (image) {
        await product.merge({ image: Attachment.fromFile(image) }).save()
      }

      // Handle packaging options
      if (packagingOptionIds !== undefined) {
        if (packagingOptionIds && packagingOptionIds.length > 0) {
          // Validate that all packaging options belong to the vendor
          const packagingOptions = await PackagingOption.query()
            .whereIn('id', packagingOptionIds)
            .where('vendorId', vendor.id)
            .where('active', true)

          if (packagingOptions.length !== packagingOptionIds.length) {
            return response.badRequest({
              message: 'Some packaging options are invalid or do not belong to this vendor',
              details: 'All packaging options must belong to this vendor and be active'
            })
          }
        }

        await product.related('packagingOptions').sync(packagingOptionIds || [])
      }

      // Load relationships for response
      await product.load('packagingOptions')

      return response.json(product)
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to update product',
        error: error.message
      })
    }
  }
}
