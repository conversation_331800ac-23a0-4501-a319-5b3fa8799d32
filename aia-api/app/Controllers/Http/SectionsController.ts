import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Section from '../../Models/Section'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Section management
 * @version 1.0.0
 * @description Section management for the application
 */
export default class SectionsController {
  /**
   * @index
   * @summary List all sections
   * @description List all sections, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const sectionQuery = Section.filter(filters).preload('branch')

    return await sectionQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a section
   * @description Create a section with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Section>
   */
  public async store({ request, response }: HttpContextContract) {
    const { branchId, name, details } = request.all()

    const section = await Section.create({ branchId, name, details })

    const image = request.file('image')

    if (image) {
      await section.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(section)
  }

  @bind()
  /**
   * @show
   * @summary Show a single section
   * @description Show a section with their details (name and details)
   * @paramPath id required number - Section ID
   * @responseBody 200 - <Section>
   * @response 404 - Section not found
   */
  public async show({ response }: HttpContextContract, section: Section) {
    return response.json(section)
  }

  @bind()
  /**
   * @update
   * @summary Update a section
   * @description Update a section with their details (name and details)
   * @paramPath id required number - Section ID
   * @requestBody <Section>
   * @responseBody 200 - <Section>
   * @response 404 - Section not found
   */
  public async update({ request, response }: HttpContextContract, section: Section) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await section.merge(input).save()

    return response.json(section)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a Section
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, section: Section) {
    return await section.delete()
  }
}
