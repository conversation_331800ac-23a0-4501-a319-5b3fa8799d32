import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'

export default class ServiceConfigurationOptionsController {
  /**
   * Display options for a specific service configuration
   */
  public async index({ params, request, response }: HttpContextContract) {
    try {
      const configurationId = params.configurationId
      const type = request.input('type')
      const active = request.input('active')

      const query = ServiceConfigurationOption.query()
        .where('serviceConfigurationId', configurationId)

      // Apply filters
      if (type) {
        query.where('type', type)
      }

      if (active !== undefined) {
        query.where('active', active === 'true')
      }

      // Include related data
      query
        .preload('duration')
        .orderBy(['type', 'sortOrder'])

      const options = await query

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch configuration options',
        error: error.message
      })
    }
  }

  /**
   * Create a new configuration option
   */
  public async store({ params, request, response }: HttpContextContract) {
    try {
      const configurationId = params.configurationId
      
      // Verify configuration exists
      await ServiceConfiguration.findOrFail(configurationId)

      const payload = request.only([
        'name', 'type', 'description', 'priceAdjustment', 'durationId',
        'isDefault', 'sortOrder', 'constraints', 'active'
      ])

      const option = await ServiceConfigurationOption.create({
        serviceConfigurationId: configurationId,
        ...payload
      })

      // Validate the option configuration
      const validation = await option.validateConfiguration()
      if (!validation.valid) {
        await option.delete()
        return response.status(400).json({
          success: false,
          message: 'Invalid option configuration',
          errors: validation.errors
        })
      }

      // Load related data for response
      await option.load('duration')

      return response.status(201).json({
        success: true,
        message: 'Configuration option created successfully',
        data: option
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create configuration option',
        error: error.message
      })
    }
  }

  /**
   * Display a specific configuration option
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const option = await ServiceConfigurationOption.query()
        .where('id', params.id)
        .where('serviceConfigurationId', params.configurationId)
        .preload('duration')
        .preload('serviceConfiguration')
        .firstOrFail()

      const validation = await option.validateConfiguration()
      const calendarBlockMinutes = await option.getCalendarBlockMinutes()

      return response.json({
        success: true,
        data: {
          ...option.toJSON(),
          validation,
          calendarBlockMinutes
        }
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Configuration option not found',
        error: error.message
      })
    }
  }

  /**
   * Update a configuration option
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const option = await ServiceConfigurationOption.query()
        .where('id', params.id)
        .where('serviceConfigurationId', params.configurationId)
        .firstOrFail()

      const payload = request.only([
        'name', 'type', 'description', 'priceAdjustment', 'durationId',
        'isDefault', 'sortOrder', 'constraints', 'active'
      ])

      option.merge(payload)

      // Validate the option configuration before saving
      const validation = await option.validateConfiguration()
      if (!validation.valid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid option configuration',
          errors: validation.errors
        })
      }

      await option.save()
      await option.load('duration')

      return response.json({
        success: true,
        message: 'Configuration option updated successfully',
        data: option
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update configuration option',
        error: error.message
      })
    }
  }

  /**
   * Delete a configuration option
   */
  public async destroy({ params, response }: HttpContextContract) {
    try {
      const option = await ServiceConfigurationOption.query()
        .where('id', params.id)
        .where('serviceConfigurationId', params.configurationId)
        .firstOrFail()

      // Soft delete by setting active to false
      option.active = false
      await option.save()

      return response.json({
        success: true,
        message: 'Configuration option deactivated successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to delete configuration option',
        error: error.message
      })
    }
  }

  /**
   * Create option from template
   */
  public async createFromTemplate({ params, request, response }: HttpContextContract) {
    try {
      const configurationId = params.configurationId
      const { name, type, options } = request.only(['name', 'type', 'options'])

      if (!name || !type) {
        return response.status(400).json({
          success: false,
          message: 'Name and type are required'
        })
      }

      // Verify configuration exists
      await ServiceConfiguration.findOrFail(configurationId)

      const template = ServiceConfigurationOption.createTemplate(name, type, configurationId, options || {})
      const option = await ServiceConfigurationOption.create(template)

      await option.load('duration')

      return response.status(201).json({
        success: true,
        message: 'Configuration option created from template successfully',
        data: option
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create option from template',
        error: error.message
      })
    }
  }

  /**
   * Bulk update option sort orders
   */
  public async updateSortOrders({ params, request, response }: HttpContextContract) {
    try {
      const configurationId = params.configurationId
      const { optionOrders } = request.only(['optionOrders'])

      if (!Array.isArray(optionOrders)) {
        return response.status(400).json({
          success: false,
          message: 'Option orders must be an array'
        })
      }

      const configuration = await ServiceConfiguration.findOrFail(configurationId)
      await configuration.updateOptionSortOrders(optionOrders)

      return response.json({
        success: true,
        message: 'Option sort orders updated successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update sort orders',
        error: error.message
      })
    }
  }

  /**
   * Get options by type
   */
  public async getByType({ params, response }: HttpContextContract) {
    try {
      const { configurationId, type } = params

      const options = await ServiceConfigurationOption.getByType(configurationId, type)

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch options by type',
        error: error.message
      })
    }
  }

  /**
   * Get default options
   */
  public async getDefaults({ params, response }: HttpContextContract) {
    try {
      const configurationId = params.configurationId

      const options = await ServiceConfigurationOption.getDefaults(configurationId)

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch default options',
        error: error.message
      })
    }
  }

  /**
   * Get available option types
   */
  public async getTypes({ response }: HttpContextContract) {
    try {
      const types = Object.values(ServiceOptionType).map(type => ({
        value: type,
        label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }))

      return response.json({
        success: true,
        data: types
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch option types',
        error: error.message
      })
    }
  }
}
