import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Payment from '../../Models/Payment'
import { bind } from '@adonisjs/route-model-binding'
import UpdatePaymentValidator from '../../Validators/UpdatePaymentValidator'
import StorePaymentValidator from '../../Validators/CreatePaymentValidator'
import Invoice from 'App/Models/Invoice'
import KeMpesa from 'App/Services/Mpesa'
import Order from 'App/Models/Order'
// Corrected: Removed unused TransactionClientContract import
import Database from '@ioc:Adonis/Lucid/Database'
// Updated: Removed TempOrder import - now using unified Order model
// Updated: Removed ProcessTempOrder import - will use unified conversion logic
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'
import MpesaConfigValidator from 'App/Services/MpesaConfigValidator'
import ReceiptService from 'App/Services/ReceiptService'

// Type interfaces for multi-invoice payment handling
interface InvoiceBreakdownItem {
  invoiceId: string
  invoiceNumber: string
  amount: number
  paymentId: string
  status: string
  invoice: {
    id: string
    number: string
    amount: number
    status: string
    orderId: string
  }
}

interface MultiPaymentStatus {
  isMultiPayment: boolean
  totalPayments: number
  totalAmount: number
  completedPayments: number
  successfulPayments: number
  failedPayments: number
  pendingPayments: number
  allComplete: boolean
  allSuccess: boolean
  anyFailed: boolean
  overallStatus: string
  progressPercentage: number
}

export default class PaymentsController {
  /**
   * Convert temp order to placed order in unified system
   * Replaces the old ProcessTempOrder.convert() functionality
   */
  private async convertTempOrderToPlaced(tempOrder: Order, payload: any): Promise<Order> {
    // Validate that this is actually a temp order
    if (tempOrder.status !== 'Pending') {
      throw new Error('Order is not a temp order (status must be Pending)')
    }

    // Ensure pricing is available before conversion
    await tempOrder.ensureTempOrderPricing()

    // Extract temp items from meta
    const tempItems = tempOrder.meta?.temp_items || {}

    // Update order status to Placed
    tempOrder.status = 'Placed'
    tempOrder.meta = {
      ...tempOrder.meta,
      // Keep temp_items for reference but mark as converted
      temp_items_converted: true,
      conversion_date: new Date().toISOString(),
      payment_method: payload.method,
    }

    await tempOrder.save()

    // Create order items from temp_items
    const orderItems = []
    for (const [productId, itemData] of Object.entries(tempItems)) {
      const quantity = typeof itemData === 'object' ? itemData.quantity : itemData

      // Get product details for pricing
      const product = await Database.from('products').where('id', productId).first()
      if (product) {
        orderItems.push({
          order_id: tempOrder.id,
          product_id: productId,
          quantity: quantity,
          price: product.price,
          meta: {},
        })
      }
    }

    // Insert order items
    if (orderItems.length > 0) {
      await Database.table('order_items').insert(orderItems)
    }

    // Load the order with its new items
    await tempOrder.load('items')
    await tempOrder.load('customer')

    // Associate customer with branch (maintain existing logic)
    if (tempOrder.customer) {
      await tempOrder.customer.related('branches').sync(
        {
          [tempOrder.branchId]: {
            active: true,
            vendor_id: tempOrder.vendorId,
            branch_id: tempOrder.branchId,
          },
        },
        false
      )
    }

    return tempOrder
  }

  /**
   * Get invoice amount for temp order with fallback pricing calculation
   */
  private async getTempOrderInvoiceAmount(tempOrder: Order): Promise<number> {
    try {
      // Try to get pricing from enhanced pricing system
      const pricing = await tempOrder.getTempOrderPricing()
      return pricing.invoiceAmount
    } catch (error) {
      console.warn('Failed to get enhanced pricing, falling back to legacy calculation:', error)

      // Fallback to legacy calculation
      return await tempOrder.calculateTempOrderTotal()
    }
  }
  /**
   * @name Payment management
   * @index
   * @summary List all Payments
   * @description List all Payments, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */

  public async index({ request, response, auth }: HttpContextContract) {
    const { page = 1, per = 15, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

    // SECURITY: Apply vendor-scoped role-based filtering
    const user = auth.user!

    // Preload user relationships for role-based filtering
    await user.load('roles')
    await user.load('vendors')
    await user.load('branches')

    // Get accessible order IDs based on user's role and scope
    const accessibleOrderQuery = Order.query()
    HierarchicalAccessControlService.applyVendorScopedFilter(accessibleOrderQuery, user)
    const accessibleOrders = await accessibleOrderQuery.select('id')
    const accessibleOrderIds = accessibleOrders.map((order) => order.id)

    const paymentQuery = Payment.filter(filters)
      .whereHas('invoice', (invoiceQuery) => {
        invoiceQuery.whereIn('orderId', accessibleOrderIds)
      })
      .preload('vendor')
      .preload('customer')
      .preload('invoice', (iq) => {
        iq.preload('order', (oq) => {
          oq.preload('items')
          oq.preload('staff')
          oq.preload('branch')
        })
      })

    const sum = await Database.from('payments').sum('amount as total').first()

    const payments = await paymentQuery.orderBy(order, sort).paginate(page, per)

    return response.json({ ...payments.toJSON(), sum })
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's payments
   * Used by customer apps to ensure customers only see their own payments
   */
  public async customerPayments({ request, response, auth }: HttpContextContract) {
    const { page = 1, per = 15, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const user = auth.user!

    // Get accessible order IDs based on customer-only filtering
    const accessibleOrderQuery = Order.query()
    HierarchicalAccessControlService.applyCustomerOnlyFilter(accessibleOrderQuery, user)
    const accessibleOrders = await accessibleOrderQuery.select('id')
    const accessibleOrderIds = accessibleOrders.map((order) => order.id)

    const paymentQuery = Payment.filter(filters)
      .whereHas('invoice', (invoiceQuery) => {
        invoiceQuery.whereIn('orderId', accessibleOrderIds)
      })
      .preload('vendor')
      .preload('customer')
      .preload('invoice', (iq) => {
        iq.preload('order', (oq) => {
          oq.preload('items')
          oq.preload('staff')
          oq.preload('branch')
        })
      })

    // Calculate sum for customer's payments only
    const customerSum = await Database.from('payments')
      .whereExists((query) => {
        query
          .select('*')
          .from('invoices')
          .whereRaw('invoices.id = payments.invoice_id')
          .whereIn('invoices.order_id', accessibleOrderIds)
      })
      .sum('amount as total')
      .first()

    const payments = await paymentQuery.orderBy(order, sort).paginate(page, per)

    return response.json({ ...payments.toJSON(), sum: customerSum })
  }

  /**
   * @store
   * @summary Create a Payment
   * @description Create a Payment with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Payment>
   */

  public async store({ request, response }: HttpContextContract) {
    const {
      amount,
      orderId,
      payer,
      userId,
      invoiceId: postedInvoiceId,
      invoiceIds,
      vendorId,
      method,
      receipt,
      status = 'Pending',
      description,
    } = await request.validate(StorePaymentValidator)

    let invoiceId = postedInvoiceId
    let invoice = postedInvoiceId ? await Invoice.find(postedInvoiceId) : null

    // Handle multiple existing invoices (new logic for invoiceIds array)
    if (invoiceIds && Array.isArray(invoiceIds) && invoiceIds.length > 0) {
      try {
        // Fetch all invoices to validate they exist
        const invoices = await Invoice.query().whereIn('id', invoiceIds)

        if (invoices.length !== invoiceIds.length) {
          return response.badRequest({
            message: 'One or more invoices not found',
            missingInvoices: invoiceIds.filter((id) => !invoices.find((inv) => inv.id === id)),
          })
        }

        // Calculate total amount from invoices for validation
        const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.amount, 0)

        // Create payments for each invoice
        const payments: Payment[] = []
        const trx = await Database.transaction()

        try {
          // For M-Pesa, create one primary payment and link others to it
          let primaryPayment: Payment | null = null
          let checkoutRequestID: string | null = null

          for (let i = 0; i < invoices.length; i++) {
            const invoiceItem = invoices[i]
            // Calculate proportional amount for this invoice
            const proportionalAmount = Math.ceil((invoiceItem.amount / totalInvoiceAmount) * amount)

            const payment = await Payment.create(
              {
                amount: proportionalAmount,
                userId,
                invoiceId: invoiceItem.id,
                vendorId,
                method,
                ref: invoiceItem.number,
                receipt,
                status,
              },
              { client: trx }
            )

            payments.push(payment)

            // Handle M-Pesa STK Push - ONLY for the first payment (primary)
            if ((method === 'ke.mpesa' || method.toLowerCase() === 'mpesa') && i === 0) {
              primaryPayment = payment
              const mpesa = new KeMpesa(payment)

              // Use total amount for STK push, not proportional
              const { data, error } = await mpesa.stkPush(
                payer!,
                amount.toString(), // Total amount, not proportional
                `MULTI-${payment.id.slice(-6)}`, // Unique reference for multi-invoice payment
                `multi-invoice-${payment.id}`
              )

              if (error) {
                console.error('M-Pesa STK Push Error:', error)
                await trx.rollback()
                return response.internalServerError({
                  message: 'M-Pesa STK push failed',
                  error: error,
                })
              } else {
                checkoutRequestID = data.CheckoutRequestID
                // Update primary payment with checkout request ID
                await Payment.query({ client: trx })
                  .where('id', payment.id)
                  .update({ req: checkoutRequestID })
              }
            } else if (method === 'ke.mpesa' || method.toLowerCase() === 'mpesa') {
              // For subsequent M-Pesa payments, link them to the primary payment's checkout request
              await Payment.query({ client: trx })
                .where('id', payment.id)
                .update({
                  req: checkoutRequestID,
                  receipt: `Linked to primary payment: ${primaryPayment?.id}`,
                })
            } else {
              // For non-M-Pesa payments, update invoice status based on method
              switch (method) {
                case 'Cash':
                case 'Card':
                case 'Wallet':
                  await Invoice.query({ client: trx })
                    .where('id', invoiceItem.id)
                    .update({ status: 'Paid' })
                  break
                case 'Bank':
                case 'Cheque':
                case 'Online':
                  await Invoice.query({ client: trx })
                    .where('id', invoiceItem.id)
                    .update({ status: 'Pending' })
                  break
              }
            }
          }

          await trx.commit()

          // Load relations for response
          for (const payment of payments) {
            await payment.load('invoice')
          }

          // Return primary payment with complete invoice breakdown for frontend
          const primaryPaymentResponse = primaryPayment || payments[0]
          await primaryPaymentResponse.load('invoice')

          // Load all payment invoices for detailed breakdown
          for (const payment of payments) {
            await payment.load('invoice')
          }

          // Create detailed invoice breakdown for frontend confirmation page
          const invoiceBreakdown = payments.map((payment) => ({
            invoiceId: payment.invoiceId,
            invoiceNumber: payment.invoice.number,
            amount: parseFloat(payment.amount.toString()),
            paymentId: payment.id,
            status: payment.status,
            invoice: {
              id: payment.invoice.id,
              number: payment.invoice.number,
              amount: parseFloat(payment.invoice.amount.toString()),
              status: payment.invoice.status,
              orderId: payment.invoice.orderId,
            },
          }))

          // Calculate the actual total amount from payments (ensure it's a number)
          const actualTotalAmount = payments.reduce(
            (sum, p) => sum + parseFloat(p.amount.toString()),
            0
          )

          // Enhanced response with full invoice details
          const responseData = {
            // Primary payment fields (for backward compatibility)
            id: primaryPaymentResponse.id,
            status: primaryPaymentResponse.status,
            amount: actualTotalAmount, // Use calculated total, not request amount
            method: primaryPaymentResponse.method,
            req: primaryPaymentResponse.req,
            receipt: primaryPaymentResponse.receipt,
            userId: primaryPaymentResponse.userId,
            vendorId: primaryPaymentResponse.vendorId,
            createdAt: primaryPaymentResponse.createdAt,
            updatedAt: primaryPaymentResponse.updatedAt,

            // Primary invoice (for backward compatibility)
            invoice: primaryPaymentResponse.invoice,
            invoiceId: primaryPaymentResponse.invoiceId,

            // Complete invoice breakdown for confirmation page
            invoiceBreakdown: invoiceBreakdown,

            // Summary information
            summary: {
              isMultiInvoicePayment: true,
              totalInvoices: invoices.length,
              totalAmount: actualTotalAmount,
              description: description || `Payment for ${invoiceIds.length} invoice(s)`,
              paymentMethod: method,
              allPaymentIds: payments.map((p) => p.id),
            },

            // STK Push information (for M-Pesa)
            stkPushInfo:
              method === 'ke.mpesa' || method.toLowerCase() === 'mpesa'
                ? {
                    checkoutRequestID: checkoutRequestID,
                    primaryPaymentId: primaryPaymentResponse.id,
                    message:
                      'Single STK push sent for total amount. All payments will be updated when callback is received.',
                    totalAmount: actualTotalAmount,
                    individualPayments: payments.length,
                  }
                : null,
          }

          console.log(
            `✅ Created multi-invoice payment: ${payments.length} payments, total: ${amount}, primary ID: ${primaryPaymentResponse.id}`
          )

          return response.json(responseData)
        } catch (error) {
          await trx.rollback()
          throw error
        }
      } catch (error) {
        console.error('Error processing multiple invoices:', error)
        return response.internalServerError({
          message: 'Failed to process payment for multiple invoices',
          error: error.message,
        })
      }
    }

    // UNIFIED INVOICE-CENTRIC PAYMENT LOGIC
    // No more temp flag dependency - payments work regardless of order status

    // If no invoice provided, create one from the order
    if (!invoice && orderId) {
      const order = await Order.findOrFail(orderId)
      invoice = await order.related('invoices').create({ amount, status: 'Pending' })
      invoiceId = invoice.id
    }

    // Validate that we have an invoice to work with
    if (!invoice) {
      return response.badRequest({
        error: 'Invoice required',
        message: 'Either provide an invoiceId or orderId to create an invoice',
      })
    }

    // Load invoice with order relationship for reference
    await invoice.load('order')

    // Validate invoice is payable
    if (invoice.status === 'Paid') {
      return response.badRequest({
        error: 'Invoice already paid',
        message: 'This invoice has already been fully paid',
      })
    }

    // Create payment record
    const payment = await Payment.create({
      amount: Math.ceil(amount),
      userId,
      invoiceId,
      vendorId,
      method,
      ref: invoice.number,
      receipt,
      status,
    })

    // Process payment based on method and update invoice status accordingly
    try {
      switch (method) {
        case 'Cash':
        case 'Card':
        case 'Wallet':
          // Immediate confirmation methods - mark invoice as paid
          await invoice.merge({ status: 'Paid' }).save()
          break

        case 'Bank':
        case 'Cheque':
        case 'Online':
          // Manual confirmation methods - keep invoice pending
          await invoice.merge({ status: 'Pending' }).save()
          break

        case 'ke.mpesa':
        case 'Mpesa':
        default:
          // M-Pesa STK Push - invoice stays pending until webhook confirmation
          const mpesa = new KeMpesa(payment)

          const { data, error } = await mpesa.stkPush(
            payer!,
            amount.toString(),
            invoice.number,
            invoice.order.id
          )

          if (error) {
            console.error('M-Pesa STK Push Error:', error)
            return response.badRequest({
              error: 'M-Pesa payment failed',
              message: 'STK Push request failed',
              details: error,
            })
          } else {
            const { CheckoutRequestID } = data
            await payment.merge({ req: CheckoutRequestID }).save()
          }
          break
      }

      // Load payment with invoice for response
      await payment.load('invoice')
      return response.json(payment)
    } catch (error) {
      console.error('Payment processing error:', error)
      return response.internalServerError({
        error: 'Payment processing failed',
        message: 'An error occurred while processing the payment',
        details: error.message,
      })
    }
  }

  /**
   * @get
   * @summary Get Successful Payments associated with Multi-Payments for the logged-in staff's vendor
   * @description Fetches successful payments and associated multi-payment records for the vendor linked to the authenticated staff user.
   * @responseBody 200 - { multi_payment: MultiPayment[], payments: Payment[], c: 1 | 0, unique_order_ids: string[] }
   * @response 401 - Unauthorized
   */
  public async getMultiPayment({ auth, response }: HttpContextContract) {
    // --- Omitting getMultiPayment method body for brevity (no changes needed here) ---
    // ... (previous getMultiPayment implementation) ...
    const user = auth.user
    if (!user) {
      return response.unauthorized({ message: 'Authentication required' })
    }

    const staff = await Database.from('staff')
      .select('vendor_id')
      .where('user_id', user!.id)
      .where('online', true)
      .first()

    if (!staff || !staff.vendor_id) {
      return response.notFound({ status: 'No associated active staff/vendor found', c: 0 })
    }

    const payments = await Database.from('payments')
      .select('ref', 'receipt', 'method', 'created_at', 'amount')
      .where('vendor_id', staff.vendor_id)
      .where('status', 'Success')

    if (payments.length === 0) {
      return response.json({ status: 'no successful payments found for this vendor', c: 0 })
    }

    var o: string[] = []
    for (var i = 0; i < payments.length; i++) {
      o[i] = payments[i].ref
    }

    const multi_payment = await Database.from('multi_payment').select('*').whereIn('ref', o)

    var unique_order_ids: string[] = []
    var j_obj
    var k = 0
    for (var i = 0; i < multi_payment.length; i++) {
      try {
        const orderIdsString = multi_payment[i].order_ids as string
        j_obj = JSON.parse(orderIdsString)

        if (Array.isArray(j_obj)) {
          for (var j = 0; j < j_obj.length; j++) {
            const currentOrderId = String(j_obj[j])
            if (!unique_order_ids.includes(currentOrderId)) {
              unique_order_ids[k] = currentOrderId
              k += 1
            }
          }
        } else {
          console.warn(
            `Parsed order_ids for multi_payment id ${multi_payment[i].id} is not an array:`,
            j_obj
          )
        }
      } catch (e) {
        console.error(`Error parsing order_ids for multi_payment id ${multi_payment[i].id}:`, e)
      }
    }

    return response.json({
      multi_payment: multi_payment,
      payments: payments,
      c: 1,
      unique_order_ids: unique_order_ids,
    })
  }

  /**
   * @store
   * @summary Create a Multiple Payment entry linking several orders
   * @description Creates an Invoice, a Payment record, and a multi_payment record. Handles M-Pesa STK push initiation. Uses DB transaction.
   * @security Payment amount is calculated server-side from order data to prevent price manipulation attacks
   * @requestBody {"order_ids": string[], "user_id": string, "vendor_id": string, "branch_id": string", "payment_type": "ke.mpesa" | "cash" | "card", "phone"?: string, "ref"?: string}
   * @responseBody 201 - { status: 'received', c: 1, ref: string }
   * @response 400 - { status: string, c: 0 }
   * @response 500 - { status: 'error', c: 0 }
   */
  public async multiPayment({ request, response }: HttpContextContract) {
    let b = request.body()

    // Check required fields (amount should NEVER be trusted from frontend)
    if (
      !Array.isArray(b.order_ids) ||
      b.order_ids.length === 0 ||
      !b.payment_type ||
      !b.user_id ||
      !b.vendor_id ||
      !b.branch_id
    ) {
      return response.badRequest({ status: 'Missing required fields', c: 0 })
    }

    // SECURITY: Always calculate amount server-side, never trust frontend
    try {
      const orders = await Order.query().whereIn('id', b.order_ids)
      if (orders.length !== b.order_ids.length) {
        return response.badRequest({ status: 'One or more orders not found', c: 0 })
      }

      // Verify all orders belong to the requesting user (security check)
      const invalidOrders = orders.filter((order) => order.userId !== b.user_id)
      if (invalidOrders.length > 0) {
        return response.badRequest({ status: 'Unauthorized: Orders do not belong to user', c: 0 })
      }

      // Verify all orders belong to the specified vendor/branch
      const vendorMismatch = orders.filter(
        (order) => order.vendorId !== b.vendor_id || order.branchId !== b.branch_id
      )
      if (vendorMismatch.length > 0) {
        return response.badRequest({ status: 'Orders do not match specified vendor/branch', c: 0 })
      }

      // Calculate total amount from orders (server-side calculation)
      let calculatedAmount = 0
      for (const order of orders) {
        const orderTotal = await order.calculateTotalAmount()
        calculatedAmount += orderTotal
      }

      if (calculatedAmount <= 0) {
        return response.badRequest({
          status: 'Invalid total amount calculated from orders',
          c: 0,
        })
      }

      // SECURITY: Override any frontend-provided amount with server-calculated amount
      b.amount = calculatedAmount

      // Log for security audit
      console.log(
        `🔒 Payment amount calculated server-side: KES ${calculatedAmount} for orders: ${b.order_ids.join(', ')}`
      )
    } catch (error) {
      console.error('Error calculating payment amount:', error)
      console.error('Error details:', error.message)
      console.error('Error stack:', error.stack)
      return response.badRequest({ status: 'Unable to calculate payment amount', c: 0 })
    }

    console.log('MultiPayment Payload: ' + JSON.stringify(b))

    const p_types = ['ke.mpesa', 'cash', 'card']
    if (!p_types.includes(b.payment_type)) {
      return response.badRequest({ status: 'unknown payment type', c: 0 })
    }

    let invoice: Invoice
    var req: string | undefined | null

    const trx = await Database.transaction()

    try {
      // Fetch all orders again within transaction
      const orders = await Order.query({ client: trx }).whereIn('id', b.order_ids)

      // Create invoices for each order (following existing patterns)
      const invoices: Invoice[] = []
      for (const order of orders) {
        const orderAmount = await order.calculateTotalAmount()
        const invoice = await order
          .related('invoices')
          .create({ amount: orderAmount, status: 'Pending' }, { client: trx })
        invoices.push(invoice)
      }

      // Use existing multi-invoice payment logic from store method
      const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.amount, 0)

      // Create payments for each invoice (following existing store method pattern)
      const payments: Payment[] = []
      let primaryPayment: Payment | null = null
      let checkoutRequestID: string | null = null

      for (let i = 0; i < invoices.length; i++) {
        const invoiceItem = invoices[i]
        // Calculate proportional amount for this invoice
        const proportionalAmount = Math.ceil((invoiceItem.amount / totalInvoiceAmount) * b.amount)

        const payment = await Payment.create(
          {
            amount: proportionalAmount,
            userId: b.user_id,
            invoiceId: invoiceItem.id,
            vendorId: b.vendor_id,
            method: b.payment_type === 'ke.mpesa' ? 'Mpesa' : b.payment_type,
            ref: invoiceItem.number,
            receipt: b.ref || '',
            status: 'Pending',
          },
          { client: trx }
        )

        payments.push(payment)

        // Handle M-Pesa STK Push - ONLY for the first payment (primary)
        if (b.payment_type === 'ke.mpesa' && i === 0) {
          if (!b.phone) {
            await trx.rollback()
            return response.badRequest({ status: 'Missing phone number for M-Pesa', c: 0 })
          }

          primaryPayment = payment
          const mpesa = new KeMpesa(payment)

          // Use total amount for STK push, not proportional
          const { data, error } = await mpesa.stkPush(
            b.phone,
            b.amount.toString(), // Total amount, not proportional
            `MULTI-${payment.id.slice(-6)}`, // Unique reference for multi-order payment
            `multi-order-${payment.id}`
          )

          if (error) {
            await trx.rollback()
            console.error('M-Pesa STK Push Error:', error)
            return response.badRequest({
              error: 'M-Pesa payment failed',
              message: 'STK Push request failed',
              details: error,
            })
          } else {
            const { CheckoutRequestID } = data
            checkoutRequestID = CheckoutRequestID
            await Payment.query({ client: trx })
              .where('id', payment.id)
              .update({ req: CheckoutRequestID })
          }
        } else if (b.payment_type === 'ke.mpesa') {
          // For subsequent M-Pesa payments, link them to the primary payment's checkout request
          await Payment.query({ client: trx })
            .where('id', payment.id)
            .update({
              req: checkoutRequestID,
              receipt: `Linked to primary payment: ${primaryPayment?.id}`,
            })
        } else {
          // For non-M-Pesa payments, update invoice status based on method
          switch (b.payment_type) {
            case 'cash':
            case 'card':
              await Invoice.query({ client: trx })
                .where('id', invoiceItem.id)
                .update({ status: 'Paid' })
              await Payment.query({ client: trx })
                .where('id', payment.id)
                .update({ status: 'Success' })

              // Generate receipt for successful cash/card payments
              try {
                const receiptService = new ReceiptService()
                await receiptService.generateReceipt(payment.id)
                console.log(`📧 Generated receipt for ${b.payment_type} payment ${payment.id}`)
              } catch (receiptError) {
                console.error(
                  `Failed to generate receipt for ${b.payment_type} payment:`,
                  receiptError
                )
                // Don't fail the payment if receipt generation fails
              }
              break
          }
        }
      }

      // Set the primary invoice for response
      const primaryInvoice = invoices[0]

      await trx.commit()

      return response.created({
        status: 'received',
        c: 1,
        ref: primaryInvoice.number,
        summary: {
          isMultiOrderPayment: true,
          totalOrders: orders.length,
          totalAmount: b.amount,
          paymentMethod: b.payment_type,
          allPaymentIds: payments.map((p) => p.id),
          allInvoiceIds: invoices.map((inv) => inv.id),
        },
        stkPushInfo:
          b.payment_type === 'ke.mpesa'
            ? {
                checkoutRequestID: checkoutRequestID,
                primaryPaymentId: primaryPayment?.id,
                message:
                  'Single STK push sent for total amount. All payments will be updated when callback is received.',
                totalAmount: b.amount,
                individualPayments: payments.length,
              }
            : null,
      })
    } catch (e) {
      await trx.rollback()
      console.error('Error in multiPayment:', e)
      if (e.code === '23503') {
        return response.badRequest({
          status: 'Invalid reference ID (e.g., user, vendor, order)',
          c: 0,
        })
      }
      if (e.message.includes('findOrFail')) {
        return response.notFound({ status: 'Primary order not found', c: 0 })
      }
      return response.internalServerError({ status: 'An unexpected error occurred', c: 0 })
    }
  } // End multiPayment

  @bind()
  /**
   * @show
   * @summary Show a single Payment
   * @description Show a Payment with their details and related Invoice, Order, Vendor, Customer
   * @paramPath id required string - Payment ID (ULID)
   * @responseBody 200 - <Payment> (with relations loaded)
   * @response 404 - Payment not found
   */
  public async show({ response }: HttpContextContract, payment: Payment) {
    await payment.load((loader) => {
      loader
        .load('vendor')
        .load('customer')
        .load('invoice', (invoiceQuery) => {
          invoiceQuery.preload('order', (orderQuery) => {
            orderQuery.preload('items').preload('staff').preload('branch')
          })
        })
    })
    return response.json(payment)
  }

  @bind()
  /**
   * @checkStatus
   * @summary Check Payment Status (Optimized for Frontend Polling)
   * @description Lightweight endpoint to check payment status with minimal data transfer
   * @paramPath id required string - Payment ID (ULID)
   * @responseBody 200 - { id, status, receipt, method, amount, createdAt, invoice: { status }, isComplete, timeElapsed }
   * @response 404 - Payment not found
   */
  public async checkStatus({ response }: HttpContextContract, payment: Payment) {
    try {
      // Load only essential relations for status checking
      await payment.load('invoice')

      const timeElapsed = Math.floor((Date.now() - payment.createdAt.toMillis()) / 1000)
      const isComplete = payment.status === 'Success' || payment.status === 'Failed'

      // Check if this is part of a multi-payment (same CheckoutRequestID)
      let relatedPayments: Payment[] = []
      let invoiceBreakdown: InvoiceBreakdownItem[] | null = null
      let multiPaymentStatus: MultiPaymentStatus | null = null

      if (payment.req) {
        relatedPayments = await Payment.query().where('req', payment.req).preload('invoice')

        if (relatedPayments.length > 1) {
          // This is a multi-payment scenario
          const allComplete = relatedPayments.every(
            (p) => p.status === 'Success' || p.status === 'Failed'
          )
          const allSuccess = relatedPayments.every((p) => p.status === 'Success')
          const anyFailed = relatedPayments.some((p) => p.status === 'Failed')
          const totalAmount = relatedPayments.reduce(
            (sum, p) => sum + parseFloat(p.amount.toString()),
            0
          )

          // Create detailed invoice breakdown for status response
          invoiceBreakdown = relatedPayments.map((p) => ({
            invoiceId: p.invoiceId,
            invoiceNumber: p.invoice.number,
            amount: parseFloat(p.amount.toString()),
            paymentId: p.id,
            status: p.status,
            invoice: {
              id: p.invoice.id,
              number: p.invoice.number,
              amount: parseFloat(p.invoice.amount.toString()),
              status: p.invoice.status,
              orderId: p.invoice.orderId,
            },
          }))

          multiPaymentStatus = {
            isMultiPayment: true,
            totalPayments: relatedPayments.length,
            totalAmount: totalAmount,
            completedPayments: relatedPayments.filter(
              (p) => p.status === 'Success' || p.status === 'Failed'
            ).length,
            successfulPayments: relatedPayments.filter((p) => p.status === 'Success').length,
            failedPayments: relatedPayments.filter((p) => p.status === 'Failed').length,
            pendingPayments: relatedPayments.filter((p) => p.status === 'Pending').length,
            allComplete,
            allSuccess,
            anyFailed,
            overallStatus: allSuccess ? 'Success' : anyFailed ? 'Failed' : 'Pending',
            progressPercentage: Math.round(
              (relatedPayments.filter((p) => p.status === 'Success').length /
                relatedPayments.length) *
                100
            ),
          }
        }
      }

      // For M-Pesa payments, also check if we should query M-Pesa directly
      let mpesaStatusQuery = null
      if (
        payment.method.toLowerCase() === 'mpesa' &&
        payment.req &&
        !isComplete &&
        timeElapsed > 30
      ) {
        // If payment is still pending after 30 seconds, query M-Pesa status
        try {
          // Note: This would require implementing status query in current library
          // For now, we'll just log that we should check
          console.log(
            `⏰ Payment ${payment.id} pending for ${timeElapsed}s - should query M-Pesa status`
          )
          // TODO: Implement M-Pesa status query when available in library
          // const mpesa = new KeMpesa(payment)
          // mpesaStatusQuery = await mpesa.queryTransactionStatus(payment.req)
        } catch (error) {
          console.error('Error querying M-Pesa status:', error)
        }
      }

      const statusResponse = {
        id: payment.id,
        status: multiPaymentStatus ? multiPaymentStatus.overallStatus : payment.status,
        receipt: payment.receipt,
        method: payment.method,
        amount: multiPaymentStatus
          ? multiPaymentStatus.totalAmount
          : parseFloat(payment.amount.toString()),
        req: payment.req, // CheckoutRequestID for M-Pesa
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        invoice: {
          id: payment.invoice.id,
          status: payment.invoice.status,
          number: payment.invoice.number,
        },

        // Enhanced fields for multi-invoice payments
        invoiceBreakdown: invoiceBreakdown,

        // Status and polling information
        isComplete: multiPaymentStatus ? multiPaymentStatus.allComplete : isComplete,
        timeElapsed,
        shouldContinuePolling: multiPaymentStatus
          ? !multiPaymentStatus.allComplete && timeElapsed < 180
          : !isComplete && timeElapsed < 180,
        nextPollInterval: this.calculatePollInterval(timeElapsed),
        mpesaStatusQuery,

        // Multi-payment status summary
        multiPaymentStatus: multiPaymentStatus,

        // Summary for frontend display
        summary: multiPaymentStatus
          ? {
              isMultiInvoicePayment: true,
              totalInvoices: multiPaymentStatus.totalPayments,
              totalAmount: multiPaymentStatus.totalAmount,
              completedInvoices: multiPaymentStatus.successfulPayments,
              progressPercentage: multiPaymentStatus.progressPercentage,
              overallStatus: multiPaymentStatus.overallStatus,
            }
          : {
              isMultiInvoicePayment: false,
              totalInvoices: 1,
              totalAmount: parseFloat(payment.amount.toString()),
              completedInvoices: payment.status === 'Success' ? 1 : 0,
              progressPercentage: payment.status === 'Success' ? 100 : 0,
              overallStatus: payment.status,
            },
      }

      console.log(
        `📊 Payment Status Check - ID: ${payment.id}, Status: ${payment.status}, Elapsed: ${timeElapsed}s${multiPaymentStatus ? ` (Multi-payment: ${multiPaymentStatus.completedPayments}/${multiPaymentStatus.totalPayments} complete)` : ''}`
      )

      return response.json(statusResponse)
    } catch (error) {
      console.error('=== PAYMENT STATUS CHECK ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to check payment status',
        message: error.message,
      })
    }
  }

  /**
   * Calculate optimal polling interval based on time elapsed
   */
  private calculatePollInterval(timeElapsed: number): number {
    if (timeElapsed < 30) return 2000 // Poll every 2 seconds for first 30 seconds
    if (timeElapsed < 60) return 3000 // Poll every 3 seconds for next 30 seconds
    if (timeElapsed < 120) return 5000 // Poll every 5 seconds for next minute
    return 10000 // Poll every 10 seconds after 2 minutes
  }

  @bind()
  /**
   * @update
   * @summary Update a Payment status, request ID, or receipt
   * @description Update specific fields of a Payment record.
   * @paramPath id required string - Payment ID (ULID)
   * @requestBody { "req"?: string, "receipt"?: string, "status"?: "Pending" | "Success" | "Failed" }
   * @responseBody 200 - <Payment> (updated payment)
   * @response 404 - Payment not found
   * @response 422 - Validation error
   */
  public async update({ request, response }: HttpContextContract, payment: Payment) {
    // --- Omitting update method body for brevity (no changes needed here) ---
    // ... (previous update implementation) ...
    const payload = await request.validate(UpdatePaymentValidator)
    payment.merge(payload)
    await payment.save()
    return response.ok(payment)
  }

  @bind()
  /**
   * @destroy
   * @summary Delete a Payment record
   * @description Permanently delete a Payment. Use with caution.
   * @paramPath id required string - Payment ID (ULID)
   * @responseBody 204 - No content
   * @response 404 - Payment not found
   */
  public async destroy({ response }: HttpContextContract, payment: Payment) {
    // --- Omitting destroy method body for brevity (no changes needed here) ---
    // ... (previous destroy implementation) ...
    await payment.delete()
    return response.noContent()
  }

  /**
   * @validate M-Pesa C2B Validation Endpoint
   * @summary Handles M-Pesa C2B validation requests
   * @description Receives validation request from M-Pesa API before processing a payment.
   * @requestBody M-Pesa C2B Validation Payload
   * @responseBody { "ResultCode": 0, "ResultDesc": "Accepted" } | { "ResultCode": 1, "ResultDesc": "Rejected" }
   */
  public async validate({ request, response }: HttpContextContract) {
    try {
      const validationData = request.body()
      console.log('=== MPESA VALIDATION REQUEST ===')
      console.log('🎯 CALLBACK RECEIVED! M-Pesa validation request successful')
      console.log('Request Body:', JSON.stringify(validationData, null, 2))
      console.log('Request Headers:', request.headers())
      console.log('Request IP:', request.ip())
      console.log('User Agent:', request.header('user-agent'))
      console.log('Timestamp:', new Date().toISOString())

      // Log key validation data
      if (validationData) {
        console.log('📋 Validation Details:')
        console.log('  - Transaction Type:', validationData.TransactionType)
        console.log('  - Amount:', validationData.TransAmount)
        console.log('  - Phone:', validationData.MSISDN)
        console.log('  - Bill Ref:', validationData.BillRefNumber)
        console.log('  - Business Short Code:', validationData.BusinessShortCode)
      }

      // Add validation logic here if needed
      // For now, accept all validation requests
      const isValid = true

      if (isValid) {
        console.log('✅ MPESA VALIDATION ACCEPTED')
        console.log('🔄 Responding to M-Pesa with acceptance')
        return response.json({ ResultCode: 0, ResultDesc: 'Accepted' })
      } else {
        console.log('❌ MPESA VALIDATION REJECTED')
        return response.json({ ResultCode: 1, ResultDesc: 'Rejected' })
      }
    } catch (error) {
      console.error('💥 MPESA VALIDATION ERROR', error)
      console.error('Stack:', error.stack)
      return response.json({ ResultCode: 1, ResultDesc: 'Validation error' })
    }
  }

  /**
   * @confirm M-Pesa C2B Confirmation Endpoint
   * @summary Handles M-Pesa C2B confirmation requests and updates payment/invoice status. Uses DB transaction.
   * @description Receives confirmation from M-Pesa API after a payment has been successfully processed. Updates payment/invoice status. Validates amount paid.
   * @requestBody M-Pesa C2B Confirmation Payload
   * @responseBody { "ResultCode": 0, "ResultDesc": "Accepted" }
   */
  public async confirm({ request, response }: HttpContextContract) {
    try {
      const confirmationData = request.body()
      console.log('=== MPESA CONFIRMATION REQUEST ===')
      console.log('🎉 PAYMENT CALLBACK RECEIVED! M-Pesa confirmation successful')
      console.log('Request Body:', JSON.stringify(confirmationData, null, 2))
      console.log('Request Headers:', request.headers())
      console.log('Request IP:', request.ip())
      console.log('User Agent:', request.header('user-agent'))
      console.log('Timestamp:', new Date().toISOString())

      // Handle both STK Push and C2B callback formats
      let invoiceRef,
        mpesaReceipt,
        amountPaidString,
        isSTKPush = false

      if (confirmationData.Body && confirmationData.Body.stkCallback) {
        // STK Push callback format
        isSTKPush = true
        const stkCallback = confirmationData.Body.stkCallback

        console.log('📱 STK Push Callback Details:')
        console.log('  - Checkout Request ID:', stkCallback.CheckoutRequestID)
        console.log('  - Result Code:', stkCallback.ResultCode)
        console.log('  - Result Description:', stkCallback.ResultDesc)

        if (stkCallback.ResultCode === 0) {
          // Successful STK Push - extract metadata
          const callbackMetadata = stkCallback.CallbackMetadata?.Item || []

          console.log('🔍 Raw Callback Metadata:', JSON.stringify(callbackMetadata, null, 2))

          const metadata = callbackMetadata.reduce(
            (acc, item) => ({
              ...acc,
              [item.Name]: item.Value,
            }),
            {}
          )

          console.log('📋 Parsed Metadata:', JSON.stringify(metadata, null, 2))

          // Extract values with multiple possible field names
          invoiceRef = metadata.AccountReference || metadata.Account || metadata.BillRefNumber
          mpesaReceipt = metadata.MpesaReceiptNumber || metadata.Receipt || metadata.TransactionId
          amountPaidString = metadata.Amount || metadata.TransAmount

          console.log('💰 STK Push Payment Success:')
          console.log('  - Account Reference:', invoiceRef)
          console.log('  - M-Pesa Receipt:', mpesaReceipt)
          console.log('  - Amount:', amountPaidString)
          console.log('  - Available Metadata Fields:', Object.keys(metadata))
        } else {
          // Failed/Cancelled STK Push
          console.log('❌ STK Push Failed/Cancelled:')
          console.log('  - Reason:', stkCallback.ResultDesc)

          // Try to find ALL payments by CheckoutRequestID (for multi-invoice payments)
          const payments = await Payment.query().where('req', stkCallback.CheckoutRequestID)

          if (payments.length > 0) {
            console.log(
              `Marking ${payments.length} payment(s) as failed due to: ${stkCallback.ResultDesc}`
            )

            for (const payment of payments) {
              await payment.merge({ status: 'Failed' }).save()
              console.log(`Payment ${payment.id} marked as failed`)
            }
          }

          return response.json({ ResultCode: 0, ResultDesc: 'STK Push failure acknowledged' })
        }
      } else {
        // C2B callback format
        invoiceRef = confirmationData.BillRefNumber
        mpesaReceipt = confirmationData.TransID
        amountPaidString = confirmationData.TransAmount

        console.log('🏪 C2B Payment Details:')
        console.log('  - Bill Reference:', invoiceRef)
        console.log('  - Transaction ID:', mpesaReceipt)
        console.log('  - Amount:', amountPaidString)
      }

      // For STK Push, we can proceed even without invoiceRef if we have CheckoutRequestID
      if (isSTKPush) {
        if (!mpesaReceipt || !amountPaidString) {
          console.error('STK Push missing essential fields:', {
            MpesaReceipt: mpesaReceipt,
            Amount: amountPaidString,
            CheckoutRequestID: confirmationData.Body.stkCallback.CheckoutRequestID,
            rawData: confirmationData,
          })
          return response.json({ ResultCode: 1, ResultDesc: 'Missing essential STK Push fields' })
        }
      } else {
        // For C2B, we need all fields
        if (!invoiceRef || !mpesaReceipt || !amountPaidString) {
          console.error('C2B confirmation missing required fields:', {
            BillRefNumber: invoiceRef,
            TransID: mpesaReceipt,
            TransAmount: amountPaidString,
            rawData: confirmationData,
          })
          return response.json({ ResultCode: 1, ResultDesc: 'Missing required C2B fields' })
        }
      }

      const trx = await Database.transaction()
      try {
        let payment: Payment | null = null

        if (isSTKPush && confirmationData.Body.stkCallback.CheckoutRequestID) {
          // For STK Push, find ALL payments with this CheckoutRequestID (for multi-invoice payments)
          const payments = await Payment.query({ client: trx }).where(
            'req',
            confirmationData.Body.stkCallback.CheckoutRequestID
          )

          console.log(
            `🔍 STK Push Payment Lookup by CheckoutRequestID: ${confirmationData.Body.stkCallback.CheckoutRequestID}`
          )
          console.log(
            `   Found ${payments.length} Payment(s): ${payments.map((p) => p.id).join(', ')}`
          )

          // Use the first payment for primary processing, but we'll update all of them
          payment = payments.length > 0 ? payments[0] : null
        }

        if (!payment && invoiceRef) {
          // Fallback: try to match by invoice reference
          payment = await Payment.query({ client: trx }).where('ref', invoiceRef).first()

          console.log(`🔍 Fallback Payment Lookup by Invoice Ref: ${invoiceRef}`)
          console.log(`   Found Payment: ${payment ? payment.id : 'NOT FOUND'}`)
        }

        if (!payment) {
          console.error(`❌ No matching payment found for M-Pesa confirmation`)
          console.error(`   - STK Push: ${isSTKPush}`)
          console.error(
            `   - CheckoutRequestID: ${isSTKPush ? confirmationData.Body.stkCallback.CheckoutRequestID : 'N/A'}`
          )
          console.error(`   - Invoice Ref: ${invoiceRef}`)
          console.error(`   - Transaction ID: ${mpesaReceipt}`)
          await trx.rollback()
          return response.json({ ResultCode: 1, ResultDesc: 'Payment not found' })
        }

        // Check for duplicate processing
        if (payment.receipt === mpesaReceipt && payment.status === 'Success') {
          console.log(
            `Payment for ref ${invoiceRef} already confirmed with receipt ${mpesaReceipt}.`
          )
          await trx.commit()
          return response.json({ ResultCode: 0, ResultDesc: 'Already processed' })
        }

        const expectedAmount = payment.amount
        const amountPaid = parseFloat(amountPaidString)

        if (isNaN(amountPaid)) {
          console.error(
            `Invalid amount received in M-Pesa confirmation for ref ${invoiceRef}: ${amountPaidString}`
          )
          await trx.rollback()
          return response.json({ ResultCode: 1, ResultDesc: 'Invalid amount' })
        }

        // Log amount discrepancies but still process
        if (amountPaid < expectedAmount) {
          console.warn(
            `Potential underpayment for ref ${invoiceRef}. Expected: ${expectedAmount}, Received: ${amountPaid}`
          )
        } else if (amountPaid > expectedAmount) {
          console.warn(
            `Potential overpayment for ref ${invoiceRef}. Expected: ${expectedAmount}, Received: ${amountPaid}`
          )
        }

        // For multi-invoice payments, update ALL payments with the same CheckoutRequestID
        if (isSTKPush && confirmationData.Body.stkCallback.CheckoutRequestID) {
          const allPayments = await Payment.query({ client: trx }).where(
            'req',
            confirmationData.Body.stkCallback.CheckoutRequestID
          )

          console.log(
            `🔄 Updating ${allPayments.length} payment(s) for CheckoutRequestID: ${confirmationData.Body.stkCallback.CheckoutRequestID}`
          )

          // Update all payments and their invoices
          for (const paymentToUpdate of allPayments) {
            paymentToUpdate.useTransaction(trx)
            paymentToUpdate.merge({
              status: 'Success',
              receipt: mpesaReceipt,
            })
            await paymentToUpdate.save()

            // Update corresponding invoice
            await Invoice.query({ client: trx })
              .where('id', paymentToUpdate.invoiceId)
              .update({ status: 'Paid' })

            console.log(
              `✅ Updated payment ${paymentToUpdate.id} and invoice ${paymentToUpdate.invoiceId}`
            )
          }
        } else {
          // Single payment update (original logic)
          payment.useTransaction(trx)
          payment.merge({
            status: 'Success',
            receipt: mpesaReceipt,
          })
          await payment.save()

          // Update invoice
          await Invoice.query({ client: trx })
            .where('id', payment.invoiceId)
            .update({ status: 'Paid' })
        }

        console.log(`=== MPESA CONFIRMATION SUCCESS ===`)
        console.log(`Payment ${payment.id} and Invoice ${payment.invoiceId} updated successfully`)
        console.log(`Reference: ${invoiceRef}, Receipt: ${mpesaReceipt}, Amount: ${amountPaid}`)

        await trx.commit()

        // Generate receipts for successful payments
        try {
          const receiptService = new ReceiptService()
          if (allPayments && allPayments.length > 0) {
            // Generate receipts for all payments in multi-payment scenario
            for (const paymentToUpdate of allPayments) {
              await receiptService.generateReceipt(paymentToUpdate.id)
              console.log(`📧 Generated receipt for payment ${paymentToUpdate.id}`)
            }
          } else {
            // Generate receipt for single payment
            await receiptService.generateReceipt(payment.id)
            console.log(`📧 Generated receipt for payment ${payment.id}`)
          }
        } catch (receiptError) {
          console.error('Failed to queue receipt generation:', receiptError)
          // Don't fail the payment confirmation if receipt generation fails
        }

        return response.json({ ResultCode: 0, ResultDesc: 'Accepted' })
      } catch (error) {
        console.error(`Error processing M-Pesa confirmation for ref ${invoiceRef}:`, error)
        await trx.rollback()
        return response.json({ ResultCode: 1, ResultDesc: 'Processing error' })
      }
    } catch (error) {
      console.error('=== MPESA CONFIRMATION ERROR ===', error)
      return response.json({ ResultCode: 1, ResultDesc: 'Server error' })
    }
  }

  /**
   * @timeout M-Pesa Timeout Endpoint
   * @summary Handles M-Pesa timeout notifications
   * @description Receives timeout notifications from M-Pesa API when a transaction times out
   * @requestBody M-Pesa Timeout Payload
   * @responseBody { "ResultCode": 0, "ResultDesc": "Accepted" }
   */
  public async timeout({ request, response }: HttpContextContract) {
    try {
      const timeoutData = request.body()
      console.log('=== MPESA TIMEOUT REQUEST ===')
      console.log('Request Body:', JSON.stringify(timeoutData, null, 2))
      console.log('Timestamp:', new Date().toISOString())

      // Handle timeout logic here if needed
      // For now, just log and acknowledge

      return response.json({ ResultCode: 0, ResultDesc: 'Timeout acknowledged' })
    } catch (error) {
      console.error('=== MPESA TIMEOUT ERROR ===', error)
      return response.json({ ResultCode: 1, ResultDesc: 'Timeout processing error' })
    }
  }

  /**
   * @result M-Pesa Result Endpoint
   * @summary Handles M-Pesa result notifications
   * @description Receives result notifications from M-Pesa API for various transaction types
   * @requestBody M-Pesa Result Payload
   * @responseBody { "ResultCode": 0, "ResultDesc": "Accepted" }
   */
  public async result({ request, response }: HttpContextContract) {
    try {
      const resultData = request.body()
      console.log('=== MPESA RESULT REQUEST ===')
      console.log('Request Body:', JSON.stringify(resultData, null, 2))
      console.log('Timestamp:', new Date().toISOString())

      // Handle result logic here if needed
      // For now, just log and acknowledge

      return response.json({ ResultCode: 0, ResultDesc: 'Result acknowledged' })
    } catch (error) {
      console.error('=== MPESA RESULT ERROR ===', error)
      return response.json({ ResultCode: 1, ResultDesc: 'Result processing error' })
    }
  }

  /**
   * @mpesaHealth M-Pesa Health Check Endpoint
   * @summary Check M-Pesa configuration and connectivity
   * @description Returns the status of M-Pesa configuration and basic health information
   * @responseBody M-Pesa Health Status
   */
  public async mpesaHealth({ response }: HttpContextContract) {
    try {
      console.log('=== MPESA HEALTH CHECK ===')

      const validation = MpesaConfigValidator.validate()
      const summary = MpesaConfigValidator.getConfigSummary()

      const healthStatus = {
        status: validation.isValid ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        configuration: {
          isValid: validation.isValid,
          summary,
          errors: validation.errors,
          warnings: validation.warnings,
        },
        endpoints: {
          validate: `${summary.callbackDomain}/v1/mpsa/e59ed6a68b83/validate`,
          confirm: `${summary.callbackDomain}/v1/mpsa/e59ed6a68b83/confirm`,
          timeout: `${summary.callbackDomain}/v1/mpsa/e59ed6a68b83/timeout`,
          result: `${summary.callbackDomain}/v1/mpsa/e59ed6a68b83/result`,
        },
      }

      console.log('Health Status:', JSON.stringify(healthStatus, null, 2))

      return response.json(healthStatus)
    } catch (error) {
      console.error('=== MPESA HEALTH CHECK ERROR ===', error)
      return response.status(500).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
      })
    }
  }

  /**
   * @testStatusPolling Test Status Polling Endpoint
   * @summary Test the payment status polling mechanism
   * @description Creates a test payment and shows how status polling should work
   * @responseBody Test Payment Status Response
   */
  public async testStatusPolling({ response }: HttpContextContract) {
    try {
      console.log('=== TESTING STATUS POLLING ===')

      // Find a recent M-Pesa payment for testing
      const recentPayment = await Payment.query()
        .where('method', 'Mpesa')
        .orderBy('created_at', 'desc')
        .preload('invoice')
        .first()

      if (!recentPayment) {
        return response.json({
          message: 'No M-Pesa payments found for testing',
          recommendation: 'Create a test payment first',
          testEndpoint: '/v1/pay/status/{paymentId}',
          example: {
            url: '/v1/pay/status/01jx4vbx68yn466s8182g5j4za',
            method: 'GET',
            headers: {
              Authorization: 'Bearer your_token',
            },
          },
        })
      }

      const timeElapsed = Math.floor((Date.now() - recentPayment.createdAt.toMillis()) / 1000)
      const isComplete = recentPayment.status === 'Success' || recentPayment.status === 'Failed'

      const testResponse = {
        message: 'Status polling test with recent payment',
        payment: {
          id: recentPayment.id,
          status: recentPayment.status,
          method: recentPayment.method,
          amount: recentPayment.amount,
          receipt: recentPayment.receipt,
          req: recentPayment.req,
          createdAt: recentPayment.createdAt,
          invoice: {
            status: recentPayment.invoice.status,
            number: recentPayment.invoice.number,
          },
          isComplete,
          timeElapsed,
          shouldContinuePolling: !isComplete && timeElapsed < 180,
          nextPollInterval: this.calculatePollInterval(timeElapsed),
        },
        pollingStrategy: {
          '0-30s': '2 second intervals',
          '30-60s': '3 second intervals',
          '60-120s': '5 second intervals',
          '120s+': '10 second intervals',
        },
        frontendImplementation: {
          endpoint: `/v1/pay/status/${recentPayment.id}`,
          method: 'GET',
          authentication: 'Bearer token required',
          pollingLogic: 'Use shouldContinuePolling and nextPollInterval from response',
        },
      }

      return response.json(testResponse)
    } catch (error) {
      console.error('=== STATUS POLLING TEST ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to test status polling',
        message: error.message,
      })
    }
  }

  /**
   * @debugPayment Debug specific payment details
   * @summary Debug payment details for troubleshooting callback issues
   * @paramPath id required string - Payment ID to debug
   * @responseBody Payment Debug Information
   */
  @bind()
  public async debugPayment({ response }: HttpContextContract, payment: Payment) {
    try {
      console.log('=== DEBUGGING PAYMENT ===')
      console.log('Payment ID:', payment.id)

      await payment.load('invoice')

      const debugInfo = {
        payment: {
          id: payment.id,
          ref: payment.ref,
          req: payment.req, // CheckoutRequestID
          receipt: payment.receipt,
          status: payment.status,
          method: payment.method,
          amount: payment.amount,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt,
        },
        invoice: {
          id: payment.invoice.id,
          number: payment.invoice.number,
          status: payment.invoice.status,
          amount: payment.invoice.amount,
        },
        callbackMatching: {
          byCheckoutRequestID: payment.req
            ? `WHERE req = '${payment.req}'`
            : 'No CheckoutRequestID stored',
          byInvoiceRef: payment.ref ? `WHERE ref = '${payment.ref}'` : 'No invoice reference',
          recommendation: payment.req
            ? 'M-Pesa callbacks should match by CheckoutRequestID (req field)'
            : 'Payment missing CheckoutRequestID - STK Push may have failed',
        },
        troubleshooting: {
          timeElapsed: Math.floor((Date.now() - payment.createdAt.toMillis()) / 1000),
          expectedCallbackFormat: {
            stkPush: {
              CheckoutRequestID: payment.req,
              expectedMatch: `payment.req === '${payment.req}'`,
            },
            c2b: {
              BillRefNumber: payment.ref,
              expectedMatch: `payment.ref === '${payment.ref}'`,
            },
          },
          nextSteps: [
            'Check if M-Pesa callback was received for this payment',
            'Verify CheckoutRequestID in callback matches payment.req',
            'Check application logs for callback processing errors',
            'Manually trigger callback if needed',
          ],
        },
      }

      console.log('Debug Info:', JSON.stringify(debugInfo, null, 2))

      return response.json(debugInfo)
    } catch (error) {
      console.error('=== PAYMENT DEBUG ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to debug payment',
        message: error.message,
      })
    }
  }

  /**
   * @manualUpdate Manually update payment status for testing
   * @summary Manually update payment status (for testing callback issues)
   * @paramPath id required string - Payment ID to update
   * @requestBody { status: "Success|Failed", receipt?: string }
   * @responseBody Updated Payment
   */
  @bind()
  public async manualUpdate({ request, response }: HttpContextContract, payment: Payment) {
    try {
      const { status, receipt } = request.only(['status', 'receipt'])

      if (!['Success', 'Failed', 'Pending'].includes(status)) {
        return response.badRequest({
          error: 'Invalid status',
          validStatuses: ['Success', 'Failed', 'Pending'],
        })
      }

      console.log('=== MANUAL PAYMENT UPDATE ===')
      console.log('Payment ID:', payment.id)
      console.log('Current Status:', payment.status)
      console.log('New Status:', status)
      console.log('Receipt:', receipt)

      const trx = await Database.transaction()

      try {
        // Update payment
        payment.useTransaction(trx)
        payment.merge({
          status,
          receipt: receipt || payment.receipt,
        })
        await payment.save()

        // Update invoice if payment is successful
        if (status === 'Success') {
          await Invoice.query({ client: trx })
            .where('id', payment.invoiceId)
            .update({ status: 'Paid' })
        }

        await trx.commit()

        // Generate receipt for successful payments
        if (status === 'Success') {
          try {
            const receiptService = new ReceiptService()
            await receiptService.generateReceipt(payment.id)
            console.log(`📧 Generated receipt for manually updated payment ${payment.id}`)
          } catch (receiptError) {
            console.error('Failed to generate receipt for manual update:', receiptError)
            // Don't fail the payment update if receipt generation fails
          }
        }

        // Reload with relations
        await payment.load('invoice')

        console.log('=== MANUAL UPDATE SUCCESS ===')
        console.log('Payment updated:', payment.id)
        console.log('New status:', payment.status)
        console.log('Invoice status:', payment.invoice.status)

        return response.json({
          message: 'Payment updated successfully',
          payment: {
            id: payment.id,
            status: payment.status,
            receipt: payment.receipt,
            invoice: {
              id: payment.invoice.id,
              status: payment.invoice.status,
            },
          },
        })
      } catch (error) {
        await trx.rollback()
        throw error
      }
    } catch (error) {
      console.error('=== MANUAL UPDATE ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to update payment',
        message: error.message,
      })
    }
  }
}
