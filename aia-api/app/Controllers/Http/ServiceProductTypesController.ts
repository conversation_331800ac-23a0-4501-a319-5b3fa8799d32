import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'

export default class ServiceProductTypesController {
  @bind()

  /**
   * @name ServiceProductType management
   * @index
   * @summary List all ServiceProductTypes
   * @description List all ServiceProductTypes, paginated
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = service.related('productTypes').query().filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a ServiceProductType
   * @description Create a ServiceProductType with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <ServiceProductType>
   */
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { name, details } = request.all()
    const type = await service.related('productTypes').create({ name, details })

    const image = request.file('image')
    if (image) {
      await type.merge({ image: Attachment.fromFile(image) })
    }

    return response.json(type)
  }
}
