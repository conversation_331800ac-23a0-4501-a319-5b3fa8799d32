import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { DateTime } from 'luxon'
import Booking, { BookingStatus } from 'App/Models/Booking'
import BookingValidationService from 'App/Services/BookingValidationService'
import OptionResolutionService from 'App/Services/OptionResolutionService'

export default class BookingsController {
  /**
   * Display a list of bookings
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const {
        page = 1,
        per = 20,
        branchId,
        customerId,
        status,
        startDate,
        endDate,
        search
      } = request.qs()

      const query = Booking.query()
        .preload('customer')
        .preload('product')
        .preload('branch')
        .preload('vendor')

      // Apply filters
      if (branchId) {
        query.where('branchId', branchId)
      }

      if (customerId) {
        query.where('customerId', customerId)
      }

      if (status) {
        query.where('status', status)
      }

      if (startDate && endDate) {
        query.whereBetween('appointmentStart', [startDate, endDate])
      }

      if (search) {
        query.whereHas('customer', (customerQuery) => {
          customerQuery.whereILike('name', `%${search}%`)
            .orWhereILike('email', `%${search}%`)
        })
        .orWhereHas('product', (productQuery) => {
          productQuery.whereILike('name', `%${search}%`)
        })
        .orWhere('confirmationCode', 'ILIKE', `%${search}%`)
      }

      const bookings = await query
        .orderBy('appointmentStart', 'desc')
        .paginate(page, per)

      return response.json({
        success: true,
        data: bookings
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch bookings',
        error: error.message
      })
    }
  }

  /**
   * Create a new booking
   */
  public async store({ request, response }: HttpContextContract) {
    try {
      const {
        customerId,
        vendorId,
        branchId,
        productId,
        appointmentStart,
        appointmentEnd,
        selectedServiceOptions = [],
        staffAssignments = [],
        equipmentReservations = [],
        bookingNotes = null
      } = request.only([
        'customerId',
        'vendorId', 
        'branchId',
        'productId',
        'appointmentStart',
        'appointmentEnd',
        'selectedServiceOptions',
        'staffAssignments',
        'equipmentReservations',
        'bookingNotes'
      ])

      // Parse appointment times
      const parsedStartTime = DateTime.fromISO(appointmentStart)
      const parsedEndTime = DateTime.fromISO(appointmentEnd)

      if (!parsedStartTime.isValid || !parsedEndTime.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid appointment time format'
        })
      }

      // Validate the booking request
      const validation = await BookingValidationService.validateBookingRequest({
        productId,
        branchId,
        customerId,
        appointmentStart: parsedStartTime,
        appointmentEnd: parsedEndTime,
        selectedServiceOptions,
        staffAssignments,
        equipmentReservations
      })

      if (!validation.valid) {
        return response.status(400).json({
          success: false,
          message: 'Booking validation failed',
          errors: validation.errors,
          warnings: validation.warnings,
          conflicts: validation.conflicts
        })
      }

      // Calculate pricing
      const resolvedOptions = await OptionResolutionService.resolveProductOptions(productId)
      const selectedOptions = resolvedOptions.allOptions.filter(option => 
        selectedServiceOptions.some(selected => selected.id === option.id)
      )
      
      const basePrice = resolvedOptions.basePrice
      const optionsTotal = OptionResolutionService.calculateTotalPrice(0, selectedOptions)
      const totalPrice = basePrice + optionsTotal

      // Calculate duration
      const durationMinutes = parsedEndTime.diff(parsedStartTime, 'minutes').minutes
      const bufferMinutes = selectedOptions
        .filter(opt => opt.type === 'duration')
        .reduce((max, opt) => Math.max(max, opt.duration?.bufferMinutes || 0), 15)

      // Create the booking
      const booking = await Booking.create({
        customerId,
        vendorId,
        branchId,
        productId,
        appointmentStart: parsedStartTime,
        appointmentEnd: parsedEndTime,
        durationMinutes,
        bufferMinutes,
        status: BookingStatus.PENDING,
        staffAssignments,
        equipmentReservations,
        selectedServiceOptions,
        basePrice,
        optionsTotal,
        totalPrice,
        bookingNotes
      })

      // Load relationships for response
      await booking.load('customer')
      await booking.load('product')
      await booking.load('branch')
      await booking.load('vendor')

      return response.status(201).json({
        success: true,
        message: 'Booking created successfully',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to create booking',
        error: error.message
      })
    }
  }

  /**
   * Display a specific booking
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const booking = await Booking.query()
        .where('id', params.id)
        .preload('customer')
        .preload('product')
        .preload('branch')
        .preload('vendor')
        .preload('order')
        .firstOrFail()

      return response.json({
        success: true,
        data: booking
      })

    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Booking not found',
        error: error.message
      })
    }
  }

  /**
   * Update a booking
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const booking = await Booking.findOrFail(params.id)

      // Check if booking can be modified
      if (!booking.canBeModified()) {
        return response.status(400).json({
          success: false,
          message: 'Booking cannot be modified in its current state'
        })
      }

      const {
        appointmentStart,
        appointmentEnd,
        selectedServiceOptions,
        staffAssignments,
        equipmentReservations,
        bookingNotes,
        internalNotes
      } = request.only([
        'appointmentStart',
        'appointmentEnd',
        'selectedServiceOptions',
        'staffAssignments',
        'equipmentReservations',
        'bookingNotes',
        'internalNotes'
      ])

      // If appointment times are being changed, validate
      if (appointmentStart || appointmentEnd) {
        const newStartTime = appointmentStart ? DateTime.fromISO(appointmentStart) : booking.appointmentStart
        const newEndTime = appointmentEnd ? DateTime.fromISO(appointmentEnd) : booking.appointmentEnd

        if (!newStartTime.isValid || !newEndTime.isValid) {
          return response.status(400).json({
            success: false,
            message: 'Invalid appointment time format'
          })
        }

        // Validate the updated booking
        const validation = await BookingValidationService.validateBookingRequest({
          productId: booking.productId,
          branchId: booking.branchId,
          customerId: booking.customerId,
          appointmentStart: newStartTime,
          appointmentEnd: newEndTime,
          selectedServiceOptions: selectedServiceOptions || booking.selectedServiceOptions,
          staffAssignments: staffAssignments || booking.staffAssignments,
          equipmentReservations: equipmentReservations || booking.equipmentReservations,
          excludeBookingId: booking.id
        })

        if (!validation.valid) {
          return response.status(400).json({
            success: false,
            message: 'Booking validation failed',
            errors: validation.errors,
            warnings: validation.warnings,
            conflicts: validation.conflicts
          })
        }

        booking.appointmentStart = newStartTime
        booking.appointmentEnd = newEndTime
        booking.durationMinutes = newEndTime.diff(newStartTime, 'minutes').minutes
      }

      // Update other fields
      if (selectedServiceOptions) {
        booking.selectedServiceOptions = selectedServiceOptions
        
        // Recalculate pricing
        const resolvedOptions = await OptionResolutionService.resolveProductOptions(booking.productId)
        const selectedOptions = resolvedOptions.allOptions.filter(option => 
          selectedServiceOptions.some(selected => selected.id === option.id)
        )
        
        booking.optionsTotal = OptionResolutionService.calculateTotalPrice(0, selectedOptions)
        booking.totalPrice = booking.basePrice + booking.optionsTotal
      }

      if (staffAssignments) booking.staffAssignments = staffAssignments
      if (equipmentReservations) booking.equipmentReservations = equipmentReservations
      if (bookingNotes) booking.bookingNotes = bookingNotes
      if (internalNotes) booking.internalNotes = internalNotes

      await booking.save()

      // Load relationships for response
      await booking.load('customer')
      await booking.load('product')
      await booking.load('branch')
      await booking.load('vendor')

      return response.json({
        success: true,
        message: 'Booking updated successfully',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to update booking',
        error: error.message
      })
    }
  }

  /**
   * Cancel a booking
   */
  public async cancel({ params, request, response, auth }: HttpContextContract) {
    try {
      const booking = await Booking.findOrFail(params.id)
      const { reason } = request.only(['reason'])

      // Check if booking can be cancelled
      if (!booking.canBeCancelled()) {
        return response.status(400).json({
          success: false,
          message: 'Booking cannot be cancelled in its current state'
        })
      }

      await booking.cancel(auth.user?.id || 'system', reason)

      return response.json({
        success: true,
        message: 'Booking cancelled successfully',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to cancel booking',
        error: error.message
      })
    }
  }

  /**
   * Confirm a booking
   */
  public async confirm({ params, response }: HttpContextContract) {
    try {
      const booking = await Booking.findOrFail(params.id)

      await booking.confirm()

      return response.json({
        success: true,
        message: 'Booking confirmed successfully',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to confirm booking',
        error: error.message
      })
    }
  }

  /**
   * Start service for a booking
   */
  public async startService({ params, response }: HttpContextContract) {
    try {
      const booking = await Booking.findOrFail(params.id)

      await booking.startService()

      return response.json({
        success: true,
        message: 'Service started successfully',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to start service',
        error: error.message
      })
    }
  }

  /**
   * Complete service for a booking
   */
  public async completeService({ params, response }: HttpContextContract) {
    try {
      const booking = await Booking.findOrFail(params.id)

      await booking.completeService()

      return response.json({
        success: true,
        message: 'Service completed successfully',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to complete service',
        error: error.message
      })
    }
  }

  /**
   * Mark booking as no-show
   */
  public async markNoShow({ params, response }: HttpContextContract) {
    try {
      const booking = await Booking.findOrFail(params.id)

      await booking.markNoShow()

      return response.json({
        success: true,
        message: 'Booking marked as no-show',
        data: booking
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to mark booking as no-show',
        error: error.message
      })
    }
  }
}
