import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Product from 'App/Models/Product'
import ProductFulfillmentSetting from 'App/Models/ProductFulfillmentSetting'
import Vendor from 'App/Models/Vendor'
import User from 'App/Models/User'
import Database from '@ioc:Adonis/Lucid/Database'
import { DistanceCalculator, Coordinates } from 'App/Utils/DistanceCalculator'
import { DeliveryPricingCalculator } from 'App/Utils/DeliveryPricingCalculator'

// Customer notifications for delivery validation
import CustomerDeliveryUnavailable from 'App/Notifications/Customer/CustomerDeliveryUnavailable'

/**
 * @summary Cart Fulfillment Management
 * @group Cart Fulfillment
 * @version 1.0.0
 * @description Manage cart fulfillment options, delivery validation, and pricing calculations
 */
export default class CartFulfillmentController {
  /**
   * @getFulfillmentOptions
   * @summary Get available fulfillment options for cart items
   * @description Calculate available fulfillment methods (delivery, pickup, dine-in, download) based on cart items and location
   * @tag Cart Fulfillment
   * @requestBody {
   *   "items": [
   *     {"productId": "product-uuid-123", "quantity": 2},
   *     {"productId": "product-uuid-456", "quantity": 1}
   *   ],
   *   "vendorId": "vendor-uuid-789",
   *   "location": {
   *     "lat": -1.2921,
   *     "lng": 36.8219,
   *     "address": "123 Main Street, Nairobi"
   *   }
   * }
   *
   * @responseBody 200 - {"fulfillmentOptions": {"delivery": {"available": true}, "pickup": {"available": true}}, "deliveryOptions": [{"id": "standard_delivery", "name": "Standard Delivery", "fee": 150}], "vendorSettings": {}, "location": {}}
   * @responseBody 400 - {"message": "Failed to get fulfillment options", "error": "string"}
   * @responseBody 422 - Validation failed
   */
  public async getFulfillmentOptions({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          productId: schema.string({}, [rules.exists({ table: 'products', column: 'id' })]),
          quantity: schema.number([rules.unsigned()]),
        })
      ),
      vendorId: schema.string.optional({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      location: schema.object.optional().members({
        lat: schema.number([rules.range(-90, 90)]),
        lng: schema.number([rules.range(-180, 180)]),
        address: schema.string.optional(),
      }),
    })

    const payload = await request.validate({ schema: validationSchema })
    const { items, vendorId, location } = payload

    try {
      // Get all product IDs from cart
      const productIds = items.map((item) => item.productId)

      // Get products with their fulfillment settings
      const products = await Product.query()
        .whereIn('id', productIds)
        .preload('fulfillmentSettings')
        .exec()

      // Get vendor delivery settings if vendorId provided
      let vendorSettings = null
      if (vendorId) {
        const vendor = await Vendor.find(vendorId)
        vendorSettings = {
          deliveryPreferences: vendor?.deliveryPreferences || {},
          availabilitySettings: vendor?.availabilitySettings || {},
        }
      }

      // Calculate available fulfillment options
      const fulfillmentOptions = this.calculateFulfillmentOptions(
        products,
        vendorSettings,
        location
      )

      // Get delivery options if delivery is available
      let deliveryOptions = []
      if (fulfillmentOptions.delivery.available && location) {
        deliveryOptions = await this.getDeliveryOptions(vendorId, location, items)
      }

      return response.ok({
        fulfillmentOptions,
        deliveryOptions,
        vendorSettings,
        location,
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to get fulfillment options',
        error: error.message,
      })
    }
  }

  /**
   * @validateFulfillment
   * @summary Validate a specific fulfillment method for cart items
   * @description Validate if a specific fulfillment type is available for all cart items and send notifications if delivery is unavailable
   * @tag Cart Fulfillment
   * @requestBody {
   *   "items": [
   *     {"productId": "product-uuid-123", "quantity": 2},
   *     {"productId": "product-uuid-456", "quantity": 1}
   *   ],
   *   "fulfillmentType": "delivery",
   *   "vendorId": "vendor-uuid-789",
   *   "deliveryAddress": {
   *     "lat": -1.2921,
   *     "lng": 36.8219,
   *     "address": "123 Main Street, Nairobi"
   *   },
   *   "scheduledTime": "2024-01-15T18:00:00Z"
   * }
   *
   * @responseBody 200 - {"isValid": true, "fulfillmentType": "delivery", "message": "Fulfillment method is valid for all cart items", "estimatedTime": 45, "additionalCharges": []}
   * @responseBody 422 - {"message": "Fulfillment validation failed", "errors": ["Product XYZ is not available for delivery"], "invalidProducts": ["product-uuid-123"]}
   * @responseBody 422 - {"message": "Delivery validation failed", "errors": ["Delivery address is outside service area"]}
   * @responseBody 400 - {"message": "Failed to validate fulfillment", "error": "string"}
   */
  public async validateFulfillment({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          productId: schema.string({}, [rules.exists({ table: 'products', column: 'id' })]),
          quantity: schema.number([rules.unsigned()]),
        })
      ),
      fulfillmentType: schema.enum(['delivery', 'pickup', 'dinein', 'download'] as const),
      vendorId: schema.string({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      deliveryAddress: schema.object.optional().members({
        lat: schema.number([rules.range(-90, 90)]),
        lng: schema.number([rules.range(-180, 180)]),
        address: schema.string(),
      }),
      scheduledTime: schema.date.optional(),
    })

    const payload = await request.validate({ schema: validationSchema })
    const { items, fulfillmentType, vendorId, deliveryAddress, scheduledTime } = payload

    try {
      // Get products with fulfillment settings
      const productIds = items.map((item) => item.productId)
      const products = await Product.query()
        .whereIn('id', productIds)
        .preload('fulfillmentSettings')
        .exec()

      // Validate fulfillment type against all products
      const validation = await this.validateFulfillmentType(
        products,
        fulfillmentType,
        vendorId,
        scheduledTime
      )

      if (!validation.isValid) {
        return response.unprocessableEntity({
          message: 'Fulfillment validation failed',
          errors: validation.errors,
          invalidProducts: validation.invalidProducts,
        })
      }

      // Additional validation for delivery
      if (fulfillmentType === 'delivery' && deliveryAddress) {
        const deliveryValidation = await this.validateDeliveryAddress(vendorId, deliveryAddress)
        if (!deliveryValidation.isValid) {
          // Send notification to customer about delivery unavailability
          await this.sendDeliveryUnavailableNotification(
            request.ctx?.auth?.user,
            { items, vendorId, location: deliveryAddress },
            deliveryValidation.errors
          )

          return response.unprocessableEntity({
            message: 'Delivery validation failed',
            errors: deliveryValidation.errors,
          })
        }
      }

      return response.ok({
        isValid: true,
        fulfillmentType,
        message: 'Fulfillment method is valid for all cart items',
        estimatedTime: validation.estimatedTime,
        additionalCharges: validation.additionalCharges,
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to validate fulfillment',
        error: error.message,
      })
    }
  }

  /**
   * @getDeliveryOptionsForCart
   * @summary Get delivery options for specific location and items
   * @description Calculate delivery options with pricing, estimated times, and vehicle types for cart items at a specific location
   * @tag Cart Fulfillment
   * @requestBody {
   *   "items": [
   *     {"productId": "product-uuid-123", "quantity": 2},
   *     {"productId": "product-uuid-456", "quantity": 1}
   *   ],
   *   "vendorId": "vendor-uuid-789",
   *   "location": {
   *     "lat": -1.2921,
   *     "lng": 36.8219,
   *     "address": "123 Main Street, Nairobi"
   *   }
   * }
   *
   * @responseBody 200 - {"deliveryOptions": [{"id": "standard_delivery", "name": "Standard Delivery", "estimatedTime": 45, "fee": 150, "isAvailable": true}], "location": {}, "totalItems": 2}
   * @responseBody 200 - {"deliveryOptions": [{"id": "standard_delivery", "isAvailable": false}], "location": {}, "totalItems": 2, "warning": "Using default delivery options due to calculation error"}
   * @responseBody 422 - Validation failed
   */
  public async getDeliveryOptionsForCart({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          productId: schema.string(),
          quantity: schema.number(),
        })
      ),
      vendorId: schema.string({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      location: schema.object().members({
        lat: schema.number([rules.range(-90, 90)]),
        lng: schema.number([rules.range(-180, 180)]),
        address: schema.string(),
      }),
    })

    const payload = await request.validate({ schema: validationSchema })
    const { items, vendorId, location } = payload

    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Delivery options calculation timeout')), 10000) // 10 second timeout
      })

      const deliveryOptionsPromise = this.getDeliveryOptions(vendorId, location, items)

      const deliveryOptions = await Promise.race([deliveryOptionsPromise, timeoutPromise])

      return response.ok({
        deliveryOptions,
        location,
        totalItems: items.length,
      })
    } catch (error) {
      console.error('Delivery options error:', error)

      // Return default options on error to prevent hanging
      const defaultOptions = this.getDefaultDeliveryOptions()

      return response.ok({
        deliveryOptions: defaultOptions.map((option) => ({
          ...option,
          isAvailable: false,
          errors: [`Delivery calculation failed: ${error.message}`],
        })),
        location,
        totalItems: items.length,
        warning: 'Using default delivery options due to calculation error',
      })
    }
  }

  /**
   * Calculate available fulfillment options based on products
   */
  private calculateFulfillmentOptions(products: Product[], vendorSettings: any, location: any) {
    const options = {
      delivery: {
        available: true,
        reasons: [],
        requirements: [],
      },
      pickup: {
        available: true,
        reasons: [],
        requirements: [],
      },
      dinein: {
        available: true,
        reasons: [],
        requirements: [],
      },
      download: {
        available: true,
        reasons: [],
        requirements: [],
      },
      schedule: {
        available: true,
        reasons: [],
        requirements: [],
      },
      preorder: {
        available: true,
        reasons: [],
        requirements: [],
      },
    }

    // Check each product's fulfillment settings
    for (const product of products) {
      const settings = product.fulfillmentSettings

      if (!settings) {
        // Use default settings based on product type
        const defaults = this.getDefaultFulfillmentSettings(product)
        this.applyProductConstraints(options, defaults, product.name)
      } else {
        this.applyProductConstraints(options, settings, product.name)
      }
    }

    // Apply vendor-level constraints
    if (vendorSettings) {
      this.applyVendorConstraints(options, vendorSettings, location)
    }

    return options
  }

  /**
   * Apply product-level fulfillment constraints
   */
  private applyProductConstraints(options: any, settings: any, productName: string) {
    if (!settings.isDeliverable) {
      options.delivery.available = false
      options.delivery.reasons.push(`${productName} is not available for delivery`)
    }

    if (!settings.isPickup) {
      options.pickup.available = false
      options.pickup.reasons.push(`${productName} is not available for pickup`)
    }

    if (!settings.physicalConsumptionIsOnsite) {
      options.dinein.available = false
      options.dinein.reasons.push(`${productName} is not available for dine-in`)
    }

    if (!settings.isDownloadable) {
      options.download.available = false
      options.download.reasons.push(`${productName} is not downloadable`)
    }

    if (!settings.scheduleAllowed) {
      options.schedule.available = false
      options.schedule.reasons.push(`${productName} cannot be scheduled`)
    }

    if (!settings.preorderAllowed) {
      options.preorder.available = false
      options.preorder.reasons.push(`${productName} cannot be preordered`)
    }
  }

  /**
   * Apply vendor-level fulfillment constraints
   */
  private applyVendorConstraints(options: any, vendorSettings: any, location: any) {
    const { deliveryPreferences } = vendorSettings

    if (deliveryPreferences && !deliveryPreferences.enableDelivery) {
      options.delivery.available = false
      options.delivery.reasons.push('Vendor does not offer delivery service')
    }

    if (location && deliveryPreferences?.deliveryRadius) {
      // Add location-based delivery validation
      options.delivery.requirements.push(
        `Delivery within ${deliveryPreferences.deliveryRadius}km radius`
      )
    }

    if (deliveryPreferences?.minimumOrderAmount) {
      options.delivery.requirements.push(
        `Minimum order amount: ${deliveryPreferences.minimumOrderAmount}`
      )
    }
  }

  /**
   * Get default fulfillment settings based on product type
   */
  private getDefaultFulfillmentSettings(product: Product) {
    const defaults = {
      isDeliverable: true,
      isPickup: true,
      physicalConsumptionIsOnsite: true,
      isDownloadable: false,
      scheduleAllowed: true,
      preorderAllowed: true,
    }

    // Adjust defaults based on product type
    if (product.type === 'Digital') {
      defaults.isDownloadable = true
      defaults.physicalConsumptionIsOnsite = false
    } else if (product.type === 'Service') {
      defaults.isDeliverable = false
      defaults.physicalConsumptionIsOnsite = false
    }

    return defaults
  }

  /**
   * Validate fulfillment type against products and vendor settings
   */
  private async validateFulfillmentType(
    products: Product[],
    fulfillmentType: string,
    vendorId: string,
    scheduledTime?: Date
  ) {
    const validation = {
      isValid: true,
      errors: [],
      invalidProducts: [],
      estimatedTime: null,
      additionalCharges: [],
    }

    // Get vendor settings for validation
    const vendor = await Vendor.find(vendorId)
    const vendorSettings = vendor?.deliveryPreferences || {}

    // Check vendor-level constraints first
    if (fulfillmentType === 'delivery' && vendorSettings && !vendorSettings.enableDelivery) {
      validation.isValid = false
      validation.errors.push('Vendor does not offer delivery service')
      return validation
    }

    for (const product of products) {
      const settings = product.fulfillmentSettings || this.getDefaultFulfillmentSettings(product)

      switch (fulfillmentType) {
        case 'delivery':
          if (!settings.isDeliverable) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not available for delivery`)
            validation.invalidProducts.push(product.id)
          }
          break
        case 'pickup':
          if (!settings.isPickup) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not available for pickup`)
            validation.invalidProducts.push(product.id)
          }
          break
        case 'dinein':
          if (!settings.physicalConsumptionIsOnsite) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not available for dine-in`)
            validation.invalidProducts.push(product.id)
          }
          break
        case 'download':
          if (!settings.isDownloadable) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not downloadable`)
            validation.invalidProducts.push(product.id)
          }
          break
      }

      // Validate scheduling if scheduled time provided
      if (scheduledTime && !settings.scheduleAllowed) {
        validation.isValid = false
        validation.errors.push(`${product.name} cannot be scheduled`)
        validation.invalidProducts.push(product.id)
      }
    }

    return validation
  }

  /**
   * Validate delivery address against vendor settings
   */
  private async validateDeliveryAddress(vendorId: string, deliveryAddress: any) {
    const vendor = await Vendor.find(vendorId)
    const validation = {
      isValid: true,
      errors: [],
    }

    if (!vendor) {
      validation.isValid = false
      validation.errors.push('Vendor not found')
      return validation
    }

    const deliveryPreferences = vendor.deliveryPreferences || {}

    // Validate delivery radius (simplified - would need actual distance calculation)
    if (deliveryPreferences.deliveryRadius) {
      // Add actual distance calculation logic here
      validation.errors.push(
        `Delivery address must be within ${deliveryPreferences.deliveryRadius}km`
      )
    }

    return validation
  }

  /**
   * Get available delivery options for location using distance-based pricing
   */
  private async getDeliveryOptions(vendorId: string | undefined, location: any, items: any[]) {
    try {
      if (!vendorId || !location) {
        return this.getDefaultDeliveryOptions()
      }

      // Add timeout for vendor lookup to prevent hanging
      const vendorPromise = Vendor.find(vendorId)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Vendor lookup timeout')), 5000) // 5 second timeout
      })

      const vendor = await Promise.race([vendorPromise, timeoutPromise])
      if (!vendor) {
        console.warn(`Vendor ${vendorId} not found, using default delivery options`)
        return this.getDefaultDeliveryOptions()
      }

      // Get vendor location coordinates
      const vendorLocation = this.getVendorCoordinates(vendor)
      if (!vendorLocation) {
        console.warn(`Vendor ${vendorId} has no location coordinates, using default pricing`)
        return this.getDefaultDeliveryOptions()
      }

      // Get customer location coordinates
      const customerLocation: Coordinates = {
        lat: location.lat,
        lng: location.lng,
      }

      // For delivery-options API, we'll skip minimum order validation since this is typically
      // used for estimation. Minimum order validation should be done at checkout time.
      const orderValue = 1000 // Use a value that meets minimum requirements for estimation

      // Use centralized pricing with fallback to vendor pricing
      const { CentralizedDeliveryPricingCalculator } = await import(
        'App/Utils/CentralizedDeliveryPricingCalculator'
      )

      const pricingResult = await CentralizedDeliveryPricingCalculator.calculateWithFallback(
        vendorLocation,
        customerLocation,
        vendor.pricingStructure,
        orderValue
      )

      // Return delivery options or errors
      if (pricingResult.errors.length > 0) {
        console.warn('Delivery pricing calculation errors:', pricingResult.errors)
        // Return options with availability set to false and error messages
        return pricingResult.deliveryOptions.map((option) => ({
          ...option,
          isAvailable: false,
          errors: pricingResult.errors,
        }))
      }

      return pricingResult.deliveryOptions
    } catch (error) {
      console.error('Error calculating delivery options:', error)
      return this.getDefaultDeliveryOptions()
    }
  }

  /**
   * Get default delivery options (fallback)
   */
  private getDefaultDeliveryOptions() {
    return [
      {
        id: 'standard_delivery',
        name: 'Standard Delivery',
        description: 'Regular motorcycle delivery',
        estimatedTime: 45,
        fee: 150,
        vehicleType: 'motorcycle',
        isAvailable: true,
      },
      {
        id: 'express_delivery',
        name: 'Express Delivery',
        description: 'Fast car delivery',
        estimatedTime: 25,
        fee: 250,
        vehicleType: 'car',
        isAvailable: true,
      },
    ]
  }

  /**
   * Get vendor coordinates from vendor model
   */
  private getVendorCoordinates(vendor: Vendor): Coordinates | null {
    // Try to get coordinates from vendor location or geom field
    if (vendor.location && typeof vendor.location === 'object') {
      const loc = vendor.location as any
      if (loc.lat && loc.lng) {
        return { lat: Number(loc.lat), lng: Number(loc.lng) }
      }
    }

    // Try to get from geom field (PostGIS format)
    if (vendor.geom && typeof vendor.geom === 'object') {
      const geom = vendor.geom as any
      if (geom.coordinates && Array.isArray(geom.coordinates) && geom.coordinates.length >= 2) {
        // GeoJSON format: [longitude, latitude]
        return { lat: Number(geom.coordinates[1]), lng: Number(geom.coordinates[0]) }
      }
    }

    // Default coordinates for testing (Nairobi CBD)
    // In production, this should return null and require vendor location setup
    console.warn(
      `Vendor ${vendor.id} has no location coordinates, using default Nairobi CBD location`
    )
    return { lat: -1.2921, lng: 36.8219 }
  }

  /**
   * Calculate total order value from items
   */
  private calculateOrderValue(items: any[]): number {
    if (!items || !Array.isArray(items)) {
      return 0
    }

    return items.reduce((total, item) => {
      const price = Number(item.price) || 0
      const quantity = Number(item.quantity) || 1
      return total + price * quantity
    }, 0)
  }

  /**
   * Send notification to customer when delivery is unavailable
   */
  private async sendDeliveryUnavailableNotification(
    user: User | undefined,
    cartData: any,
    reasons: string[]
  ) {
    try {
      if (!user) {
        console.warn('No authenticated user found for delivery unavailable notification')
        return
      }

      await user.notify(new CustomerDeliveryUnavailable(cartData, reasons))
      console.log(`Delivery unavailable notification sent to user ${user.id}`)
    } catch (error) {
      console.error('Failed to send delivery unavailable notification:', error)
    }
  }
}
