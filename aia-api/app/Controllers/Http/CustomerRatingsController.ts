import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import CustomerRating from '../../Models/CustomerRating'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name CustomerRating management
 * @version 1.0.0
 * @description CustomerRating management for the application
 */
export default class CustomerRatingsController {
  /**
   * @index
   * @summary List all ratings
   * @description List all ratings, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const ratingQuery = CustomerRating.filter(filters)

    return await ratingQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a rating
   * @description Create a rating with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <CustomerRating>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    const { points, comment, customerId, ratings = null } = request.all()

    if (ratings) {
      ratings.map(async (r) => {
        await CustomerRating.create({
          name: r.name,
          points: r.points,
          comment: 'N/A',
          customerId,
          staffId: auth.user?.id,
        })
      })
    } else {
      await CustomerRating.create({ points, comment, customerId, staffId: auth.user?.id })
    }

    return response.json({ message: 'rated!' })
  }

  @bind()
  /**
   * @show
   * @summary Show a single rating
   * @description Show a rating with their details (name and details)
   * @paramPath id required number - CustomerRating ID
   * @responseBody 200 - <CustomerRating>
   * @response 404 - CustomerRating not found
   */
  public async show({ response }: HttpContextContract, rating: CustomerRating) {
    return response.json(rating)
  }

  @bind()
  /**
   * @update
   * @summary Update a rating
   * @description Update a rating with their details (name and details)
   * @paramPath id required number - CustomerRating ID
   * @requestBody <CustomerRating>
   * @responseBody 200 - <CustomerRating>
   * @response 404 - CustomerRating not found
   */
  public async update({ request, response }: HttpContextContract, rating: CustomerRating) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await rating.merge(input).save()

    return response.json(rating)
  }

  @bind()

  /**
   * @destroy
   * @summary Delete a rating
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, rating: CustomerRating) {
    return await rating.delete()
  }
}
