import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Group from '../../Models/Group'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Group management
 * @version 1.0.0
 * @description Group management for the application
 */
export default class GroupsController {
  /**
   * @index
   * @summary List all groups
   * @description List all groups, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const groupQuery = Group.filter(filters).preload('members')

    return await groupQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a group
   * @description Create a group with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Group>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, details, vendorId, branchId } = request.all()
    const group = new Group()

    group.fill({ name, details, vendorId, branchId })

    const image = request.file('image')
    if (image) {
      group.merge({ image: Attachment.fromFile(image) })
    }

    await group.save()

    return response.json(group)
  }

  @bind()
  /**
   * @show
   * @summary Show a single group
   * @description Show a group with their details (name and details)
   * @paramPath id required number - Group ID
   * @responseBody 200 - <Group>
   * @response 404 - Group not found
   */
  public async show({ response }: HttpContextContract, group: Group) {
    return response.json(group)
  }

  @bind()
  /**
   * @update
   * @summary Update a group
   * @description Update a group with their details (name and details)
   * @paramPath id required number - Group ID
   * @requestBody <Group>
   * @responseBody 200 - <Group>
   * @response 404 - Group not found
   */
  public async update({ request, response }: HttpContextContract, group: Group) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await group.merge(input).save()

    return response.json(group)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a group
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, group: Group) {
    return await group.delete()
  }
}
