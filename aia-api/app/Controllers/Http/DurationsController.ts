import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Duration from 'App/Models/Duration'
import CreateDurationValidator from 'App/Validators/CreateDurationValidator'
import UpdateDurationValidator from 'App/Validators/UpdateDurationValidator'

export default class DurationsController {
  /**
   * Display a list of durations with filtering and pagination
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const category = request.input('category')
      const active = request.input('active')
      const search = request.input('search')

      const query = Duration.query()

      // Apply filters
      if (category) {
        query.where('category', category)
      }

      if (active !== undefined) {
        query.where('active', active === 'true')
      }

      if (search) {
        query.where((builder) => {
          builder
            .where('name', 'ILIKE', `%${search}%`)
            .orWhere('description', 'ILIKE', `%${search}%`)
        })
      }

      // Order by category and name
      query.orderBy([
        { column: 'category', order: 'asc' },
        { column: 'minutes', order: 'asc' },
        { column: 'name', order: 'asc' }
      ])

      const durations = await query.paginate(page, limit)

      return response.json({
        success: true,
        data: durations.toJSON(),
        meta: {
          total: durations.total,
          perPage: durations.perPage,
          currentPage: durations.currentPage,
          lastPage: durations.lastPage,
          hasMorePages: durations.hasMorePages
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch durations',
        error: error.message
      })
    }
  }

  /**
   * Create a new duration
   */
  public async store({ request, response }: HttpContextContract) {
    try {
      const payload = await request.validate(CreateDurationValidator)
      
      const duration = await Duration.create(payload)

      // Validate the configuration
      const validation = duration.validateConfiguration()
      if (!validation.valid) {
        await duration.delete()
        return response.status(400).json({
          success: false,
          message: 'Invalid duration configuration',
          errors: validation.errors
        })
      }

      return response.status(201).json({
        success: true,
        message: 'Duration created successfully',
        data: duration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create duration',
        error: error.message
      })
    }
  }

  /**
   * Display a specific duration
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const duration = await Duration.findOrFail(params.id)

      return response.json({
        success: true,
        data: {
          ...duration.toJSON(),
          calendarBlockMinutes: duration.calendarBlockMinutes,
          totalHours: duration.totalHours,
          validation: duration.validateConfiguration()
        }
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Duration not found',
        error: error.message
      })
    }
  }

  /**
   * Update a duration
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const duration = await Duration.findOrFail(params.id)
      const payload = await request.validate(UpdateDurationValidator)

      duration.merge(payload)
      
      // Validate the configuration before saving
      const validation = duration.validateConfiguration()
      if (!validation.valid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid duration configuration',
          errors: validation.errors
        })
      }

      await duration.save()

      return response.json({
        success: true,
        message: 'Duration updated successfully',
        data: duration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update duration',
        error: error.message
      })
    }
  }

  /**
   * Delete a duration
   */
  public async destroy({ params, response }: HttpContextContract) {
    try {
      const duration = await Duration.findOrFail(params.id)
      
      // TODO: Check if duration is being used by any service configurations or options
      // For now, we'll just soft delete by setting active to false
      duration.active = false
      await duration.save()

      return response.json({
        success: true,
        message: 'Duration deactivated successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to delete duration',
        error: error.message
      })
    }
  }

  /**
   * Get duration categories with counts
   */
  public async categories({ response }: HttpContextContract) {
    try {
      const categories = await Duration.query()
        .select('category')
        .count('* as total')
        .where('active', true)
        .groupBy('category')
        .orderBy('category')

      return response.json({
        success: true,
        data: categories
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch categories',
        error: error.message
      })
    }
  }

  /**
   * Create duration from template
   */
  public async createFromTemplate({ request, response }: HttpContextContract) {
    try {
      const { templateType, name, minutes, options } = request.only(['templateType', 'name', 'minutes', 'options'])
      
      if (!templateType || !name || !minutes) {
        return response.status(400).json({
          success: false,
          message: 'Template type, name, and minutes are required'
        })
      }

      const template = Duration.createTemplate(name, minutes, templateType, options || {})
      const duration = await Duration.create(template)

      return response.status(201).json({
        success: true,
        message: 'Duration created from template successfully',
        data: duration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create duration from template',
        error: error.message
      })
    }
  }

  /**
   * Validate duration configuration
   */
  public async validate({ request, response }: HttpContextContract) {
    try {
      const payload = request.only([
        'minutes', 'bufferMinutes', 'maxConcurrent', 'requiredBreakAfter',
        'schedulingRules', 'branchConstraints'
      ])

      // Create a temporary duration instance for validation
      const tempDuration = new Duration()
      tempDuration.fill(payload)

      const validation = tempDuration.validateConfiguration()

      return response.json({
        success: true,
        data: validation
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to validate configuration',
        error: error.message
      })
    }
  }
}
