import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Vendor from '../../Models/Vendor'
import User from '../../Models/User'
import { bind } from '@adonisjs/route-model-binding'
import { DateTime } from 'luxon'
import Database from '@ioc:Adonis/Lucid/Database'

/**
 * @name Vendor management
 * @version 1.0.0
 * @description Vendor management for the application
 */
export default class VendorsController {
  /**
   * @index
   * @summary List all vendors
   * @description List all vendors, paginated
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @responseBody 200 - <Vendor[]>
   */
  public async index({ request, response }: HttpContextContract) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const vendorQuery = Vendor.filter(filters)
      .preload('branches')
      .preload('categories')
      .preload('specialities')

    const vendors = await vendorQuery.orderBy(order, sort).paginate(page, per)

    return response.json(vendors)
  }

  /**
   * @store
   * @summary Create a vendor
   * @description Create a new vendor with their details and branch information
   * @requestBody {
   *   "name": "string",
   *   "slug": "string",
   *   "reg": "string",
   *   "kra": "string",
   *   "email": "string",
   *   "phone": "string",
   *   "details": "string",
   *   "serviceId": "string",
   *   "vendorCategoryId": "string",
   *   "userId": "string",
   *   "branch": {
   *     "name": "string",
   *     "email": "string",
   *     "phone": "string",
   *     "details": "string",
   *     "location": {
   *       "name": "string",
   *       "address": "string",
   *       "regions": {
   *         "administrative_area_level_1": "string",
   *         "country": "string"
   *       },
   *       "coordinates": {
   *         "lat": "number",
   *         "lng": "number"
   *       },
   *       "place_id": "string"
   *     },
   *     "identifier": "string"
   *   }
   * }
   * @responseBody 200 - {
   *   "id": "string",
   *   "name": "string",
   *   "slug": "string",
   *   "reg": "string",
   *   "kra": "string",
   *   "email": "string",
   *   "phone": "string",
   *   "details": "string",
   *   "logo": "string",
   *   "cover": "string",
   *   "serviceId": "string",
   *   "userId": "string",
   *   "createdAt": "string",
   *   "updatedAt": "string",
   *   "branches": "array",
   *   "categories": "array"
   * }
   * @response 400 - Invalid input
   * @response 401 - Unauthorized
   * @response 403 - Forbidden
   */
  public async store({ request, response }: HttpContextContract) {
    const {
      name,
      slug,
      reg,
      kra,
      email,
      phone: postedPhone,
      details,
      serviceId,
      vendorCategoryId,
      userId,
      branch = null,
      code = null,
    } = request.all()

    const phone = postedPhone.replace(/^\+/, '')

    // Generate a code from the name if not provided
    const vendorCode =
      code ||
      name
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase())
        .join('')
        .substring(0, 3)

    const vendorQ = await Vendor.create({
      userId,
      name,
      code: vendorCode,
      slug,
      email,
      phone,
      reg: reg || DateTime.now().toISODate(),
      kra: kra || DateTime.now().toISODate(),
      serviceId,
    })

    const logo = request.file('logo')
    if (logo) {
      await vendorQ.merge({ logo: Attachment.fromFile(logo) }).save()
    }

    const cover = request.file('cover')
    if (cover) {
      await vendorQ.merge({ cover: Attachment.fromFile(cover) }).save()
    }

    if (branch) {
      const { name, email, phone: postedPhone, details, location, identifier = null } = branch
      const phone = postedPhone.replace(/^\+/, '')
      const branchQ = await vendorQ
        .related('branches')
        .create({ name, email, phone, details, location })

      const image = request.file('image')
      if (image) {
        branchQ.merge({ image: Attachment.fromFile(image) }).save()
      }

      branchQ
        .related('staff')
        .attach({ [userId]: { vendor_id: vendorQ.id, identifier: identifier || phone } })
    } else {
      const hq = await vendorQ.related('branches').create({
        name: 'HQ',
        email,
        phone,
        details,
      })
      await hq.related('staff').attach({ [userId]: { vendor_id: vendorQ.id, identifier: phone } })
    }

    await vendorQ.related('categories').attach([vendorCategoryId])

    return response.json(vendorQ)
  }

  /**
   * @show
   * @summary Show a single vendor
   * @description Show a vendor with their details
   * @paramPath id required string - Vendor ID
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor) {
    await vendor.load('branches')
    await vendor.load('categories')
    await vendor.load('specialities')
    return response.json(vendor)
  }

  /**
   * @update
   * @summary Update a vendor
   * @description Update a vendor with their details
   * @paramPath id required string - Vendor ID
   * @requestBody {
   *   "name": "string",
   *   "slug": "string",
   *   "reg": "string",
   *   "kra": "string",
   *   "email": "string",
   *   "phone": "string",
   *   "details": "string",
   *   "serviceId": "string",
   *   "vendorCategoryId": "string"
   * }
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  @bind()
  public async update({ request, response }: HttpContextContract, vendor: Vendor) {
    const input = request.all()
    const image = request.file('logo')
    const cover = request.file('cover')!

    // Convert empty serviceId to null
    if (input.serviceId === '') {
      input.serviceId = null
    }

    delete input.logo
    delete input.cover

    await vendor.merge(input).save()

    if (image) {
      await vendor.merge({ logo: Attachment.fromFile(image) }).save()
    }

    if (cover) {
      await vendor.merge({ cover: Attachment.fromFile(cover) }).save()
    }

    return response.json(vendor)
  }

  /**
   * @destroy
   * @summary Delete a vendor
   * @description Delete a vendor and all associated data
   * @paramPath id required string - Vendor ID
   * @responseBody 204 - No content
   * @response 404 - Vendor not found
   */
  @bind()
  public async destroy(_: HttpContextContract, vendor: Vendor) {
    return await vendor.delete()
  }

  /**
   * @updateCategory
   * @summary Update a vendor's category
   * @description Change a vendor's category association
   * @paramPath id required string - Vendor ID
   * @requestBody {"categoryId": "string"}
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  @bind()
  public async updateCategory({ request, response }: HttpContextContract, vendor: Vendor) {
    const { categoryId } = request.all()

    // First, detach from all current categories
    await vendor.related('categories').detach()

    // Then attach to the new category if provided
    if (categoryId) {
      // Use the correct pivot table structure with vendor_id and vendor_category_id
      await Database.table('vendor_set_categories').insert({
        vendor_id: vendor.id,
        vendor_category_id: categoryId,
        created_at: new Date(),
        updated_at: new Date(),
      })
    }

    // Load the updated vendor with its categories
    await vendor.load('categories')

    return response.json(vendor)
  }

  /**
   * @staff
   * @summary Get vendor staff
   * @description Get all staff members associated with a vendor across all branches
   * @paramPath id - Vendor ID
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @responseBody 200 - Staff list with branch and role information
   * @response 404 - Vendor not found
   */
  @bind()
  public async staff({ request, response }: HttpContextContract, vendor: Vendor) {
    try {
      const { per = 25, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

      // Get all staff for this vendor using a simpler query
      const staff = await Database.rawQuery(
        `
        SELECT
          u.*,
          s.identifier,
          s.online,
          s.vendor_id,
          s.branch_id,
          s.access_level,
          s.can_supervise_department,
          s.can_manage_branch,
          s.can_manage_vendor,
          b.name as branch_name,
          b.email as branch_email
        FROM users u
        JOIN staff s ON u.id = s.user_id
        JOIN branches b ON s.branch_id = b.id
        WHERE s.vendor_id = ?
        ORDER BY u.created_at DESC
      `,
        [vendor.id]
      )

      return response.json({
        data: staff.rows,
        meta: {
          total: staff.rows.length,
          vendor_id: vendor.id,
          vendor_name: vendor.name,
        },
      })
    } catch (error) {
      console.error('Error fetching vendor staff:', error)
      return response.status(500).json({
        error: 'Failed to fetch vendor staff',
        message: error.message,
      })
    }
  }
}
