import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Vendor, { VendorQRPreferences } from 'App/Models/Vendor'
import Lot from 'App/Models/Lot'
import Section from 'App/Models/Section'
import Branch from 'App/Models/Branch'
import QRCodeGenerationService from 'App/Services/QRCodeGenerationService'
import TableQRCode from 'App/Models/TableQRCode'

export default class TestAutoQRController {
  /**
   * Test enabling auto-generation for a vendor
   */
  public async enableAutoGeneration({ request, response }: HttpContextContract) {
    const { vendorId } = request.params()

    try {
      const vendor = await Vendor.findOrFail(vendorId)

      // Enable auto-generation
      const preferences: VendorQRPreferences = {
        autoGeneration: {
          enabled: true,
          onLotCreation: true,
          onBulkImport: false,
        },
        defaultOptions: {
          width: 300,
          errorCorrectionLevel: 'M',
          color: {
            dark: '#000000',
            light: '#ffffff',
          },
        },
        notifications: {
          onSuccess: false,
          onFailure: true,
        },
      }

      vendor.qrCodePreferences = preferences
      await vendor.save()

      return response.json({
        success: true,
        data: {
          vendorId: vendor.id,
          vendorName: vendor.name,
          preferences: vendor.qrCodePreferences,
        },
        message: 'Auto-generation enabled successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to enable auto-generation',
        error: error.message,
      })
    }
  }

  /**
   * Test creating a lot and verify QR code auto-generation
   */
  public async testLotCreation({ request, response }: HttpContextContract) {
    const { vendorId } = request.params()
    const { sectionId, tableName } = request.all()

    try {
      // First, ensure vendor has auto-generation enabled
      const vendor = await Vendor.findOrFail(vendorId)

      if (!vendor.qrCodePreferences?.autoGeneration?.enabled) {
        return response.status(400).json({
          success: false,
          message: 'Auto-generation not enabled for this vendor. Call enableAutoGeneration first.',
        })
      }

      // Get section details
      const section = await Section.query().where('id', sectionId).preload('branch').firstOrFail()

      // Create a new lot (table)
      const lot = await Lot.create({
        name: tableName,
        details: `Test table created for auto QR generation`,
        sectionId: sectionId,
      })

      // Simulate the auto-generation process (synchronously for testing)
      const qrService = new QRCodeGenerationService()

      const tableData = {
        vendorId: vendor.id,
        branchId: section.branchId,
        sectionId: section.id,
        lotId: lot.id,
        tableNumber: lot.name,
      }

      const qrCode = await qrService.generateTableQRCode(tableData, {
        ...vendor.qrCodePreferences.defaultOptions,
        isAutoGenerated: true,
      })

      return response.json({
        success: true,
        data: {
          lot: {
            id: lot.id,
            name: lot.name,
            sectionId: lot.sectionId,
          },
          qrCode: {
            id: qrCode.id,
            tableNumber: qrCode.tableNumber,
            qrCodeUrl: qrCode.qrCodeUrl,
            isActive: qrCode.isActive,
            generatedAt: qrCode.generatedAt,
          },
          vendor: {
            id: vendor.id,
            name: vendor.name,
          },
          section: {
            id: section.id,
            name: section.name,
          },
        },
        message: 'Lot created and QR code auto-generated successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to create lot or generate QR code',
        error: error.message,
      })
    }
  }

  /**
   * Test getting QR code preferences
   */
  public async getPreferences({ request, response }: HttpContextContract) {
    const { vendorId } = request.params()

    try {
      const vendor = await Vendor.findOrFail(vendorId)

      const preferences = vendor.qrCodePreferences || {
        autoGeneration: {
          enabled: false,
          onLotCreation: false,
          onBulkImport: false,
        },
        defaultOptions: {
          width: 300,
          errorCorrectionLevel: 'M',
          color: {
            dark: '#000000',
            light: '#ffffff',
          },
        },
        notifications: {
          onSuccess: false,
          onFailure: true,
        },
      }

      return response.json({
        success: true,
        data: {
          vendorId: vendor.id,
          vendorName: vendor.name,
          preferences,
        },
        message: 'Preferences retrieved successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get preferences',
        error: error.message,
      })
    }
  }

  /**
   * Test cleanup - remove test QR codes and lots
   */
  public async cleanup({ request, response }: HttpContextContract) {
    const { vendorId } = request.all()

    try {
      // Find test lots (those with "Test table" in details)
      const testLots = await Lot.query()
        .whereHas('section', (sectionQuery) => {
          sectionQuery.whereHas('branch', (branchQuery) => {
            branchQuery.where('vendorId', vendorId)
          })
        })
        .where('details', 'like', '%Test table created for auto QR generation%')

      let deletedLots = 0
      let deletedQRCodes = 0

      for (const lot of testLots) {
        // Delete associated QR codes
        const qrCodes = await TableQRCode.query().where('lotId', lot.id)
        for (const qrCode of qrCodes) {
          await qrCode.delete()
          deletedQRCodes++
        }

        // Delete the lot
        await lot.delete()
        deletedLots++
      }

      return response.json({
        success: true,
        data: {
          deletedLots,
          deletedQRCodes,
        },
        message: 'Test data cleaned up successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to cleanup test data',
        error: error.message,
      })
    }
  }

  /**
   * Get test status and statistics
   */
  public async getTestStatus({ request, response }: HttpContextContract) {
    const { vendorId } = request.params()

    try {
      const vendor = await Vendor.findOrFail(vendorId)

      // Count test lots
      const testLotsCount = await Lot.query()
        .whereHas('section', (sectionQuery) => {
          sectionQuery.whereHas('branch', (branchQuery) => {
            branchQuery.where('vendorId', vendorId)
          })
        })
        .where('details', 'like', '%Test table created for auto QR generation%')
        .count('* as total')

      // Count QR codes for this vendor
      const qrCodesCount = await TableQRCode.query().where('vendorId', vendorId).count('* as total')

      return response.json({
        success: true,
        data: {
          vendor: {
            id: vendor.id,
            name: vendor.name,
            autoGenerationEnabled: vendor.qrCodePreferences?.autoGeneration?.enabled || false,
          },
          testLots: parseInt(testLotsCount[0].$extras.total),
          totalQRCodes: parseInt(qrCodesCount[0].$extras.total),
        },
        message: 'Test status retrieved successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get test status',
        error: error.message,
      })
    }
  }
}
