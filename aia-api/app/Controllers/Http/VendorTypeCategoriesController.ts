import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import VendorType from 'App/Models/VendorType'

export default class VendorTypeCategoriesController {
  @bind()

  /**
   * @name VendorType management
   * @index
   * @summary List all VendorTypes
   * @description List all VendorTypes, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  public async index({ request }: HttpContextContract, type: VendorType) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const categoryQuery = type.related('vendorCategories').query().filter(filters)

    return await categoryQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a VendorType
   * @description Create a VendorType with their details (name and details)
   * @requestBody {"name": "", "details": "", "vendorTypeId": ""}
   * @responseBody 200 - <VendorType>
   */
  public async store({ request }: HttpContextContract, type: VendorType) {
    const { name, details, vendorTypeId } = request.all()

    const category = await type.related('vendorCategories').create({ name, details, vendorTypeId })

    const image = request.file('image')
    if (image) {
      category.merge({ image: Attachment.fromFile(image) })
    }
  }
}
