import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from '../../Models/Order'
import User from '../../Models/User'
import Product from '../../Models/Product'
import { bind } from '@adonisjs/route-model-binding'
import CustomerOrderStatusChange from 'App/Notifications/Customer/CustomerOrderStatusChange'
import { DateTime } from 'luxon'
import { Queue } from '@ioc:Rlanz/Queue'
import QRCode from 'qrcode'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import ModifierOption from 'App/Models/ModifierOption'
import OrderItem from '../../Models/OrderItem'
import OrderPricingService from 'App/Services/OrderPricingService'
import StatusChangeBroadcaster from '../../Services/StatusChangeBroadcaster'
import PackagingChargeService from 'App/Services/PackagingChargeService'
import OrderChargeService from 'App/Services/OrderChargeService'
import DeliveryChargeService from 'App/Services/DeliveryChargeService'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

/**
 * @name Order management
 * @version 1.0.0
 * @description Order management for the application
 */
export default class OrdersController {
  /**
   * @index
   * @summary List all Orders for authenticated user
   * @description List all Orders for the authenticated user, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request, auth }: HttpContextContract) {
    const {
      per = 15,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      include_fulfillment = 'false',
      status_filter,
      department_filter,
      ...filters
    } = request.qs()

    // SECURITY: Apply vendor-scoped role-based filtering
    const user = auth.user!

    // Preload user relationships for role-based filtering
    await user.load('roles')
    await user.load('vendors')
    await user.load('branches')
    await user.load('employers') // Staff vendor assignments
    await user.load('stations') // Staff branch assignments

    const OrderQuery = Order.filter(filters)
      .preload('customer')
      .preload('branch')
      .preload('vendor')
      .preload('items', (itemQuery) => {
        itemQuery.preload('product')
        itemQuery.preload('modifiers', (modifierQuery) => {
          if (include_fulfillment === 'true') {
            modifierQuery.preload('preparedByStaff')
          }
        })
        if (include_fulfillment === 'true') {
          itemQuery.preload('department')
          itemQuery.preload('assignedStaff')
        }
      })
      .preload('staff')
      .preload('section')
      .preload('invoices', (iq) => iq.preload('payments'))

    // Apply hierarchical role-based filtering with table assignments
    await HierarchicalAccessControlService.applyRoleBasedFilter(OrderQuery, user)

    // Apply status filtering if requested
    if (status_filter) {
      OrderQuery.whereHas('items', (itemQuery) => {
        itemQuery.where('status', status_filter)
      })
    }

    // Apply department filtering if requested
    if (department_filter) {
      OrderQuery.whereHas('items', (itemQuery) => {
        itemQuery.where('department_id', department_filter)
      })
    }

    const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

    // Handle temp orders (Pending status) - populate items from meta.temp_items
    const ordersData = orders.serialize()
    for (const orderData of ordersData.data) {
      if (orderData.status === 'Pending' && orderData.meta?.temp_items) {
        // This is a temp order - populate items from meta.temp_items
        const tempItems = orderData.meta.temp_items
        const productIds = Object.keys(tempItems)

        if (productIds.length > 0) {
          // Import Product model dynamically to avoid circular dependency
          const { default: Product } = await import('../../Models/Product')
          const products = await Product.query()
            .whereIn('id', productIds)
            .preload('category')
            .preload('gallery')
            .preload('forms')
            .preload('branch')
            .preload('vendor')
            .exec()

          // Replace empty items array with populated items from temp_items
          orderData.items = products.map((product) => {
            const quantity = tempItems[product.id]?.quantity || 0
            const productPrice = product.price || 0
            const totalItemCost = productPrice * quantity

            return {
              id: null, // No order_items record yet
              orderId: orderData.id,
              productId: product.id,
              quantity: quantity,
              meta: null,
              createdAt: orderData.createdAt,
              updatedAt: orderData.updatedAt,
              price: productPrice, // ✅ FIX: Set actual product price
              status: 'pending',
              departmentId: null,
              assignedStaffId: null,
              estimatedPreparationTime: null,
              preparationStartedAt: null,
              preparationCompletedAt: null,
              servedAt: null,
              priorityLevel: 1,
              requiresSpecialAttention: false,
              specialInstructions: null,
              preparationNotes: null,
              statusHistory: null,
              qualityCheckStatus: 'not_required',
              qualityCheckedBy: null,
              customerModifications: null,
              cancellationReason: null,
              actualPreparationTime: null,
              preparationAttempts: 1,
              modifiers: [],
              product: product.toJSON(), // ✅ FIX: Now includes all relationships
              actualPreparationTimeMinutes: null,
              isOverdue: false,
              preparationProgress: 0,
              statusDisplayName: 'Pending',
              canBeStarted: true,
              canBeCompleted: false,
              canBeServed: false,
              requiresAttention: false,
              estimatedCompletionTime: null,
              allModifiersCompleted: true,
              totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost
            }
          })
        }
      }
    }

    // Add pricing information to the modified data
    const ordersWithPricing = {
      ...ordersData,
      data: OrderPricingService.addPricingToOrders(ordersData.data),
    }

    // Add fulfillment information if requested
    if (include_fulfillment === 'true') {
      for (const orderData of ordersWithPricing.data) {
        // Calculate fulfillment progress for each order
        const totalItems = orderData.items?.length || 0
        const completedItems =
          orderData.items?.filter((item) => ['ready', 'served'].includes(item.status)).length || 0

        orderData.fulfillment = {
          progress: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,
          total_items: totalItems,
          completed_items: completedItems,
          pending_items: orderData.items?.filter((item) => item.status === 'pending').length || 0,
          preparing_items:
            orderData.items?.filter((item) => item.status === 'preparing').length || 0,
          ready_items: orderData.items?.filter((item) => item.status === 'ready').length || 0,
          has_overdue_items: orderData.items?.some((item) => item.is_overdue) || false,
          requires_attention: orderData.items?.some((item) => item.requires_attention) || false,
        }
      }

      return ordersWithPricing
    }

    return ordersWithPricing
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's orders
   * Used by customer apps to ensure customers only see their own orders
   */
  public async customerOrders({ request, auth }: HttpContextContract) {
    const {
      per = 15,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      include_fulfillment = 'false',
      status_filter,
      ...filters
    } = request.qs()

    const user = auth.user!

    const OrderQuery = Order.filter(filters)
      .preload('customer')
      .preload('branch')
      .preload('vendor')
      .preload('items', (itemQuery) => {
        itemQuery.preload('product')
        itemQuery.preload('modifiers', (modifierQuery) => {
          if (include_fulfillment === 'true') {
            modifierQuery.preload('preparedByStaff')
          }
        })
        if (include_fulfillment === 'true') {
          itemQuery.preload('department')
          itemQuery.preload('assignedStaff')
        }
      })
      .preload('staff')
      .preload('section')
      .preload('invoices', (iq) => iq.preload('payments'))

    // Customer endpoint ALWAYS shows only user's own orders
    HierarchicalAccessControlService.applyCustomerOnlyFilter(OrderQuery, user)

    // Apply status filter if provided
    if (status_filter) {
      const statusArray = status_filter.split(',').map((s) => s.trim())
      OrderQuery.whereIn('status', statusArray)
    }

    const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

    // Custom serialization to include unified items (OrderItems + temp_items)
    const serializedOrders = orders.serialize()
    serializedOrders.data = orders.all().map((order) => {
      const orderData = order.serializeForMessageCenter()
      // Add pricing information
      const orderWithPricing = OrderPricingService.addPricingToOrder(orderData)
      return {
        ...orderData,
        pricing: orderWithPricing.pricing,
      }
    })

    return serializedOrders
  }

  /**
   * Customer checkout endpoint - Secure cart-to-order conversion
   * Always creates order for authenticated user only
   */
  public async customerCheckout({ request, response, auth }: HttpContextContract) {
    try {
      const {
        vendorId,
        branchId,
        sectionId,
        lotId,
        action = 'Purchase',
        type = 'Instant',
        delivery = 'Takeaway',
        status = 'Pending',
        meta = {},
        items = {},
        startAt = DateTime.now().toISO(),
        endAt = null,
        location_context = 'within', // Default to 'within' if not provided
        deliveryAddress, // Add delivery address parameter
      } = request.all()

      const user = auth.user!

      // Validate that order has items
      if (!items || Object.keys(items).length === 0) {
        return response.badRequest({
          error: 'Order must contain at least one item',
          details: 'Cannot create an order without items',
        })
      }

      // Normalize delivery value to match database constraint
      const normalizedDelivery = this.normalizeDeliveryValue(delivery)

      // Validate normalized delivery value
      const validDeliveryTypes = ['Takeaway', 'Dinein', 'Delivery', 'Selfpick']
      if (!validDeliveryTypes.includes(normalizedDelivery)) {
        return response.badRequest({
          error: 'Invalid delivery type',
          details: `Delivery must be one of: ${validDeliveryTypes.join(', ')}`,
          received: delivery,
          normalized: normalizedDelivery,
        })
      }

      // Customer checkout ALWAYS assigns order to authenticated user
      const finalCustomerId = user.id

      // Add location context to meta for staff operations
      const enhancedMeta = {
        ...meta,
        location_context: location_context, // 'within' or 'away' for staff reference
      }

      // Add delivery address to meta for delivery orders
      if (normalizedDelivery === 'Delivery' && deliveryAddress) {
        enhancedMeta.deliveryAddress = deliveryAddress
      }

      const order = await Order.create({
        userId: finalCustomerId,
        staffId: null, // No staff for customer orders
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery: normalizedDelivery,
        status,
        meta: enhancedMeta,
        startAt: DateTime.fromISO(startAt),
        endAt,
      })

      // Create order items with modifier processing
      await Promise.all(
        Object.keys(items).map(async (productId) => {
          const product = await Product.find(productId)
          const productPrice = product ? Number(product.price || 0) : 0
          const itemData = items[productId]

          // Handle both old format (number) and new format (object)
          const quantity = typeof itemData === 'object' ? itemData.quantity : itemData
          const modifiers = typeof itemData === 'object' ? itemData.modifiers : {}
          const selectedModifiers = typeof itemData === 'object' ? itemData.selected_modifiers : []

          const orderItem = await order.related('items').create({
            productId: productId,
            quantity: Number(quantity),
            price: productPrice,
            meta: {
              modifiers: modifiers,
              // Store any additional item metadata
              ...(typeof itemData === 'object' ? itemData : {}),
            },
          })

          // Process modifiers if present (handle both formats)
          const hasModifiers =
            (modifiers && Object.keys(modifiers).length > 0) ||
            (selectedModifiers && selectedModifiers.length > 0)

          if (hasModifiers) {
            // Load product with available modifiers for price calculation
            await orderItem.load('product', (productQuery) => {
              productQuery.preload('availableModifiers')
            })

            // Process modifiers from both formats
            const modifiersToProcess = []

            // Handle old format (object with modifier_id: quantity)
            if (modifiers && Object.keys(modifiers).length > 0) {
              for (const [modifierOptionId, modifierQuantity] of Object.entries(modifiers)) {
                modifiersToProcess.push({
                  modifier_option_id: modifierOptionId,
                  quantity: modifierQuantity,
                })
              }
            }

            // Handle new format (array of objects)
            if (selectedModifiers && selectedModifiers.length > 0) {
              modifiersToProcess.push(...selectedModifiers)
            }

            for (const modifierData of modifiersToProcess) {
              try {
                const modifierOptionId = modifierData.modifier_option_id
                const modifierQuantity = modifierData.quantity || 1

                // Find the specific product modifier pivot data
                const productModifierPivot = orderItem.product?.availableModifiers.find(
                  (mod) => mod.id === modifierOptionId
                )?.$extras.pivot

                // Fetch ModifierOption details for default price
                const modifierOption = await ModifierOption.find(modifierOptionId)

                if (!modifierOption) {
                  console.error(`ModifierOption ${modifierOptionId} not found.`)
                  continue
                }

                const priceToApply =
                  productModifierPivot?.price_adjustment_override ??
                  modifierOption.defaultPriceAdjustment

                // Create the OrderItemModifier record
                await orderItem.related('modifiers').create({
                  modifierOptionId: modifierOptionId,
                  quantity: Number(modifierQuantity) || 1,
                  priceAtTimeOfOrder: priceToApply,
                })
              } catch (modifierError) {
                console.error(
                  `Failed to process modifier ${modifierData.modifier_option_id}:`,
                  modifierError
                )
                // Continue with other modifiers
              }
            }
          }

          return orderItem
        })
      )

      // Load relationships for response including modifiers for pricing calculation
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await order.load('staff')
      await order.load('items', (itemQuery) => {
        itemQuery.preload('product')
        itemQuery.preload('modifiers') // Load modifiers for accurate pricing calculation
      })

      // BUSINESS RULES: Apply all charges before final pricing calculation
      try {
        // Apply service charges (10% surcharge as per business rules)
        await OrderChargeService.applyChargesToOrder(order)

        // Apply packaging charges for applicable delivery types
        if (['Takeaway', 'Delivery', 'Selfpick'].includes(order.delivery)) {
          await PackagingChargeService.applyPackagingCharges(order)
        }

        // Apply delivery charges for delivery orders
        if (order.delivery === 'Delivery') {
          await DeliveryChargeService.applyDeliveryCharges(order)
        }

        // NOTE: Preorder surcharge is now handled by the ChargeConfiguration system
        // The "Preorder Surcharge" charge configuration automatically applies 5% to Preorder type orders
        // No manual application needed here

        // Refresh order to get updated charges
        await order.refresh()

        console.log(`✅ Charges applied to order ${order.id}`)
      } catch (error) {
        console.error(`❌ Error applying charges to order ${order.id}:`, error)
      }

      // Load all relationships needed for pricing calculation
      await order.load('items', (itemQuery) => {
        itemQuery.preload('product').preload('modifiers')
      })

      // Calculate pricing with all charges included
      const orderWithPricing = OrderPricingService.addPricingToOrder(order.serialize())

      // DATABASE FIX: Persist calculated pricing to database
      try {
        const updatedMeta = {
          ...order.meta,
          pricing: orderWithPricing.pricing,
        }

        order.meta = updatedMeta
        await order.save()

        console.log(
          `✅ Pricing stored for order ${order.id}: ${JSON.stringify(orderWithPricing.pricing)}`
        )
      } catch (error) {
        console.error(`❌ Error storing pricing for order ${order.id}:`, error)
      }

      return response.json({
        ...order.serialize({ relations: true }),
        pricing: orderWithPricing.pricing,
      })
    } catch (error) {
      console.error('Customer checkout error:', error)
      return response.status(500).json({
        error: 'Failed to create order',
        details: error.message,
      })
    }
  }

  /**
   * Normalize delivery value to match database constraint
   * Handles common variations and user input
   */
  private normalizeDeliveryValue(delivery: string): string {
    if (!delivery || typeof delivery !== 'string') {
      return 'Takeaway' // Default fallback
    }

    // Convert to lowercase and trim for comparison
    const normalized = delivery.toLowerCase().trim()

    // Handle common variations
    const deliveryMap: Record<string, string> = {
      'takeaway': 'Takeaway',
      'take away': 'Takeaway',
      'take-away': 'Takeaway',
      'pickup': 'Takeaway',
      'pick up': 'Takeaway',
      'pick-up': 'Takeaway',

      'dinein': 'Dinein',
      'dine in': 'Dinein',
      'dine-in': 'Dinein',
      'eat in': 'Dinein',
      'eat-in': 'Dinein',
      'restaurant': 'Dinein',

      'delivery': 'Delivery',
      'deliver': 'Delivery',
      'home delivery': 'Delivery',
      'door delivery': 'Delivery',

      'selfpick': 'Selfpick',
      'self pick': 'Selfpick',
      'self-pick': 'Selfpick',
      'self pickup': 'Selfpick',
      'self-pickup': 'Selfpick',
    }

    // Return mapped value or original if already valid
    return deliveryMap[normalized] || delivery
  }

  /**
   * @store
   * @summary Create a new order
   * @description Create a new order with items, modifiers, and associated metadata. The method handles order creation, item attachment, modifier processing, invoice generation, and notifications.
   * @requestBody {
   *   "branchId": "string",
   *   "vendorId": "string",
   *   "items": [{
   *     "product_id": "string",
   *     "quantity": "number",
   *     "selected_modifiers": "array"
   *   }],
   *   "delivery": "string",
   *   "action": "string",
   *   "type": "string",
   *   "status": "string",
   *   "startAt": "string|optional",
   *   "sectionId": "string|optional",
   *   "lotId": "string|optional",
   *   "meta": "object|optional"
   * }
   * @responseBody 200 - <Order>
   * @response 400 - Bad Request
   */
  public async store({ request, response, auth }: HttpContextContract) {
    try {
      const {
        customerId,
        staffId,
        vendorId,
        branchId,
        sectionId,
        lotId,
        action,
        type,
        delivery,
        status,
        meta,
        items = {},
        startAt = DateTime.now().toISO(),
        endAt = null,
        location_context = 'within', // Default to 'within' if not provided
      } = request.all()

      // SECURITY: Apply vendor-scoped role-based filtering for order creation
      const user = auth.user!
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []

      // Validate that order has items
      if (!items || Object.keys(items).length === 0) {
        return response.badRequest({
          error: 'Order must contain at least one item',
          details: 'Cannot create an order without items',
        })
      }

      // Determine customer based on role and provided data
      let finalCustomerId: string | null = null

      if (customerId) {
        // Staff creating order for specific customer
        if (
          roleNames.includes('super admin') ||
          roleNames.includes('platform admin') ||
          roleNames.includes('admin')
        ) {
          // Admins can create orders for any customer
          finalCustomerId = customerId
        } else {
          // Other staff roles need vendor/branch validation
          const customer = await User.find(customerId)
          if (!customer) {
            return response.badRequest({
              error: 'Customer not found',
              details: 'The specified customer does not exist',
            })
          }
          // TODO: Add vendor/branch scope validation for staff roles
          finalCustomerId = customerId
        }
      } else if (meta?.customerEmail) {
        // Staff creating order with customer info in meta
        let customer = await User.query()
          .where('email', meta.customerEmail)
          .orWhere('phone', meta.customerPhone)
          .first()

        if (!customer && meta.customerName && meta.customerEmail) {
          // Create new customer if not found
          const [firstName, ...lastNameParts] = meta.customerName.trim().split(' ')
          const lastName = lastNameParts.join(' ') || ''

          customer = await User.create({
            firstName,
            lastName,
            email: meta.customerEmail,
            phone: meta.customerPhone,
            password: 'temp123', // Temporary password
            status: 'Active',
          })

          // Assign customer role
          await customer.assignRole('customer')
        }

        finalCustomerId = customer?.id || null
      } else {
        // No customer specified - assign to authenticated user (personal order)
        finalCustomerId = user.id
      }

      // Final validation: must have a customer
      if (!finalCustomerId) {
        return response.badRequest({
          error: 'Unable to determine customer',
          details: 'Could not assign order to a customer. Please check your request parameters.',
        })
      }

      // SECURITY: Validate vendor/branch access for non-admin users
      if (
        !roleNames.includes('super admin') &&
        !roleNames.includes('platform admin') &&
        !roleNames.includes('admin')
      ) {
        if (vendorId) {
          const userVendorIds = user.vendors?.map((vendor) => vendor.id) || []
          if (userVendorIds.length > 0 && !userVendorIds.includes(vendorId)) {
            return response.forbidden({
              error: 'Access denied',
              details: 'You do not have access to create orders for this vendor',
            })
          }
        }

        if (branchId) {
          const userBranchIds = user.branches?.map((branch) => branch.id) || []
          if (userBranchIds.length > 0 && !userBranchIds.includes(branchId)) {
            return response.forbidden({
              error: 'Access denied',
              details: 'You do not have access to create orders for this branch',
            })
          }
        }
      }

      // Add location context to meta for staff operations
      const enhancedMeta = {
        ...meta,
        location_context: location_context, // 'within' or 'away' for staff reference
      }

      const order = await Order.create({
        userId: finalCustomerId,
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta: enhancedMeta,
        startAt: DateTime.fromISO(startAt),
        endAt,
      })

      // Define interface for payload items
      interface ItemPayload {
        quantity: number
        selected_modifiers?: { modifier_option_id: string; quantity?: number }[]
      }

      // Create order items with their modifiers
      let itemsCreated = 0
      for (const [productId, itemData] of Object.entries<ItemPayload>(items)) {
        try {
          // Validate item data
          if (!itemData.quantity || itemData.quantity <= 0) {
            console.warn(`Skipping item ${productId} with invalid quantity: ${itemData.quantity}`)
            continue
          }

          // Get product to set correct price
          const product = await Product.find(productId)
          const productPrice = product ? Number(product.price || 0) : 0

          // Revert to using the relationship method for creation with correct price
          const orderItem = (await order.related('items').create({
            productId: productId, // Use model property name
            quantity: itemData.quantity,
            price: productPrice, // ✅ FIX: Set actual product price
            // meta can be added here if needed
          })) as OrderItem // Keep type assertion for clarity

          itemsCreated++

          // orderItem should now be a valid, persisted OrderItem instance linked to the order

          // Handle modifiers if present
          if (itemData.selected_modifiers && itemData.selected_modifiers.length > 0) {
            // Preload the product for this specific orderItem to access its availableModifiers
            await orderItem.load('product', (productQuery) => {
              // We need the availableModifiers relationship from the product
              productQuery.preload('availableModifiers')
            })

            if (!orderItem.product) {
              // Handle case where product couldn't be loaded for the order item
              console.error(
                `Product ${productId} could not be loaded for OrderItem ${orderItem.id}`
              )
              continue // Skip modifiers for this item if product is missing
            }

            for (const selectedMod of itemData.selected_modifiers) {
              // Find the specific product modifier pivot data from the preloaded relationship
              const productModifierPivot = orderItem.product.availableModifiers.find(
                (mod) => mod.id === selectedMod.modifier_option_id
              )?.$extras.pivot

              // Fetch ModifierOption details for default price (consider find instead of findOrFail)
              const modifierOption = await ModifierOption.find(selectedMod.modifier_option_id)

              if (!modifierOption) {
                console.error(`ModifierOption ${selectedMod.modifier_option_id} not found.`)
                continue // Skip this modifier if it doesn't exist
              }

              const priceToApply =
                productModifierPivot?.price_adjustment_override ??
                modifierOption.defaultPriceAdjustment

              // Create the OrderItemModifier related to the orderItem
              await orderItem.related('modifiers').create({
                modifierOptionId: selectedMod.modifier_option_id,
                quantity: selectedMod.quantity || 1,
                priceAtTimeOfOrder: priceToApply,
              })
            }
          }
        } catch (itemError) {
          console.error(`Failed to create item ${productId}:`, itemError)
          // Continue with other items
        }
      }

      // Validate that at least one item was created
      if (itemsCreated === 0) {
        // Delete the order since no items were created
        await order.delete()
        return response.badRequest({
          error: 'Failed to create any order items',
          details: 'Order was cancelled because no valid items could be processed',
        })
      }

      // Calculate total amount including modifiers
      // Load items first
      await order.load('items')

      // Ensure items is properly loaded and is an array
      if (!order.items || !Array.isArray(order.items)) {
        console.error('Order items not properly loaded or not an array:', order.items)
        // Try to reload the order with items
        await order.refresh()
        await order.load('items')
      }

      // Then, explicitly load relations onto each item
      if (order.items && order.items.length > 0) {
        for (const item of order.items) {
          await item.load('product') // Restore loading product
          await item.load('modifiers')
        }
      }

      let amount = 0
      // Add safety check before iterating
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          // Base item cost (item.product should be loaded now)
          if (item.product) {
            amount += item.product.price * item.quantity // Restore using item.product.price
          } else {
            console.error(`Product with ID ${item.productId} not loaded for OrderItem ${item.id}`) // Keep original error message
          }

          // Add modifier costs (item.modifiers should be loaded now)
          for (const modifier of item.modifiers) {
            amount += modifier.priceAtTimeOfOrder * modifier.quantity
          }
        }
      } else {
        console.error('Order items is not iterable or empty:', order.items)
      }

      // Apply packaging charges automatically for applicable delivery types
      // Items are already loaded above, so pass them directly
      await PackagingChargeService.applyPackagingCharges(order)

      // Reload order to get updated meta with packaging charges
      await order.refresh()

      // Add any additional charges from meta (including packaging charges)
      if (order.meta && order.meta.charges) {
        amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
          (acc, charge) => acc + charge,
          0
        )
      }

      // Note: Invoice is automatically created by Order model's afterCreate hook
      // But we need to update the invoice amount after items are created
      await order.updateInvoiceAmounts()

      // Process forms only if items are properly loaded
      if (order.items && Array.isArray(order.items)) {
        order.items.map(async (item: OrderItem) => {
          // Access forms via the loaded product relationship
          if (item.product) {
            // Check if product was loaded
            const productForms = await item.product.related('forms').query().exec()

            productForms.map(async (productForm) => {
              Object.entries(productForm.sections).map(async ([sectionId, { fields }]) => {
                fields.map(async (field) => {
                  if (field.type === 'file') {
                    const upload = request.file(`${sectionId}.${field.name}`)
                    if (upload) {
                      const uploaded = Attachment.fromFile(upload)

                      await order
                        .merge({
                          meta: {
                            ...order.meta,
                            responses: {
                              ...order.meta.responses,
                              [item.id]: {
                                ...order.meta.responses[item.id],
                                [field.name]: uploaded.url,
                              },
                            },
                          },
                        })
                        .save()
                    }
                  }
                })
              })
            })
          } else {
            console.error(`Product not loaded for OrderItem ${item.id}, cannot process forms.`)
          }
        })
      }

      await order.load('customer')

      // Only sync customer branches if customer exists (not a walk-in customer)
      if (order.customer) {
        order.customer.related('branches').sync(
          {
            [order.branchId]: {
              active: true,
              vendor_id: order.vendorId,
              branch_id: order.branchId,
            },
          },
          false
        )
      }

      // Load the order with all relationships (same as show method)
      await order.load((loader) => {
        loader
          .load('items', (itemsQuery) => {
            itemsQuery.preload('product', (productQuery) => {
              productQuery
                .preload('category')
                .preload('gallery')
                .preload('forms')
                .preload('branch')
                .preload('vendor')
            })
            itemsQuery.preload('modifiers')
          })
          .load('customer')
          .load('vendor')
          .load('branch')
          .load('invoices')
      })

      // Only load section if sectionId is present (same fix as temp orders)
      if (order.sectionId) {
        await order.load('section')
      }

      // Calculate pricing information using the same service as show method
      const OrderPricingService = (await import('App/Services/OrderPricingService')).default
      const orderWithPricing = await OrderPricingService.calculateOrderPricing(order)

      // Try to dispatch queue job but don't block response if Redis is unavailable
      try {
        // Add timeout to prevent hanging
        const queuePromise = Queue.dispatch('App/Jobs/ProcessOrder', {
          orderId: order.id,
        })

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Queue dispatch timeout')), 3000)
        })

        await Promise.race([queuePromise, timeoutPromise])
      } catch (queueError) {
        console.warn('⚠️ Queue dispatch failed (Redis unavailable or timeout):', queueError.message)
        // Continue with response even if queue fails
      }

      // Return standardized response with pricing (same structure as show method)
      return response.json({
        ...order.serialize({ relations: true }),
        pricing: orderWithPricing.pricing,
      })
    } catch (error) {
      console.error(error)

      return response.badRequest({ error: error.message })
    }
  }

  @bind()
  /**
   * @show
   * @summary Show a single Order
   * @description Show a Order with their details (name and details)
   * @paramPath id required number - Order ID
   * @paramQuery with - Comma-separated list of relationships to load (e.g., customer,branch,vendor,items,staff,section,invoices,payments)
   * @responseBody 200 - <Order>
   * @response 404 - Order not found
   */
  public async show({ request, response, auth }: HttpContextContract, order: Order) {
    try {
      // UNIVERSAL BRANCH ACCESS: Anyone associated with a branch can view orders from that branch
      const user = auth.user!

      // Load user relationships
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Get user's branch and vendor associations
      const userBranchIds = user.branches.map(branch => branch.id)
      const userVendorIds = user.vendors.map(vendor => vendor.id)
      const userRoles = user.roles.map(role => role.name.toLowerCase())

      // Debug logging
      console.log(`[AUTH] User ${user.id} (${user.email}) trying to access order ${order.id}`)
      console.log(`[AUTH] User roles:`, userRoles)
      console.log(`[AUTH] User branch IDs:`, userBranchIds)
      console.log(`[AUTH] User vendor IDs:`, userVendorIds)
      console.log(`[AUTH] Order branch ID:`, order.branchId)
      console.log(`[AUTH] Order vendor ID:`, order.vendorId)

      // Allow access if:
      // 1. User is admin/super admin (no restrictions)
      // 2. User is associated with the order's branch
      // 3. User is associated with the order's vendor
      // 4. User has vendor role but no associations (fallback for development)
      const isAdmin = userRoles.includes('admin') || userRoles.includes('super admin') || userRoles.includes('platform admin')
      const hasBranchAccess = userBranchIds.includes(order.branchId)
      const hasVendorAccess = userVendorIds.includes(order.vendorId)
      const isVendorWithoutAssociations = userRoles.includes('vendor') && userBranchIds.length === 0 && userVendorIds.length === 0

      console.log(`[AUTH] Is admin:`, isAdmin)
      console.log(`[AUTH] Has branch access:`, hasBranchAccess)
      console.log(`[AUTH] Has vendor access:`, hasVendorAccess)
      console.log(`[AUTH] Is vendor without associations:`, isVendorWithoutAssociations)

      if (!isAdmin && !hasBranchAccess && !hasVendorAccess && !isVendorWithoutAssociations) {
        console.log(`[AUTH] ACCESS DENIED: User not associated with order's branch or vendor`)
        return response.status(403).json({
          error: 'Forbidden',
          message: 'You do not have permission to view this order',
        })
      }

      console.log(`[AUTH] ACCESS GRANTED: User has permission to view order`)

      const { with: withParam } = request.qs()

      // Parse the 'with' parameter to determine which relationships to load
      const relationships = withParam ? withParam.split(',').map((rel) => rel.trim()) : []

      // Default relationships if none specified
      const defaultRelationships = ['items', 'customer', 'vendor', 'branch', 'section', 'invoices']
      const relationshipsToLoad = relationships.length > 0 ? relationships : defaultRelationships

      // Load relationships dynamically
      for (const relationship of relationshipsToLoad) {
        switch (relationship) {
          case 'items':
            await order.load('items', (itemQuery) => {
              // Preload complete product information with all relationships
              itemQuery.preload('product', (productQuery) => {
                productQuery.preload('forms')
                productQuery.preload('category')
                productQuery.preload('gallery')
                productQuery.preload('vendor')
                productQuery.preload('branch')
              })
              // Preload complete modifier information with option details
              itemQuery.preload('modifiers', (modifierQuery) => {
                modifierQuery.preload('option') // Load the actual modifier option details
                modifierQuery.preload('preparedByStaff')
                modifierQuery.preload('qualityChecker')
              })
              // Preload status tracking relationships
              itemQuery.preload('department')
              itemQuery.preload('assignedStaff')
              itemQuery.preload('qualityChecker')
            })
            break
          case 'customer':
            await order.load('customer')
            break
          case 'vendor':
            await order.load('vendor')
            break
          case 'branch':
            await order.load('branch')
            break
          case 'staff':
            await order.load('staff')
            break
          case 'section':
            await order.load('section')
            break
          case 'invoices':
            await order.load('invoices')
            break
          case 'payments':
            await order.load('payments')
            break
          case 'lot':
            await order.load('lot')
            break
          default:
            // Ignore unknown relationships
            console.warn(`Unknown relationship: ${relationship}`)
            break
        }
      }

      const qrCode = await QRCode.toDataURL(`aiastaff://orders/${order.id}`, {
        width: 300,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
        errorCorrectionLevel: 'L',
      })

      // Get fulfillment tracking information
      const fulfillmentSummary = await order.getOrderSummary()

      // Serialize the order with all loaded relationships
      const serializedOrder = order.serialize({
        relations: true,
      })

      // Handle temp orders (Pending status) - populate items from meta.temp_items if empty
      if (
        order.status === 'Pending' &&
        order.meta?.temp_items &&
        (!serializedOrder.items || serializedOrder.items.length === 0)
      ) {
        const tempItems = order.meta.temp_items
        const productIds = Object.keys(tempItems)

        if (productIds.length > 0) {
          // Import Product model dynamically to avoid circular dependency
          const { default: Product } = await import('../../Models/Product')
          const products = await Product.query()
            .whereIn('id', productIds)
            .preload('category')
            .preload('gallery')
            .preload('forms')
            .preload('branch')
            .preload('vendor')
            .exec()

          // Populate items from temp_items with enhanced structure
          serializedOrder.items = products.map((product) => {
            const quantity = tempItems[product.id]?.quantity || 0
            const productPrice = product.price || 0
            const totalItemCost = productPrice * quantity

            return {
              id: null, // No order_items record yet
              orderId: order.id,
              productId: product.id,
              quantity: quantity,
              meta: null,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              price: productPrice, // ✅ FIX: Set actual product price
              status: 'pending',
              departmentId: null,
              assignedStaffId: null,
              estimatedPreparationTime: null,
              preparationStartedAt: null,
              preparationCompletedAt: null,
              servedAt: null,
              priorityLevel: 1,
              requiresSpecialAttention: false,
              specialInstructions: null,
              preparationNotes: null,
              statusHistory: null,
              qualityCheckStatus: 'not_required',
              qualityCheckedBy: null,
              customerModifications: null,
              cancellationReason: null,
              actualPreparationTime: null,
              preparationAttempts: 1,
              modifiers: [],
              product: product.toJSON(), // ✅ FIX: Now includes all relationships
              actualPreparationTimeMinutes: null,
              isOverdue: false,
              preparationProgress: 0,
              statusDisplayName: 'Pending',
              canBeStarted: true,
              canBeCompleted: false,
              canBeServed: false,
              requiresAttention: false,
              estimatedCompletionTime: null,
              allModifiersCompleted: true,
              totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost
            }
          })
        }
      }

      // Calculate pricing information AFTER populating items for temp orders
      const pricing = OrderPricingService.calculateOrderPricing(serializedOrder)

      return response.json({
        ...serializedOrder,
        qrCode,
        pricing,
        fulfillment: fulfillmentSummary,
      })
    } catch (error) {
      console.error('Error loading order:', error)
      return response.status(500).json({
        error: 'Failed to load order details',
        details: error.message,
      })
    }
  }

  @bind()
  /**
   * @update
   * @summary Update a Order
   * @description Update a Order with their details (name and details)
   * @paramPath id required number - Order ID
   * @requestBody <Order>
   * @responseBody 200 - <Order>
   * @response 404 - Order not found
   */
  public async update({ request, response, auth }: HttpContextContract, order: Order) {
    try {
      // UNIVERSAL BRANCH ACCESS: Anyone associated with a branch can update orders from that branch
      const user = auth.user!

      // Load user relationships
      await user.load('roles')
      await user.load('vendors')
      await user.load('branches')

      // Get user's branch and vendor associations
      const userBranchIds = user.branches.map(branch => branch.id)
      const userVendorIds = user.vendors.map(vendor => vendor.id)
      const userRoles = user.roles.map(role => role.name.toLowerCase())

      // Debug logging
      console.log(`[AUTH] User ${user.id} (${user.email}) trying to update order ${order.id}`)
      console.log(`[AUTH] User roles:`, userRoles)
      console.log(`[AUTH] User branch IDs:`, userBranchIds)
      console.log(`[AUTH] User vendor IDs:`, userVendorIds)
      console.log(`[AUTH] Order branch ID:`, order.branchId)
      console.log(`[AUTH] Order vendor ID:`, order.vendorId)

      // Allow access if:
      // 1. User is admin/super admin (no restrictions)
      // 2. User is associated with the order's branch
      // 3. User is associated with the order's vendor
      // 4. User has vendor role but no associations (fallback for development)
      const isAdmin = userRoles.includes('admin') || userRoles.includes('super admin') || userRoles.includes('platform admin')
      const hasBranchAccess = userBranchIds.includes(order.branchId)
      const hasVendorAccess = userVendorIds.includes(order.vendorId)
      const isVendorWithoutAssociations = userRoles.includes('vendor') && userBranchIds.length === 0 && userVendorIds.length === 0

      console.log(`[AUTH] Is admin:`, isAdmin)
      console.log(`[AUTH] Has branch access:`, hasBranchAccess)
      console.log(`[AUTH] Has vendor access:`, hasVendorAccess)
      console.log(`[AUTH] Is vendor without associations:`, isVendorWithoutAssociations)

      if (!isAdmin && !hasBranchAccess && !hasVendorAccess && !isVendorWithoutAssociations) {
        console.log(`[AUTH] ACCESS DENIED: User not associated with order's branch or vendor`)
        return response.status(403).json({
          error: 'Forbidden',
          message: 'You do not have permission to update this order',
        })
      }

      console.log(`[AUTH] ACCESS GRANTED: User has permission to update order`)

      const { staffId, delivery, status, ref, meta, auto_update_status = false } = request.all()

      const oldStatus = order.status

    console.log('DATA: ' + JSON.stringify(request.all()))

    // If auto_update_status is enabled, check if order should be automatically updated
    if (auto_update_status) {
      await order.checkAndUpdateOrderStatus()
      // Reload order to get updated status
      await order.refresh()
    }

    // Normalize charges in meta if present
    let normalizedMeta = meta
    if (meta && meta.charges) {
      normalizedMeta = { ...meta }
      const normalizedCharges: Record<string, number> = {}

      for (const [key, value] of Object.entries(meta.charges)) {
        if (value === 'NaN' || value === '"NaN"') {
          normalizedCharges[key] = 0
        } else if (typeof value === 'string') {
          const cleanValue = value.replace(/^"|"$/g, '') // Remove surrounding quotes
          const numericValue = parseFloat(cleanValue)
          normalizedCharges[key] = isNaN(numericValue) ? 0 : numericValue
        } else {
          normalizedCharges[key] = Number(value) || 0
        }
      }

      normalizedMeta.charges = normalizedCharges
    }

    order.merge({
      staffId,
      delivery,
      status: status || order.status, // Use existing status if not provided
      ref,
      meta: normalizedMeta,
      acceptedAt: DateTime.now(),
    })

    await order.save()

    // Load fulfillment information for response
    await order.load('items', (itemQuery) => {
      itemQuery.preload('department')
      itemQuery.preload('assignedStaff')
      itemQuery.preload('modifiers', (modifierQuery) => {
        modifierQuery.preload('preparedByStaff')
      })
    })

    // Get fulfillment summary
    const fulfillmentSummary = await order.getOrderSummary()

    // Broadcast order status change if status changed
    if (oldStatus !== order.status) {
      await StatusChangeBroadcaster.broadcastOrderStatusChange(order, oldStatus, auth.user?.name, {
        auto_update_status,
        updated_via: 'api',
      })
    }

    await order.customer?.notify(new CustomerOrderStatusChange(order, oldStatus))

    return response.json({
      ...order.serialize(),
      fulfillment: fulfillmentSummary,
      status_changed: oldStatus !== order.status,
      old_status: oldStatus,
    })
    } catch (error) {
      console.error('Error updating order:', error)
      return response.status(500).json({
        error: 'Failed to update order',
        details: error.message,
      })
    }
  }

  @bind()
  /**
   * @summary Get order fulfillment status
   * @description Get detailed fulfillment tracking information for an order
   * @paramPath id required number - Order ID
   * @responseBody 200 - Fulfillment status
   * @response 404 - Order not found
   */
  public async fulfillmentStatus({ response }: HttpContextContract, order: Order) {
    try {
      // Load all necessary relationships for fulfillment tracking
      await order.load('items', (itemQuery) => {
        itemQuery.preload('department')
        itemQuery.preload('assignedStaff')
        itemQuery.preload('modifiers', (modifierQuery) => {
          modifierQuery.preload('preparedByStaff')
        })
      })

      // Get comprehensive fulfillment summary
      const fulfillmentSummary = await order.getOrderSummary()

      // Get department breakdown
      const departmentBreakdown = order.departmentBreakdown

      // Check if order can be completed
      const canBeCompleted = order.canBeCompleted

      return response.json({
        order_id: order.id,
        order_status: order.status,
        fulfillment: fulfillmentSummary,
        department_breakdown: departmentBreakdown,
        can_be_completed: canBeCompleted,
        estimated_completion: order.estimatedCompletionTime,
        has_overdue_items: order.hasOverdueItems,
        requires_attention: order.requiresAttention,
        total_preparation_time: order.totalPreparationTime,
      })
    } catch (error) {
      console.error('Error getting fulfillment status:', error)
      return response.status(500).json({
        error: 'Failed to get fulfillment status',
        details: error.message,
      })
    }
  }

  @bind()
  /**
   * @summary Auto-assign items to departments
   * @description Automatically assign order items to appropriate departments
   * @paramPath id required number - Order ID
   * @responseBody 200 - Assignment results
   * @response 404 - Order not found
   */
  public async autoAssignDepartments({ response }: HttpContextContract, order: Order) {
    try {
      await order.assignItemsToDepartments()

      // Reload order with department assignments
      await order.load('items', (itemQuery) => {
        itemQuery.preload('department')
        itemQuery.preload('product')
      })

      const assignmentResults = order.items.map((item) => ({
        item_id: item.id,
        product_name: item.product?.name,
        department_id: item.departmentId,
        department_name: item.department?.name,
        assigned: !!item.departmentId,
      }))

      return response.json({
        order_id: order.id,
        assignments: assignmentResults,
        total_items: order.items.length,
        assigned_items: assignmentResults.filter((a) => a.assigned).length,
        unassigned_items: assignmentResults.filter((a) => !a.assigned).length,
      })
    } catch (error) {
      console.error('Error auto-assigning departments:', error)
      return response.status(500).json({
        error: 'Failed to auto-assign departments',
        details: error.message,
      })
    }
  }

  @bind()

  /**
   * @destroy
   * @summary delete an order
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, order: Order) {
    return await order.delete()
  }
}
