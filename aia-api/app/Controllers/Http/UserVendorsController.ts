import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Vendor from '../../Models/Vendor'
import { bind } from '@adonisjs/route-model-binding'
import { DateTime } from 'luxon'
import User from 'App/Models/User'

export default class UserVendorsController {
  @bind()
  /**
   * @index
   * @summary List all vendors
   * @description List all vendors, paginated
   */
  public async index({ request, response }: HttpContextContract, user: User) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const vendorQuery = Vendor.filter(filters).where('userId', user.id).preload('branches')

    const vendors = await vendorQuery.orderBy(order, sort).paginate(page, per)

    return response.json(vendors)
  }

  @bind()
  /**
   * @store
   * @summary Create a Vendor
   * @description Create a Vendor with their details (name and details)
   * @requestBody {"name": "", "slug": {}, "reg": "", "kra":"", "email": "", "phone": "", "details": "", "serviceId": "", "vendorCategoryId": "", "userId": "", "branch": <Branch>}
   * @responseBody 200 - <Vendor>
   */
  public async store({ request, response }: HttpContextContract, user: User) {
    const {
      name,
      slug,
      reg,
      kra,
      email,
      phone,
      details,
      serviceId,
      vendorCategoryId,
      userId,
      branch = null,
    } = request.all()

    const vendorQ = await user.related('vendors').create({
      userId,
      name,
      slug,
      email,
      phone,
      reg: reg || DateTime.now().toISODate(),
      kra: kra || DateTime.now().toISODate(),
      serviceId,
    })

    const logo = request.file('logo')
    if (logo) {
      await vendorQ.merge({ logo: Attachment.fromFile(logo) }).save()
    }

    const cover = request.file('cover')
    if (cover) {
      await vendorQ.merge({ cover: Attachment.fromFile(cover) }).save()
    }

    if (branch) {
      const { name, email, phone, details, location, identifier = null } = branch
      const branchQ = await vendorQ
        .related('branches')
        .create({ name, email, phone, details, location })

      const image = request.file('image')
      if (image) {
        branchQ.merge({ image: Attachment.fromFile(image) }).save()
      }

      branchQ
        .related('staff')
        .attach({ [userId]: { vendor_id: vendorQ.id, identifier: identifier || phone } })
    } else {
      const hq = await vendorQ.related('branches').create({
        name: 'HQ',
        email,
        phone,
        details,
      })
      await hq.related('staff').attach({ [userId]: { vendor_id: vendorQ.id, identifier: phone } })
    }

    await vendorQ.related('categories').attach([vendorCategoryId])

    return response.json(vendorQ)
  }
}
