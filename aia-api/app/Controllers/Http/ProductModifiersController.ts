import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'
import ModifierOption from 'App/Models/ModifierOption'
import { ModifierType } from 'App/Enums/ModifierType'

/**
 * @summary Product Modifier Management
 * @group Product Modifiers
 * @version 1.0.0
 * @description Manage modifier attachments to products with granular control over individual modifier relationships
 */
export default class ProductModifiersController {
  /**
   * @index
   * @summary List product modifiers
   * @description Retrieve all modifiers attached to a specific product, grouped by type and sorted by sort_order
   * @tag Product Modifiers
   * @security BearerAuth
   * @responseBody 200 - {"data": {"preparation": [{"id": "string", "name": "string", "type": "preparation", "priceAdjustment": 2.50, "isDefault": false, "sortOrder": 1}], "condiment": [], "extra": []}}
   * @responseBody 404 - {"message": "Product not found"}
   * @responseBody 400 - {"message": "Failed to retrieve product modifiers", "error": "string"}
   */
  public async index({ params, response }: HttpContextContract) {
    try {
      const product = await Product.query()
        .where('id', params.id)
        .preload('availableModifiers')
        .firstOrFail()

      // Group modifiers by type (following existing pattern from ProductsController)
      const modifiers = product.availableModifiers.reduce((acc, modifier) => {
        const type = modifier.type
        if (!acc[type]) {
          acc[type] = []
        }
        acc[type].push({
          ...modifier.toJSON(),
          priceAdjustment: modifier.$extras.price_adjustment_override ?? modifier.defaultPriceAdjustment,
          isDefault: modifier.$extras.is_default ?? false,
          sortOrder: modifier.$extras.sort_order ?? 0,
        })
        return acc
      }, {} as Record<ModifierType, any[]>)

      // Sort each type's modifiers by sort_order
      Object.keys(modifiers).forEach((type) => {
        modifiers[type as ModifierType].sort((a, b) => a.sortOrder - b.sortOrder)
      })

      return response.ok({ data: modifiers })
    } catch (error) {
      if (error.code === 'E_ROW_NOT_FOUND') {
        return response.notFound({ message: 'Product not found' })
      }
      return response.badRequest({ message: 'Failed to retrieve product modifiers', error: error.message })
    }
  }

  /**
   * @store
   * @summary Attach modifier to product
   * @description Attach a modifier option to a product with custom relationship properties
   * @tag Product Modifiers
   * @security BearerAuth
   * @requestBody {"modifierOptionId": "string", "priceAdjustmentOverride": 2.50, "isDefault": false, "sortOrder": 1}
   * @responseBody 201 - {"success": true, "data": {"productId": "string", "modifierOptionId": "string", "priceAdjustmentOverride": 2.50, "isDefault": false, "sortOrder": 1}}
   * @responseBody 400 - {"message": "Invalid request", "error": "string"}
   * @responseBody 404 - {"message": "Product or modifier not found"}
   * @responseBody 409 - {"message": "Modifier already attached to product"}
   * @responseBody 422 - {"message": "Validation failed", "errors": {}}
   */
  public async store({ params, request, response }: HttpContextContract) {
    try {
      const { modifierOptionId, priceAdjustmentOverride, isDefault = false, sortOrder = 0 } = request.only([
        'modifierOptionId',
        'priceAdjustmentOverride', 
        'isDefault',
        'sortOrder'
      ])

      // Validate required fields
      if (!modifierOptionId) {
        return response.status(422).json({
          message: 'Validation failed',
          errors: { modifierOptionId: ['Modifier option ID is required'] }
        })
      }

      // Validate numeric fields
      if (priceAdjustmentOverride !== undefined && (isNaN(priceAdjustmentOverride) || priceAdjustmentOverride < -999.99 || priceAdjustmentOverride > 999.99)) {
        return response.status(422).json({
          message: 'Validation failed',
          errors: { priceAdjustmentOverride: ['Price adjustment must be between -999.99 and 999.99'] }
        })
      }

      if (sortOrder !== undefined && (isNaN(sortOrder) || sortOrder < 0)) {
        return response.status(422).json({
          message: 'Validation failed',
          errors: { sortOrder: ['Sort order must be a non-negative number'] }
        })
      }

      // Check if product exists
      const product = await Product.findOrFail(params.id)

      // Check if modifier option exists
      const modifierOption = await ModifierOption.findOrFail(modifierOptionId)

      // Check if modifier is already attached to product
      const existingRelation = await product
        .related('availableModifiers')
        .query()
        .where('modifier_option_id', modifierOptionId)
        .first()

      if (existingRelation) {
        return response.status(409).json({
          message: 'Modifier already attached to product'
        })
      }

      // Attach modifier to product
      await product.related('availableModifiers').attach({
        [modifierOptionId]: {
          price_adjustment_override: priceAdjustmentOverride || null,
          is_default: isDefault,
          sort_order: sortOrder,
        }
      })

      const responseData = {
        productId: product.id,
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: priceAdjustmentOverride || null,
        isDefault: isDefault,
        sortOrder: sortOrder,
      }

      return response.status(201).json({
        success: true,
        data: responseData
      })

    } catch (error) {
      if (error.code === 'E_ROW_NOT_FOUND') {
        return response.notFound({ message: 'Product or modifier not found' })
      }
      return response.badRequest({ message: 'Invalid request', error: error.message })
    }
  }

  /**
   * @update
   * @summary Update product-modifier relationship
   * @description Update the relationship properties between a product and modifier
   * @tag Product Modifiers
   * @security BearerAuth
   * @requestBody {"priceAdjustmentOverride": 3.00, "isDefault": true, "sortOrder": 2}
   * @responseBody 200 - {"success": true, "data": {"productId": "string", "modifierOptionId": "string", "priceAdjustmentOverride": 3.00, "isDefault": true, "sortOrder": 2}}
   * @responseBody 404 - {"message": "Product or modifier relationship not found"}
   * @responseBody 422 - {"message": "Validation failed", "errors": {}}
   * @responseBody 400 - {"message": "Failed to update modifier relationship", "error": "string"}
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const { priceAdjustmentOverride, isDefault, sortOrder } = request.only([
        'priceAdjustmentOverride',
        'isDefault', 
        'sortOrder'
      ])

      // Validate numeric fields if provided
      if (priceAdjustmentOverride !== undefined && (isNaN(priceAdjustmentOverride) || priceAdjustmentOverride < -999.99 || priceAdjustmentOverride > 999.99)) {
        return response.status(422).json({
          message: 'Validation failed',
          errors: { priceAdjustmentOverride: ['Price adjustment must be between -999.99 and 999.99'] }
        })
      }

      if (sortOrder !== undefined && (isNaN(sortOrder) || sortOrder < 0)) {
        return response.status(422).json({
          message: 'Validation failed',
          errors: { sortOrder: ['Sort order must be a non-negative number'] }
        })
      }

      // Check if product exists
      const product = await Product.findOrFail(params.id)

      // Check if modifier option exists
      const modifierOption = await ModifierOption.findOrFail(params.modifierId)

      // Check if relationship exists
      const existingRelation = await product
        .related('availableModifiers')
        .query()
        .where('modifier_option_id', params.modifierId)
        .first()

      if (!existingRelation) {
        return response.notFound({ message: 'Product or modifier relationship not found' })
      }

      // Prepare update data - only include fields that were provided
      const updateData: any = {}
      if (priceAdjustmentOverride !== undefined) {
        updateData.price_adjustment_override = priceAdjustmentOverride
      }
      if (isDefault !== undefined) {
        updateData.is_default = isDefault
      }
      if (sortOrder !== undefined) {
        updateData.sort_order = sortOrder
      }

      // Get current pivot data to merge with updates
      const currentPivotData = {
        price_adjustment_override: existingRelation.$extras.price_adjustment_override,
        is_default: existingRelation.$extras.is_default,
        sort_order: existingRelation.$extras.sort_order,
        ...updateData
      }

      // Update the relationship using sync with merged data
      await product.related('availableModifiers').sync({
        [params.modifierId]: currentPivotData
      }, false) // false = don't detach existing relationships

      const responseData = {
        productId: product.id,
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: currentPivotData.price_adjustment_override,
        isDefault: currentPivotData.is_default,
        sortOrder: currentPivotData.sort_order,
      }

      return response.ok({
        success: true,
        data: responseData
      })

    } catch (error) {
      if (error.code === 'E_ROW_NOT_FOUND') {
        return response.notFound({ message: 'Product or modifier relationship not found' })
      }
      return response.badRequest({ message: 'Failed to update modifier relationship', error: error.message })
    }
  }

  /**
   * @destroy
   * @summary Detach modifier from product
   * @description Remove a specific modifier from a product
   * @tag Product Modifiers
   * @security BearerAuth
   * @responseBody 204 - No content
   * @responseBody 404 - {"message": "Product or modifier relationship not found"}
   * @responseBody 400 - {"message": "Failed to detach modifier", "error": "string"}
   */
  public async destroy({ params, response }: HttpContextContract) {
    try {
      // Check if product exists
      const product = await Product.findOrFail(params.id)

      // Check if modifier option exists
      await ModifierOption.findOrFail(params.modifierId)

      // Check if relationship exists
      const existingRelation = await product
        .related('availableModifiers')
        .query()
        .where('modifier_option_id', params.modifierId)
        .first()

      if (!existingRelation) {
        return response.notFound({ message: 'Product or modifier relationship not found' })
      }

      // Detach the modifier from the product
      await product.related('availableModifiers').detach([params.modifierId])

      return response.noContent()

    } catch (error) {
      if (error.code === 'E_ROW_NOT_FOUND') {
        return response.notFound({ message: 'Product or modifier relationship not found' })
      }
      return response.badRequest({ message: 'Failed to detach modifier', error: error.message })
    }
  }
}
