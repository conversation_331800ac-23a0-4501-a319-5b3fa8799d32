import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import VendorRating from '../../Models/VendorRating'
import { bind } from '@adonisjs/route-model-binding'
import { DateTime } from 'luxon'

/**
 * @name VendorRating management
 * @version 1.0.0
 * @description VendorRating management for the application
 */
export default class VendorRatingsController {
  /**
   * @index
   * @summary List all ratings
   * @description List all ratings, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request, response }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const ratingQuery = VendorRating.filter(filters).preload('customer')

    const reviews = await ratingQuery.orderBy(order, sort).paginate(page, per)

    return response.json(reviews)
  }

  /**
   * @store
   * @summary Create a rating
   * @description Create a rating with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <VendorRating>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    const { points, comment, customerId, vendorId, ratings = null } = request.all()

    if (ratings) {
      ratings.map(async (r) => {
        await VendorRating.create({
          name: r.name,
          points: r.points,
          comment: 'N/A',
          vendorId,
          customerId: customerId || auth.user?.id,
        })
      })
    } else {
      await VendorRating.create({
        name: `Rating from ${auth.user?.firstName} on ${DateTime.now().toISODate()}`,
        points,
        comment,
        vendorId,
        customerId: customerId || auth.user?.id,
      })
    }

    return response.json({ message: 'rated!' })
  }

  @bind()
  /**
   * @show
   * @summary Show a single rating
   * @description Show a rating with their details (name and details)
   * @paramPath id required number - VendorRating ID
   * @responseBody 200 - <VendorRating>
   * @response 404 - VendorRating not found
   */
  public async show({ response }: HttpContextContract, rating: VendorRating) {
    return response.json(rating)
  }

  @bind()
  /**
   * @update
   * @summary Update a rating
   * @description Update a rating with their details (name and details)
   * @paramPath id required number - VendorRating ID
   * @requestBody <VendorRating>
   * @responseBody 200 - <VendorRating>
   * @response 404 - VendorRating not found
   */
  public async update({ request, response }: HttpContextContract, rating: VendorRating) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await rating.merge(input).save()

    return response.json(rating)
  }

  @bind()

  /**
   * @destroy
   * @summary Delete a rating
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, rating: VendorRating) {
    return await rating.delete()
  }
}
