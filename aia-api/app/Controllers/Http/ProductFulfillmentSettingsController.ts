import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'
import ProductFulfillmentSetting from 'App/Models/ProductFulfillmentSetting'
import { ProductArchetype } from 'App/Enums/ProductArchetype'

export default class ProductFulfillmentSettingsController {
  public async index({ params, response }: HttpContextContract) {
    const productId = params.product_id
    const settings = await ProductFulfillmentSetting.query()
      .where('productId', productId)
      .preload('product')

    return response.ok(settings)
  }

  /**
   * Get fulfillment settings for a specific product
   */
  public async showByProduct({ params, response }: HttpContextContract) {
    const productId = params.product_id
    const settings = await ProductFulfillmentSetting.findBy('productId', productId)

    if (!settings) {
      // Return default settings if none exist
      return response.ok({
        productId,
        archetype: 'PHYSICAL',
        isPayable: true,
        hasForm: false,
        isInformational: false,
        isDeliverable: true,
        isPickup: true,
        physicalConsumptionIsOnsite: true,
        isDownloadable: false,
        digitalDeliveryMethod: null,
        isSchedulable: false,
        serviceIsOnsite: false,
        serviceIsRemote: true,
        serviceIsDynamicQuery: false,
        preorderAllowed: true,
        scheduleAllowed: true
      })
    }

    return response.ok(settings)
  }

  public async store({ params, request, response }: HttpContextContract) {
    console.log('Route params:', params)
    const productId = params.product_id // Changed from products_id to product_id
    await Product.findOrFail(productId)

    // Check if settings already exist
    const existingSettings = await ProductFulfillmentSetting.findBy('productId', productId)
    if (existingSettings) {
      return response.conflict({ message: 'Fulfillment settings already exist for this product' })
    }

    const settings = await ProductFulfillmentSetting.create({
      productId,
      archetype: request.input('archetype', ProductArchetype.PHYSICAL),
      isPayable: request.input('isPayable', true),
      hasForm: request.input('hasForm', false),
      isInformational: request.input('isInformational', false),
      isDeliverable: request.input('isDeliverable', true),
      isPickup: request.input('isPickup', true),
      physicalConsumptionIsOnsite: request.input('physicalConsumptionIsOnsite', true),
      isDownloadable: request.input('isDownloadable', false),
      digitalDeliveryMethod: request.input('digitalDeliveryMethod'),
      isSchedulable: request.input('isSchedulable', false),
      serviceIsOnsite: request.input('serviceIsOnsite', false),
      serviceIsRemote: request.input('serviceIsRemote', true),
      serviceIsDynamicQuery: request.input('serviceIsDynamicQuery', false),
      preorderAllowed: request.input('preorderAllowed', true),
      scheduleAllowed: request.input('scheduleAllowed', true),
    })

    return response.created(settings)
  }

  public async update({ params, request, response }: HttpContextContract) {
    const settings = await ProductFulfillmentSetting.findOrFail(params.id)

    const data = request.only([
      'archetype',
      'isPayable',
      'hasForm',
      'isInformational',
      'isDeliverable',
      'isPickup',
      'physicalConsumptionIsOnsite',
      'isDownloadable',
      'digitalDeliveryMethod',
      'isSchedulable',
      'serviceIsOnsite',
      'serviceIsRemote',
      'serviceIsDynamicQuery',
      'preorderAllowed',
      'scheduleAllowed',
    ])

    settings.merge(data)
    await settings.save()

    return response.ok(settings)
  }

  public async show({ params, response }: HttpContextContract) {
    const settings = await ProductFulfillmentSetting.findOrFail(params.id)
    await settings.load('product')
    return response.ok(settings)
  }

  public async destroy({ params, response }: HttpContextContract) {
    const settings = await ProductFulfillmentSetting.findOrFail(params.id)
    await settings.delete()
    return response.noContent()
  }
}
