import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Speciality from 'App/Models/Speciality'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

export default class SpecialityVendorsController {
  @bind()

  /**
   * @name SpecialityVendor management
   * @index
   * @summary List all SpecialityVendors
   * @description List all SpecialityVendors, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   *
   */
  public async index({ request }: HttpContextContract, speciality: Speciality) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = speciality.related('vendors').query().filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a SpecialityVendors
   * @description Create a SpecialityVendors with their details (name and details)
   * @requestBody {"firstName": "", "lastName": "", "email": "", "phone": "", "vendorCategoryId": "", "vendor": ""}
   * @responseBody 200 - <Speciality>
   */
  public async store({ request, response }: HttpContextContract, speciality: Speciality) {
    const { firstName, lastName, email, phone, vendorCategoryId, vendor } = request.all()

    const user = await User.create({
      firstName,
      lastName,
      email,
      phone,
      password: phone,
    })

    const vendorQ = await user.related('vendors').create({
      name: vendor.name,
      email: vendor.email,
      phone: vendor.phone,
      reg: vendor.reg || DateTime.now().toISODate(),
      kra: vendor.kra || DateTime.now().toISODate(),
    })

    await vendorQ.related('specialities').attach([speciality.id])

    const logo = request.file('vendor.logo')

    if (logo) {
      await vendorQ.merge({ logo: Attachment.fromFile(logo) }).save()
    }

    await vendorQ.related('categories').attach([vendorCategoryId])

    return response.json(vendorQ)
  }
}
