import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import NotificationPreference from 'App/Models/NotificationPreference'
import { schema, rules } from '@ioc:Adonis/Core/Validator'

export default class NotificationPreferencesController {
  public async index({ auth, response }: HttpContextContract) {
    const user = auth.user!
    const preferences = await NotificationPreference.query()
      .where('user_id', user.id)
      .preload('vendor')

    return response.ok(preferences)
  }

  public async show({ auth, params, response }: HttpContextContract) {
    const user = auth.user!
    const preference = await NotificationPreference.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .preload('vendor')
      .firstOrFail()

    return response.ok(preference)
  }

  public async store({ auth, request, response }: HttpContextContract) {
    const user = auth.user!
    const data = await request.validate({
      schema: schema.create({
        vendorId: schema.string.optional({}, [rules.exists({ table: 'vendors', column: 'id' })]),
        type: schema.string(),
        channels: schema.array().members(schema.string()),
        preferences: schema.object().members({
          email: schema.boolean(),
          sms: schema.boolean(),
          push: schema.boolean(),
          order_assigned: schema.boolean(),
          order_picked_up: schema.boolean(),
          order_in_transit: schema.boolean(),
          order_delivered: schema.boolean(),
          order_failed: schema.boolean(),
          order_cancelled: schema.boolean(),
        }),
        meta: schema.object.optional().anyMembers(),
      }),
    })

    const preference = await NotificationPreference.create({
      userId: user.id,
      ...data,
    })

    return response.created(preference)
  }

  public async update({ auth, params, request, response }: HttpContextContract) {
    const user = auth.user!
    const preference = await NotificationPreference.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .firstOrFail()

    const data = await request.validate({
      schema: schema.create({
        channels: schema.array.optional().members(schema.string()),
        preferences: schema.object.optional().members({
          email: schema.boolean(),
          sms: schema.boolean(),
          push: schema.boolean(),
          order_assigned: schema.boolean(),
          order_picked_up: schema.boolean(),
          order_in_transit: schema.boolean(),
          order_delivered: schema.boolean(),
          order_failed: schema.boolean(),
          order_cancelled: schema.boolean(),
        }),
        meta: schema.object.optional().anyMembers(),
      }),
    })

    preference.merge(data)
    await preference.save()

    return response.ok(preference)
  }

  public async destroy({ auth, params, response }: HttpContextContract) {
    const user = auth.user!
    const preference = await NotificationPreference.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .firstOrFail()

    await preference.delete()

    return response.noContent()
  }
} 