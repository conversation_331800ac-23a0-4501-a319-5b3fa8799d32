import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'

class PublicBranchProductsController {
  public async index({ request, response, params }: HttpContextContract) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc' } = request.qs()
    const { branch_id } = params

    const productQuery = Product.query()
      .where('branchId', branch_id)
      .where('active', true)
      .where('status', 'Published')
      .where('visibility', 'Public')
      .preload('vendor')
      .preload('service')
      .preload('category')
      .orderBy(order, sort)

    const products = await productQuery.paginate(page, per)
    // Only expose public-safe fields
    const result = products.serialize({
      fields: [
        'id',
        'name',
        'details',
        'price',
        'discounted',
        'image',
        'createdAt',
        'vendor',
        'service',
        'category',
      ],
    })
    return response.json(result)
  }

  public async show({ response, params }: HttpContextContract) {
    const { branch_id, product_id } = params
    const product = await Product.query()
      .where('id', product_id)
      .where('branchId', branch_id)
      .where('active', true)
      .where('status', 'Published')
      .where('visibility', 'Public')
      .preload('vendor')
      .preload('service')
      .firstOrFail()
    // Only expose public-safe fields
    const result = product.serialize({
      fields: [
        'id',
        'name',
        'details',
        'price',
        'discounted',
        'image',
        'createdAt',
        'vendor',
        'service',
      ],
    })
    return response.json(result)
  }
}

export default new PublicBranchProductsController()
