import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Campaign from 'App/Models/Campaign'
import { DateTime } from 'luxon'

export default class PublicCampaignsController {
  /**
   * @index
   * @summary Get public campaigns
   * @description Get approved, active campaigns for public display
   * @paramQuery per - Number of items per page (max 50)
   * @paramQuery page - Page number
   * @paramQuery order - Order by column (createdAt, position, startDate)
   * @paramQuery sort - Sort direction (asc or desc)
   * @paramQuery vendor - Filter by vendor ID
   * @paramQuery location - Filter by location (lat,lng format)
   * @paramQuery radius - Radius in meters for location filtering (default: 10000)
   */
  public async index({ request, response }: HttpContextContract) {
    const {
      per = 20,
      page = 1,
      order = 'position',
      sort = 'asc',
      vendor,
      location,
      radius = 10000,
    } = request.qs()

    // Validate pagination limits
    const perPage = Math.min(parseInt(per), 50)

    const campaignQuery = Campaign.query()
      .where('status', 'Approved')
      .where('startDate', '<=', DateTime.now().toSQL())
      .where('endDate', '>=', DateTime.now().toSQL())
      .preload('vendor', (vendorQuery) => {
        vendorQuery.where('active', true)
      })
      .preload('branch')

    // Filter by vendor if specified
    if (vendor) {
      campaignQuery.where('vendorId', vendor)
    }

    // Location-based filtering (if implemented)
    if (location) {
      const [lat, lng] = location.split(',').map(parseFloat)
      if (lat && lng) {
        // This would require geospatial queries on vendor/branch locations
        campaignQuery.whereHas('vendor', (vendorQuery) => {
          vendorQuery.whereHas('branches', (branchQuery) => {
            // Implement geospatial distance calculation
            // branchQuery.whereRaw('ST_DWithin(location, ST_Point(?, ?), ?)', [lng, lat, radius])
          })
        })
      }
    }

    // Order by position first (for carousel ordering), then by specified order
    if (order === 'position') {
      campaignQuery.orderByRaw('position ASC NULLS LAST')
    } else {
      campaignQuery.orderBy(order, sort)
    }

    const campaigns = await campaignQuery.paginate(page, perPage)

    // Only expose public-safe fields
    const result = campaigns.serialize({
      fields: [
        'id',
        'name',
        'details',
        'link',
        'image',
        'displayDuration',
        'position',
        'startDate',
        'endDate',
        'vendor',
        'branch',
      ],
    })

    return response.json(result)
  }

  /**
   * @show
   * @summary Get single campaign
   * @description Get a specific approved campaign by ID
   * @paramPath id - Campaign ID
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const campaign = await Campaign.query()
        .where('id', params.id)
        .where('status', 'Approved')
        .where('startDate', '<=', DateTime.now().toSQL())
        .where('endDate', '>=', DateTime.now().toSQL())
        .preload('vendor', (vendorQuery) => {
          vendorQuery.where('active', true)
        })
        .preload('branch')
        .firstOrFail()

      // Only expose public-safe fields
      const result = campaign.serialize({
        fields: [
          'id',
          'name',
          'details',
          'link',
          'image',
          'displayDuration',
          'position',
          'startDate',
          'endDate',
          'vendor',
          'branch',
        ],
      })

      return response.json(result)
    } catch (error) {
      return response.status(404).json({
        error: 'Campaign not found or not available',
      })
    }
  }

  /**
   * @nearby
   * @summary Get campaigns near location
   * @description Get approved campaigns near a specific location
   * @paramQuery lat - Latitude
   * @paramQuery lng - Longitude
   * @paramQuery radius - Radius in meters (default: 10000)
   * @paramQuery per - Number of items per page (max 50)
   * @paramQuery page - Page number
   */
  public async nearby({ request, response }: HttpContextContract) {
    const {
      lat,
      lng,
      radius = 10000,
      per = 20,
      page = 1,
    } = request.qs()

    if (!lat || !lng) {
      return response.badRequest({
        error: 'Latitude and longitude are required',
      })
    }

    const perPage = Math.min(parseInt(per), 50)

    // This is a simplified version - would need proper geospatial implementation
    const campaignQuery = Campaign.query()
      .where('status', 'Approved')
      .where('startDate', '<=', DateTime.now().toSQL())
      .where('endDate', '>=', DateTime.now().toSQL())
      .preload('vendor', (vendorQuery) => {
        vendorQuery.where('active', true)
      })
      .preload('branch')
      .orderByRaw('position ASC NULLS LAST')

    const campaigns = await campaignQuery.paginate(page, perPage)

    // Only expose public-safe fields
    const result = campaigns.serialize({
      fields: [
        'id',
        'name',
        'details',
        'link',
        'image',
        'displayDuration',
        'position',
        'startDate',
        'endDate',
        'vendor',
        'branch',
      ],
    })

    return response.json(result)
  }
}
