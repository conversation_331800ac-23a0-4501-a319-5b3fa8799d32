import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'
import ProductCategory from 'App/Models/ProductCategory'
import { ModifierType } from 'App/Enums/ModifierType'

export default class PublicProductsController {
  public async index({ request, response }: HttpContextContract) {
    const {
      per = 25,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      vendor,
      branch,
      service,
      price_min,
      price_max,
      search,
      category,
      include_modifiers = 'false',
    } = request.qs()

    const productQuery = Product.query()
      .where('active', true)
      .where('status', 'Published')
      .where('visibility', 'Public')
      .preload('category')
      .preload('vendor')
      .preload('branch')
      .preload('service')

    // Load modifiers and related data if requested
    if (include_modifiers === 'true') {
      productQuery
        .preload('availableModifiers')
        .preload('accompaniments')
        .preload('upsells')
        .preload('packagingOptions')
    }

    // Apply filters
    if (vendor) productQuery.where('vendorId', vendor)
    if (branch) productQuery.where('branchId', branch)

    // Handle service filtering - support both service ID and service slug
    if (service) {
      // Check if service is a ULID (26 characters, alphanumeric)
      if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
        // It's a service ID
        productQuery.where('serviceId', service)
      } else {
        // It's a service slug, need to find the service ID first
        productQuery.whereHas('service', (serviceQuery) => {
          serviceQuery.where('slug', service).orWhere('name', service)
        })
      }
    }

    // Handle category filtering - support both category ID and category name
    if (category) {
      // Check if category is a ULID (26 characters, alphanumeric)
      if (category.length === 26 && /^[0-9a-z]{26}$/i.test(category)) {
        // It's a category ID
        productQuery.where('productCategoryId', category)
      } else {
        // It's a category name, need to find the category ID first
        const categoryRecord = await ProductCategory.query()
          .where('name', category)
          .orWhere('slug', category)
          .first()

        if (categoryRecord) {
          productQuery.where('productCategoryId', categoryRecord.id)
        } else {
          // Category not found, return empty results
          productQuery.where('productCategoryId', 'non-existent-category')
        }
      }
    }

    // Price range filtering
    if (price_min) productQuery.where('price', '>=', parseFloat(price_min))
    if (price_max) productQuery.where('price', '<=', parseFloat(price_max))

    // Search functionality
    if (search) {
      productQuery.where((query) => {
        query.whereILike('name', `%${search}%`).orWhereILike('details', `%${search}%`)
      })
    }

    const products = await productQuery.orderBy(order, sort).paginate(page, per)

    // If modifiers are loaded, format them properly for frontend consumption
    if (include_modifiers === 'true') {
      // Convert to JSON and create a completely new response object
      const productsJson = products.toJSON()

      const transformedData = productsJson.data.map((product) => {
        const transformedProduct = {
          id: product.id,
          name: product.name,
          details: product.details,
          price: product.price,
          discounted: product.discounted,
          image: product.image,
          createdAt: product.createdAt,
          category: product.category,
          vendor: product.vendor,
          branch: product.branch,
          service: product.service,
        }

        // Format modifiers by type
        if (product.availableModifiers && product.availableModifiers.length > 0) {
          const modifiers = product.availableModifiers.reduce(
            (acc, modifier) => {
              const type = modifier.type
              if (!acc[type]) {
                acc[type] = []
              }

              // Check different possible locations for pivot data
              const pivotData = modifier.$extras || modifier.pivot || {}

              acc[type].push({
                id: modifier.id,
                name: modifier.name,
                description: modifier.description,
                type: modifier.type,
                defaultPriceAdjustment: modifier.defaultPriceAdjustment,
                price_adjustment:
                  pivotData.pivot_price_adjustment_override ?? modifier.defaultPriceAdjustment,
                is_default: pivotData.pivot_is_default ?? false,
                sort_order: pivotData.pivot_sort_order ?? 0,
              })
              return acc
            },
            {} as Record<ModifierType, any[]>
          )

          // Sort each type's modifiers by sort_order
          Object.keys(modifiers).forEach((type) => {
            modifiers[type as ModifierType].sort((a, b) => a.sort_order - b.sort_order)
          })

          transformedProduct.modifiers = modifiers
        } else {
          transformedProduct.modifiers = {}
        }

        // Add accompaniments with public-safe fields
        if (product.accompaniments) {
          transformedProduct.accompaniments = product.accompaniments.map((acc) => ({
            id: acc.id,
            name: acc.name,
            details: acc.details,
            price: acc.price,
            image: acc.image,
          }))
        }

        // Add upsells with public-safe fields
        if (product.upsells) {
          transformedProduct.upsells = product.upsells.map((upsell) => ({
            id: upsell.id,
            name: upsell.name,
            details: upsell.details,
            price: upsell.price,
            image: upsell.image,
          }))
        }

        // Add packaging options
        if (product.packagingOptions) {
          transformedProduct.packagingOptions = product.packagingOptions.map((pkg) => ({
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            price: pkg.price,
          }))
        }

        return transformedProduct
      })

      // Return a completely new response object
      return response.json({
        meta: productsJson.meta,
        data: transformedData,
      })
    }

    // Standard response without modifiers
    const result = products.serialize({
      fields: [
        'id',
        'name',
        'details',
        'price',
        'discounted',
        'image',
        'createdAt',
        'category',
        'vendor',
        'branch',
        'service',
      ],
    })
    return response.json(result)
  }

  public async show({ request, response, params }: HttpContextContract) {
    const { include_modifiers = 'true' } = request.qs() // Default to true for individual product view

    const productQuery = Product.query()
      .where('id', params.id)
      .where('active', true)
      .where('status', 'Published')
      .where('visibility', 'Public')
      .preload('category')
      .preload('vendor')
      .preload('branch')
      .preload('service')

    // Load modifiers and related data if requested (default for show endpoint)
    if (include_modifiers === 'true') {
      productQuery
        .preload('availableModifiers')
        .preload('accompaniments')
        .preload('upsells')
        .preload('packagingOptions')
    }

    const product = await productQuery.firstOrFail()

    // If modifiers are loaded, format them properly
    if (include_modifiers === 'true') {
      const productData = {
        id: product.id,
        name: product.name,
        details: product.details,
        price: product.price,
        discounted: product.discounted,
        image: product.image,
        createdAt: product.createdAt,
        category: product.category,
        vendor: product.vendor,
        branch: product.branch,
        service: product.service,
      }

      // Format modifiers by type
      if (product.availableModifiers && product.availableModifiers.length > 0) {
        const modifiers = product.availableModifiers.reduce(
          (acc, modifier) => {
            const type = modifier.type
            if (!acc[type]) {
              acc[type] = []
            }
            acc[type].push({
              id: modifier.id,
              name: modifier.name,
              description: modifier.description,
              type: modifier.type,
              defaultPriceAdjustment: modifier.defaultPriceAdjustment,
              price_adjustment:
                modifier.$extras.price_adjustment_override ?? modifier.defaultPriceAdjustment,
              is_default: modifier.$extras.is_default ?? false,
              sort_order: modifier.$extras.sort_order ?? 0,
            })
            return acc
          },
          {} as Record<ModifierType, any[]>
        )

        // Sort each type's modifiers by sort_order
        Object.keys(modifiers).forEach((type) => {
          modifiers[type as ModifierType].sort((a, b) => a.sort_order - b.sort_order)
        })

        productData.modifiers = modifiers
      } else {
        productData.modifiers = {}
      }

      // Add accompaniments with public-safe fields
      if (product.accompaniments) {
        productData.accompaniments = product.accompaniments.map((acc) => ({
          id: acc.id,
          name: acc.name,
          details: acc.details,
          price: acc.price,
          image: acc.image,
        }))
      }

      // Add upsells with public-safe fields
      if (product.upsells) {
        productData.upsells = product.upsells.map((upsell) => ({
          id: upsell.id,
          name: upsell.name,
          details: upsell.details,
          price: upsell.price,
          image: upsell.image,
        }))
      }

      // Add packaging options
      if (product.packagingOptions) {
        productData.packagingOptions = product.packagingOptions.map((pkg) => ({
          id: pkg.id,
          name: pkg.name,
          description: pkg.description,
          price: pkg.price,
        }))
      }

      return response.json(productData)
    }

    // Standard response without modifiers
    const result = product.serialize({
      fields: [
        'id',
        'name',
        'details',
        'price',
        'discounted',
        'image',
        'createdAt',
        'category',
        'vendor',
        'branch',
        'service',
      ],
    })
    return response.json(result)
  }
}
