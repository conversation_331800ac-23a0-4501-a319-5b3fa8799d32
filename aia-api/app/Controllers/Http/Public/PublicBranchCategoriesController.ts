import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductCategory from 'App/Models/ProductCategory'
import Branch from 'App/Models/Branch'
import { bind } from '@adonisjs/route-model-binding'

export default class PublicBranchCategoriesController {
  @bind()
  public async index({ request, response }: HttpContextContract, branch: Branch) {
    const {
      per = 25,
      page = 1,
      order = 'name',
      sort = 'asc',
      service,
      productsPerCategory = 5,
    } = request.qs()

    // Load vendor to verify it's active
    await branch.load('vendor')
    if (!branch.vendor.active) {
      return response.status(404).json({
        error: 'Branch vendor not found or inactive',
      })
    }

    const categoryQuery = ProductCategory.query()
      .whereHas('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
          .where('branchId', branch.id)

        if (service) productQuery.where('serviceId', service)
      })
      .preload('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
          .where('branchId', branch.id)

        if (service) productQuery.where('serviceId', service)

        productQuery
          .limit(Math.min(parseInt(productsPerCategory) || 5, 20))
          .preload('service')
          .preload('vendor')
      })
      .withCount('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
          .where('branchId', branch.id)
      })
      .orderBy(order, sort)

    const categories = await categoryQuery.paginate(page, per)

    // Only expose public-safe fields and manually add product counts
    const result = categories.serialize({
      fields: ['id', 'name', 'slug', 'details', 'image', 'createdAt', 'products'],
    })

    // Manually add product counts to avoid exposing Lucid internals
    if (result.data) {
      result.data = result.data.map((category, index) => ({
        ...category,
        productsCount: categories[index].$extras?.products_count || 0,
      }))
    }

    return response.json({
      ...result,
      branch: {
        id: branch.id,
        name: branch.name,
        details: branch.details,
        location: branch.location,
      },
      vendor: {
        id: branch.vendor.id,
        name: branch.vendor.name,
        details: branch.vendor.details,
      },
    })
  }
}
