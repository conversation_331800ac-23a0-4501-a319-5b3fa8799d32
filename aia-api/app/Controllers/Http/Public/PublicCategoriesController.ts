import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductCategory from 'App/Models/ProductCategory'

export default class PublicCategoriesController {
  public async index({ request, response }: HttpContextContract) {
    const {
      per = 25,
      page = 1,
      order = 'name',
      sort = 'asc',
      vendor,
      branch,
      service,
      featured = false,
      productsPerCategory = 5,
    } = request.qs()

    const categoryQuery = ProductCategory.query()
      .whereHas('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')

        if (vendor) productQuery.where('vendorId', vendor)
        if (branch) productQuery.where('branchId', branch)

        // Handle service filtering - support both service ID and service slug
        if (service) {
          // Check if service is a ULID (26 characters, alphanumeric)
          if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
            // It's a service ID
            productQuery.where('serviceId', service)
          } else {
            // It's a service slug, need to find the service ID first
            productQuery.whereHas('service', (serviceQuery) => {
              serviceQuery.where('slug', service).orWhere('name', service)
            })
          }
        }
      })
      .preload('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')

        if (vendor) productQuery.where('vendorId', vendor)
        if (branch) productQuery.where('branchId', branch)

        // Handle service filtering - support both service ID and service slug
        if (service) {
          // Check if service is a ULID (26 characters, alphanumeric)
          if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
            // It's a service ID
            productQuery.where('serviceId', service)
          } else {
            // It's a service slug, need to find the service ID first
            productQuery.whereHas('service', (serviceQuery) => {
              serviceQuery.where('slug', service).orWhere('name', service)
            })
          }
        }

        productQuery
          .limit(Math.min(parseInt(productsPerCategory) || 5, 20))
          .preload('vendor')
          .preload('service')
      })
      .withCount('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')

        if (vendor) productQuery.where('vendorId', vendor)
        if (branch) productQuery.where('branchId', branch)

        // Handle service filtering - support both service ID and service slug
        if (service) {
          // Check if service is a ULID (26 characters, alphanumeric)
          if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
            // It's a service ID
            productQuery.where('serviceId', service)
          } else {
            // It's a service slug, need to find the service ID first
            productQuery.whereHas('service', (serviceQuery) => {
              serviceQuery.where('slug', service).orWhere('name', service)
            })
          }
        }
      })
      .orderBy(order, sort)

    // Add featured filtering if requested
    if (featured === 'true') {
      // For now, we'll consider categories with more products as "featured"
      // This can be enhanced with a dedicated featured flag later
      categoryQuery.whereHas(
        'products',
        (productQuery) => {
          productQuery
            .where('active', true)
            .where('status', 'Published')
            .where('visibility', 'Public')

          if (vendor) productQuery.where('vendorId', vendor)
          if (branch) productQuery.where('branchId', branch)
          if (service) productQuery.where('serviceId', service)
        },
        '>',
        5
      ) // Categories with more than 5 products are considered "featured"
    }

    const categories = await categoryQuery.paginate(page, per)

    // Only expose public-safe fields and manually add product counts
    const result = categories.serialize({
      fields: ['id', 'name', 'slug', 'details', 'image', 'createdAt', 'products'],
    })

    // Manually add product counts to avoid exposing Lucid internals
    if (result.data) {
      result.data = result.data.map((category, index) => ({
        ...category,
        productsCount: categories[index].$extras?.products_count || 0,
      }))
    }

    return response.json(result)
  }

  public async show({ request, response, params }: HttpContextContract) {
    const { vendor, branch, service, productsPerCategory = 20 } = request.qs()

    const category = await ProductCategory.query()
      .where('id', params.id)
      .whereHas('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
      })
      .preload('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')

        if (vendor) productQuery.where('vendorId', vendor)
        if (branch) productQuery.where('branchId', branch)

        // Handle service filtering - support both service ID and service slug
        if (service) {
          // Check if service is a ULID (26 characters, alphanumeric)
          if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
            // It's a service ID
            productQuery.where('serviceId', service)
          } else {
            // It's a service slug, need to find the service ID first
            productQuery.whereHas('service', (serviceQuery) => {
              serviceQuery.where('slug', service).orWhere('name', service)
            })
          }
        }

        productQuery
          .limit(Math.min(parseInt(productsPerCategory) || 20, 100))
          .preload('vendor')
          .preload('service')
          .preload('branch')
      })
      .withCount('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')

        if (vendor) productQuery.where('vendorId', vendor)
        if (branch) productQuery.where('branchId', branch)

        // Handle service filtering - support both service ID and service slug
        if (service) {
          // Check if service is a ULID (26 characters, alphanumeric)
          if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
            // It's a service ID
            productQuery.where('serviceId', service)
          } else {
            // It's a service slug, need to find the service ID first
            productQuery.whereHas('service', (serviceQuery) => {
              serviceQuery.where('slug', service).orWhere('name', service)
            })
          }
        }
      })
      .firstOrFail()

    // Only expose public-safe fields and manually add product count
    const result = category.serialize({
      fields: ['id', 'name', 'slug', 'details', 'image', 'createdAt', 'products'],
    })

    // Manually add product count to avoid exposing Lucid internals
    const finalResult = {
      ...result,
      productsCount: category.$extras?.products_count || 0,
    }

    return response.json(finalResult)
  }
}
