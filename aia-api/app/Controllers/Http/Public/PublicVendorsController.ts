import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Vendor from 'App/Models/Vendor'

export default class PublicVendorsController {
  public async index({ request, response }: HttpContextContract) {
    const {
      per = 25,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      service,
      category,
      search,
    } = request.qs()

    // Define valid columns for ordering to prevent SQL injection and errors
    const validOrderColumns = [
      'id',
      'name',
      'slug',
      'details',
      'email',
      'phone',
      'active',
      'featured',
      'createdAt',
      'updatedAt',
      'verificationStatus',
      'verifiedAt',
    ]

    // Validate and sanitize the order parameter
    const safeOrder = validOrderColumns.includes(order) ? order : 'createdAt'
    const safeSort = ['asc', 'desc'].includes(sort?.toLowerCase()) ? sort.toLowerCase() : 'desc'

    const vendorQuery = Vendor.query()
      .where('active', true)
      .preload('branches')
      .preload('categories')
      .preload('specialities')

    // Apply service filtering - filter vendors by the services they offer
    if (service) {
      // Check if service is a ULID (26 characters, alphanumeric)
      if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
        // It's a service ID
        vendorQuery.whereHas('services', (serviceQuery) => {
          serviceQuery.where('service_id', service).where('vendor_services.active', true)
        })
      } else {
        // It's a service slug or name, need to find the service ID first using a subquery
        const Service = (await import('App/Models/Service')).default
        const serviceRecord = await Service.query()
          .where('slug', service)
          .orWhere('name', service)
          .first()

        if (serviceRecord) {
          vendorQuery.whereHas('services', (serviceQuery) => {
            serviceQuery.where('service_id', serviceRecord.id).where('vendor_services.active', true)
          })
        } else {
          // Service not found, return no results
          vendorQuery.where('id', 'non-existent-service')
        }
      }
    }

    // Apply category filtering - filter vendors by their categories
    if (category) {
      vendorQuery.whereHas('categories', (categoryQuery) => {
        // Support both category ID and category name
        if (category.length === 26 && /^[0-9a-z]{26}$/i.test(category)) {
          // It's a category ID
          categoryQuery.where('vendor_categories.id', category)
        } else {
          // It's a category name or slug
          categoryQuery.where('name', category).orWhere('slug', category)
        }
      })
    }

    // Apply search filtering
    if (search) {
      vendorQuery.where((query) => {
        query.whereILike('name', `%${search}%`).orWhereILike('details', `%${search}%`)
      })
    }

    vendorQuery.orderBy(safeOrder, safeSort)

    const vendors = await vendorQuery.paginate(page, per)
    // Only expose public-safe fields
    const result = vendors.serialize({
      fields: [
        'id',
        'name',
        'slug',
        'details',
        'logo',
        'logoUrl',
        'cover',
        'active',
        'featured',
        'location',
        'verificationStatus',
        'createdAt',
        'updatedAt',
        'branches',
        'categories',
        'specialities',
      ],
    })

    // Add metadata about parameter validation if needed
    if (order !== safeOrder) {
      result.meta = {
        ...result.meta,
        warning: `Invalid order parameter '${order}'. Using '${safeOrder}' instead.`,
        validOrderColumns: validOrderColumns,
      }
    }

    return response.json(result)
  }

  public async show({ response, params }: HttpContextContract) {
    const vendor = await Vendor.query()
      .where('id', params.id)
      .where('active', true)
      .preload('branches')
      .preload('categories')
      .preload('specialities')
      .firstOrFail()
    // Only expose public-safe fields
    const result = vendor.serialize({
      fields: [
        'id',
        'name',
        'slug',
        'details',
        'logo',
        'logoUrl',
        'cover',
        'active',
        'featured',
        'location',
        'verificationStatus',
        'createdAt',
        'updatedAt',
        'branches',
        'categories',
        'specialities',
      ],
    })
    return response.json(result)
  }
}
