import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Task from 'App/Models/Task'

export default class PublicTasksController {
  public async index({ request, response }: HttpContextContract) {
    const { per = 20, page = 1, order = 'name', sort = 'asc' } = request.qs()
    const taskQuery = Task.query()
      .where('active', true)
      .orderBy(order, sort)

    const tasks = await taskQuery.paginate(page, per)
    // Only expose public-safe fields
    const result = tasks.serialize({
      fields: [
        'id', 'name', 'details', 'slug', 'imageUrl', 'createdAt'
      ]
    })
    return response.json(result)
  }

  public async show({ response, params }: HttpContextContract) {
    const task = await Task.query()
      .where('id', params.id)
      .where('active', true)
      .firstOrFail()
    // Only expose public-safe fields
    const result = task.serialize({
      fields: [
        'id', 'name', 'details', 'slug', 'imageUrl', 'createdAt'
      ]
    })
    return response.json(result)
  }
} 