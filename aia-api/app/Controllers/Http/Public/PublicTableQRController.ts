import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import QRCodeProcessingService from 'App/Services/QRCodeProcessingService'

export default class PublicTableQRController {
  private qrCodeProcessingService: QRCodeProcessingService

  constructor() {
    this.qrCodeProcessingService = new QRCodeProcessingService()
  }

  /**
   * @show
   * @summary Get table context from QR code scan
   * @description Process QR code scan and return complete table context for Digital Waiter
   * @paramPath id required string - Table QR Code ID
   * @responseBody 200 - Table context with restaurant, branch, section, and table information
   * @response 404 - QR code not found or inactive
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const { id: qrCodeId } = params

      // Process QR code scan and get table context
      const tableContext = await this.qrCodeProcessingService.processQRCodeScan(qrCodeId)

      return response.json({
        success: true,
        data: tableContext,
        message: 'QR code scanned successfully'
      })
    } catch (error) {
      if (error.message === 'QR code not found') {
        return response.notFound({
          success: false,
          message: 'QR code not found',
          error: 'QRCODE_NOT_FOUND'
        })
      }

      if (error.message === 'QR code is inactive') {
        return response.badRequest({
          success: false,
          message: 'This QR code is no longer active',
          error: 'QRCODE_INACTIVE'
        })
      }

      return response.internalServerError({
        success: false,
        message: 'Failed to process QR code',
        error: 'PROCESSING_ERROR'
      })
    }
  }

  /**
   * @validate
   * @summary Validate QR code without recording scan
   * @description Check if QR code is valid and active without tracking the scan
   * @paramPath id required string - Table QR Code ID
   * @responseBody 200 - Validation result
   */
  public async validate({ params, response }: HttpContextContract) {
    try {
      const { id: qrCodeId } = params

      const isValid = await this.qrCodeProcessingService.validateQRCode(qrCodeId)

      return response.json({
        success: true,
        data: {
          qrCodeId,
          isValid,
          message: isValid ? 'QR code is valid' : 'QR code is invalid or inactive'
        }
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to validate QR code',
        error: 'VALIDATION_ERROR'
      })
    }
  }

  /**
   * @preview
   * @summary Get table context without recording scan
   * @description Get table information for preview without tracking usage
   * @paramPath id required string - Table QR Code ID
   * @responseBody 200 - Table context
   */
  public async preview({ params, response }: HttpContextContract) {
    try {
      const { id: qrCodeId } = params

      const tableContext = await this.qrCodeProcessingService.getTableContext(qrCodeId)

      return response.json({
        success: true,
        data: tableContext,
        message: 'Table context retrieved successfully'
      })
    } catch (error) {
      if (error.message === 'QR code not found') {
        return response.notFound({
          success: false,
          message: 'QR code not found',
          error: 'QRCODE_NOT_FOUND'
        })
      }

      return response.internalServerError({
        success: false,
        message: 'Failed to get table context',
        error: 'CONTEXT_ERROR'
      })
    }
  }

  /**
   * @stats
   * @summary Get QR code usage statistics
   * @description Get usage statistics for a specific QR code
   * @paramPath id required string - Table QR Code ID
   * @responseBody 200 - Usage statistics
   */
  public async stats({ params, response }: HttpContextContract) {
    try {
      const { id: qrCodeId } = params

      const stats = await this.qrCodeProcessingService.getQRCodeStats(qrCodeId)

      return response.json({
        success: true,
        data: stats,
        message: 'QR code statistics retrieved successfully'
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to get QR code statistics',
        error: 'STATS_ERROR'
      })
    }
  }

  /**
   * @validateBulk
   * @summary Validate multiple QR codes
   * @description Validate multiple QR codes in a single request
   * @requestBody {"qrCodeIds": ["id1", "id2", "id3"]}
   * @responseBody 200 - Validation results for all QR codes
   */
  public async validateBulk({ request, response }: HttpContextContract) {
    try {
      const { qrCodeIds } = request.only(['qrCodeIds'])

      if (!Array.isArray(qrCodeIds) || qrCodeIds.length === 0) {
        return response.badRequest({
          success: false,
          message: 'qrCodeIds must be a non-empty array',
          error: 'INVALID_INPUT'
        })
      }

      const validationResults = await this.qrCodeProcessingService.validateMultipleQRCodes(qrCodeIds)

      return response.json({
        success: true,
        data: validationResults,
        message: 'Bulk validation completed successfully'
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to validate QR codes',
        error: 'BULK_VALIDATION_ERROR'
      })
    }
  }
}
