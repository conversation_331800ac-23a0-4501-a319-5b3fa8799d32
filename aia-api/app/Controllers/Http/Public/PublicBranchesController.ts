import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Branch from 'App/Models/Branch'
import Vendor from 'App/Models/Vendor'

export default class PublicBranchesController {
  public async index({ request, response }: HttpContextContract) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc' } = request.qs()
    // Only show branches whose vendor is active
    const branchQuery = Branch.query()
      .whereHas('vendor', (vendorQuery) => {
        vendorQuery.where('active', true)
      })
      .preload('vendor')
      .orderBy(order, sort)

    const branches = await branchQuery.paginate(page, per)
    // Only expose public-safe fields
    const result = branches.serialize({
      fields: [
        'id', 'name', 'details', 'location', 'image', 'createdAt', 'vendor'
      ]
    })
    return response.json(result)
  }

  public async show({ response, params }: HttpContextContract) {
    const branch = await Branch.query()
      .where('id', params.id)
      .whereHas('vendor', (vendorQuery) => {
        vendorQuery.where('active', true)
      })
      .preload('vendor')
      .firstOrFail()
    // Only expose public-safe fields
    const result = branch.serialize({
      fields: [
        'id', 'name', 'details', 'location', 'image', 'createdAt', 'vendor'
      ]
    })
    return response.json(result)
  }
} 