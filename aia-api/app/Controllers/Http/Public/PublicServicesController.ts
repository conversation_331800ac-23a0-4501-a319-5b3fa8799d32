import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Service from 'App/Models/Service'

export default class PublicServicesController {
  public async index({ request, response }: HttpContextContract) {
    const { per = 20, page = 1, order = 'name', sort = 'asc' } = request.qs()
    const serviceQuery = Service.query()
      .where('active', true)
      .orderBy(order, sort)

    const services = await serviceQuery.paginate(page, per)
    // Only expose public-safe fields
    const result = services.serialize({
      fields: [
        'id', 'name', 'details', 'slug', 'image', 'createdAt'
      ]
    })
    return response.json(result)
  }

  public async show({ response, params }: HttpContextContract) {
    const service = await Service.query()
      .where('id', params.id)
      .where('active', true)
      .firstOrFail()
    // Only expose public-safe fields
    const result = service.serialize({
      fields: [
        'id', 'name', 'details', 'slug', 'image', 'createdAt'
      ]
    })
    return response.json(result)
  }
} 