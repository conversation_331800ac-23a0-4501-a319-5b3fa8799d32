import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductCategory from 'App/Models/ProductCategory'
import Vendor from 'App/Models/Vendor'
import { bind } from '@adonisjs/route-model-binding'
import { ModifierType } from 'App/Enums/ModifierType'

export default class PublicVendorCategoriesController {
  @bind()
  public async index({ request, response }: HttpContextContract, vendor: Vendor) {
    const {
      per = 25,
      page = 1,
      order = 'name',
      sort = 'asc',
      branch,
      service,
      productsPerCategory = 5,
      include_modifiers = 'false',
    } = request.qs()

    // Verify vendor is active
    if (!vendor.active) {
      return response.status(404).json({
        error: 'Vendor not found or inactive',
      })
    }

    const categoryQuery = ProductCategory.query()
      .whereHas('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
          .where('vendorId', vendor.id)

        if (branch) productQuery.where('branchId', branch)
        if (service) productQuery.where('serviceId', service)
      })
      .preload('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
          .where('vendorId', vendor.id)

        if (branch) productQuery.where('branchId', branch)
        if (service) productQuery.where('serviceId', service)

        productQuery
          .limit(Math.min(parseInt(productsPerCategory) || 5, 20))
          .preload('service')
          .preload('branch')

        // Load modifiers for product samples if requested
        if (include_modifiers === 'true') {
          productQuery
            .preload('availableModifiers')
            .preload('accompaniments')
            .preload('upsells')
            .preload('packagingOptions')
        }
      })
      .withCount('products', (productQuery) => {
        productQuery
          .where('active', true)
          .where('status', 'Published')
          .where('visibility', 'Public')
          .where('vendorId', vendor.id)
      })
      .orderBy(order, sort)

    const categories = await categoryQuery.paginate(page, per)

    // Only expose public-safe fields and manually add product counts
    const result = categories.serialize({
      fields: ['id', 'name', 'slug', 'details', 'image', 'createdAt', 'products'],
    })

    // Manually add product counts to avoid exposing Lucid internals
    if (result.data) {
      result.data = result.data.map((category, index) => ({
        ...category,
        productsCount: categories[index].$extras?.products_count || 0,
      }))
    }

    return response.json({
      ...result,
      vendor: {
        id: vendor.id,
        name: vendor.name,
        details: vendor.details,
      },
    })
  }
}
