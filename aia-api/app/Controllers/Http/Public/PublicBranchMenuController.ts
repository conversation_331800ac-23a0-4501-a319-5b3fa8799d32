import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Branch from 'App/Models/Branch'
import Product from 'App/Models/Product'
import { bind } from '@adonisjs/route-model-binding'
import { ModifierType } from 'App/Enums/ModifierType'

export default class PublicBranchMenuController {
  @bind()
  public async index({ request, response }: HttpContextContract, branch: Branch) {
    const {
      per = 50,
      page = 1,
      order = 'name',
      sort = 'asc',
      service,
      category,
      price_min,
      price_max,
      search,
      include_modifiers = 'true', // Default to true for menu endpoint
    } = request.qs()

    // Load vendor to verify it's active
    await branch.load('vendor')
    if (!branch.vendor.active) {
      return response.status(404).json({
        error: 'Branch vendor not found or inactive',
      })
    }

    const productQuery = Product.query()
      .where('active', true)
      .where('status', 'Published')
      .where('visibility', 'Public')
      .where('branchId', branch.id)
      .preload('category')
      .preload('vendor')
      .preload('service')

    // Apply filters
    // Handle service filtering - support both service ID and service slug
    if (service) {
      // Check if service is a ULID (26 characters, alphanumeric)
      if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
        // It's a service ID
        productQuery.where('serviceId', service)
      } else {
        // It's a service slug, need to find the service ID first
        productQuery.whereHas('service', (serviceQuery) => {
          serviceQuery.where('slug', service).orWhere('name', service)
        })
      }
    }
    if (category) {
      // Support both category ID and name
      if (category.length === 26 && /^[0-9a-z]{26}$/i.test(category)) {
        productQuery.where('productCategoryId', category)
      } else {
        productQuery.whereHas('category', (categoryQuery) => {
          categoryQuery.where('name', category).orWhere('slug', category)
        })
      }
    }

    // Price range filtering
    if (price_min) productQuery.where('price', '>=', parseFloat(price_min))
    if (price_max) productQuery.where('price', '<=', parseFloat(price_max))

    // Search functionality
    if (search) {
      productQuery.where((query) => {
        query.whereILike('name', `%${search}%`).orWhereILike('details', `%${search}%`)
      })
    }

    // Load modifiers and related data if requested
    if (include_modifiers === 'true') {
      productQuery
        .preload('availableModifiers')
        .preload('accompaniments')
        .preload('upsells')
        .preload('packagingOptions')
    }

    const products = await productQuery.orderBy(order, sort).paginate(page, per)

    // If modifiers are loaded, format them properly for frontend consumption
    if (include_modifiers === 'true') {
      // Convert to JSON and create a completely new response object
      const productsJson = products.toJSON()

      const transformedData = productsJson.data.map((product) => {
        const transformedProduct = {
          id: product.id,
          name: product.name,
          details: product.details,
          price: product.price,
          discounted: product.discounted,
          image: product.image,
          createdAt: product.createdAt,
          category: product.category,
          vendor: product.vendor,
          service: product.service,
        }

        // Format modifiers by type
        if (product.availableModifiers && product.availableModifiers.length > 0) {
          const modifiers = product.availableModifiers.reduce(
            (acc, modifier) => {
              const type = modifier.type
              if (!acc[type]) {
                acc[type] = []
              }

              // Check different possible locations for pivot data
              const pivotData = modifier.$extras || modifier.pivot || {}

              acc[type].push({
                id: modifier.id,
                name: modifier.name,
                description: modifier.description,
                type: modifier.type,
                defaultPriceAdjustment: modifier.defaultPriceAdjustment,
                price_adjustment:
                  pivotData.pivot_price_adjustment_override ?? modifier.defaultPriceAdjustment,
                is_default: pivotData.pivot_is_default ?? false,
                sort_order: pivotData.pivot_sort_order ?? 0,
              })
              return acc
            },
            {} as Record<ModifierType, any[]>
          )

          // Sort each type's modifiers by sort_order
          Object.keys(modifiers).forEach((type) => {
            modifiers[type as ModifierType].sort((a, b) => a.sort_order - b.sort_order)
          })

          transformedProduct.modifiers = modifiers
        } else {
          transformedProduct.modifiers = {}
        }

        // Add accompaniments with public-safe fields
        if (product.accompaniments) {
          transformedProduct.accompaniments = product.accompaniments.map((acc) => ({
            id: acc.id,
            name: acc.name,
            details: acc.details,
            price: acc.price,
            image: acc.image,
          }))
        }

        // Add upsells with public-safe fields
        if (product.upsells) {
          transformedProduct.upsells = product.upsells.map((upsell) => ({
            id: upsell.id,
            name: upsell.name,
            details: upsell.details,
            price: upsell.price,
            image: upsell.image,
          }))
        }

        // Add packaging options
        if (product.packagingOptions) {
          transformedProduct.packagingOptions = product.packagingOptions.map((pkg) => ({
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            price: pkg.price,
          }))
        }

        return transformedProduct
      })

      // Return a completely new response object with branch info
      return response.json({
        meta: productsJson.meta,
        branch: {
          id: branch.id,
          name: branch.name,
          details: branch.details,
          email: branch.email,
          phone: branch.phone,
          location: branch.location,
          vendor: {
            id: branch.vendor.id,
            name: branch.vendor.name,
            slug: branch.vendor.slug,
            logo: branch.vendor.logo,
            cover: branch.vendor.cover,
          },
        },
        data: transformedData,
      })
    }

    // Standard response without modifiers
    const result = products.serialize({
      fields: [
        'id',
        'name',
        'details',
        'price',
        'discounted',
        'image',
        'createdAt',
        'category',
        'vendor',
        'service',
      ],
    })

    // Add branch info to standard response
    result.branch = {
      id: branch.id,
      name: branch.name,
      details: branch.details,
      email: branch.email,
      phone: branch.phone,
      location: branch.location,
      vendor: {
        id: branch.vendor.id,
        name: branch.vendor.name,
        slug: branch.vendor.slug,
        logo: branch.vendor.logo,
        cover: branch.vendor.cover,
      },
    }

    return response.json(result)
  }
}
