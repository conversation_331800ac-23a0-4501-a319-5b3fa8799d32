import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductCategory from 'App/Models/ProductCategory'
import Product from 'App/Models/Product'
import { bind } from '@adonisjs/route-model-binding'
import { ModifierType } from 'App/Enums/ModifierType'

export default class PublicCategoryProductsController {
  public async index({ request, response, params }: HttpContextContract) {
    const {
      per = 25,
      page = 1,
      order = 'name',
      sort = 'asc',
      vendor,
      branch,
      service,
      price_min,
      price_max,
      search,
      category,
      include_modifiers = 'false',
    } = request.qs()

    let productQuery

    // Handle "all" categories case vs specific category
    if (params.id === 'all') {
      // Search all products across all categories
      productQuery = Product.query()
        .where('active', true)
        .where('status', 'Published')
        .where('visibility', 'Public')
        .preload('vendor')
        .preload('service')
        .preload('branch')
        .preload('category')

      // Load modifiers and related data if requested
      if (include_modifiers === 'true') {
        productQuery
          .preload('availableModifiers')
          .preload('accompaniments')
          .preload('upsells')
          .preload('packagingOptions')
      }
    } else {
      // Search products in specific category
      const category = await ProductCategory.findOrFail(params.id)
      productQuery = category
        .related('products')
        .query()
        .where('active', true)
        .where('status', 'Published')
        .where('visibility', 'Public')
        .preload('vendor')
        .preload('service')
        .preload('branch')
        .preload('category')

      // Load modifiers and related data if requested
      if (include_modifiers === 'true') {
        productQuery
          .preload('availableModifiers')
          .preload('accompaniments')
          .preload('upsells')
          .preload('packagingOptions')
      }
    }

    // Apply filters
    if (vendor) productQuery.where('vendorId', vendor)
    if (branch) productQuery.where('branchId', branch)

    // Handle service filtering - support both service ID and service slug
    if (service) {
      // Check if service is a ULID (26 characters, alphanumeric)
      if (service.length === 26 && /^[0-9a-z]{26}$/i.test(service)) {
        // It's a service ID
        productQuery.where('serviceId', service)
      } else {
        // It's a service slug, need to find the service ID first
        productQuery.whereHas('service', (serviceQuery) => {
          serviceQuery.where('slug', service).orWhere('name', service)
        })
      }
    }

    // Price range filtering
    if (price_min) productQuery.where('price', '>=', parseFloat(price_min))
    if (price_max) productQuery.where('price', '<=', parseFloat(price_max))

    // Search functionality
    if (search) {
      productQuery.where((query) => {
        query.whereILike('name', `%${search}%`).orWhereILike('details', `%${search}%`)
      })
    }

    // Handle category filtering when params.id is 'all' but category query param is provided
    if (params.id === 'all' && category) {
      // Check if category is a ULID (26 characters, alphanumeric)
      if (category.length === 26 && /^[0-9a-z]{26}$/i.test(category)) {
        // It's a category ID
        productQuery.where('productCategoryId', category)
      } else {
        // It's a category name, need to find the category ID first
        const categoryRecord = await ProductCategory.query()
          .where('name', category)
          .orWhere('slug', category)
          .first()

        if (categoryRecord) {
          productQuery.where('productCategoryId', categoryRecord.id)
        } else {
          // Category not found, return empty results
          productQuery.where('productCategoryId', 'non-existent-category')
        }
      }
    }

    const products = await productQuery.orderBy(order, sort).paginate(page, per)

    // Only expose public-safe fields
    const result = products.serialize({
      fields: [
        'id',
        'name',
        'details',
        'price',
        'discounted',
        'image',
        'createdAt',
        'vendor',
        'service',
        'branch',
        'category',
      ],
    })

    return response.json(result)
  }
}
