import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Address from '../../Models/Address'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Address management
 * @version 1.0.0
 * @description Address management for the application
 */
export default class AddressController {
  /**
   * @index
   * @summary List all address
   * @description List all address, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const addressQuery = Address.filter(filters)

    return await addressQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a address
   * @description Create a address with their details (name and details)
   * @requestBody {"name": "", "details": "", "location": {"name": "", "address": "", "regions": {"country": ""}, "coordinates": {"lat": 0, "lng": 0}, "place_id": ""}, "phone": ""}
   * @responseBody 200 - <Address>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, details, userId, location, phone } = request.all()

    const address = await Address.create({ name, details, userId, location, phone })

    return response.json(address)
  }

  @bind()
  /**
   * @show
   * @summary Show a single address
   * @description Show a address with their details (name and details)
   * @paramPath id required number - Address ID
   * @responseBody 200 - <Address>
   * @response 404 - Address not found
   */
  public async show({ response }: HttpContextContract, address: Address) {
    return response.json(address)
  }

  @bind()
  /**
   * @update
   * @summary Update a address
   * @description Update a address with their details (name and details)
   * @paramPath id required number - Address ID
   * @requestBody <Address>
   * @responseBody 200 - <Address>
   * @response 404 - Address not found
   */
  public async update({ request, response }: HttpContextContract, address: Address) {
    const input = request.all()

    await address.merge(input).save()

    return response.json(address)
  }

  @bind()

  /**
   * @destroy
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, address: Address) {
    return await address.delete()
  }
}
