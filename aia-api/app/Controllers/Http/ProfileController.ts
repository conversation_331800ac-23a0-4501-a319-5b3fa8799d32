import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'

export default class ProfileController {
  /**
   * Get authenticated user's profile
   */
  public async show({ auth, response }: HttpContextContract) {
    const user = auth.user!
    await user.load('roles')
    await user.load('permissions')
    await user.load('devices', (dq) => dq.where('status', 'Active'))

    return response.json(user)
  }

  /**
   * Update authenticated user's profile (PATCH - partial updates only)
   */
  public async update({ auth, request, response }: HttpContextContract) {
    const user = auth.user!
    
    const requestData = request.all()
    
    // Debug: Log the incoming request data
    console.log('=== PROFILE UPDATE REQUEST DEBUG ===')
    console.log('Request body:', JSON.stringify(requestData, null, 2))
    console.log('=== END DEBUG ===')
    
    // Only process fields that are actually present in the request
    const updateData: any = {}
    const validationErrors: any = {}
    
    // Helper function to validate and add field to update data
    const processField = (fieldName: string, value: any, validationFn?: (val: any) => string | null) => {
      if (value !== undefined && value !== null && value !== '') {
        if (validationFn) {
          const error = validationFn(value)
          if (error) {
            validationErrors[fieldName] = error
            return
          }
        }
        updateData[fieldName] = value
      }
    }
    
    // Validation functions
    const validateEmail = (email: string) => {
      const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i
      if (!emailRegex.test(email)) {
        return 'Please enter a valid email address'
      }
      return null
    }
    
    const validatePhone = (phone: string) => {
      const cleanPhone = phone.replace(/^\+/, '')
      if (!cleanPhone.startsWith('254')) {
        return 'Phone number should start with 254'
      }
      return null
    }
    
    const validateRequired = (value: string, fieldName: string) => {
      if (!value.trim()) {
        return `${fieldName} is required`
      }
      return null
    }
    // Avatar handling - commented out for now due to Attachment addon issues
    // const avatar = request.file('avatar')
    // if (avatar) {
    //   user.avatar = Attachment.fromFile(avatar)
    // }

    // Process each field that's present in the request
    if (requestData.title !== undefined) {
      processField('title', requestData.title)
    }
    
    if (requestData.firstName !== undefined) {
      processField('firstName', requestData.firstName, (val) => validateRequired(val, 'First name'))
    }
    
    if (requestData.lastName !== undefined) {
      processField('lastName', requestData.lastName, (val) => validateRequired(val, 'Last name'))
    }
    
    if (requestData.email !== undefined) {
      processField('email', requestData.email, (val) => {
        const requiredError = validateRequired(val, 'Email')
        if (requiredError) return requiredError
        return validateEmail(val)
      })
    }
    
    if (requestData.phone !== undefined) {
      processField('phone', requestData.phone, (val) => {
        const requiredError = validateRequired(val, 'Phone')
        if (requiredError) return requiredError
        return validatePhone(val)
      })
    }
    
    if (requestData.gender !== undefined) {
      processField('gender', requestData.gender)
    }
    
    if (requestData.details !== undefined) {
      processField('details', requestData.details)
    }
    
    if (requestData.idpass !== undefined) {
      processField('idpass', requestData.idpass, (val) => validateRequired(val, 'ID/Passport'))
    }
    
    if (requestData.dob !== undefined) {
      processField('dob', requestData.dob && requestData.dob.trim() !== '' ? requestData.dob : null)
    }
    
    if (requestData.location !== undefined) {
      processField('location', requestData.location)
    }
    
    // Check for validation errors
    if (Object.keys(validationErrors).length > 0) {
      return response.badRequest({
        message: 'Validation errors',
        errors: validationErrors
      })
    }
    
    // Check for duplicate email (only if email is being updated)
    if (updateData.email) {
      const existingUserWithEmail = await User.query()
        .where('email', updateData.email)
        .whereNot('id', user.id)
        .first()
      
      if (existingUserWithEmail) {
        return response.conflict({
          message: 'Email already exists',
          errors: {
            email: 'This email address is already in use'
          }
        })
      }
    }
    
    // Check for duplicate phone (only if phone is being updated)
    if (updateData.phone) {
      const cleanPhone = updateData.phone.replace(/^\+/, '')
      const existingUserWithPhone = await User.query()
        .where('phone', cleanPhone)
        .whereNot('id', user.id)
        .first()
      
      if (existingUserWithPhone) {
        return response.conflict({
          message: 'Phone number already exists',
          errors: {
            phone: 'This phone number is already in use'
          }
        })
      }
      
      // Update phone with clean format
      updateData.phone = cleanPhone
    }
    
    try {
      // Only update if there are fields to update
      if (Object.keys(updateData).length === 0) {
        return response.json({
          message: 'No changes to update',
          user
        })
      }
      
      console.log('=== FINAL UPDATE DATA ===')
      console.log('Update data:', updateData)
      console.log('=== END FINAL UPDATE DATA ===')
      
      await user.merge(updateData).save()

      // Reload the user with relationships
      await user.load('roles')
      await user.load('permissions')
      await user.load('devices', (dq) => dq.where('status', 'Active'))

      return response.json({
        message: 'Profile updated successfully',
        user
      })
    } catch (error) {
      console.error('Profile update error:', error)
      return response.internalServerError({
        message: 'Failed to update profile',
        error: error.message
      })
    }
  }

  /**
   * Update authenticated user's password
   */
  public async updatePassword({ auth, request, response }: HttpContextContract) {
    const user = auth.user!
    const { currentPassword, newPassword } = request.all()

    if (!currentPassword || !newPassword) {
      return response.badRequest({
        message: 'Current password and new password are required'
      })
    }

    // Verify current password
    const isValidPassword = await user.verifyPassword(currentPassword)
    if (!isValidPassword) {
      return response.badRequest({
        message: 'Current password is incorrect'
      })
    }

    // Update password
    user.password = newPassword
    await user.save()

    return response.json({
      message: 'Password updated successfully'
    })
  }

  /**
   * Update authenticated user's avatar
   */
  public async updateAvatar({ auth, request, response }: HttpContextContract) {
    const user = auth.user!
    const avatar = request.file('avatar')

    if (!avatar) {
      return response.badRequest({
        message: 'Avatar file is required'
      })
    }

    // Avatar handling - commented out for now due to Attachment addon issues
    // user.avatar = Attachment.fromFile(avatar)
    // await user.save()

    return response.json({
      message: 'Avatar update temporarily disabled due to configuration issues',
      avatar: user.avatar
    })
  }
}
