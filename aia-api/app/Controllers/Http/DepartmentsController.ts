import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Department from '../../Models/Department'
import User from '../../Models/User'
import { bind } from '@adonisjs/route-model-binding'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { ulid } from 'ulidx'

/**
 * @name Department management
 * @version 1.0.0
 * @description Department management for restaurant fulfillment tracking
 */
export default class DepartmentsController {
  /**
   * @index
   * @summary List all departments
   * @description List all departments with filtering and pagination
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery vendor_id - Filter by vendor ID
   * @paramQuery branch_id - Filter by branch ID
   * @paramQuery active - Filter by active status
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  public async index({ request, response }: HttpContextContract) {
    const {
      per = 10,
      page = 1,
      vendor_id,
      branch_id,
      active,
      order = 'name',
      sort = 'asc',
      include_staff = false,
      include_workload = false,
    } = request.qs()

    const departmentQuery = Department.query().preload('vendor').preload('branch')

    // Apply filters
    if (vendor_id) {
      departmentQuery.where('vendor_id', vendor_id)
    }

    if (branch_id) {
      departmentQuery.where('branch_id', branch_id)
    }

    if (active !== undefined) {
      departmentQuery.where('active', active === 'true')
    }

    // Include staff if requested
    if (include_staff === 'true') {
      departmentQuery.preload('staff', (staffQuery) => {
        staffQuery.wherePivot('active', true)
      })
    }

    // Include workload data if requested
    if (include_workload === 'true') {
      departmentQuery.preload('orderItems', (itemsQuery) => {
        itemsQuery.whereIn('status', ['pending', 'preparing', 'ready'])
      })
    }

    const departments = await departmentQuery.orderBy(order, sort).paginate(page, per)

    return response.json(departments.serialize())
  }

  /**
   * @store
   * @summary Create a new department
   * @description Create a new department with configuration
   * @requestBody Department creation data
   * @responseBody 201 - Created department
   */
  public async store({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      vendor_id: schema.string({}, [rules.regex(/^[0-9a-z]{26}$/)]),
      branch_id: schema.string({}, [rules.regex(/^[0-9a-z]{26}$/)]),
      name: schema.string({}, [rules.maxLength(100)]),
      description: schema.string.optional({}, [rules.maxLength(500)]),
      active: schema.boolean.optional(),
      average_preparation_time: schema.number.optional([rules.range(1, 300)]),
      max_concurrent_orders: schema.number.optional([rules.range(1, 100)]),
      priority_level: schema.number.optional([rules.range(1, 5)]),
      working_hours: schema.object.optional().anyMembers(),
      break_times: schema.object.optional().anyMembers(),
      workflow_settings: schema.object.optional().anyMembers(),
      notification_settings: schema.object.optional().anyMembers(),
      meta: schema.object.optional().anyMembers(),
    })

    const payload = await request.validate({ schema: validationSchema })

    const department = new Department()
    department.id = ulid().toLowerCase()
    department.fill(payload)

    await department.save()
    await department.load('vendor')
    await department.load('branch')

    return response.status(201).json(department.serialize())
  }

  @bind()
  /**
   * @show
   * @summary Show a single department
   * @description Get department details with optional related data
   * @paramPath id required string - Department ID
   * @paramQuery include_staff - Include staff assignments
   * @paramQuery include_workload - Include current workload
   * @paramQuery include_performance - Include performance metrics
   * @responseBody 200 - Department details
   * @response 404 - Department not found
   */
  public async show({ request, response }: HttpContextContract, department: Department) {
    const { include_staff, include_workload, include_performance } = request.qs()

    await department.load('vendor')
    await department.load('branch')

    if (include_staff === 'true') {
      await department.load('staff', (staffQuery) => {
        staffQuery.wherePivot('active', true)
      })
    }

    if (include_workload === 'true') {
      await department.load('orderItems', (itemsQuery) => {
        itemsQuery
          .whereIn('status', ['pending', 'preparing', 'ready'])
          .preload('order')
          .preload('assignedStaff')
      })
    }

    if (include_performance === 'true') {
      await department.updatePerformanceMetrics()
    }

    return response.json(department.serialize())
  }

  @bind()
  /**
   * @update
   * @summary Update a department
   * @description Update department configuration and settings
   * @paramPath id required string - Department ID
   * @requestBody Department update data
   * @responseBody 200 - Updated department
   * @response 404 - Department not found
   */
  public async update({ request, response }: HttpContextContract, department: Department) {
    const validationSchema = schema.create({
      name: schema.string.optional({}, [rules.maxLength(100)]),
      description: schema.string.optional({}, [rules.maxLength(500)]),
      active: schema.boolean.optional(),
      average_preparation_time: schema.number.optional([rules.range(1, 300)]),
      max_concurrent_orders: schema.number.optional([rules.range(1, 100)]),
      priority_level: schema.number.optional([rules.range(1, 5)]),
      working_hours: schema.object.optional().anyMembers(),
      break_times: schema.object.optional().anyMembers(),
      workflow_settings: schema.object.optional().anyMembers(),
      notification_settings: schema.object.optional().anyMembers(),
      meta: schema.object.optional().anyMembers(),
    })

    const payload = await request.validate({ schema: validationSchema })

    await department.merge(payload).save()
    await department.load('vendor')
    await department.load('branch')

    return response.json(department.serialize())
  }

  @bind()
  /**
   * @destroy
   * @summary Delete a department
   * @description Delete a department (soft delete if has historical data)
   * @paramPath id required string - Department ID
   * @responseBody 204 - No content
   * @response 404 - Department not found
   */
  public async destroy({ response }: HttpContextContract, department: Department) {
    // Check if department has historical data
    await department.load('orderItems')

    if (department.orderItems && department.orderItems.length > 0) {
      // Soft delete by marking as inactive
      department.active = false
      await department.save()
    } else {
      // Hard delete if no historical data
      await department.delete()
    }

    return response.noContent()
  }

  @bind()
  /**
   * @summary Get department workload summary
   * @description Get current workload and capacity information
   * @paramPath id required string - Department ID
   * @responseBody 200 - Workload summary
   */
  public async workload({ response }: HttpContextContract, department: Department) {
    const workloadSummary = await department.getWorkloadSummary()

    return response.json({
      department_id: department.id,
      department_name: department.name,
      workload: workloadSummary,
      is_operational: department.isOperational,
      estimated_wait_time: department.estimatedWaitTime,
      can_accept_orders: department.canAcceptNewOrder(),
    })
  }

  @bind()
  /**
   * @summary Get department staff assignments
   * @description Get all staff assigned to this department
   * @paramPath id required string - Department ID
   * @paramQuery active_only - Show only active assignments
   * @responseBody 200 - Staff assignments
   */
  public async staff({ request, response }: HttpContextContract, department: Department) {
    const { active_only = 'true' } = request.qs()

    const staffQuery = department
      .related('staff')
      .query()
      .pivotColumns([
        'role',
        'active',
        'is_primary_department',
        'skill_level',
        'performance_rating',
      ])

    if (active_only === 'true') {
      staffQuery.wherePivot('active', true)
    }

    const staff = await staffQuery.exec()

    return response.json({
      department_id: department.id,
      department_name: department.name,
      staff: staff.map((member) => {
        const serialized = member.serialize()
        return {
          ...serialized,
          assignment: {
            role: serialized.meta?.pivot_role || member.$pivot?.role || null,
            active: serialized.meta?.pivot_active || member.$pivot?.active || false,
            is_primary_department:
              serialized.meta?.pivot_is_primary_department ||
              member.$pivot?.is_primary_department ||
              false,
            skill_level: serialized.meta?.pivot_skill_level || member.$pivot?.skill_level || 1,
            performance_rating:
              serialized.meta?.pivot_performance_rating ||
              member.$pivot?.performance_rating ||
              null,
          },
        }
      }),
    })
  }

  @bind()
  /**
   * @summary Assign staff to department
   * @description Assign a staff member to this department
   * @paramPath id required string - Department ID
   * @requestBody Staff assignment data
   * @responseBody 201 - Assignment created
   */
  public async assignStaff({ request, response }: HttpContextContract, department: Department) {
    const validationSchema = schema.create({
      user_id: schema.string({}, [rules.regex(/^[0-9a-z]{26}$/)]),
      role: schema.enum(['supervisor', 'lead', 'staff', 'trainee', 'substitute'] as const),
      skill_level: schema.number.optional([rules.range(1, 5)]),
      is_primary_department: schema.boolean.optional(),
    })

    const {
      user_id,
      role,
      skill_level = 1,
      is_primary_department = false,
    } = await request.validate({ schema: validationSchema })

    // Verify user exists
    const user = await User.findOrFail(user_id)

    // Check if already assigned
    const existingAssignment = await department
      .related('staff')
      .query()
      .where('user_id', user_id)
      .first()

    if (existingAssignment) {
      return response.badRequest({
        error: 'Staff member is already assigned to this department',
      })
    }

    await department.assignStaff(user_id, role, skill_level)

    // If this is primary department, update the pivot
    if (is_primary_department) {
      await department
        .related('staff')
        .pivotQuery()
        .where('user_id', user_id)
        .update({ is_primary_department: true })
    }

    return response.status(201).json({
      message: 'Staff member assigned successfully',
      assignment: {
        department_id: department.id,
        user_id,
        role,
        skill_level,
        is_primary_department,
      },
    })
  }

  @bind()
  /**
   * @summary Update staff assignment
   * @description Update a staff member's assignment in this department
   * @paramPath id required string - Department ID
   * @paramPath user_id required string - User ID
   * @requestBody Assignment update data
   * @responseBody 200 - Assignment updated
   */
  public async updateStaffAssignment(
    { request, response, params }: HttpContextContract,
    department: Department
  ) {
    const { user_id } = params

    const validationSchema = schema.create({
      role: schema.enum.optional(['supervisor', 'lead', 'staff', 'trainee', 'substitute'] as const),
      skill_level: schema.number.optional([rules.range(1, 5)]),
      active: schema.boolean.optional(),
      is_primary_department: schema.boolean.optional(),
      performance_rating: schema.number.optional([rules.range(0, 5)]),
    })

    const payload = await request.validate({ schema: validationSchema })

    // Verify assignment exists
    const assignment = await department.related('staff').query().where('user_id', user_id).first()

    if (!assignment) {
      return response.notFound({
        error: 'Staff assignment not found',
      })
    }

    await department.related('staff').pivotQuery().where('user_id', user_id).update(payload)

    return response.json({
      message: 'Staff assignment updated successfully',
      assignment: {
        department_id: department.id,
        user_id,
        ...payload,
      },
    })
  }

  @bind()
  /**
   * @summary Remove staff from department
   * @description Remove a staff member from this department
   * @paramPath id required string - Department ID
   * @paramPath user_id required string - User ID
   * @responseBody 204 - No content
   */
  public async removeStaff({ response, params }: HttpContextContract, department: Department) {
    const { user_id } = params

    await department.removeStaff(user_id)

    return response.noContent()
  }

  @bind()
  /**
   * @summary Get department performance metrics
   * @description Get detailed performance and efficiency metrics
   * @paramPath id required string - Department ID
   * @responseBody 200 - Performance metrics
   */
  public async performance({ response }: HttpContextContract, department: Department) {
    await department.updatePerformanceMetrics()

    return response.json({
      department_id: department.id,
      department_name: department.name,
      performance: {
        efficiency_rating: department.efficiencyRating,
        total_orders_completed: department.totalOrdersCompleted,
        average_completion_time: department.averageCompletionTime,
        performance_status: department.performanceStatus,
        average_items_per_hour: department.averageItemsPerHour,
      },
      current_status: {
        is_operational: department.isOperational,
        is_within_working_hours: department.isWithinWorkingHours,
        current_workload: department.currentWorkload,
        workload_percentage: department.workloadPercentage,
        estimated_wait_time: department.estimatedWaitTime,
      },
    })
  }

  @bind()
  /**
   * @summary Get department configuration
   * @description Get department workflow and notification settings
   * @paramPath id required string - Department ID
   * @responseBody 200 - Department configuration
   */
  public async configuration({ response }: HttpContextContract, department: Department) {
    return response.json({
      department_id: department.id,
      department_name: department.name,
      configuration: {
        working_hours: department.workingHours,
        break_times: department.breakTimes,
        workflow_settings: department.workflowSettings,
        notification_settings: department.notificationSettings,
        capacity_settings: {
          max_concurrent_orders: department.maxConcurrentOrders,
          average_preparation_time: department.averagePreparationTime,
          priority_level: department.priorityLevel,
        },
      },
    })
  }

  @bind()
  /**
   * @summary Update department configuration
   * @description Update department workflow and notification settings
   * @paramPath id required string - Department ID
   * @requestBody Configuration update data
   * @responseBody 200 - Updated configuration
   */
  public async updateConfiguration(
    { request, response }: HttpContextContract,
    department: Department
  ) {
    const validationSchema = schema.create({
      working_hours: schema.object.optional().anyMembers(),
      break_times: schema.object.optional().anyMembers(),
      workflow_settings: schema.object.optional().anyMembers(),
      notification_settings: schema.object.optional().anyMembers(),
      max_concurrent_orders: schema.number.optional([rules.range(1, 100)]),
      average_preparation_time: schema.number.optional([rules.range(1, 300)]),
      priority_level: schema.number.optional([rules.range(1, 5)]),
    })

    const payload = await request.validate({ schema: validationSchema })

    await department.merge(payload).save()

    return response.json({
      message: 'Department configuration updated successfully',
      configuration: {
        department_id: department.id,
        ...payload,
      },
    })
  }
}
