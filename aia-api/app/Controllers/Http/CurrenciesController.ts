import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Currency from '../../Models/Currency'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Currency management
 * @version 1.0.0
 * @description Currency management for the application
 */
export default class CurrenciesController {
  /**
   * @index
   * @summary List all currencies
   * @description List all currencies, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const currencyQuery = Currency.filter(filters)

    return await currencyQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a currency
   * @description Create a currency with their details (name and details)
   * @requestBody {name, symbol, code, rate, default, active}
   * @responseBody 200 - <Currency>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, symbol, code, rate, default: isDefault, active } = request.all()
    const currency = new Currency()

    currency.fill({ name, symbol, code, rate, isDefault, active })

    await currency.save()

    return response.json(currency)
  }

  @bind()
  /**
   * @show
   * @summary Show a single currency
   * @description Show a currency with their details (name and details)
   * @paramPath id required number - Currency ID
   * @responseBody 200 - <Currency>
   * @response 404 - Currency not found
   */
  public async show({ response }: HttpContextContract, currency: Currency) {
    return response.json(currency)
  }

  @bind()
  /**
   * @update
   * @summary Update a currency
   * @description Update a currency with their details (name and details)
   * @paramPath id required number - Currency ID
   * @requestBody <Currency>
   * @responseBody 200 - <Currency>
   * @response 404 - Currency not found
   */
  public async update({ request, response }: HttpContextContract, currency: Currency) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await currency.merge(input).save()

    return response.json(currency)
  }

  @bind()

  /**
   * @destroy
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, currency: Currency) {
    return await currency.delete()
  }
}
