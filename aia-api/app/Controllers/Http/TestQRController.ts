import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { ulid } from 'ulidx'
import Lot from 'App/Models/Lot'
import Section from 'App/Models/Section'
import QRCodeGenerationService from 'App/Services/QRCodeGenerationService'

// Import QRCode directly for testing
const QRCode = require('qrcode')

export default class TestQRController {
  /**
   * Create a test QR code for a food restaurant
   */
  public async createFoodTestQR({ response }: HttpContextContract) {
    try {
      // Test data - Using Karen Blixen restaurant which has food products
      const testData = {
        id: ulid().toLowerCase(),
        vendorId: '01jrwz21crwaggay9cs1xrj93m', // Karen Blixen Coffee Garden & Cottages
        branchId: '01jrwz21csyma4n4gaqydympk4', // Main branch
        sectionId: '01jv24ck05ekf4v5mpwb1pr5n1', // East Garden section
        lotId: '01jv24d2fbtqjvhjwt7w1vgwyt', // EGR 1 table
        tableNumber: 'EGR 1',
      }

      const qrCodeUrl = `https://app.appinapp.ke/table/${testData.id}`

      // Insert test QR code directly into database
      await Database.table('table_qr_codes').insert({
        id: testData.id,
        vendor_id: testData.vendorId,
        branch_id: testData.branchId,
        section_id: testData.sectionId,
        lot_id: testData.lotId,
        table_number: testData.tableNumber,
        qr_code_url: qrCodeUrl,
        qr_code_image: 'data:image/png;base64,test-food-image-data',
        is_active: true,
        generated_at: new Date(),
        scan_count: 0,
        created_at: new Date(),
        updated_at: new Date(),
      })

      return response.json({
        success: true,
        data: {
          qrCodeId: testData.id,
          qrCodeUrl,
          tableNumber: testData.tableNumber,
          vendorName: 'Karen Blixen Coffee Garden & Cottages',
          testEndpoints: {
            scan: `http://127.0.0.1:3080/v1/public/table-qr/${testData.id}`,
            validate: `http://127.0.0.1:3080/v1/public/table-qr/${testData.id}/validate`,
            preview: `http://127.0.0.1:3080/v1/public/table-qr/${testData.id}/preview`,
          },
        },
        message: 'Test food restaurant QR code created successfully',
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to create test food QR code',
        error: error.message,
      })
    }
  }

  /**
   * Create a test QR code with parameters (handles duplicates)
   */
  public async create({ request, response }: HttpContextContract) {
    try {
      const { vendorId, sectionId, tableName } = request.all()

      if (!vendorId || !sectionId || !tableName) {
        return response.badRequest({
          success: false,
          message: 'vendorId, sectionId, and tableName are required',
        })
      }

      // Get section details
      const section = await Section.query().where('id', sectionId).preload('branch').first()

      if (!section) {
        return response.notFound({
          success: false,
          message: 'Section not found',
        })
      }

      // Create or find existing lot
      let lot = await Lot.query().where('name', tableName).where('sectionId', sectionId).first()

      if (!lot) {
        // Create new lot
        lot = await Lot.create({
          name: tableName,
          details: `Test table created for QR testing`,
          sectionId: sectionId,
        })
      }

      // Use QR generation service (handles duplicates gracefully)
      const qrService = new QRCodeGenerationService()

      const tableData = {
        vendorId: vendorId,
        branchId: section.branchId,
        sectionId: section.id,
        lotId: lot.id,
        tableNumber: lot.name,
      }

      const qrCode = await qrService.generateTableQRCode(tableData, {
        width: 300,
        errorCorrectionLevel: 'M',
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
        isAutoGenerated: false,
      })

      return response.json({
        success: true,
        data: {
          qrCodeId: qrCode.id,
          qrCodeUrl: qrCode.qrCodeUrl,
          tableNumber: qrCode.tableNumber,
          lot: {
            id: lot.id,
            name: lot.name,
          },
          section: {
            id: section.id,
            name: section.name,
          },
          testEndpoints: {
            scan: `http://127.0.0.1:3080/v1/public/table-qr/${qrCode.id}`,
            validate: `http://127.0.0.1:3080/v1/public/table-qr/${qrCode.id}/validate`,
            preview: `http://127.0.0.1:3080/v1/public/table-qr/${qrCode.id}/preview`,
          },
        },
        message: 'Test QR code created successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to create test QR code',
        error: error.message,
      })
    }
  }

  /**
   * Create a test QR code for testing purposes (legacy method)
   */
  public async createTestQR({ response }: HttpContextContract) {
    try {
      // Test data
      const testData = {
        id: ulid().toLowerCase(),
        vendorId: '01jrsrd52yshagpjyhmfqvdr5c',
        branchId: '01jrsrd530w37nm0c156af0v48',
        sectionId: '01jvahxmm0rp82tnhfpzze202q',
        lotId: '01jvspekgey7dg5dyhsy0qs8nc',
        tableNumber: 'Test Table 3',
      }

      const qrCodeUrl = `https://app.appinapp.ke/table/${testData.id}`

      // Insert test QR code directly into database
      await Database.table('table_qr_codes').insert({
        id: testData.id,
        vendor_id: testData.vendorId,
        branch_id: testData.branchId,
        section_id: testData.sectionId,
        lot_id: testData.lotId,
        table_number: testData.tableNumber,
        qr_code_url: qrCodeUrl,
        qr_code_image: 'data:image/png;base64,test-image-data',
        is_active: true,
        generated_at: new Date(),
        scan_count: 0,
        created_at: new Date(),
        updated_at: new Date(),
      })

      return response.json({
        success: true,
        data: {
          qrCodeId: testData.id,
          qrCodeUrl,
          tableNumber: testData.tableNumber,
          testEndpoints: {
            scan: `http://127.0.0.1:3080/v1/public/table-qr/${testData.id}`,
            validate: `http://127.0.0.1:3080/v1/public/table-qr/${testData.id}/validate`,
            preview: `http://127.0.0.1:3080/v1/public/table-qr/${testData.id}/preview`,
          },
        },
        message: 'Test QR code created successfully',
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to create test QR code',
        error: error.message,
      })
    }
  }

  /**
   * Clean up test QR codes
   */
  public async cleanupTestQR({ response }: HttpContextContract) {
    try {
      const deletedCount = await Database.table('table_qr_codes')
        .where('table_number', 'Test Table 3')
        .delete()

      return response.json({
        success: true,
        data: {
          deletedCount,
        },
        message: 'Test QR codes cleaned up successfully',
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to cleanup test QR codes',
        error: error.message,
      })
    }
  }

  /**
   * Deactivate a test QR code
   */
  public async deactivateQR({ params, response }: HttpContextContract) {
    try {
      const { id } = params

      const updatedCount = await Database.table('table_qr_codes')
        .where('id', id)
        .update({ is_active: false, updated_at: new Date() })

      return response.json({
        success: true,
        data: {
          qrCodeId: id,
          updatedCount,
          isActive: false,
        },
        message: 'QR code deactivated successfully',
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to deactivate QR code',
        error: error.message,
      })
    }
  }

  /**
   * List all QR codes for testing
   */
  public async listQRCodes({ response }: HttpContextContract) {
    try {
      const qrCodes = await Database.from('table_qr_codes')
        .select('id', 'table_number', 'qr_code_url', 'is_active', 'scan_count', 'created_at')
        .orderBy('created_at', 'desc')
        .limit(10)

      return response.json({
        success: true,
        data: qrCodes,
        message: 'QR codes retrieved successfully',
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to retrieve QR codes',
        error: error.message,
      })
    }
  }
}
