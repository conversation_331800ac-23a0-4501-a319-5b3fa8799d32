import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from '../../Models/Order'
import { bind } from '@adonisjs/route-model-binding'
import CustomerNewOrder from 'App/Notifications/Customer/CustomerNewOrder'
import User from 'App/Models/User'
import StaffNewOrder from 'App/Notifications/Staff/StaffNewOrder'
import VendorNewOrder from 'App/Notifications/Vendor/VendorNewOrder'
import Lot from 'App/Models/Lot'
import Vendor from 'App/Models/Vendor'
import OrderPricingService from 'App/Services/OrderPricingService'
import { OrderValidationHelper } from 'App/Helpers/OrderValidationHelper'

/**
 * @name Order management
 * @version 1.0.0
 * @description Order management for the application
 */
export default class VendorOrdersController {
  /**
   * @index
   * @summary List all Orders
   * @description List all Orders, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      include_fulfillment = 'false',
      status_filter,
      department_filter,
      ...filters
    } = request.qs()

    const OrderQuery = Order.filter(filters)
      .where('vendorId', vendor.id)
      .preload('branch')
      .preload('customer')
      .preload('lot')
      .preload('items', (itemQuery) => {
        itemQuery.preload('product')
        itemQuery.preload('modifiers', (modifierQuery) => {
          if (include_fulfillment === 'true') {
            modifierQuery.preload('preparedByStaff')
          }
        })
        if (include_fulfillment === 'true') {
          itemQuery.preload('department')
          itemQuery.preload('assignedStaff')
        }
      })
      .preload('staff')
      .preload('section')
      .preload('invoices')

    // Apply status filtering if requested
    if (status_filter) {
      OrderQuery.whereHas('items', (itemQuery) => {
        itemQuery.where('status', status_filter)
      })
    }

    // Apply department filtering if requested
    if (department_filter) {
      OrderQuery.whereHas('items', (itemQuery) => {
        itemQuery.where('department_id', department_filter)
      })
    }

    const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

    // Serialize orders first to avoid serialization issues
    const serializedOrders = orders.serialize()

    // Handle temp orders (Pending status) - populate items from meta.temp_items
    for (const orderData of serializedOrders.data) {
      if (orderData.status === 'Pending' && orderData.meta?.temp_items) {
        // This is a temp order - populate items from meta.temp_items
        const tempItems = orderData.meta.temp_items
        const productIds = Object.keys(tempItems)

        if (productIds.length > 0) {
          // Import Product model dynamically to avoid circular dependency
          const { default: Product } = await import('../../Models/Product')
          const products = await Product.query()
            .whereIn('id', productIds)
            .preload('category')
            .preload('gallery')
            .preload('forms')
            .preload('branch')
            .preload('vendor')
            .exec()

          // Replace empty items array with populated items from temp_items
          orderData.items = products.map((product) => {
            const quantity = tempItems[product.id]?.quantity || 0
            const productPrice = product.price || 0
            const totalItemCost = productPrice * quantity

            return {
              id: null, // No order_items record yet
              orderId: orderData.id,
              productId: product.id,
              quantity: quantity,
              meta: null,
              createdAt: orderData.createdAt,
              updatedAt: orderData.updatedAt,
              price: productPrice, // ✅ FIX: Set actual product price
              status: 'pending',
              departmentId: null,
              assignedStaffId: null,
              estimatedPreparationTime: null,
              preparationStartedAt: null,
              preparationCompletedAt: null,
              servedAt: null,
              priorityLevel: 1,
              requiresSpecialAttention: false,
              specialInstructions: null,
              preparationNotes: null,
              statusHistory: null,
              qualityCheckStatus: 'not_required',
              qualityCheckedBy: null,
              customerModifications: null,
              cancellationReason: null,
              actualPreparationTime: null,
              preparationAttempts: 1,
              modifiers: [],
              product: product.toJSON(), // ✅ FIX: Use toJSON() for product serialization
              actualPreparationTimeMinutes: null,
              isOverdue: false,
              preparationProgress: 0,
              statusDisplayName: 'Pending',
              canBeStarted: true,
              canBeCompleted: false,
              canBeServed: false,
              requiresAttention: false,
              estimatedCompletionTime: null,
              allModifiersCompleted: true,
              totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost
            }
          })
        }
      }
    }

    // Add pricing information to the serialized data
    const ordersWithPricing = {
      ...serializedOrders,
      data: OrderPricingService.addPricingToOrders(serializedOrders.data),
    }

    // Add fulfillment information if requested
    if (include_fulfillment === 'true') {
      const serializedOrders = ordersWithPricing.data
      for (const orderData of serializedOrders) {
        // Calculate fulfillment progress for each order
        const totalItems = orderData.items?.length || 0
        const completedItems =
          orderData.items?.filter((item) => ['ready', 'served'].includes(item.status)).length || 0

        orderData.fulfillment = {
          progress: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,
          total_items: totalItems,
          completed_items: completedItems,
          pending_items: orderData.items?.filter((item) => item.status === 'pending').length || 0,
          preparing_items:
            orderData.items?.filter((item) => item.status === 'preparing').length || 0,
          ready_items: orderData.items?.filter((item) => item.status === 'ready').length || 0,
          has_overdue_items: orderData.items?.some((item) => item.is_overdue) || false,
          requires_attention: orderData.items?.some((item) => item.requires_attention) || false,
          department_breakdown: this.calculateDepartmentBreakdown(orderData.items),
        }
      }

      return ordersWithPricing
    }

    return ordersWithPricing
  }

  // Helper method to calculate department breakdown
  private calculateDepartmentBreakdown(items: any[]): Record<string, any> {
    const breakdown = {}

    items?.forEach((item) => {
      if (!item.department) return

      const deptName = item.department.name
      if (!breakdown[deptName]) {
        breakdown[deptName] = {
          total: 0,
          pending: 0,
          preparing: 0,
          ready: 0,
          served: 0,
        }
      }

      breakdown[deptName].total++
      breakdown[deptName][item.status]++
    })

    return breakdown
  }

  /**
   * @store
   * @summary Create a Order
   * @description Create a Order with their details (name and details)
   * @requestBody {"staffId": "", "vendorId": "", "vendorId": "", "sectionId": "", "action": "", "type": "", "delivery": "", "status": "", "meta": {}} - <Order>
   * @responseBody 200 - <Order>
   */
  @bind()
  public async store({ request, response, auth }: HttpContextContract, vendor: Vendor) {
    try {
      const {
        staffId,
        branchId,
        vendorId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
        userId = null,
        items = [],
      } = request.all()

      // Validate items before creating order
      const itemValidation = OrderValidationHelper.validateDirectOrderItems(items)
      if (!itemValidation.isValid) {
        return OrderValidationHelper.createItemValidationErrorResponse(response, itemValidation)
      }

      const order = await Order.create({
        userId: userId ? userId : auth.user?.id,
        staffId,
        vendorId: vendor.id,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
      })

      // Create order items using HasMany relationship with correct pricing
      await Promise.all(
        items.map(async (item: Record<string, number>) => {
          return Promise.all(
            Object.keys(item).map(async (productId) => {
              // Get product to set correct price
              const product = await Product.find(productId)
              const productPrice = product ? Number(product.price || 0) : 0

              return await order.related('items').create({
                productId: productId,
                quantity: item[productId],
                price: productPrice, // ✅ FIX: Set actual product price
              })
            })
          )
        })
      )

      await order.load('items')

      let amount = order.items?.reduce((acc, item) => acc + item.price * item.quantity, 0)

      if (order.meta && order.meta.charges) {
        amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
          (acc, charge) => acc + charge,
          0
        )
      }

      await order.related('invoices').create({
        amount,
        status: 'Pending',
      })

      await order.load('items', (itemQuery) => {
        itemQuery.preload('product')
      })
      await order.load('customer')
      await order.load('vendor')
      await order.load('invoices', (iq) => iq.preload('order', (oq) => oq.preload('payments')))

      // Auto-assign items to departments if enabled
      const autoAssignDepartments = meta?.auto_assign_departments || false
      if (autoAssignDepartments) {
        try {
          await order.assignItemsToDepartments()
          // Reload items with department assignments
          await order.load('items', (itemQuery) => {
            itemQuery.preload('department')
            itemQuery.preload('product')
          })
        } catch (error) {
          console.warn('Failed to auto-assign departments:', error.message)
        }
      }

      const customer = await User.findOrFail(userId)
      await customer.notify(new CustomerNewOrder(order))

      customer
        .related('brands')
        .sync({ [vendorId]: { active: true, vendor_id: vendorId, branch_id: branchId } })

      // Send notifications to product vendor (staff)
      await this.sendProductVendorNotifications(order, vendorId, lotId)

      // Note: Delivery provider notifications are handled separately in OrderDeliveryController
      // when delivery assignment occurs, maintaining separation between product and delivery operations

      return response.json(order)
    } catch (error) {
      console.error(error)

      return response.badRequest({ error: error.message })
    }
  }

  /**
   * @summary Get vendor fulfillment dashboard
   * @description Get comprehensive fulfillment tracking dashboard for vendor
   * @paramPath vendor_id required string - Vendor ID
   * @paramQuery period - Time period (today, week, month)
   * @responseBody 200 - Fulfillment dashboard data
   */
  @bind()
  public async fulfillmentDashboard({ request, response }: HttpContextContract, vendor: Vendor) {
    const { period = 'today' } = request.qs()

    try {
      // Get orders for the specified period
      let startDate
      switch (period) {
        case 'week':
          startDate = new Date()
          startDate.setDate(startDate.getDate() - 7)
          break
        case 'month':
          startDate = new Date()
          startDate.setMonth(startDate.getMonth() - 1)
          break
        default:
          startDate = new Date()
          startDate.setHours(0, 0, 0, 0)
      }

      const orders = await Order.query()
        .where('vendor_id', vendor.id)
        .where('created_at', '>=', startDate.toISOString())
        .preload('items', (itemQuery) => {
          itemQuery.preload('department')
          itemQuery.preload('assignedStaff')
          itemQuery.preload('modifiers')
        })
        .exec()

      // Calculate dashboard metrics
      const totalOrders = orders.length
      const totalItems = orders.reduce((sum, order) => sum + order.items.length, 0)

      const itemsByStatus = {
        pending: 0,
        preparing: 0,
        ready: 0,
        served: 0,
        cancelled: 0,
      }

      const departmentBreakdown = {}
      const overdueItems = []
      const highPriorityItems = []

      orders.forEach((order) => {
        order.items.forEach((item) => {
          // Count by status
          itemsByStatus[item.status] = (itemsByStatus[item.status] || 0) + 1

          // Department breakdown
          if (item.department) {
            const deptName = item.department.name
            if (!departmentBreakdown[deptName]) {
              departmentBreakdown[deptName] = {
                total: 0,
                pending: 0,
                preparing: 0,
                ready: 0,
                served: 0,
                cancelled: 0,
              }
            }
            departmentBreakdown[deptName].total++
            departmentBreakdown[deptName][item.status]++
          }

          // Check for overdue items
          if (item.isOverdue) {
            overdueItems.push({
              order_id: order.id,
              item_id: item.id,
              product_name: item.product?.name,
              department: item.department?.name,
              assigned_staff: item.assignedStaff?.name,
              overdue_minutes: item.preparationStartedAt
                ? Math.floor((Date.now() - item.preparationStartedAt.getTime()) / 60000) -
                  (item.estimatedPreparationTime || 0)
                : 0,
            })
          }

          // Check for high priority items
          if (item.priorityLevel === 1) {
            highPriorityItems.push({
              order_id: order.id,
              item_id: item.id,
              product_name: item.product?.name,
              status: item.status,
              department: item.department?.name,
            })
          }
        })
      })

      const completionRate =
        totalItems > 0
          ? Math.round(((itemsByStatus.served + itemsByStatus.ready) / totalItems) * 100)
          : 0

      return response.json({
        vendor_id: vendor.id,
        vendor_name: vendor.name,
        period,
        date_range: {
          start: startDate.toISOString(),
          end: new Date().toISOString(),
        },
        summary: {
          total_orders: totalOrders,
          total_items: totalItems,
          completion_rate: completionRate,
          overdue_items: overdueItems.length,
          high_priority_items: highPriorityItems.length,
        },
        items_by_status: itemsByStatus,
        department_breakdown: departmentBreakdown,
        alerts: {
          overdue_items: overdueItems.slice(0, 10), // Top 10 overdue items
          high_priority_items: highPriorityItems.slice(0, 10), // Top 10 high priority items
        },
      })
    } catch (error) {
      console.error('Error getting fulfillment dashboard:', error)
      return response.status(500).json({
        error: 'Failed to get fulfillment dashboard',
        details: error.message,
      })
    }
  }

  /**
   * @summary Get vendor department performance
   * @description Get performance metrics for all departments in vendor
   * @paramPath vendor_id required string - Vendor ID
   * @responseBody 200 - Department performance data
   */
  @bind()
  public async departmentPerformance({ response }: HttpContextContract, vendor: Vendor) {
    try {
      // This would need to be implemented with proper Department model relationships
      // For now, return a placeholder response
      return response.json({
        vendor_id: vendor.id,
        vendor_name: vendor.name,
        message: 'Department performance endpoint - requires Department model integration',
        departments: [],
      })
    } catch (error) {
      console.error('Error getting department performance:', error)
      return response.status(500).json({
        error: 'Failed to get department performance',
        details: error.message,
      })
    }
  }
  /**
   * Send notifications to product vendor staff
   * Separated from delivery provider notifications to maintain dual-verification separation
   */
  private async sendProductVendorNotifications(order: Order, vendorId: string, lotId?: string) {
    try {
      if (lotId) {
        const lot = await Lot.findOrFail(lotId)
        const staff = await lot.related('staff').query().orderBy('created_at', 'desc').firstOrFail()
        await staff.notify(new StaffNewOrder(order))
      } else {
        const staff = await User.query().whereHas('employers', (q) => {
          q.where('vendor_id', vendorId)
        })

        // Send notifications to all staff members
        await Promise.all(staff.map(async (user) => {
          await user.notify(new StaffNewOrder(order))
        }))
      }

      // Also send notification to vendor owner if they have a user account
      const vendor = await Vendor.query()
        .where('id', vendorId)
        .preload('user')
        .first()

      if (vendor?.user) {
        await vendor.user.notify(new VendorNewOrder(order))
      }

      console.log(`Product vendor notifications sent for order ${order.id}`)
    } catch (error) {
      console.error('Failed to send product vendor notifications:', error)
    }
  }
}
