import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Message from '../../Models/Message'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'
import CustomerBroadcast from 'App/Notifications/Customer/CustomerBroadcast'

/**
 * @name Message management
 * @version 1.0.0
 * @description Message management for the application
 */
export default class MessagesController {
  /**
   * @index
   * @summary List all messages
   * @description List all messages, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const messageQuery = Message.filter(filters).preload('author').preload('template')

    return await messageQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a message
   * @description Create a message with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Message>
   */
  public async store({ request, response }: HttpContextContract) {
    try {
      const {
        userId,
        vendorId,
        branchId,
        title,
        details,
        templateId,
        channels = ['database'],
        receipients = [],
      } = request.all()

      // Basic validation
      const errors: string[] = []

      // Required fields validation
      if (!userId) errors.push('userId is required')
      if (!vendorId) errors.push('vendorId is required')
      if (!branchId) errors.push('branchId is required')
      if (!title) errors.push('title is required')
      if (!details) errors.push('details is required')
      if (!templateId) errors.push('templateId is required')

      // Type validation
      if (title && typeof title !== 'string') errors.push('title must be a string')
      if (details && typeof details !== 'string') errors.push('details must be a string')
      if (templateId && isNaN(Number(templateId))) errors.push('templateId must be a number')
      if (channels && !Array.isArray(channels)) errors.push('channels must be an array')
      if (receipients && !Array.isArray(receipients)) errors.push('receipients must be an array')

      // Return validation errors if any
      if (errors.length > 0) {
        return response.status(400).json({
          error: 'Validation failed',
          details: errors,
        })
      }

      const message = await Message.create({ userId, vendorId, branchId, templateId })

      const users =
        receipients.length > 0
          ? await User.query().whereIn('id', receipients)
          : await User.query().whereHas('devices', (dq) => {
              dq.whereNotNull('token')
            })

      const actions = [
        {
          type: 'VIEW_NOTIFICATIONS',
          label: 'View notifications',
          context: { unread: true },
        },
      ]

      // Handle notifications with proper error handling
      const notificationErrors: { userId: string; error: string }[] = []
      await Promise.all(
        users.map(async (user) => {
          try {
            await user.notify(new CustomerBroadcast(title, details, channels, actions))
          } catch (error) {
            notificationErrors.push({
              userId: user.id,
              error: error.message,
            })
            console.error(`Failed to notify user ${user.id}:`, error)
          }
        })
      )

      return response.json({
        message,
        notificationErrors: notificationErrors.length > 0 ? notificationErrors : undefined,
      })
    } catch (error) {
      console.error('Message creation failed:', error)
      return response.status(500).json({
        error: 'Failed to create message',
        details: error.message,
      })
    }
  }

  @bind()
  /**
   * @show
   * @summary Show a single message
   * @description Show a message with their details (name and details)
   * @paramPath id required number - Message ID
   * @responseBody 200 - <Message>
   * @response 404 - Message not found
   */
  public async show({ response }: HttpContextContract, message: Message) {
    return response.json(message)
  }

  @bind()
  /**
   * @update
   * @summary Update a message
   * @description Update a message with their details (name and details)
   * @paramPath id required number - Message ID
   * @requestBody <Message>
   * @responseBody 200 - <Message>
   * @response 404 - Message not found
   */
  public async update({ request, response }: HttpContextContract, message: Message) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await message.merge(input).save()

    return response.json(message)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a lot
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, message: Message) {
    return await message.delete()
  }
}
