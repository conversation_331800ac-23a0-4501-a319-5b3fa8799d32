import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Account from '../../Models/Account'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Account management
 * @version 1.0.0
 * @description Account management for the application
 */
export default class AccountsController {
  /**
   * @index
   * @summary List all accounts
   * @description List all accounts, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()

    const accountQuery = Account.filter(filters)

    return await accountQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a account
   * @description Create a account with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Account>
   */
  public async store({ request, response }: HttpContextContract) {
    const { providerId, provider, active } = request.all()
    const account = new Account()

    account.fill({ providerId, provider, active })

    await account.save()

    return response.json(account)
  }

  @bind()
  /**
   * @show
   * @summary Show a single account
   * @description Show a account with their details (name and details)
   * @paramPath id required number - Account ID
   * @responseBody 200 - <Account>
   * @response 404 - Account not found
   */
  public async show({ response }: HttpContextContract, account: Account) {
    return response.json(account)
  }

  @bind()
  /**
   * @update
   * @summary Update a account
   * @description Update a account with their details (name and details)
   * @paramPath id required number - Account ID
   * @requestBody <Account>
   * @responseBody 200 - <Account>
   * @response 404 - Account not found
   */
  public async update({ request, response }: HttpContextContract, account: Account) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await account.merge(input).save()

    return response.json(account)
  }

  @bind()
  public async destroy(_: HttpContextContract, account: Account) {
    return await account.delete()
  }
}
