import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Invoice from 'App/Models/Invoice'
import { bind } from '@adonisjs/route-model-binding'
import Order from '../../Models/Order'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

export default class InvoicesController {
  /**
   * @index
   * @summary List all Invoices
   * @description List all Invoices, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request, auth }: HttpContextContract) {
    const { per = 15, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

    // SECURITY: Apply vendor-scoped role-based filtering
    const user = auth.user!

    // Preload user relationships for role-based filtering
    await user.load('roles')
    await user.load('vendors')
    await user.load('branches')

    // Get accessible order IDs based on user's role and scope
    const accessibleOrderQuery = Order.query()
    HierarchicalAccessControlService.applyVendorScopedFilter(accessibleOrderQuery, user)
    const accessibleOrders = await accessibleOrderQuery.select('id')
    const accessibleOrderIds = accessibleOrders.map((order) => order.id)

    const invoiceQuery = Invoice.filter(filters).whereIn('orderId', accessibleOrderIds)

    return await invoiceQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a Invoice
   * @description Create a Invoice with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Invoice>
   */
  public async store({ request, response }: HttpContextContract) {
    const { amount, orderId } = request.all()

    const invoice = await Invoice.create({
      amount,
      orderId,
    })

    return response.json(invoice)
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's invoices
   * Used by customer apps to ensure customers only see their own invoices
   */
  public async customerInvoices({ request, auth }: HttpContextContract) {
    const { per = 15, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const user = auth.user!

    // Get accessible order IDs based on customer-only filtering
    const accessibleOrderQuery = Order.query()
    HierarchicalAccessControlService.applyCustomerOnlyFilter(accessibleOrderQuery, user)
    const accessibleOrders = await accessibleOrderQuery.select('id')
    const accessibleOrderIds = accessibleOrders.map((order) => order.id)

    // Query invoices for accessible orders only
    const invoiceQuery = Invoice.filter(filters)
      .whereIn('orderId', accessibleOrderIds)
      .preload('order', (orderQuery) => {
        orderQuery.preload('customer')
        orderQuery.preload('vendor')
        orderQuery.preload('branch')
      })
      .preload('payments')

    const invoices = await invoiceQuery.orderBy(order, sort).paginate(page, per)
    return invoices
  }

  @bind()
  /**
   * @show
   * @summary Show a single Invoice
   * @description Show a Invoice with their details (name and details)
   * @paramPath id required number - Invoice ID
   * @responseBody 200 - <Invoice>
   * @response 404 - Invoice not found
   */
  public async show({ response, auth }: HttpContextContract, invoice: Invoice) {
    // SECURITY FIX: Verify invoice access based on hierarchical access control
    const user = auth.user!
    await invoice.load('order')

    // Check if user has access to this order based on their role and scope
    const accessibleOrderQuery = Order.query().where('id', invoice.order.id)
    await HierarchicalAccessControlService.applyRoleBasedFilter(accessibleOrderQuery, user)
    const accessibleOrder = await accessibleOrderQuery.first()

    if (!accessibleOrder) {
      return response.status(403).json({
        error: 'Forbidden',
        message: 'You do not have permission to view this invoice',
      })
    }

    return response.json(invoice)
  }

  @bind()
  /**
   * @update
   * @summary Update a Invoice
   * @description Update a Invoice with their details (name and details)
   * @paramPath id required number - Invoice ID
   * @requestBody <Invoice>
   * @responseBody 200 - <Invoice>
   * @response 404 - Invoice not found
   */
  public async update({ request, response }: HttpContextContract, invoice: Invoice) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await invoice.merge(input).save()

    return response.json(invoice)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Invoice
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, invoice: Invoice) {
    return await invoice.delete()
  }
}
