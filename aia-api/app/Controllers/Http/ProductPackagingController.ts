import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Product from 'App/Models/Product'
import PackagingOption from 'App/Models/PackagingOption'
import { schema, rules } from '@ioc:Adonis/Core/Validator'

export default class ProductPackagingController {
  /**
   * Get packaging options assigned to a product
   */
  public async index({ params, response }: HttpContextContract) {
    try {
      const product = await Product.findOrFail(params.productId)
      await product.load('packagingOptions', (query) => {
        query.where('active', true)
      })

      return response.ok({
        product: {
          id: product.id,
          name: product.name,
          vendorId: product.vendorId,
        },
        packagingOptions: product.packagingOptions.map((option) => ({
          id: option.id,
          name: option.name,
          description: option.description,
          price: option.price,
          vendorId: option.vendorId,
          active: option.active,
        })),
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to get product packaging options',
        error: error.message,
      })
    }
  }

  /**
   * Assign a packaging option to a product
   */
  public async store({ params, request, response, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      packagingOptionId: schema.string({}, [
        rules.exists({ table: 'packaging_options', column: 'id' }),
      ]),
    })

    try {
      const payload = await request.validate({ schema: validationSchema })
      const product = await Product.findOrFail(params.productId)
      const packagingOption = await PackagingOption.findOrFail(payload.packagingOptionId)

      // REMOVED ALL AUTHORIZATION RESTRICTIONS - Any vendor or admin can manage product packaging
      await product.load('vendor')
      await auth.user!.load('roles')
      
      const roleNames = auth.user!.roles.map((role) => role.name.toLowerCase())
      const isVendor = roleNames.includes('vendor')
      const isAdmin = roleNames.includes('admin') || roleNames.includes('platform admin') || roleNames.includes('super admin')
      
      if (!isVendor && !isAdmin) {
        return response.forbidden({
          message: 'Only users with vendor or admin role can manage product packaging'
        })
      }

      // Ensure packaging option belongs to the same vendor as the product
      if (packagingOption.vendorId !== product.vendorId) {
        return response.badRequest({
          message: 'Packaging option must belong to the same vendor as the product',
        })
      }

      // Check if assignment already exists
      const existingAssignment = await Database.from('product_packaging_options')
        .where('product_id', product.id)
        .where('packaging_option_id', packagingOption.id)
        .first()

      if (existingAssignment) {
        return response.badRequest({
          message: 'This packaging option is already assigned to this product',
        })
      }

      // Create the assignment
      await Database.table('product_packaging_options').insert({
        product_id: product.id,
        packaging_option_id: packagingOption.id,
        created_at: new Date(),
        updated_at: new Date(),
      })

      return response.created({
        message: 'Packaging option assigned successfully',
        assignment: {
          productId: product.id,
          productName: product.name,
          packagingOptionId: packagingOption.id,
          packagingOptionName: packagingOption.name,
          price: packagingOption.price,
        },
      })
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.badRequest({
        message: 'Failed to assign packaging option',
        error: error.message,
      })
    }
  }

  /**
   * Remove a packaging option assignment from a product
   */
  public async destroy({ params, response, auth }: HttpContextContract) {
    try {
      const product = await Product.findOrFail(params.productId)
      const packagingOption = await PackagingOption.findOrFail(params.packagingOptionId)

      // REMOVED ALL AUTHORIZATION RESTRICTIONS - Any vendor or admin can manage product packaging
      await product.load('vendor')
      await auth.user!.load('roles')
      
      const roleNames = auth.user!.roles.map((role) => role.name.toLowerCase())
      const isVendor = roleNames.includes('vendor')
      const isAdmin = roleNames.includes('admin') || roleNames.includes('platform admin') || roleNames.includes('super admin')
      
      if (!isVendor && !isAdmin) {
        return response.forbidden({
          message: 'Only users with vendor or admin role can manage product packaging'
        })
      }

      // Remove the assignment
      const deletedRows = await Database.from('product_packaging_options')
        .where('product_id', product.id)
        .where('packaging_option_id', packagingOption.id)
        .delete()

      if (deletedRows[0] === 0) {
        return response.notFound({
          message: 'Packaging option assignment not found',
        })
      }

      return response.ok({
        message: 'Packaging option assignment removed successfully',
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to remove packaging option assignment',
        error: error.message,
      })
    }
  }

  /**
   * Get all available packaging options for a product (vendor-specific)
   */
  public async available({ params, response }: HttpContextContract) {
    try {
      const product = await Product.findOrFail(params.productId)

      // Get all packaging options for this product's vendor
      const availableOptions = await PackagingOption.query()
        .where('vendorId', product.vendorId)
        .where('active', true)

      // Get currently assigned options
      const assignedOptions = await Database.from('product_packaging_options')
        .where('product_id', product.id)
        .select('packaging_option_id')

      const assignedOptionIds = assignedOptions.map((row) => row.packaging_option_id)

      // Mark which options are assigned
      const optionsWithStatus = availableOptions.map((option) => ({
        id: option.id,
        name: option.name,
        description: option.description,
        price: option.price,
        vendorId: option.vendorId,
        active: option.active,
        isAssigned: assignedOptionIds.includes(option.id),
      }))

      return response.ok({
        product: {
          id: product.id,
          name: product.name,
          vendorId: product.vendorId,
        },
        availableOptions: optionsWithStatus,
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to get available packaging options',
        error: error.message,
      })
    }
  }
}
