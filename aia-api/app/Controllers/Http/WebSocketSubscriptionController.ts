import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import WebSocketSubscriptionManager from '../../Services/WebSocketSubscriptionManager'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { DateTime } from 'luxon'

/**
 * Controller for managing WebSocket subscriptions
 */
export default class WebSocketSubscriptionController {

  /**
   * @summary Subscribe to WebSocket channel
   * @description Subscribe to a specific WebSocket channel for real-time updates
   * @requestBody Subscription request data
   * @responseBody 200 - Subscription result
   */
  public async subscribe({ request, response, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      socket_id: schema.string({ trim: true }),
      type: schema.enum(['order', 'department', 'vendor', 'branch', 'staff', 'customer', 'admin']),
      target_id: schema.string({ trim: true }),
      sub_type: schema.string.optional({ trim: true }),
      filters: schema.object.optional().anyMembers(),
      metadata: schema.object.optional().anyMembers()
    })

    const { socket_id, type, target_id, sub_type, filters, metadata } = 
      await request.validate({ schema: validationSchema })

    try {
      // Get user information
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Load user roles and relationships
      await user.load('roles')
      await user.load('employers')

      // Convert user to SocketUser format
      const socketUser = {
        id: user.id,
        name: user.name,
        email: user.email,
        roles: user.roles.map(role => role.name),
        vendorId: user.employers?.[0]?.vendorId,
        branchId: user.employers?.[0]?.branchId,
        departmentIds: [] // This would need to be populated from department assignments
      }

      // Create subscription
      const result = await WebSocketSubscriptionManager.subscribe(
        socket_id,
        socketUser,
        {
          type,
          target_id,
          sub_type,
          filters,
          metadata
        }
      )

      if (result.success) {
        return response.json({
          status: 'success',
          message: 'Successfully subscribed to channel',
          subscription: {
            id: result.subscription_id,
            channel: result.channel,
            type,
            target_id,
            sub_type,
            subscribed_at: DateTime.now().toISO()
          }
        })
      } else {
        return response.badRequest({
          error: 'Subscription failed',
          details: result.error,
          validation_result: result.validation_result
        })
      }

    } catch (error) {
      console.error('Error creating subscription:', error)
      return response.status(500).json({
        error: 'Failed to create subscription',
        details: error.message
      })
    }
  }

  /**
   * @summary Unsubscribe from WebSocket channel
   * @description Unsubscribe from a specific WebSocket channel
   * @requestBody Unsubscription request data
   * @responseBody 200 - Unsubscription result
   */
  public async unsubscribe({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      socket_id: schema.string({ trim: true }),
      subscription_id: schema.string({ trim: true })
    })

    const { socket_id, subscription_id } = await request.validate({ schema: validationSchema })

    try {
      const result = await WebSocketSubscriptionManager.unsubscribe(socket_id, subscription_id)

      if (result.success) {
        return response.json({
          status: 'success',
          message: 'Successfully unsubscribed from channel',
          subscription_id,
          unsubscribed_at: DateTime.now().toISO()
        })
      } else {
        return response.badRequest({
          error: 'Unsubscription failed',
          details: result.error
        })
      }

    } catch (error) {
      console.error('Error unsubscribing:', error)
      return response.status(500).json({
        error: 'Failed to unsubscribe',
        details: error.message
      })
    }
  }

  /**
   * @summary Get user subscriptions
   * @description Get all active subscriptions for the authenticated user
   * @responseBody 200 - User subscriptions
   */
  public async getUserSubscriptions({ response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      const subscriptions = WebSocketSubscriptionManager.getUserSubscriptions(user.id)

      return response.json({
        user_id: user.id,
        total_subscriptions: subscriptions.length,
        subscriptions: subscriptions.map(sub => ({
          id: sub.id,
          type: sub.type,
          target_id: sub.target_id,
          sub_type: sub.sub_type,
          subscribed_at: sub.subscribed_at.toISO(),
          last_activity: sub.last_activity.toISO(),
          filters: sub.filters,
          metadata: sub.metadata
        }))
      })

    } catch (error) {
      console.error('Error getting user subscriptions:', error)
      return response.status(500).json({
        error: 'Failed to get user subscriptions',
        details: error.message
      })
    }
  }

  /**
   * @summary Get channel subscriptions
   * @description Get all subscriptions for a specific channel (admin only)
   * @paramQuery channel - Channel name
   * @responseBody 200 - Channel subscriptions
   */
  public async getChannelSubscriptions({ request, response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user is admin
      await user.load('roles')
      const isAdmin = user.roles.some(role => role.name === 'admin')
      
      if (!isAdmin) {
        return response.forbidden({ error: 'Admin access required' })
      }

      const { channel } = request.qs()
      if (!channel) {
        return response.badRequest({ error: 'Channel parameter required' })
      }

      const subscriptions = WebSocketSubscriptionManager.getChannelSubscriptions(channel)

      return response.json({
        channel,
        total_subscriptions: subscriptions.length,
        subscriptions: subscriptions.map(sub => ({
          id: sub.id,
          user_id: sub.user_id,
          socket_id: sub.socket_id,
          subscribed_at: sub.subscribed_at.toISO(),
          last_activity: sub.last_activity.toISO(),
          filters: sub.filters,
          metadata: sub.metadata
        }))
      })

    } catch (error) {
      console.error('Error getting channel subscriptions:', error)
      return response.status(500).json({
        error: 'Failed to get channel subscriptions',
        details: error.message
      })
    }
  }

  /**
   * @summary Get subscription statistics
   * @description Get overall subscription statistics (admin only)
   * @responseBody 200 - Subscription statistics
   */
  public async getStatistics({ response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user is admin
      await user.load('roles')
      const isAdmin = user.roles.some(role => role.name === 'admin')
      
      if (!isAdmin) {
        return response.forbidden({ error: 'Admin access required' })
      }

      const statistics = WebSocketSubscriptionManager.getStatistics()

      return response.json({
        status: 'success',
        timestamp: DateTime.now().toISO(),
        statistics
      })

    } catch (error) {
      console.error('Error getting subscription statistics:', error)
      return response.status(500).json({
        error: 'Failed to get subscription statistics',
        details: error.message
      })
    }
  }

  /**
   * @summary Cleanup stale subscriptions
   * @description Remove inactive subscriptions (admin only)
   * @paramQuery max_inactive_minutes - Maximum inactive time in minutes (default: 30)
   * @responseBody 200 - Cleanup result
   */
  public async cleanupStaleSubscriptions({ request, response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user is admin
      await user.load('roles')
      const isAdmin = user.roles.some(role => role.name === 'admin')
      
      if (!isAdmin) {
        return response.forbidden({ error: 'Admin access required' })
      }

      const { max_inactive_minutes = 30 } = request.qs()
      const cleanedCount = WebSocketSubscriptionManager.cleanupStaleSubscriptions(
        parseInt(max_inactive_minutes)
      )

      return response.json({
        status: 'success',
        message: 'Stale subscriptions cleaned up',
        cleaned_subscriptions: cleanedCount,
        cleanup_threshold_minutes: max_inactive_minutes,
        timestamp: DateTime.now().toISO()
      })

    } catch (error) {
      console.error('Error cleaning up subscriptions:', error)
      return response.status(500).json({
        error: 'Failed to cleanup subscriptions',
        details: error.message
      })
    }
  }

  /**
   * @summary Validate subscription request
   * @description Validate if a subscription request would be allowed (testing endpoint)
   * @requestBody Subscription validation request
   * @responseBody 200 - Validation result
   */
  public async validateSubscription({ request, response, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      type: schema.enum(['order', 'department', 'vendor', 'branch', 'staff', 'customer', 'admin']),
      target_id: schema.string({ trim: true }),
      sub_type: schema.string.optional({ trim: true })
    })

    const { type, target_id, sub_type } = await request.validate({ schema: validationSchema })

    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Load user roles and relationships
      await user.load('roles')
      await user.load('employers')

      // Convert user to SocketUser format
      const socketUser = {
        id: user.id,
        name: user.name,
        email: user.email,
        roles: user.roles.map(role => role.name),
        vendorId: user.employers?.[0]?.vendorId,
        branchId: user.employers?.[0]?.branchId,
        departmentIds: []
      }

      // This would require exposing the validation method or creating a public version
      // For now, we'll simulate the validation
      return response.json({
        status: 'success',
        message: 'Subscription validation completed',
        request: { type, target_id, sub_type },
        user_context: {
          id: user.id,
          roles: socketUser.roles,
          vendor_id: socketUser.vendorId,
          branch_id: socketUser.branchId
        },
        validation_note: 'Use the subscribe endpoint to get actual validation results'
      })

    } catch (error) {
      console.error('Error validating subscription:', error)
      return response.status(500).json({
        error: 'Failed to validate subscription',
        details: error.message
      })
    }
  }
}
