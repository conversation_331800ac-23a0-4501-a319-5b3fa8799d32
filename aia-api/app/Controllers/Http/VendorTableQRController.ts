import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import Branch from 'App/Models/Branch'
import Section from 'App/Models/Section'
import TableQRCode from 'App/Models/TableQRCode'
import QRCodeGenerationService from 'App/Services/QRCodeGenerationService'
import QRCodeProcessingService from 'App/Services/QRCodeProcessingService'

export default class VendorTableQRController {
  private qrCodeGenerationService: QRCodeGenerationService
  private qrCodeProcessingService: QRCodeProcessingService

  constructor() {
    this.qrCodeGenerationService = new QRCodeGenerationService()
    this.qrCodeProcessingService = new QRCodeProcessingService()
  }

  /**
   * @index
   * @summary List all QR codes for vendor
   * @description Get all table QR codes for a vendor with filtering and pagination
   * @paramPath vendorId required string - Vendor ID
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery branch - Filter by branch ID
   * @paramQuery section - Filter by section ID
   * @paramQuery active - Filter by active status
   * @paramQuery search - Search term
   * @responseBody 200 - Paginated list of QR codes
   */
  @bind()
  public async index({ request, response }: HttpContextContract, vendor: Vendor) {
    const { 
      per = 25, 
      page = 1, 
      order = 'createdAt', 
      sort = 'desc',
      ...filters 
    } = request.qs()

    const qrCodeQuery = TableQRCode.query()
      .where('vendorId', vendor.id)
      .preload('branch')
      .preload('section')
      .preload('lot')
      .filter(filters)

    const qrCodes = await qrCodeQuery.orderBy(order, sort).paginate(page, per)

    const result = qrCodes.serialize({
      fields: [
        'id', 'tableNumber', 'qrCodeUrl', 'isActive', 'scanCount', 
        'lastScannedAt', 'generatedAt', 'createdAt', 'branch', 'section', 'lot'
      ]
    })

    return response.json(result)
  }

  /**
   * @show
   * @summary Get specific QR code details
   * @description Get detailed information about a specific table QR code
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - QR Code ID
   * @responseBody 200 - QR code details with analytics
   */
  @bind()
  public async show({ params, response }: HttpContextContract, vendor: Vendor) {
    try {
      const qrCode = await TableQRCode.query()
        .where('id', params.id)
        .where('vendorId', vendor.id)
        .preload('branch')
        .preload('section')
        .preload('lot')
        .firstOrFail()

      // Get usage statistics
      const stats = await this.qrCodeProcessingService.getQRCodeStats(params.id)

      const result = {
        ...qrCode.serialize({
          fields: [
            'id', 'tableNumber', 'qrCodeUrl', 'qrCodeImage', 'isActive', 
            'scanCount', 'lastScannedAt', 'generatedAt', 'createdAt', 
            'branch', 'section', 'lot'
          ]
        }),
        analytics: stats
      }

      return response.json(result)
    } catch (error) {
      return response.notFound({
        message: 'QR code not found',
        error: 'QRCODE_NOT_FOUND'
      })
    }
  }

  /**
   * @generateSection
   * @summary Generate QR codes for all tables in a section
   * @description Bulk generate QR codes for all tables in a specific section
   * @paramPath vendorId required string - Vendor ID
   * @paramPath sectionId required string - Section ID
   * @requestBody {"options": {"width": 300, "errorCorrectionLevel": "M"}}
   * @responseBody 200 - Generated QR codes
   */
  @bind()
  public async generateSection({ params, request, response }: HttpContextContract, vendor: Vendor) {
    try {
      const { sectionId } = params
      const { options = {} } = request.only(['options'])

      // Verify section belongs to vendor
      const section = await Section.query()
        .where('id', sectionId)
        .whereHas('branch', (branchQuery) => {
          branchQuery.where('vendorId', vendor.id)
        })
        .firstOrFail()

      const generatedQRCodes = await this.qrCodeGenerationService.generateSectionQRCodes(
        sectionId, 
        options
      )

      return response.json({
        success: true,
        data: {
          sectionId,
          sectionName: section.name,
          generatedCount: generatedQRCodes.length,
          qrCodes: generatedQRCodes.map(qr => ({
            id: qr.id,
            tableNumber: qr.tableNumber,
            qrCodeUrl: qr.qrCodeUrl,
            isActive: qr.isActive
          }))
        },
        message: `Generated ${generatedQRCodes.length} QR codes for section ${section.name}`
      })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to generate QR codes',
        error: error.message
      })
    }
  }

  /**
   * @generateBranch
   * @summary Generate QR codes for all tables in a branch
   * @description Bulk generate QR codes for all tables in all sections of a branch
   * @paramPath vendorId required string - Vendor ID
   * @paramPath branchId required string - Branch ID
   * @requestBody {"options": {"width": 300, "errorCorrectionLevel": "M"}}
   * @responseBody 200 - Generated QR codes
   */
  @bind()
  public async generateBranch({ params, request, response }: HttpContextContract, vendor: Vendor) {
    try {
      const { branchId } = params
      const { options = {} } = request.only(['options'])

      // Verify branch belongs to vendor
      const branch = await Branch.query()
        .where('id', branchId)
        .where('vendorId', vendor.id)
        .firstOrFail()

      const generatedQRCodes = await this.qrCodeGenerationService.generateBranchQRCodes(
        branchId, 
        options
      )

      return response.json({
        success: true,
        data: {
          branchId,
          branchName: branch.name,
          generatedCount: generatedQRCodes.length,
          qrCodes: generatedQRCodes.map(qr => ({
            id: qr.id,
            tableNumber: qr.tableNumber,
            qrCodeUrl: qr.qrCodeUrl,
            sectionName: qr.section?.name,
            isActive: qr.isActive
          }))
        },
        message: `Generated ${generatedQRCodes.length} QR codes for branch ${branch.name}`
      })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to generate QR codes',
        error: error.message
      })
    }
  }

  /**
   * @regenerate
   * @summary Regenerate a specific QR code
   * @description Regenerate QR code image and URL for a specific table
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - QR Code ID
   * @requestBody {"options": {"width": 300, "errorCorrectionLevel": "M"}}
   * @responseBody 200 - Regenerated QR code
   */
  @bind()
  public async regenerate({ params, request, response }: HttpContextContract, vendor: Vendor) {
    try {
      const { id } = params
      const { options = {} } = request.only(['options'])

      // Verify QR code belongs to vendor
      await TableQRCode.query()
        .where('id', id)
        .where('vendorId', vendor.id)
        .firstOrFail()

      const regeneratedQRCode = await this.qrCodeGenerationService.regenerateQRCode(id, options)

      return response.json({
        success: true,
        data: {
          id: regeneratedQRCode.id,
          tableNumber: regeneratedQRCode.tableNumber,
          qrCodeUrl: regeneratedQRCode.qrCodeUrl,
          qrCodeImage: regeneratedQRCode.qrCodeImage,
          generatedAt: regeneratedQRCode.generatedAt,
          isActive: regeneratedQRCode.isActive
        },
        message: 'QR code regenerated successfully'
      })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to regenerate QR code',
        error: error.message
      })
    }
  }

  /**
   * @activate
   * @summary Activate QR code
   * @description Activate a deactivated QR code
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - QR Code ID
   * @responseBody 200 - Success message
   */
  @bind()
  public async activate({ params, response }: HttpContextContract, vendor: Vendor) {
    try {
      const { id } = params

      // Verify QR code belongs to vendor
      const qrCode = await TableQRCode.query()
        .where('id', id)
        .where('vendorId', vendor.id)
        .firstOrFail()

      await this.qrCodeGenerationService.activateQRCode(id)

      return response.json({
        success: true,
        message: `QR code for Table ${qrCode.tableNumber} activated successfully`
      })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to activate QR code',
        error: error.message
      })
    }
  }

  /**
   * @deactivate
   * @summary Deactivate QR code
   * @description Deactivate a QR code to prevent scanning
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - QR Code ID
   * @responseBody 200 - Success message
   */
  @bind()
  public async deactivate({ params, response }: HttpContextContract, vendor: Vendor) {
    try {
      const { id } = params

      // Verify QR code belongs to vendor
      const qrCode = await TableQRCode.query()
        .where('id', id)
        .where('vendorId', vendor.id)
        .firstOrFail()

      await this.qrCodeGenerationService.deactivateQRCode(id)

      return response.json({
        success: true,
        message: `QR code for Table ${qrCode.tableNumber} deactivated successfully`
      })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to deactivate QR code',
        error: error.message
      })
    }
  }

  /**
   * @analytics
   * @summary Get QR code analytics for vendor
   * @description Get comprehensive analytics for all QR codes in the vendor
   * @paramPath vendorId required string - Vendor ID
   * @responseBody 200 - Analytics data
   */
  @bind()
  public async analytics({ response }: HttpContextContract, vendor: Vendor) {
    try {
      const analytics = await this.qrCodeGenerationService.getQRCodeAnalytics(vendor.id)

      return response.json({
        success: true,
        data: analytics,
        message: 'Analytics retrieved successfully'
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to get analytics',
        error: 'ANALYTICS_ERROR'
      })
    }
  }
}
