import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import ActionType from 'App/Models/ActionType'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @swagger
 * components:
 *   schemas:
 *     ActionType:
 *       type: object
 *       properties:
 *         id:
 *           type: number
 *         type:
 *           type: string
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         config_schema:
 *           type: object
 *         default_config:
 *           type: object
 *         is_active:
 *           type: boolean
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
export default class ActionTypesController {
  /**
   * @swagger
   * /api/v1/action-types:
   *   get:
   *     tags:
   *       - Action Types
   *     summary: List all action types
   *     description: Returns a list of all active action types
   *     responses:
   *       200:
   *         description: List of action types
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 types:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/ActionType'
   */
  public async index({ response }: HttpContextContract) {
    const types = await ActionType.query().where('is_active', true)
    return response.ok({ types })
  }

  /**
   * @swagger
   * /api/v1/action-types:
   *   post:
   *     tags:
   *       - Action Types
   *     summary: Create a new action type
   *     description: Creates a new action type with its configuration
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - type
   *               - name
   *               - config_schema
   *             properties:
   *               type:
   *                 type: string
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *               config_schema:
   *                 type: object
   *               default_config:
   *                 type: object
   *               is_active:
   *                 type: boolean
   *     responses:
   *       201:
   *         description: Action type created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 type:
   *                   $ref: '#/components/schemas/ActionType'
   */
  public async store({ request, response }: HttpContextContract) {
    const data = await request.validate({
      schema: schema.create({
        type: schema.string({ trim: true }, [
          rules.unique({ table: 'action_types', column: 'type' })
        ]),
        name: schema.string({ trim: true }),
        description: schema.string.optional({ trim: true }),
        config_schema: schema.object().members({}),
        default_config: schema.object().members({}),
        is_active: schema.boolean.optional()
      })
    })

    const type = await ActionType.create(data)
    return response.created({ type })
  }

  /**
   * @swagger
   * /api/v1/action-types/{id}:
   *   get:
   *     tags:
   *       - Action Types
   *     summary: Get a specific action type
   *     description: Returns details of a specific action type
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       200:
   *         description: Action type details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 type:
   *                   $ref: '#/components/schemas/ActionType'
   *       404:
   *         description: Action type not found
   */
  @bind()
  public async show({ response }: HttpContextContract, type: ActionType) {
    return response.ok({ type })
  }

  /**
   * @swagger
   * /api/v1/action-types/{id}:
   *   put:
   *     tags:
   *       - Action Types
   *     summary: Update an action type
   *     description: Updates an existing action type
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *               config_schema:
   *                 type: object
   *               default_config:
   *                 type: object
   *               is_active:
   *                 type: boolean
   *     responses:
   *       200:
   *         description: Action type updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 type:
   *                   $ref: '#/components/schemas/ActionType'
   *       404:
   *         description: Action type not found
   */
  @bind()
  public async update({ request, response }: HttpContextContract, type: ActionType) {
    const data = await request.validate({
      schema: schema.create({
        name: schema.string.optional({ trim: true }),
        description: schema.string.optional({ trim: true }),
        config_schema: schema.object().members({}),
        default_config: schema.object().members({}),
        is_active: schema.boolean.optional()
      })
    })

    await type.merge(data).save()
    return response.ok({ type })
  }

  /**
   * @swagger
   * /api/v1/action-types/{id}:
   *   delete:
   *     tags:
   *       - Action Types
   *     summary: Delete an action type
   *     description: Deletes an existing action type
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       204:
   *         description: Action type deleted successfully
   *       404:
   *         description: Action type not found
   */
  @bind()
  public async destroy({ response }: HttpContextContract, type: ActionType) {
    await type.delete()
    return response.noContent()
  }

  /**
   * @swagger
   * /api/v1/action-types/{id}/validate-config:
   *   post:
   *     tags:
   *       - Action Types
   *     summary: Validate a configuration
   *     description: Validates a configuration against an action type's schema
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - config
   *             properties:
   *               config:
   *                 type: object
   *     responses:
   *       200:
   *         description: Configuration is valid
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 valid:
   *                   type: boolean
   *                 config:
   *                   type: object
   *       400:
   *         description: Invalid configuration
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: string
   *                 details:
   *                   type: array
   *                   items:
   *                     type: string
   *       404:
   *         description: Action type not found
   */
  @bind()
  public async validateConfig({ request, response }: HttpContextContract, type: ActionType) {
    const { config } = request.only(['config'])

    const isValid = type.validateConfig(config)
    if (!isValid) {
      return response.badRequest({
        error: 'Invalid configuration',
        details: type.getValidationErrors(config)
      })
    }

    return response.ok({
      valid: true,
      config: type.mergeConfig(config)
    })
  }
} 