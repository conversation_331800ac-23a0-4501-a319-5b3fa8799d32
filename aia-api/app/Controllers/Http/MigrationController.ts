import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import TempOrderMigrationService from 'App/Services/TempOrderMigrationService'
import Order from 'App/Models/Order'

export default class MigrationController {
  /**
   * @summary Get temp order migration status
   * @description Get information about temp orders that need migration from meta.temp_items to OrderItems
   * @responseBody 200 - Migration status information
   */
  public async status({ response }: HttpContextContract) {
    try {
      const status = await TempOrderMigrationService.getMigrationStatus()
      
      return response.json({
        status: 'success',
        data: {
          migration_status: status,
          recommendations: this.getRecommendations(status),
        },
      })
    } catch (error) {
      console.error('Error getting migration status:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to get migration status',
        error: error.message,
      })
    }
  }

  /**
   * @summary Migrate all temp orders
   * @description Migrate all temp orders from meta.temp_items to OrderItems table
   * @responseBody 200 - Migration results
   */
  public async migrateAll({ response }: HttpContextContract) {
    try {
      console.log('🔄 Starting migration of all temp orders...')
      
      const summary = await TempOrderMigrationService.migrateAllOrders()
      
      const responseData = {
        status: 'success',
        message: 'Migration completed',
        data: {
          summary,
          recommendations: this.getMigrationRecommendations(summary),
        },
      }

      if (summary.failedMigrations > 0) {
        responseData.status = 'partial_success'
        responseData.message = 'Migration completed with some errors'
      }

      console.log('✅ Migration completed:', summary)
      return response.json(responseData)
      
    } catch (error) {
      console.error('Error during migration:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Migration failed',
        error: error.message,
      })
    }
  }

  /**
   * @summary Migrate specific order
   * @description Migrate a specific order from meta.temp_items to OrderItems table
   * @paramPath id required string - Order ID
   * @responseBody 200 - Migration result
   */
  public async migrateOrder({ params, response }: HttpContextContract) {
    try {
      const order = await Order.findOrFail(params.id)
      
      const result = await TempOrderMigrationService.migrateOrder(order)
      
      if (result.success) {
        return response.json({
          status: 'success',
          message: 'Order migrated successfully',
          data: result,
        })
      } else {
        return response.status(400).json({
          status: 'error',
          message: 'Order migration failed',
          data: result,
        })
      }
      
    } catch (error) {
      console.error('Error migrating order:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to migrate order',
        error: error.message,
      })
    }
  }

  /**
   * @summary Check if order needs migration
   * @description Check if a specific order needs migration from meta.temp_items to OrderItems
   * @paramPath id required string - Order ID
   * @responseBody 200 - Migration check result
   */
  public async checkOrder({ params, response }: HttpContextContract) {
    try {
      const order = await Order.findOrFail(params.id)
      
      const needsMigration = await TempOrderMigrationService.orderNeedsMigration(order)
      
      // Load items and meta for detailed response
      await order.load('items')
      
      return response.json({
        status: 'success',
        data: {
          order_id: order.id,
          needs_migration: needsMigration,
          has_temp_items: !!(order.meta?.temp_items && Object.keys(order.meta.temp_items).length > 0),
          has_order_items: !!(order.items && order.items.length > 0),
          temp_items_count: order.meta?.temp_items ? Object.keys(order.meta.temp_items).length : 0,
          order_items_count: order.items ? order.items.length : 0,
          order_status: order.status,
          created_at: order.createdAt,
        },
      })
      
    } catch (error) {
      console.error('Error checking order:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to check order',
        error: error.message,
      })
    }
  }

  private getRecommendations(status: any): string[] {
    const recommendations: string[] = []
    
    if (status.ordersNeedingMigration > 0) {
      recommendations.push(`${status.ordersNeedingMigration} orders need migration from temp_items to OrderItems`)
      recommendations.push('Run migration to ensure consistent data structure across all orders')
    }
    
    if (status.ordersNeedingMigration === 0) {
      recommendations.push('All orders are using the standardized OrderItems structure')
    }
    
    if (status.ordersWithTempItems > status.ordersNeedingMigration) {
      const alreadyMigrated = status.ordersWithTempItems - status.ordersNeedingMigration
      recommendations.push(`${alreadyMigrated} orders have been successfully migrated`)
    }
    
    return recommendations
  }

  private getMigrationRecommendations(summary: any): string[] {
    const recommendations: string[] = []
    
    if (summary.successfulMigrations > 0) {
      recommendations.push(`Successfully migrated ${summary.successfulMigrations} orders`)
      recommendations.push(`Created ${summary.totalItemsCreated} OrderItems from temp_items`)
    }
    
    if (summary.failedMigrations > 0) {
      recommendations.push(`${summary.failedMigrations} orders failed to migrate - check logs for details`)
      recommendations.push('Review failed orders and retry migration if needed')
    }
    
    if (summary.failedMigrations === 0 && summary.successfulMigrations > 0) {
      recommendations.push('All orders migrated successfully - system is now fully standardized')
    }
    
    return recommendations
  }
}
