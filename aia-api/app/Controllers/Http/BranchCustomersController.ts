import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Branch from 'App/Models/Branch'
import User from 'App/Models/User'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class BranchCustomersController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const branchQuery = branch.related('customers').query().filter(filters)

    return await branchQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a branch
   * @description Create a branch with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, branch: Branch) {
    const {
      vendorId,
      userId: existingUserId,
      firstName,
      lastName,
      phone,
      email,
      gender,
      idpass,
      dob,
      details,
      password = null,
      role = 'customer',
      active = true,
    } = request.all()

    let userId = existingUserId

    if (existingUserId) {
      branch.related('customers').sync({ [userId]: { active, vendor_id: vendorId } })
    } else {
      const user = await User.create({
        firstName,
        lastName,
        phone,
        email,
        gender,
        dob,
        idpass,
        details,
        password: password || phone,
      })

      await user.assignRole(role)

      branch.related('customers').attach({ [user.id]: { active, vendor_id: vendorId } })
    }

    return response.json(branch)
  }
}
