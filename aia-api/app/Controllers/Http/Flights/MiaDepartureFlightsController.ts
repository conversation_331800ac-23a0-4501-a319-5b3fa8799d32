import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Mail from '@ioc:Adonis/Addons/Mail'
import MiaDepartureFlight from 'App/Models/Flights/MiaDepartureFlight'

/**
 * @summary MIA Departure Flights
 * @description Controller for managing departure flights from Mombasa International Airport (MIA).
 */
export default class MiaDepartureFlightsController {
  /**
   * @index
   *
   * @summary Fetch MIA Departure Flights
   * @description Retrieves a list of departure flights from MIA.
   *
   * @paramQuery page - The page number for pagination.
   * @paramQuery per - The number of records per page.
   *
   * @responseBody 200 - A paginated list of <MiaDepartureFlight> objects.
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const { page, per, ...filters } = request.qs()

      const flightQuery = MiaDepartureFlight.filter(filters)

      const flights = await flightQuery.paginate(page, per)
      return response.json(flights)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Arrival Flights Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }

  /**
   * @show
   *
   * @summary Show Departure Flight
   * @description Retrieves details of a specific departure flight from MIA.
   *
   * @pathParam id - The ID of the flight to retrieve.
   *
   * @responseBody 200 - Details of the requested <MiaDepartureFlight> object.
   * @response 404 - If the flight with the given ID is not found.
   */
  public async show({ response, params }: HttpContextContract) {
    try {
      const flight = await MiaDepartureFlight.findOrFail(params.id)

      return response.json(flight)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Arrival Flights Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }
}
