import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Mail from '@ioc:Adonis/Addons/Mail'
import KiaDepartureFlight from 'App/Models/Flights/KiaDepartureFlight'

/**
 * @summary Kia Departure Flights
 * @description Controller for managing departure flights from Kisumu International Airport (KIA).
 */
export default class KiaDepartureFlightsController {
  /**
   * @index
   *
   * @summary List Departure Flights
   * @description Retrieves a paginated list of departure flights from KIA.
   *
   * @paramQuery page - The page number for pagination.
   * @paramQuery per - The number of records per page.
   *
   * @responseBody 200 - A paginated list of <KiaDepartureFlight> objects.
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const { page, per, ...filters } = request.qs()

      const flightQuery = KiaDepartureFlight.filter(filters)
      const flights = await flightQuery.paginate(page, per)
      return response.json(flights)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Departure Flights Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }

  /**
   * @show
   *
   * @summary Show Departure Flight
   * @description Retrieves details of a specific departure flight from KIA.
   *
   * @pathParam id - The ID of the flight to retrieve.
   *
   * @responseBody 200 - Details of the requested <KiaDepartureFlight> object.
   * @response 404 - If the flight with the given ID is not found.
   */
  public async show({ request, response }: HttpContextContract) {
    try {
      const flight = await KiaDepartureFlight.findOrFail(request.param('id'))
      return response.json(flight)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Departure Flight Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }
}
