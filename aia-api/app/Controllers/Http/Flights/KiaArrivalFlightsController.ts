import Mail from '@ioc:Adonis/Addons/Mail'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import KiaArrivalFlight from 'App/Models/Flights/KiaArrivalFlight'

/**
 * @kiaArrivalFlights
 *
 * @summary KIA Arrival Flights
 * @description Controller for managing arrival flights at Kisumu International Airport (KIA).
 */
export default class KiaArrivalFlightsController {
  /**
   * @index
   *
   * @summary Fetch KIA Arrival Flights
   * @description Retrieves a list of arrival flights at KIA, optionally filtered by airline and origin airport.
   *
   * @paramQuery page - The page number for pagination.
   * @paramQuery per - The number of records per page.
   * @paramQuery airline - The airline code to filter by.
   *
   * @responseHeader 200 - @use(paginated)
   * @responseBody 200 - <KiaArrivalFlight[]> - A paginated list of KiaArrivalFlight objects.
   * @responseBody 500 - If an error occurs while fetching the flights.
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const { page = 1, per = 10, ...filters } = request.qs()
      const flightQuery = KiaArrivalFlight.filter(filters)

      const flights = await flightQuery.paginate(page, per)

      return response.json(flights)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Arrival Flights Error')
          .htmlView('emails/errors/server', { error })
      })

      return response.status(500).json({
        message: 'An error occurred while fetching KIA arrival flights',
      })
    }
  }

  /**
   * @show
   *
   * @summary Show a Flight
   * @description Retrieves details of a specific arrival flight at KIA.
   *
   * @pathParam id - The ID of the flight to retrieve.
   *
   * @responseBody 200 - Details of the requested <KiaArrivalFlight> object.
   * @response 404 - If the flight with the given ID is not found.
   */
  public async show({ response, params }: HttpContextContract) {
    try {
      const flight = await KiaArrivalFlight.findOrFail(params.id)

      return response.json(flight)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Arrival Flight Error')
          .htmlView('emails/errors/server', { error })
      })

      return response.status(404).json({
        message: 'The requested flight was not found',
      })
    }
  }
}
