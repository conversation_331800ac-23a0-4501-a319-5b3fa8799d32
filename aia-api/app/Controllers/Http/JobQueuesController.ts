import Redis from '@ioc:Adonis/Addons/Redis'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Queue } from '@ioc:Rlanz/Queue'

export default class JobQueuesController {
  public async handle({ request, response }: HttpContextContract) {
    const { channel, vendor, path = 'products' } = request.all()

    let lastSaved = (await Redis.get('lastSaved')) || 1

    switch (path) {
      case 'products':
        Queue.dispatch('App/Jobs/SyncProduct', {
          channel,
          vendor,
        })
        break

      case 'staff':
        Queue.dispatch('App/Jobs/SyncStaff', {
          channel,
          vendor,
        })
        break

      case 'orders':
        Queue.dispatch('App/Jobs/SyncOrders', {
          channel,
          vendor,
        })
        break
      default:
        throw new Error('Path not found')
    }

    return response.json({
      message: `Dispatched ${channel} sync for ${vendor} ${path} ${lastSaved}.`,
    })
  }
}
