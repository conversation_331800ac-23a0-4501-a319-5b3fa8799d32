import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Lot from '../../Models/Lot'
import { bind } from '@adonisjs/route-model-binding'

export default class LotsController {
  /**
   * @index
   * @summary Show all lots
   * @version 1.0.0
   * @description Lot management for the application
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 50, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const lotQuery = Lot.filter(filters)

    return await lotQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a lot
   * @description Create a lot with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   */
  public async store({ request }: HttpContextContract) {
    const { name, details, sectionId } = request.all()
    const lot = new Lot()

    lot.fill({ name, details, sectionId })

    const image = request.file('image')

    if (image) {
      lot.image = Attachment.fromFile(image)
    }

    return await lot.save()
  }

  @bind()
  /**
   * @show
   * @summary Show a single Lot
   * @description Show a Lot with their details (name and details)
   * @paramPath id required number - Lot ID
   * @responseBody 200 - <Lot>
   * @response 404 - Lot not found
   */
  public async show({ response }: HttpContextContract, lot: Lot) {
    return response.json(lot)
  }

  @bind()
  /**
   * @update
   * @summary Update a Lot
   * @description Update a Lot with their details (name and details)
   * @paramPath id required number - Lot ID
   * @requestBody <Lot>
   * @responseBody 200 - <Lot>
   * @response 404 - Lot not found
   */
  public async update({ request, response }: HttpContextContract, lot: Lot) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await lot.merge(input).save()

    return response.json(lot)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a lot
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, lot: Lot) {
    return await lot.delete()
  }
}
