import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Device from '../../Models/Device'
import { bind } from '@adonisjs/route-model-binding'

export default class DevicesController {
  /**
   * @index
   * @summary Show all devices
   * @version 1.0.0
   * @description Device management for the application
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  public async index({ request, auth }: HttpContextContract) {
    const { per = 20, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const deviceQuery = Device.filter(filters).where('userId', auth.user?.id!)

    return await deviceQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a device
   * @description Create a device with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Device>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    const { id, name, details, token, meta, status = 'Active' } = request.all()
    try {
      // Generate a default name if none is provided
      const deviceName = name || `${meta?.platform || 'Unknown'} Device (${meta?.model || 'Unknown Model'})`

      const device = await Device.updateOrCreate(
        { id },
        {
          name: deviceName,
          details,
          token,
          meta,
          status,
          userId: auth.user?.id,
        }
      )

      return response.json(device)
    } catch (error) {
      console.error(error)
      return response.status(500).json({
        error: 'Failed to register device',
        details: error.message,
      })
    }
  }

  @bind()

  /**
   * @show
   * @summary Show a single device
   * @description Show a device with their details (name and details)
   * @paramPath id required number - Device Id
   * @responseBody 200 - <Device>
   * @response 404 - Device not found
   */
  public async show({ response }: HttpContextContract, device: Device) {
    return response.json(device)
  }

  @bind()
  /**
   * @update
   * @summary Update a device
   * @description Update a device with their details (name and details)
   * @paramPath id required number - Device ID
   * @requestBody <Device>
   * @responseBody 200 - <Device>
   * @response 404 - Device not found
   */
  public async update({ request, response }: HttpContextContract, device: Device) {
    const input = request.all()
    await device.merge(input).save()

    return response.json(device)
  }

  @bind()

  /**
   * @destroy
   * @summary Delete a device
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, device: Device) {
    return await device.delete()
  }
}
