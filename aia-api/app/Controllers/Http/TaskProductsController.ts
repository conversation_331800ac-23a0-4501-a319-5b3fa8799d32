import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Task from 'App/Models/Task'
import Product from 'App/Models/Product'

/**
 * @name Product management
 * @version 1.0.0
 * @description Product management for the application
 *
 */
export default class TaskProductsController {
  /**
   * @index
   * @summary List all products
   * @description List all products, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, task: Task) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const services = (await task.related('services').query()).map((service) => service.id)

    const productQuery = Product.query()
      .whereIn('serviceId', services)
      .filter(filters)
      .preload('vendor')

    return await productQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a product
   * @description Create a product with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Product>
   */
  @bind()
  public async store({ request, response, auth }: HttpContextContract, _task: Task) {
    const {
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type = 'Digital',
      condition = 'New',
      status = 'Draft',
      availability = 'In Stock',
      shipping = 'Free',
      unit = 'other',
      mode = 'Single',
      payment = 'Prepaid',
      visibility = 'Public',
      productCategoryId,
      vendorId,
      meta,
      extra,
    } = request.all()

    const product = await Product.create({
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type,
      condition,
      status,
      availability,
      shipping,
      unit,
      mode,
      visibility,
      payment,
      productCategoryId,
      userId: auth.user?.id,
      vendorId,
      meta,
      extra,
    })

    const image = request.file('image')

    if (image) {
      await product.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(product)
  }
}
