import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Service from '../../Models/Service'
import { bind } from '@adonisjs/route-model-binding'

export default class ServicesController {
  /**
   * @index
   * @summary Show all services
   * @version 1.0.0
   * @description Service management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 20, page = 1, order = 'name', sort = 'asc', ...filters } = request.qs()
    const serviceQuery = Service.filter(filters)

    return await serviceQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a service
   * @description Create a service with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   */
  public async store({ request }: HttpContextContract) {
    const { name, details, taskId } = request.all()
    const service = new Service()

    service.fill({ name, details, taskId })

    // const image = request.file('image')

    // if (image) {
    //   service.image = Attachment.fromFile(image)
    // }

    return await service.save()
  }

  @bind()
  /**
   * @show
   * @summary Show a single service
   * @description Show a service with their details (name and details)
   * @paramPath id required number - Service ID
   * @responseBody 200 - <Service>
   * @response 404 - Service not found
   */
  public async show({ response }: HttpContextContract, service: Service) {
    return response.json(service)
  }

  @bind()

  /**
   * @update
   * @summary Update a Service
   * @description Update a Service with their details (name and details)
   * @paramPath id required number - Service ID
   * @requestBody <Service>
   * @responseBody 200 - <Service>
   * @response 404 - Service not found
   */
  public async update({ request, response }: HttpContextContract, service: Service) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await service.merge(input).save()

    return response.json(service)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Service
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, service: Service) {
    return await service.delete()
  }
}
