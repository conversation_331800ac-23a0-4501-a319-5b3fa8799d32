import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Vendor from '../../Models/Vendor'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Vendor management
 * @version 1.0.0
 * @description Vendor management for the application
 */
export default class VendorCustomersController {
  /**
   * @index
   * @summary List all vendores
   * @description List all vendores, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const vendorQuery = vendor.related('customers').query().filter(filters)

    return await vendorQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a vendor
   * @description Create a vendor with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Vendor>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { branchId, userId, active = true } = request.all()
    vendor.related('customers').attach({ [userId]: { active, branch_id: branchId } })

    await vendor.save()

    return response.json(vendor)
  }
}
