import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import User from '../../Models/User'
import { bind } from '@adonisjs/route-model-binding'
// import UpdateUserValidator from '../../Validators/UpdateUserValidator'
import CreateUserValidator from '../../Validators/CreateUserValidator'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

export default class UsersController {
  /**
   * @index
   * @summary List all Users
   * @description List all Users with pagination and filtering options
   * @paramQuery per number - Number of items per page
   * @paramQuery page number - Page number
   * @paramQuery order string - Order by column
   * @paramQuery sort string - Sort order (asc or desc)
   * @paramQuery s string - Search term to filter users by name, email, or phone
   * @paramQuery status string - Filter by user status (Active, Inactive, Suspended)
   * @paramQuery email string - Filter by exact email
   * @paramQuery phone string - Filter by exact phone
   * @paramQuery gender string - Filter by gender
   * @responseBody 200 - <User[]>
   */
  public async index({ request, response, auth }: HttpContextContract) {
    const { page = 1, per = 10, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const user = auth.user!

    // SECURITY: Apply vendor-scoped role-based filtering
    await user.load('roles')
    await user.load('vendors')
    await user.load('branches')

    const userQuery = User.filter(filters).preload('roles')

    // Apply vendor-scoped filtering for user access
    const roleNames = user.roles?.map((role) => role.name.toLowerCase()) || []

    if (
      roleNames.includes('super admin') ||
      roleNames.includes('platform admin') ||
      roleNames.includes('admin')
    ) {
      // Super admins and admins can see all users
    } else if (roleNames.includes('vendor admin')) {
      // Vendor admins see users associated with their vendors
      const vendorIds = user.vendors?.map((vendor) => vendor.id) || []
      if (vendorIds.length > 0) {
        userQuery.whereHas('vendors', (vendorQuery) => {
          vendorQuery.whereIn('vendors.id', vendorIds)
        })
      } else {
        userQuery.where('id', user.id) // Fallback to personal
      }
    } else if (roleNames.includes('branch manager')) {
      // Branch managers see users associated with their branches
      const branchIds = user.branches?.map((branch) => branch.id) || []
      if (branchIds.length > 0) {
        userQuery.whereHas('branches', (branchQuery) => {
          branchQuery.whereIn('branches.id', branchIds)
        })
      } else {
        userQuery.where('id', user.id) // Fallback to personal
      }
    } else {
      // Other roles see only themselves
      userQuery.where('id', user.id)
    }

    const users = await userQuery.orderBy(order, sort).paginate(page, per)

    return response.json(users)
  }

  /**
   * Customer-only endpoint - Always returns only the authenticated user's data
   * Used by customer apps to ensure customers only see their own profile
   */
  public async customerProfile({ auth, response }: HttpContextContract) {
    const user = auth.user!
    await user.load('roles')

    return response.json(user)
  }

  /**
   * @store
   * @summary Create a user
   * @description Create a user with their details (phone and/or email address) and password
   * @requestBody {"firstName": "", "lastName": "", "gender": "Other", "dob": "YYYY-MM-DD", "email": "", "phone": "254700000000", "details": "", "location": {}}
   */
  public async store({ request, response }: HttpContextContract) {
    const {
      title = null,
      firstName,
      lastName,
      email,
      phone: postedPhone,
      idpass,
      dob,
      role = 'customer',
      location = null,
    } = await request.validate(CreateUserValidator)

    const phone = postedPhone ? postedPhone.replace(/^\+/, '') : ''

    let user = await User.query().where('phone', phone).orWhere('email', email).first()

    if (!user) {
      user = await User.create({
        title,
        firstName,
        lastName,
        email,
        phone,
        idpass,
        dob,
        location,
        password: phone.toString(),
      })
    }

    const avatar = request.file('avatar')

    if (avatar) {
      user.avatar = Attachment.fromFile(avatar)
    }

    await user.save()
    await user.syncRoles(role)

    return response.json(user)
  }

  /**
   * @show
   * @summary Show a single User
   * @description Show a User with their details (name and details)
   * @paramPath id required number - User ID
   * @responseBody 200 - <User>
   * @response 404 - User not found
   */
  @bind()
  public async show({ response }: HttpContextContract, user: User) {
    await user.load('roles')
    await user.load('permissions')
    await user.load('devices', (dq) => dq.where('status', 'Active'))

    return response.json(user)
  }

  /**
   * @update
   *
   * @summary Update a User
   * @description Update a User with their details (name and details)
   * @paramPath id required number - User ID
   *
   * @requestBody <User>
   * @responseBody 200 - <User>
   * @response 404 - User not found
   */
  @bind()
  public async update({ request, response, auth }: HttpContextContract, user: User) {
    // Authorization: Users can update their own profile, or admins/managers can update staff profiles
    const currentUser = auth.user!
    await currentUser.load('roles')
    await currentUser.load('branches')
    await currentUser.load('vendors')
    
    const roleNames = currentUser.roles.map((role) => role.name.toLowerCase())
    const isAdmin = roleNames.includes('admin') || roleNames.includes('platform admin') || roleNames.includes('super admin')
    const isVendorAdmin = roleNames.includes('vendor admin')
    const isVendor = roleNames.includes('vendor')
    const isBranchManager = roleNames.includes('branch manager')
    
    // Allow if user is updating their own profile
    if (currentUser.id === user.id) {
      // User can update their own profile
    } else if (isAdmin) {
      // Admins can update any user
    } else if (isVendorAdmin || isVendor) {
      // Vendor admins and vendors can update users associated with their vendors
      const vendorIds = currentUser.vendors?.map((vendor) => vendor.id) || []
      const userVendorIds = await user.related('vendors').query().pluck('vendors.id')
      const hasAccess = userVendorIds.some(id => vendorIds.includes(id))
      
      if (!hasAccess) {
        return response.forbidden({
          message: 'You can only update users associated with your vendors'
        })
      }
    } else if (isBranchManager) {
      // Branch managers can update users associated with their branches
      const branchIds = currentUser.branches?.map((branch) => branch.id) || []
      const userBranchIds = await user.related('branches').query().pluck('branches.id')
      const hasAccess = userBranchIds.some(id => branchIds.includes(id))
      
      if (!hasAccess) {
        return response.forbidden({
          message: 'You can only update users associated with your branches'
        })
      }
    } else {
      return response.forbidden({
        message: 'You are not authorized to update this user profile'
      })
    }
    const { 
      firstName, 
      lastName, 
      email, 
      phone: postedPhone, 
      gender, 
      details, 
      idpass,
      title,
      dob,
      location
    } = request.all()
    
    const phone = postedPhone ? postedPhone.replace(/^\+/, '') : ''
    const avatar = request.file('avatar')

    if (avatar) {
      user.avatar = Attachment.fromFile(avatar)
    }

    // Handle empty date fields properly
    const processedDob = dob && dob.trim() !== '' ? dob : null

    await user.merge({ 
      firstName, 
      lastName, 
      email, 
      phone, 
      gender, 
      details, 
      idpass,
      title,
      dob: processedDob,
      location
    }).save()

    return response.json(user)
  }

  /**
   * @updateRoles
   * @summary Update user roles
   * @description Update the roles assigned to a user
   * @paramPath id required string - User ID
   * @requestBody {"roles": ["string"]}
   * @responseBody 200 - Success response
   */
  @bind()
  public async updateRoles({ request, response, auth }: HttpContextContract, user: User) {
    // Authorization: Only admins can update user roles
    await auth.user!.load('roles')
    const roleNames = auth.user!.roles.map((role) => role.name.toLowerCase())
    const isAdmin = roleNames.includes('admin') || roleNames.includes('platform admin') || roleNames.includes('super admin')

    if (!isAdmin) {
      return response.forbidden({
        message: 'You are not authorized to update user roles'
      })
    }

    const { roles } = request.all()

    if (!Array.isArray(roles)) {
      return response.badRequest({ message: 'Roles must be provided as an array' })
    }

    try {
      // Sync the user's roles with the provided roles
      await user.syncRoles(roles)

      return response.json({
        message: 'User roles updated successfully',
        user: user
      })
    } catch (error) {
      console.error('Error updating user roles:', error)
      return response.internalServerError({
        message: 'Failed to update user roles'
      })
    }
  }

  /**
   * @destroy
   * @summary delete a user
   * @responseBody 204 - No content
   */
  @bind()
  public async destroy({ request, response }: HttpContextContract, user: User) {
    if (request.input('force')) {
      await user.forceDelete()
    } else {
      await user.delete()
    }

    return response.noContent()
  }
}
