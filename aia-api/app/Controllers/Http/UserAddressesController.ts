import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'
import Address from '../../Models/Address'

/**
 * @name Address management
 * @version 1.0.0
 * @description Address management for the application
 */
export default class UserAddressController {
  /**
   * @index
   * @summary List all address
   * @description List all address, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const queryParams = request.qs()

    // Handle both parameter formats: per/per_page, order/sort swapped
    const per = queryParams.per || queryParams.per_page || 10
    const page = queryParams.page || 1

    // Handle swapped order/sort parameters
    let orderColumn = queryParams.order || queryParams.sort || 'createdAt'
    let sortDirection = queryParams.sort || queryParams.order || 'asc'

    // If order contains direction words, swap them
    if (orderColumn === 'desc' || orderColumn === 'asc') {
      const temp = orderColumn
      orderColumn = sortDirection
      sortDirection = temp
    }

    // Convert snake_case to camelCase for database columns
    if (orderColumn === 'created_at') orderColumn = 'createdAt'
    if (orderColumn === 'updated_at') orderColumn = 'updatedAt'

    // Ensure valid sort direction
    if (sortDirection !== 'asc' && sortDirection !== 'desc') {
      sortDirection = 'asc'
    }

    const { ...filters } = queryParams
    delete filters.per
    delete filters.per_page
    delete filters.page
    delete filters.order
    delete filters.sort

    const addressQuery = user.related('addresses').query().filter(filters)

    const addresses = await addressQuery.orderBy(orderColumn, sortDirection).paginate(page, per)

    // Custom serialization for clean API response
    const serializedResponse = addresses.serialize()
    serializedResponse.data = addresses.all().map((address) => address.serialize())

    return serializedResponse
  }

  /**
   * @store
   * @summary Create a address
   * @description Create a address with their details (name and details)
   * @requestBody {"name": "", "details": "", "location": {"name": "", "address": "", "regions": {"country": ""}, "coordinates": {"lat": 0, "lng": 0}, "place_id": ""}, "phone": ""}
   * @responseBody 200 - <Address>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, user: User) {
    const requestData = request.all()
    const { name, details, phone, isDefault } = requestData

    // Handle both location object format and flat format
    let location = null

    if (requestData.location) {
      // If location object is provided directly
      location = requestData.location
    } else if (requestData.address && requestData.coordinates) {
      // If flat format is provided, construct location object
      location = {
        name: requestData.locationName || name,
        address: requestData.address,
        regions: requestData.regions || {},
        coordinates: requestData.coordinates,
        place_id: requestData.place_id || '',
      }
    }

    const address = await user.related('addresses').create({
      name,
      details,
      userId: user.id,
      location,
      phone,
      primary: isDefault || false,
    })

    // Return using custom serialization
    return response.json(address.serialize())
  }

  /**
   * @show
   * @summary Show a single address
   * @description Show a user's address with their details
   * @paramPath id required string - Address ID
   * @responseBody 200 - <Address>
   * @response 404 - Address not found
   */
  @bind()
  public async show({ response }: HttpContextContract, user: User, address: Address) {
    // Ensure the address belongs to the user
    if (address.userId !== user.id) {
      return response.status(404).json({ message: 'Address not found' })
    }

    return response.json(address)
  }

  /**
   * @update
   * @summary Update a user's address
   * @description Update a user's address with their details
   * @paramPath id required string - Address ID
   * @requestBody {"name": "", "details": "", "location": {"name": "", "address": "", "regions": {"country": ""}, "coordinates": {"lat": 0, "lng": 0}, "place_id": ""}, "phone": ""}
   * @responseBody 200 - <Address>
   * @response 404 - Address not found
   */
  @bind()
  public async update({ request, response }: HttpContextContract, user: User, address: Address) {
    // Ensure the address belongs to the user
    if (address.userId !== user.id) {
      return response.status(404).json({ message: 'Address not found' })
    }

    const requestData = request.all()
    const { name, details, phone, isDefault } = requestData

    // Handle both location object format and flat format
    let location = address.location // Keep existing location if not provided

    if (requestData.location) {
      // If location object is provided directly
      location = requestData.location
    } else if (requestData.address && requestData.coordinates) {
      // If flat format is provided, construct location object
      location = {
        name: requestData.locationName || name,
        address: requestData.address,
        regions: requestData.regions || {},
        coordinates: requestData.coordinates,
        place_id: requestData.place_id || '',
      }
    }

    await address
      .merge({
        name,
        details,
        location,
        phone,
        primary: isDefault !== undefined ? isDefault : address.primary,
      })
      .save()

    // Return using custom serialization
    return response.json(address.serialize())
  }

  /**
   * @destroy
   * @summary Delete a user's address
   * @description Delete a user's address
   * @paramPath id required string - Address ID
   * @responseBody 204 - No content
   * @response 404 - Address not found
   */
  @bind()
  public async destroy({ response }: HttpContextContract, user: User, address: Address) {
    // Ensure the address belongs to the user
    if (address.userId !== user.id) {
      return response.status(404).json({ message: 'Address not found' })
    }

    await address.delete()

    return response.status(204).json({})
  }
}
