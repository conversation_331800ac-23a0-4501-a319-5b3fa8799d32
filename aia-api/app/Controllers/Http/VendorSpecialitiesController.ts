import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import Speciality from 'App/Models/Speciality'

/**
 * @name Speciality management
 * @version 1.0.0
 * @description Speciality management for the application
 */
export default class VendorSpecialitiesController {
  /**
   * @index
   * @summary List all specialities
   * @description List all specialities, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const specialityQuery = vendor.related('specialities').query().filter(filters)

    return await specialityQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a speciality
   * @description Create a speciality with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Speciality>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { specialization = [] } = request.all()
    const speciality = await vendor.related('specialities').attach(specialization)

    return response.json(speciality)
  }

  /**
   * @destroy
   * @summary Delete a speciality
   * @responseBody 204 - No content
   */
  @bind()
  public async destroy({ response }: HttpContextContract, vendor: Vendor, speciality: Speciality) {
    await vendor.related('specialities').detach([speciality.id])

    return response.noContent()
  }
}
