import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Database from '@ioc:Adonis/Lucid/Database'
import Vendor from 'App/Models/Vendor'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class VendorBranchesController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const {
      per = 10,
      page = 1,
      near = '0 0',
      radius = 15000,
      order = 'createdAt',
      sort = 'asc',
      ...filters
    } = request.qs()
    const branchQuery = vendor
      .related('branches')
      .query()
      .filter(filters)
      .preload('vendor')
      .preload('sections')
      .preload('settings')

    if (near !== '0 0') {
      const st = Database.st()
      branchQuery.where(
        st.distanceSphere('geom', st.geomFromText(`POINT(${near})`, 4326)),
        '<=',
        radius
      )
    }

    return await branchQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a branch
   * @description Create a branch with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { vendorId, name, details, location, phone: postedPhone, code = null } = request.all()
    // Safely handle missing phone number
    const phone = postedPhone ? postedPhone.replace(/^\+/, '') : null

    // Generate a code from the name if not provided
    const branchCode = code || name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .substring(0, 2)

    const branch = await vendor
      .related('branches')
      .create({ vendorId, name, code: branchCode, details, location, phone })

    const image = request.file('image')
    if (image) {
      branch.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(branch)
  }
}
