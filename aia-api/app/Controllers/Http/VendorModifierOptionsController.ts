import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ModifierOption from 'App/Models/ModifierOption'
import { ModifierType } from 'App/Enums/ModifierType'
import Vendor from 'App/Models/Vendor'

/**
 * @name Vendor Modifier Options management
 * @version 1.0.0
 * @description API endpoints for vendors to manage their modifier options
 */
export default class VendorModifierOptionsController {
  /**
   * @index
   * @summary List vendor's modifier options
   * @description Retrieve a list of all modifier options for a specific vendor
   *
   * @paramParam vendor_id - Vendor ID
   * @paramQuery type - Filter by modifier type (preparation, condiment, extra)
   * @responseBody 200 - <ModifierOption[]>
   */
  public async index({ params, request, response }: HttpContextContract) {
    const { vendorId } = params
    const { type } = request.qs()

    // Verify vendor exists
    const vendor = await Vendor.findOrFail(vendorId)

    const query = ModifierOption.query().where('vendor_id', vendor.id)

    if (type && Object.values(ModifierType).includes(type)) {
      query.where('type', type)
    }

    const modifierOptions = await query.exec()
    return response.ok(modifierOptions)
  }

  /**
   * @store
   * @summary Create a new modifier option for vendor
   * @description Create a new modifier option for the vendor's products
   * @requestBody {"name": "", "type": "", "description": "", "default_price_adjustment": 0, "max_quantity": 0, "active": true}
   * @responseBody 201 - <ModifierOption>
   */
  public async store({ params, request, response }: HttpContextContract) {
    const { vendorId } = params
    
    if (!vendorId) {
      return response.badRequest({ error: 'Vendor ID is required' })
    }

    // Verify vendor exists
    const vendor = await Vendor.findOrFail(vendorId)

    try {
      const data = request.only([
        'name',
        'type',
        'description',
        'default_price_adjustment',
        'max_quantity',
        'active'
      ])

      // Validate modifier type
      if (!Object.values(ModifierType).includes(data.type)) {
        return response.badRequest({ error: 'Invalid modifier type' })
      }

      // Create modifier option with vendor ID
      const modifierOption = await ModifierOption.create({
        ...data,
        vendorId: vendor.id,
        maxQuantity: data.max_quantity || null
      })

      return response.created(modifierOption)
    } catch (error) {
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * @show
   * @summary Get vendor's modifier option
   * @description Retrieve a specific modifier option for the vendor
   * @paramParam vendor_id - Vendor ID
   * @paramParam id - Modifier Option ID
   * @responseBody 200 - <ModifierOption>
   */
  public async show({ params, response }: HttpContextContract) {
    const { vendorId, id } = params
    
    if (!vendorId || !id) {
      return response.badRequest({ error: 'Vendor ID and Modifier Option ID are required' })
    }

    const modifierOption = await ModifierOption.query()
      .where('id', id)
      .where('vendor_id', vendorId)
      .firstOrFail()

    return response.ok(modifierOption)
  }

  /**
   * @update
   * @summary Update vendor's modifier option
   * @description Update a specific modifier option for the vendor
   * @paramParam vendor_id - Vendor ID
   * @paramParam id - Modifier Option ID
   * @requestBody {"name": "", "type": "", "description": "", "default_price_adjustment": 0, "max_quantity": 0, "active": true}
   * @responseBody 200 - <ModifierOption>
   */
  public async update({ params, request, response }: HttpContextContract) {
    const { vendorId, id } = params
    
    if (!vendorId || !id) {
      return response.badRequest({ error: 'Vendor ID and Modifier Option ID are required' })
    }

    const modifierOption = await ModifierOption.query()
      .where('id', id)
      .where('vendor_id', vendorId)
      .firstOrFail()

    try {
      const data = request.only([
        'name',
        'type',
        'description',
        'default_price_adjustment',
        'max_quantity',
        'active'
      ])

      // Validate modifier type if provided
      if (data.type && !Object.values(ModifierType).includes(data.type)) {
        return response.badRequest({ error: 'Invalid modifier type' })
      }

      modifierOption.merge(data)
      await modifierOption.save()

      return response.ok(modifierOption)
    } catch (error) {
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * @destroy
   * @summary Delete vendor's modifier option
   * @description Delete a specific modifier option for the vendor
   * @paramParam vendor_id - Vendor ID
   * @paramParam id - Modifier Option ID
   * @responseBody 204 - No content
   */
  public async destroy({ params, response }: HttpContextContract) {
    const { vendorId, id } = params
    
    if (!vendorId || !id) {
      return response.badRequest({ error: 'Vendor ID and Modifier Option ID are required' })
    }

    const modifierOption = await ModifierOption.query()
      .where('id', id)
      .where('vendor_id', vendorId)
      .firstOrFail()

    await modifierOption.delete()
    return response.noContent()
  }
} 