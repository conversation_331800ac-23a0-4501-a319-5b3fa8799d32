import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import { DateTime } from 'luxon'
import VendorDeliverySettingsValidator from 'App/Validators/VendorDeliverySettingsValidator'

/**
 * @summary Vendor Delivery Settings Management
 * @group Vendor Delivery Settings
 * @version 1.0.0
 * @description Manage vendor delivery preferences, availability, pricing, and verification settings
 */
export default class VendorDeliverySettingsController {
  /**
   * @show
   * @summary Get delivery settings
   * @description Retrieve vendor's delivery preferences, availability settings, and pricing structure
   * @tag Vendor Delivery Settings
   * @paramPath id - Vendor ID - @example(vendor-uuid-123)
   * @responseBody 200 - {"deliveryPreferences": {"maxDeliveryDistance": 10, "deliveryFee": 5.99}, "availabilitySettings": {}, "pricingStructure": {"baseFee": 3.99}, "verificationStatus": "verified"}
   * @responseBody 404 - Vendor not found
   * @responseBody 401 - Unauthorized
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor) {
    return response.json({
      deliveryPreferences: vendor.deliveryPreferences,
      availabilitySettings: vendor.availabilitySettings,
      pricingStructure: vendor.pricingStructure,
      verificationStatus: vendor.verificationStatus,
    })
  }

  /**
   * @updatePreferences
   * @summary Update delivery preferences
   * @description Update vendor's delivery preferences including distance limits, fees, and service options
   * @tag Vendor Delivery Settings
   * @paramPath id - Vendor ID - @example(vendor-uuid-123)
   * @requestBody {
   *   "preferences": {
   *     "maxDeliveryDistance": 15,
   *     "deliveryFee": 6.99,
   *     "freeDeliveryThreshold": 30,
   *     "estimatedDeliveryTime": 45,
   *     "acceptsScheduledDeliveries": true,
   *     "deliveryInstructions": "Ring doorbell twice"
   *   }
   * }
   *
   * @responseBody 200 - {"maxDeliveryDistance": 15, "deliveryFee": 6.99, "freeDeliveryThreshold": 30, "estimatedDeliveryTime": 45, "acceptsScheduledDeliveries": true, "deliveryInstructions": "Ring doorbell twice"}
   * @responseBody 404 - Vendor not found
   * @responseBody 401 - Unauthorized
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async updatePreferences({ request, response }: HttpContextContract, vendor: Vendor) {
    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.preferences) {
      vendor.deliveryPreferences = data.preferences
      await vendor.save()
    }
    return response.json(vendor.deliveryPreferences)
  }

  /**
   * @updateAvailability
   * @summary Update availability settings
   * @description Update vendor's delivery availability including working hours, service areas, and capacity limits
   * @tag Vendor Delivery Settings
   * @paramPath id - Vendor ID - @example(vendor-uuid-123)
   * @requestBody {
   *   "settings": {
   *     "workingHours": {
   *       "monday": {"start": "08:00", "end": "20:00", "active": true},
   *       "tuesday": {"start": "08:00", "end": "20:00", "active": true},
   *       "sunday": {"start": "10:00", "end": "16:00", "active": false}
   *     },
   *     "maxConcurrentOrders": 10,
   *     "serviceAreas": ["downtown", "midtown"],
   *     "holidaySchedule": {"2024-12-25": {"active": false}}
   *   }
   * }
   *
   * @responseBody 200 - {"workingHours": {}, "maxConcurrentOrders": 10, "serviceAreas": ["downtown", "midtown"], "holidaySchedule": {}}
   * @responseBody 404 - Vendor not found
   * @responseBody 401 - Unauthorized
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async updateAvailability({ request, response }: HttpContextContract, vendor: Vendor) {
    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.settings) {
      vendor.availabilitySettings = data.settings
      await vendor.save()
    }
    return response.json(vendor.availabilitySettings)
  }

  /**
   * @updatePricing
   * @summary Update pricing structure
   * @description Update vendor's delivery pricing structure including base fees, distance rates, and surge pricing
   * @tag Vendor Delivery Settings
   * @paramPath id - Vendor ID - @example(vendor-uuid-123)
   * @requestBody {
   *   "pricing": {
   *     "baseFee": 4.99,
   *     "perKmRate": 0.75,
   *     "minimumOrderValue": 15,
   *     "surgePricingMultiplier": 1.5,
   *     "peakHourRates": {
   *       "lunch": {"start": "11:00", "end": "14:00", "multiplier": 1.2},
   *       "dinner": {"start": "17:00", "end": "21:00", "multiplier": 1.3}
   *     }
   *   }
   * }
   *
   * @responseBody 200 - {"baseFee": 4.99, "perKmRate": 0.75, "minimumOrderValue": 15, "surgePricingMultiplier": 1.5, "peakHourRates": {}}
   * @responseBody 404 - Vendor not found
   * @responseBody 401 - Unauthorized
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async updatePricing({ request, response }: HttpContextContract, vendor: Vendor) {
    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.pricing) {
      vendor.pricingStructure = data.pricing
      await vendor.save()
    }
    return response.json(vendor.pricingStructure)
  }

  /**
   * @updateVerification
   * @summary Update verification status (admin only)
   * @description Update vendor's delivery verification status with admin notes and timestamps
   * @tag Admin - Vendor Management
   * @paramPath id - Vendor ID - @example(vendor-uuid-123)
   * @requestBody {
   *   "status": "verified",
   *   "notes": "All delivery requirements met and documentation verified"
   * }
   *
   * @responseBody 200 - {"verificationStatus": "verified", "verificationNotes": "All delivery requirements met and documentation verified", "verifiedAt": "2024-01-15T10:30:00Z"}
   * @responseBody 403 - {"message": "Only admins can update verification status"}
   * @responseBody 404 - Vendor not found
   * @responseBody 401 - Unauthorized
   * @responseBody 422 - Validation failed
   */
  @bind()
  public async updateVerification({ request, response, auth }: HttpContextContract, vendor: Vendor) {
    // Ensure user is admin
    if (!auth.user?.hasRole('admin')) {
      return response.forbidden({ message: 'Only admins can update verification status' })
    }

    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.status) {
      vendor.verificationStatus = data.status as 'pending' | 'verified' | 'rejected'
      vendor.verificationNotes = data.notes || null
      vendor.verifiedAt = data.status === 'verified' ? DateTime.now() : null
      await vendor.save()
    }

    return response.json({
      verificationStatus: vendor.verificationStatus,
      verificationNotes: vendor.verificationNotes,
      verifiedAt: vendor.verifiedAt,
    })
  }
} 