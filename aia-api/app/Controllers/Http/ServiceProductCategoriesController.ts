import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'
import ProductType from 'App/Models/ProductType'

export default class ServiceProductCategoriesController {
  @bind()

  /**
   * @name ServiceProductCategory management
   * @index
   * @summary List all ServiceProductCategories
   * @description List all ServiceProductCategories, paginated
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const categoryQuery = service.related('productCategories').query().filter(filters)

    return await categoryQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a ServiceProductCategory
   * @description Create a ServiceProductCategory with their details (name and details)
   * @requestBody {"name": "", "details": "", "productTypeId": ""}
   * @responseBody 200 - <ServiceProductCategory>
   */
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { name, details, productTypeId } = request.all()
    
    // First verify that the product type belongs to this service
    const productType = await ProductType.query()
      .where('id', productTypeId)
      .where('serviceId', service.id)
      .firstOrFail()

    const category = await productType.related('categories').create({ 
      name, 
      details
    })

    const image = request.file('image')
    if (image) {
      await category.merge({ image: Attachment.fromFile(image) })
    }

    return response.json(category)
  }
} 