import axios from 'axios'
import { Bot, Context, InlineKeyboard, SessionFlavor, session } from 'grammy'
import {
  type Conversation,
  type ConversationFlavor,
  conversations,
  createConversation,
} from '@grammyjs/conversations'
import Env from '@ioc:Adonis/Core/Env'
// import { Calendar } from '@michpl/telegram-calendar'
import Account from 'App/Models/Account'
import Task from 'App/Models/Task'
import Service from 'App/Models/Service'
import ProductType from 'App/Models/ProductType'
import Product from 'App/Models/Product'
import ProductCategory from 'App/Models/ProductCategory'

type RafikiOrder = {
  task: number
  service: number
  productType: number
  product: number
  date: string
  time: string
  name: string
  phone: string
  email: string
  people: number
  instructions: string
}
type RafikiContext = Context & ConversationFlavor & SessionFlavor<RafikiOrder>
type RafikiConversation = Conversation<RafikiContext>

export default class Telegram {
  public bot: Bot<RafikiContext>

  constructor() {
    this.bot = new Bot<RafikiContext>(process.env.TELEGRAM_TOKEN || '')
  }

  // Write a method toString that takes a number and converts to string
  public toString(n: number) {
    return n.toString()
  }

  public start() {
    this.bot.use(
      session({
        initial: () => ({
          task: 0,
          service: 0,
        }),
      })
    )
    this.bot.use(conversations())
    this.bot.use(
      createConversation(async (conversation: RafikiConversation, ctx: RafikiContext) => {
        const existingUser = await Account.query()
          .preload('user')
          .where('provider', 'telegram')
          .where('providerId', ctx.from?.id!)
          .first()

        await ctx.reply(
          (existingUser ? `Welcome back ${existingUser.user.name}` : 'Welcome to AppInApp') +
            '\nWhat would you like to do today?'
        )

        // @todo Implement own calendar using @grammyjs/menu and getMonthDates as an array
        // const calendar = new Calendar({
        // date_format: 'YYYY-MM-DD', //Datetime result format
        // language: 'en', //Language (en/es/de/es/fr/it/tr/id)
        // bot_api: 'grammy', //Telegram bot library
        // close_calendar: true, //Close calendar after date selection
        // start_week_day: 0, //First day of the week(Sunday - `0`, Monday - `1`, Tuesday - `2` and so on)
        // time_selector_mod: true, //Enable time selection after a date is selected.
        // time_range: '00:00-23:59', //Allowed time range in "HH:mm-HH:mm" format
        // time_step: '30m', //Time step in the format "<Time step><m | h>"
        // start_date: false, //Minimum date of the calendar in the format "YYYY-MM-DD"
        // stop_date: false, //Maximum date of the calendar in the format "YYYY-MM-DD"
        // custom_start_msg: false, //Text of the message sent with the calendar/time selector
        // })

        // ctx.reply('Select date', {
        //   parse_mode: 'HTML',
        //   reply_markup: new InlineKeyboard(calendar.getPage(new Date())),
        // })

        // this.bot.on('callback_query:data', async (ctx) => {
        //   console.log('Unknown button event with payload', ctx.callbackQuery.data)
        //   await ctx.answerCallbackQuery()
        // })

        const tasks = await Task.query().orderBy('createdAt', 'asc').exec()

        tasks.map(async (task, index) => {
          ctx.reply(`${index + 1}: ${task.name}`)
        })

        const taskIndex = await conversation.form.number()

        const services = await Service.query()
          .where('taskId', tasks[taskIndex - 1]['id'])
          .exec()

        await ctx.reply('Select a service')
        services.map(async (service, index) => {
          ctx.reply(`${index + 1}: ${service.name}`)
        })

        const serviceIndex = await conversation.form.number()
        const productTypes = await ProductType.query()
          .where('serviceId', services[serviceIndex - 1]['id'])
          .exec()

        await ctx.reply(`Select a ${services[serviceIndex - 1]['name']} type \n`)
        productTypes.map(async (productType, index) => {
          ctx.reply(`${index + 1}: ${productType.name}`)
        })

        const productTypeIndex = await conversation.form.number()
        const productType = productTypes.find(
          (d) => d.id === productTypes[productTypeIndex - 1]['id']
        )

        if (!productType) {
          await ctx.reply('Invalid product type')
          return
        }

        await ctx.reply(`Select a ${productType.name} category`)

        const productCategories = await ProductCategory.query()
          .where('productTypeId', productTypes[productTypeIndex - 1]['id'])
          .exec()

        productCategories.map(async (productCategory, index) => {
          ctx.reply(`${index + 1}: ${productCategory.name}`)
        })

        const productCategoryIndex = await conversation.form.number()
        const productCategory = productCategories.find(
          (d) => d.id === productCategories[productCategoryIndex - 1]['id']
        )

        if (!productCategory) {
          await ctx.reply('Invalid product category')
          return
        }

        // await calendar.startNavCalendar(ctx.msg)
        // const date = await conversation.waitFor('callback_query:data')

        // const date = '2023-11-01 00:00:00'

        // const res = calendar.clickButtonCalendar(date.update.callback_query)
        // ctx.session.date = res

        if (productCategoryIndex) {
          const products = await Product.query()
            .where('productCategoryId', productCategories[productCategoryIndex - 1]['id'])
            .exec()

          await ctx.reply('Select a product')

          products.map(async (product, index) => {
            ctx.reply(`${index + 1}: ${product.name}`)
            ctx.replyWithPhoto(product.image ? Env.get('APP_URL') + product.image.url : '')
          })

          const productId = await conversation.form.text()
          const product = products.find((p) => p.id === productId)

          if (!product) {
            await ctx.reply('Invalid product')
            return
          }
        }

        await ctx.reply('Enter your name')
        const name = await conversation.form.text()

        await ctx.reply('Enter your phone number')
        const phone = await conversation.form.number()

        await ctx.reply('Enter your email address')
        const email = await conversation.form.text()

        await ctx.reply('Enter the number of people')
        const people = await conversation.form.number()

        await ctx.reply('Confirm booking? \n 1. Yes \n 2. No')

        const confirm = await conversation.form.number()

        if (confirm !== 1) {
          await ctx.reply('Thank you for your time')
          return
        }

        // const order = await Order.create({
        //   vendorId: productCategoryId,
        //   productId: product.id,
        //   userId: ctx.from?.id,
        //   meta: {
        //     date: ctx.session.date,
        //     people,
        //   },
        // })

        // await order.related('items').create({
        //   productTypeId: productType.id,
        // })

        try {
          const { authorization_url: url } = await axios
            .post(
              'https://api.paystack.co/transaction/initialize',
              {
                name,
                email,
                phone,
                amount: 10 * people,
              },
              {
                headers: {
                  'Authorization': 'Bearer ************************************************',
                  'Content-Type': 'application/json',
                },
              }
            )
            .then(({ data: { data } }) => {
              console.info({ data })
              return data
            })

          await ctx.reply('Please click the link below to complete your booking', {
            reply_markup: new InlineKeyboard().url('Pay', url),
          })
        } catch (error) {}
      }, 'process')
    )

    // this.bot.api.setMyCommands([
    //   { command: 'start', description: 'Start the bot' },
    //   { command: 'help', description: 'Show help text' },
    //   { command: 'settings', description: 'Open settings' },
    // ])
    this.bot.command('start', async (ctx) => ctx.conversation.enter('process'))
    this.bot.start()
  }
}
