import { BaseDeliveryProviderNotification } from './BaseDeliveryProviderNotification'
import DeliveryProviderProfile from 'App/Models/DeliveryProviderProfile'
import { DateTime } from 'luxon'

/**
 * Notification sent when a delivery provider application is approved by admin
 */
export default class DeliveryProviderApplicationApproved extends BaseDeliveryProviderNotification {
  private approvalDetails: {
    approvalDate: DateTime
    approvedBy: string
    verificationNotes?: string
    nextSteps: string[]
    serviceAreasEnabled: string[]
    capabilitiesApproved: Record<string, any>
  }

  constructor(deliveryProfile: DeliveryProviderProfile, approvalDetails: any) {
    super(undefined, deliveryProfile)
    this.approvalDetails = approvalDetails
  }

  protected isHighPriority(): boolean {
    return true // Milestone notification
  }

  protected isCritical(): boolean {
    return false
  }

  protected isMilestone(): boolean {
    return true // Application approval is a major milestone
  }

  protected getNotificationData(): Record<string, any> {
    const profile = this.deliveryProfile!
    const approval = this.approvalDetails

    return {
      title: 'Delivery Provider Application Approved',
      message: `Congratulations! Your delivery provider application has been approved. You can now accept delivery assignments in your service areas.`,
      
      // Database data
      vendor_id: profile.vendorId,
      delivery_profile_id: profile.id,
      approval_date: approval.approvalDate.toISO(),
      approved_by: approval.approvedBy,
      verification_notes: approval.verificationNotes || null,
      next_steps: approval.nextSteps,
      service_areas_enabled: approval.serviceAreasEnabled,
      capabilities_approved: approval.capabilitiesApproved,
      
      // Action data
      action_url: `/vendor/delivery-provider/dashboard`,
      action_label: 'View Dashboard',
      
      // FCM specific data
      fcm_action: 'view_dashboard',
      fcm_data: {
        vendor_id: profile.vendorId,
        service_areas_count: approval.serviceAreasEnabled.length,
        can_accept_orders: true,
      },
      
      // Email specific data
      email_subject: 'Delivery Provider Application Approved - Welcome to the Network!',
      email_template: 'mails/delivery-provider/application-approved',
      
      // SMS specific data
      sms_message: `Great news! Your delivery provider application has been approved. You can now start accepting delivery assignments. Check your dashboard for details.`,
    }
  }
}
