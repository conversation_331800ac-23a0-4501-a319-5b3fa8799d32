import { BaseDeliveryProviderNotification } from './BaseDeliveryProviderNotification'
import Order from 'App/Models/Order'
import { DateTime } from 'luxon'

/**
 * Notification sent when a delivery provider is assigned to fulfill a delivery order
 */
export default class DeliveryProviderOrderAssigned extends BaseDeliveryProviderNotification {
  private assignmentDetails: {
    pickupLocation: {
      vendorName: string
      address: string
      coordinates: { lat: number; lng: number }
      contactPhone: string
    }
    deliveryLocation: {
      customerName: string
      address: string
      coordinates: { lat: number; lng: number }
      contactPhone: string
    }
    orderDetails: {
      totalItems: number
      estimatedWeight?: number
      specialInstructions?: string
      deliveryFee: number
    }
    timing: {
      estimatedPickupTime: DateTime
      estimatedDeliveryTime: DateTime
      deliveryWindow?: string
    }
    assignmentExpiresAt: DateTime
  }

  constructor(order: Order, assignmentDetails: any) {
    super(order)
    this.assignmentDetails = assignmentDetails
  }

  protected isHighPriority(): boolean {
    return true // Assignment requires immediate attention
  }

  protected isCritical(): boolean {
    return false
  }

  protected isMilestone(): boolean {
    return false
  }

  protected getNotificationData(): Record<string, any> {
    const order = this.order!
    const pickup = this.assignmentDetails.pickupLocation
    const delivery = this.assignmentDetails.deliveryLocation
    const timing = this.assignmentDetails.timing

    return {
      title: 'New Delivery Assignment',
      message: `You've been assigned delivery order #${order.ref}. Pickup from ${pickup.vendorName} and deliver to ${delivery.customerName}.`,
      
      // Database data
      order_id: order.id,
      order_ref: order.ref,
      pickup_location: pickup,
      delivery_location: delivery,
      order_details: this.assignmentDetails.orderDetails,
      timing: {
        estimated_pickup_time: timing.estimatedPickupTime.toISO(),
        estimated_delivery_time: timing.estimatedDeliveryTime.toISO(),
        delivery_window: timing.deliveryWindow,
      },
      assignment_expires_at: this.assignmentDetails.assignmentExpiresAt.toISO(),
      
      // Action data
      action_url: `/delivery/orders/${order.id}`,
      action_label: 'View Assignment',
      expires_at: this.assignmentDetails.assignmentExpiresAt.toISO(),
      
      // FCM specific data
      fcm_action: 'view_assignment',
      fcm_data: {
        order_id: order.id,
        pickup_vendor: pickup.vendorName,
        delivery_customer: delivery.customerName,
        delivery_fee: this.assignmentDetails.orderDetails.deliveryFee,
        expires_in_minutes: Math.round(
          this.assignmentDetails.assignmentExpiresAt.diff(DateTime.now(), 'minutes').minutes
        ),
      },
      
      // Email specific data
      email_subject: `New Delivery Assignment - Order #${order.ref}`,
      email_template: 'mails/delivery-provider/order-assigned',
      
      // SMS specific data
      sms_message: `New delivery assignment: Order #${order.ref}. Pickup from ${pickup.vendorName}. Accept within ${Math.round(this.assignmentDetails.assignmentExpiresAt.diff(DateTime.now(), 'minutes').minutes)} minutes.`,
    }
  }
}
