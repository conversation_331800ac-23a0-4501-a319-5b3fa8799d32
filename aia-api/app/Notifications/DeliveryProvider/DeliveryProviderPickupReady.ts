import { BaseDeliveryProviderNotification } from './BaseDeliveryProviderNotification'
import Order from 'App/Models/Order'
import { DateTime } from 'luxon'

/**
 * Notification sent when an order is ready for pickup by the delivery provider
 */
export default class DeliveryProviderPickupReady extends BaseDeliveryProviderNotification {
  private pickupDetails: {
    pickupLocation: {
      vendorName: string
      address: string
      coordinates: { lat: number; lng: number }
      contactPhone: string
    }
    preparationCompletedAt: DateTime
    pickupDeadline?: DateTime
    specialHandlingNotes?: string
    verificationCode?: string
  }

  constructor(order: Order, pickupDetails: any) {
    super(order)
    this.pickupDetails = pickupDetails
  }

  protected isHighPriority(): boolean {
    return true // Time-sensitive pickup notification
  }

  protected isCritical(): boolean {
    return false
  }

  protected isMilestone(): boolean {
    return false
  }

  protected getNotificationData(): Record<string, any> {
    const order = this.order!
    const pickup = this.pickupDetails.pickupLocation
    const deadline = this.pickupDetails.pickupDeadline

    return {
      title: 'Order Ready for Pickup',
      message: `Order #${order.ref} is ready for pickup at ${pickup.vendorName}. ${deadline ? `Please pickup by ${deadline.toFormat('h:mm a')}.` : ''}`,
      
      // Database data
      order_id: order.id,
      order_ref: order.ref,
      pickup_location: pickup,
      preparation_completed_at: this.pickupDetails.preparationCompletedAt.toISO(),
      pickup_deadline: deadline?.toISO() || null,
      special_handling_notes: this.pickupDetails.specialHandlingNotes || null,
      verification_code: this.pickupDetails.verificationCode || null,
      
      // Action data
      action_url: `/delivery/orders/${order.id}/pickup`,
      action_label: 'Start Pickup',
      expires_at: deadline?.toISO() || null,
      
      // FCM specific data
      fcm_action: 'start_pickup',
      fcm_data: {
        order_id: order.id,
        vendor_name: pickup.vendorName,
        vendor_phone: pickup.contactPhone,
        verification_code: this.pickupDetails.verificationCode || '',
        has_deadline: !!deadline,
        minutes_until_deadline: deadline ? Math.round(deadline.diff(DateTime.now(), 'minutes').minutes) : 0,
      },
      
      // Email specific data
      email_subject: `Order #${order.ref} Ready for Pickup`,
      email_template: 'mails/delivery-provider/pickup-ready',
      
      // SMS specific data
      sms_message: `Order #${order.ref} ready for pickup at ${pickup.vendorName}. ${this.pickupDetails.verificationCode ? `Code: ${this.pickupDetails.verificationCode}` : ''} Call: ${pickup.contactPhone}`,
    }
  }
}
