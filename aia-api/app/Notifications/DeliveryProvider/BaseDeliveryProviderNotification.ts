import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'
import Order from 'App/Models/Order'
import DeliveryProviderProfile from 'App/Models/DeliveryProviderProfile'

/**
 * Base class for all delivery provider notifications
 * Provides common functionality and channel selection logic
 */
export abstract class BaseDeliveryProviderNotification implements NotificationContract {
  protected order?: Order
  protected deliveryProfile?: DeliveryProviderProfile

  constructor(order?: Order, deliveryProfile?: DeliveryProviderProfile) {
    this.order = order
    this.deliveryProfile = deliveryProfile
  }

  /**
   * Determine notification channels based on priority and user preferences
   */
  public via(notifiable: User): string[] {
    const channels: string[] = ['database'] // Always store in database

    // Check if user has notification preferences
    const preferences = notifiable.notificationPreferences?.delivery || {}

    // Add FCM push notifications for high priority notifications
    if (this.isHighPriority() && preferences.push_enabled !== false) {
      channels.push('fcm')
    }

    // Add SMS for critical notifications
    if (this.isCritical() && preferences.sms_enabled === true && notifiable.phone) {
      channels.push('sms')
    }

    // Add email for milestone notifications
    if (this.isMilestone() && preferences.email_enabled !== false && notifiable.email) {
      channels.push('mail')
    }

    return channels
  }

  /**
   * Abstract methods to be implemented by specific notification classes
   */
  protected abstract isHighPriority(): boolean
  protected abstract isCritical(): boolean
  protected abstract isMilestone(): boolean
  protected abstract getNotificationData(): Record<string, any>

  /**
   * Common database notification structure
   */
  public toDatabase(notifiable: User) {
    const data = this.getNotificationData()
    
    return {
      title: data.title,
      message: data.message,
      data: {
        ...data,
        notification_type: this.constructor.name,
        user_id: notifiable.id,
        order_id: this.order?.id || null,
        delivery_profile_id: this.deliveryProfile?.id || null,
      },
      action_url: data.action_url || null,
      action_label: data.action_label || null,
      expires_at: data.expires_at || null,
    }
  }

  /**
   * Common FCM notification structure
   */
  public toFcm(notifiable: User) {
    const data = this.getNotificationData()
    
    return {
      title: data.title,
      body: data.message,
      data: {
        type: 'delivery_provider_notification',
        notification_class: this.constructor.name,
        order_id: this.order?.id || '',
        delivery_profile_id: this.deliveryProfile?.id || '',
        action: data.fcm_action || 'view_notification',
        ...data.fcm_data || {},
      },
      android: {
        priority: this.isHighPriority() ? 'high' : 'normal',
        notification: {
          channelId: this.getChannelId(),
          sound: this.getNotificationSound(),
          ...(this.isHighPriority() && { vibrationPattern: [200, 100, 200] }),
        },
      },
      apns: {
        payload: {
          aps: {
            sound: this.getNotificationSound(),
            badge: 1,
          },
        },
      },
    }
  }

  /**
   * Common email notification structure
   */
  public toMail(notifiable: User) {
    const data = this.getNotificationData()
    
    return {
      subject: data.email_subject || data.title,
      template: data.email_template || 'mails/delivery-provider/generic-notification',
      data: {
        user: notifiable,
        notification_data: data,
        order: this.order,
        delivery_profile: this.deliveryProfile,
      },
    }
  }

  /**
   * Common SMS notification structure
   */
  public toSms(notifiable: User) {
    const data = this.getNotificationData()
    
    return {
      text: data.sms_message || data.message,
      phone: notifiable.phone?.replace(/^\+/, '') || '',
    }
  }

  /**
   * Get FCM channel ID based on notification type
   */
  protected getChannelId(): string {
    if (this.isCritical()) return 'delivery_critical'
    if (this.isHighPriority()) return 'delivery_assignments'
    return 'delivery_general'
  }

  /**
   * Get notification sound based on priority
   */
  protected getNotificationSound(): string {
    if (this.isCritical()) return 'critical_alert.wav'
    if (this.isHighPriority()) return 'delivery_assignment.wav'
    return 'default'
  }
}
