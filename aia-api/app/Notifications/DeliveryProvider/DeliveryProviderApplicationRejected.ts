import { BaseDeliveryProviderNotification } from './BaseDeliveryProviderNotification'
import DeliveryProviderProfile from 'App/Models/DeliveryProviderProfile'
import { DateTime } from 'luxon'

/**
 * Notification sent when a delivery provider application is rejected
 */
export default class DeliveryProviderApplicationRejected extends BaseDeliveryProviderNotification {
  private rejectionDetails: {
    rejectionDate: DateTime
    rejectedBy: string
    rejectionReasons: string[]
    reapplicationAllowed: boolean
    reapplicationDate?: DateTime
    improvementSuggestions: string[]
  }

  constructor(deliveryProfile: DeliveryProviderProfile, rejectionDetails: any) {
    super(undefined, deliveryProfile)
    this.rejectionDetails = rejectionDetails
  }

  protected isHighPriority(): boolean {
    return true // Important status update
  }

  protected isCritical(): boolean {
    return false
  }

  protected isMilestone(): boolean {
    return true // Application decision is a milestone
  }

  protected getNotificationData(): Record<string, any> {
    const profile = this.deliveryProfile!
    const rejection = this.rejectionDetails

    const canReapply = rejection.reapplicationAllowed
    const reapplyDate = rejection.reapplicationDate

    return {
      title: 'Delivery Provider Application Update',
      message: `Your delivery provider application has been reviewed. ${canReapply ? 'You can reapply after addressing the feedback provided.' : 'Please review the feedback for future reference.'}`,
      
      // Database data
      vendor_id: profile.vendorId,
      delivery_profile_id: profile.id,
      rejection_date: rejection.rejectionDate.toISO(),
      rejected_by: rejection.rejectedBy,
      rejection_reasons: rejection.rejectionReasons,
      reapplication_allowed: rejection.reapplicationAllowed,
      reapplication_date: reapplyDate?.toISO() || null,
      improvement_suggestions: rejection.improvementSuggestions,
      
      // Action data
      action_url: canReapply ? `/vendor/delivery-provider/reapply` : `/vendor/delivery-provider/feedback`,
      action_label: canReapply ? 'Review & Reapply' : 'View Feedback',
      
      // FCM specific data
      fcm_action: canReapply ? 'reapply' : 'view_feedback',
      fcm_data: {
        vendor_id: profile.vendorId,
        can_reapply: canReapply,
        reapply_available_date: reapplyDate?.toISODate() || null,
        reasons_count: rejection.rejectionReasons.length,
      },
      
      // Email specific data
      email_subject: 'Delivery Provider Application Status Update',
      email_template: 'mails/delivery-provider/application-rejected',
      
      // SMS specific data
      sms_message: `Your delivery provider application has been reviewed. ${canReapply ? `You can reapply ${reapplyDate ? `after ${reapplyDate.toFormat('MMM dd')}` : 'after addressing the feedback'}.` : 'Please check your email for detailed feedback.'}`,
    }
  }
}
