import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

interface Invoice {
  id: string | number
  number: string
  amount: number
  currency: string
  dueDate: Date
  description?: string
  orderId?: string | number
}

export default class NewInvoice implements NotificationContract {
  constructor(
    private invoice: Invoice,

  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const title = 'New Invoice Generated'
    const body = `Invoice ${this.invoice.number} for ${this.invoice.currency} ${this.invoice.amount} has been generated. ${this.invoice.description || ''}`

    return NotificationHelper.createNotificationData(
      title,
      body,
      NotificationHelper.createBillingActions(this.invoice.id, 'invoice'),
      {
        category: NotificationCategory.BILLING,
        notificationType: NotificationType.INVOICE_GENERATED,
        priority: NotificationPriority.MEDIUM,
        entityId: this.invoice.id,
        entityType: 'invoice',
        invoiceNumber: this.invoice.number,
        amount: this.invoice.amount,
        currency: this.invoice.currency,
        dueDate: this.invoice.dueDate.toISOString(),
        description: this.invoice.description,
        orderId: this.invoice.orderId
      },
      'https://cdn.verful.com/icons/invoice-icon.png'
    )
  }
}
