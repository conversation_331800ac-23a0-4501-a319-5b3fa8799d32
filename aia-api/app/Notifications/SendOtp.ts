import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationMessagePayload } from 'App/Interfaces/NotificationMessagePayload'
import User from 'App/Models/User'

export default class SendOtp implements NotificationContract {
  public via(_notifiable: User) {
    return ['database' as const, 'fcm' as const]
  }

  public toDatabase(notifiable: User) {
    return {
      subject: 'Your OTP',
      message: `Use ${notifiable.otp} as your OTP to login`,
    }
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const body = `Use ${notifiable.otp} as your OTP to login`
    // this.message
    //   .replace('{firstName}', notifiable.firstName)
    //   .replace('{status}', this.order.status)
    //   .replace('{vendor}', this.order.vendor.name)

    return {
      title: 'Your OTP',
      body,
      // icon: 'https://cdn.verful.com/icons/verful-512x512.png',
      // url: 'aiauser://notifications',
    }
  }
}
