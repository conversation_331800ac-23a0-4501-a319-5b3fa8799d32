import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'
import { NotificationMessagePayload } from 'firebase-admin/messaging'

export default class TestNotification implements NotificationContract {
  public via(_notifiable) {
    return ['database' as const, 'fcm' as const]
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    return {
      title: 'Test notification',
      body: `Hello ${notifiable.name}, Test notification to fix FCM notifications`,
      // url: `aiauser://orders/${this.order.id}`,
      // icon: 'https://cdn.verful.com/icons/verful-512x512.png',
    }
  }

  public toDatabase(notifiable: User) {
    return {
      title: 'Test notification',
      body: `Hello ${notifiable.name}, Test notification to fix database notifications`,
    }
  }
}
