import { NotificationContract } from '@ioc:Verful/Notification'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import User from 'App/Models/User'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'

export default class Welcome implements NotificationContract {
  public via(_notifiable) {
    return ['database' as const, 'mail' as const]
  }

  public toDatabase(notifiable: User) {
    const title = `Welcome, ${notifiable.firstName}!`
    const body = 'Welcome to our platform! Get started by completing your profile and exploring our services.'

    const actions = [
      NotificationHelper.createAction(
        NotificationActionType.COMPLETE_PROFILE,
        'Complete Profile',
        {}
      ),
      NotificationHelper.createAction(
        NotificationActionType.VIEW_RATES,
        'View Services',
        {}
      ),
      NotificationHelper.createAction(
        NotificationActionType.CONTACT_SALES,
        'Contact Support',
        { topic: 'getting_started' }
      )
    ]

    return NotificationHelper.createNotificationData(
      title,
      body,
      actions,
      {
        category: NotificationCategory.ACCOUNT,
        notificationType: NotificationType.WELCOME,
        priority: NotificationPriority.MEDIUM,
        userId: notifiable.id,
        welcomeDate: new Date().toISOString()
      },
      'https://cdn.verful.com/icons/welcome-icon.png'
    )
  }

  public toMail(notifiable: User) {
    return new CustomerMailer(notifiable, 'mailswelcome', {
      greeting: `Hello ${notifiable.firstName} ${notifiable.lastName}!`,
      intro: 'Welcome to the application',
      body: 'Welcome to the application',
    })
  }
}
