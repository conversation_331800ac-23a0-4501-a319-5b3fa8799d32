import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'
import Order from 'App/Models/Order'

/**
 * Notification sent to product vendors when they receive a new order
 * This is separate from delivery provider notifications to maintain dual-verification separation
 */
export default class VendorNewOrder implements NotificationContract {
  constructor(private order: Order) {}

  public via(notifiable: User): string[] {
    const channels: string[] = ['database']

    // Check user notification preferences
    const preferences = notifiable.notificationPreferences?.orders || {}

    // Add push notifications if enabled
    if (preferences.push_enabled !== false) {
      channels.push('fcm')
    }

    // Add email notifications if enabled
    if (preferences.email_enabled === true && notifiable.email) {
      channels.push('mail')
    }

    return channels
  }

  public toDatabase(notifiable: User) {
    const order = this.order
    const isDeliveryOrder = order.fulfillmentType === 'delivery'

    return {
      title: 'New Product Order',
      message: `You have received a new ${isDeliveryOrder ? 'delivery' : 'pickup'} order #${order.ref}${isDeliveryOrder ? '. A delivery provider will be assigned separately.' : '.'}`,
      data: {
        notification_type: 'VendorNewOrder',
        order_id: order.id,
        order_ref: order.ref,
        fulfillment_type: order.fulfillmentType,
        total_amount: order.totalAmount,
        customer_name: order.customer?.name || 'Unknown Customer',
        is_delivery_order: isDeliveryOrder,
      },
      action_url: `/vendor/orders/${order.id}`,
      action_label: 'View Order',
    }
  }

  public toFcm(notifiable: User) {
    const order = this.order
    const isDeliveryOrder = order.fulfillmentType === 'delivery'

    return {
      title: 'New Product Order',
      body: `Order #${order.ref} received${isDeliveryOrder ? ' (Delivery)' : ' (Pickup)'}`,
      data: {
        type: 'vendor_new_order',
        order_id: order.id,
        fulfillment_type: order.fulfillmentType,
        action: 'view_order',
      },
      android: {
        priority: 'high',
        notification: {
          channelId: 'vendor_orders',
          sound: 'new_order.wav',
          vibrationPattern: [200, 100, 200],
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'new_order.wav',
            badge: 1,
          },
        },
      },
    }
  }

  public toMail(notifiable: User) {
    const order = this.order

    return {
      subject: `New Order #${order.ref} Received`,
      template: 'mails/vendor/new-order',
      data: {
        user: notifiable,
        order: order,
        is_delivery_order: order.fulfillmentType === 'delivery',
      },
    }
  }
}
