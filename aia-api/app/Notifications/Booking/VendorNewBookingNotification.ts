import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import Booking from 'App/Models/Booking'
import User from 'App/Models/User'

export default class VendorNewBookingNotification implements NotificationContract {
  constructor(
    private booking: Booking,
    private notificationType: 'new_booking' | 'booking_confirmed' = 'new_booking'
  ) {}

  public via(_notifiable: User) {
    return ['database' as const, 'fcm' as const, 'mail' as const]
  }

  public toDatabase(notifiable: User) {
    const isNewBooking = this.notificationType === 'new_booking'
    const title = isNewBooking ? 'New Booking Request' : 'Booking Confirmed'
    const customerName = this.booking.customer?.name || 'Customer'
    const serviceName = this.booking.product?.name || 'Service'
    const appointmentTime = this.booking.appointmentStart.toFormat('MMM dd \'at\' h:mm a')
    
    const message = isNewBooking
      ? `New booking request from ${customerName} for ${serviceName} on ${appointmentTime}. Requires confirmation.`
      : `Booking confirmed for ${customerName} - ${serviceName} on ${appointmentTime}.`

    return NotificationHelper.createDatabaseNotification({
      category: NotificationCategory.BOOKING,
      type: NotificationType.VENDOR_BOOKING_ALERT,
      priority: NotificationPriority.HIGH,
      title,
      message,
      actionType: NotificationActionType.MANAGE_BOOKING,
      actionUrl: `/vendor/bookings/${this.booking.id}`,
      actionLabel: isNewBooking ? 'Review Booking' : 'View Booking',
      data: {
        booking_id: this.booking.id,
        confirmation_code: this.booking.confirmationCode,
        customer_id: this.booking.customerId,
        customer_name: customerName,
        customer_phone: this.booking.customer?.phone,
        customer_email: this.booking.customer?.email,
        appointment_date: this.booking.appointmentStart.toFormat('yyyy-MM-dd'),
        appointment_time: this.booking.appointmentStart.toFormat('HH:mm'),
        service_name: serviceName,
        duration_minutes: this.booking.durationMinutes,
        total_price: this.booking.totalPrice,
        status: this.booking.status,
        requires_action: isNewBooking
      }
    })
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const isNewBooking = this.notificationType === 'new_booking'
    const title = isNewBooking ? 'New Booking Request' : 'Booking Confirmed'
    const customerName = this.booking.customer?.name || 'Customer'
    const serviceName = this.booking.product?.name || 'Service'
    const appointmentTime = this.booking.appointmentStart.toFormat('MMM dd \'at\' h:mm a')
    
    const body = isNewBooking
      ? `${customerName} requested ${serviceName} on ${appointmentTime}. Tap to review.`
      : `Booking confirmed: ${customerName} - ${serviceName} on ${appointmentTime}.`

    return {
      title,
      body,
      url: `aiavendor://bookings/${this.booking.id}`,
      icon: 'https://cdn.verful.com/icons/verful-512x512.png',
      data: {
        booking_id: this.booking.id,
        customer_id: this.booking.customerId,
        requires_action: isNewBooking.toString(),
        type: 'vendor_booking_alert'
      }
    }
  }

  public toMail(notifiable: User) {
    const isNewBooking = this.notificationType === 'new_booking'
    const subject = isNewBooking ? 'New Booking Request Received' : 'Booking Confirmation Update'
    const customerName = this.booking.customer?.name || 'Customer'
    const appointmentDate = this.booking.appointmentStart.toFormat('EEEE, MMMM dd, yyyy')
    const appointmentTime = this.booking.appointmentStart.toFormat('h:mm a')

    const emailData = {
      greeting: `Hello ${notifiable.firstName || notifiable.name}!`,
      subject,
      intro: isNewBooking 
        ? 'You have received a new booking request that requires your attention.'
        : 'A booking has been confirmed and added to your schedule.',
      booking_details: {
        confirmation_code: this.booking.confirmationCode,
        customer_name: customerName,
        customer_phone: this.booking.customer?.phone || 'Not provided',
        customer_email: this.booking.customer?.email || 'Not provided',
        service_name: this.booking.product?.name,
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        duration: `${this.booking.durationMinutes} minutes`,
        total_price: `$${this.booking.totalPrice.toFixed(2)}`,
        status: this.booking.status,
        branch_name: this.booking.branch?.name
      },
      selected_options: this.booking.selectedServiceOptions || [],
      staff_assignments: this.booking.staffAssignments || [],
      equipment_reservations: this.booking.equipmentReservations || [],
      customer_notes: this.booking.bookingNotes,
      action_required: isNewBooking,
      action_url: `/vendor/bookings/${this.booking.id}`,
      action_label: isNewBooking ? 'Review & Confirm Booking' : 'View Booking Details',
      footer_text: isNewBooking 
        ? 'Please review this booking request and confirm or decline it as soon as possible. The customer is waiting for your response.'
        : 'This booking is now confirmed and has been added to your schedule. Please prepare accordingly.'
    }

    return new CustomerMailer(notifiable, 'mails/booking/vendor-notification', emailData)
  }

  public toSms(notifiable: User) {
    if (!notifiable.phone) {
      throw new Error('Cannot send SMS to user without phone number')
    }

    const isNewBooking = this.notificationType === 'new_booking'
    const customerName = this.booking.customer?.name || 'Customer'
    const serviceName = this.booking.product?.name || 'service'
    const date = this.booking.appointmentStart.toFormat('MMM dd')
    const time = this.booking.appointmentStart.toFormat('h:mm a')
    const confirmationCode = this.booking.confirmationCode

    let text: string
    if (isNewBooking) {
      text = `NEW BOOKING: ${customerName} requested ${serviceName} on ${date} at ${time}. Code: ${confirmationCode}. Please confirm ASAP.`
    } else {
      text = `CONFIRMED: Booking for ${customerName} - ${serviceName} on ${date} at ${time}. Code: ${confirmationCode}.`
    }

    return {
      text,
      phone: notifiable.phone.includes('+')
        ? notifiable.phone.replace(/^\+/, '')
        : notifiable.phone,
    }
  }
}
