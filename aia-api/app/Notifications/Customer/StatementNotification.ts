import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

interface Statement {
  id: string | number
  month: string
  year: number
  totalAmount: number
  currency: string
  generatedAt: Date
}

export default class StatementNotification implements NotificationContract {
  constructor(
    private statement: Statement,

  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const monthYear = `${this.statement.month} ${this.statement.year}`
    
    return NotificationHelper.createNotificationData(
      'Monthly Statement Available',
      `Your ${monthYear} account statement is ready for review. Total: ${this.statement.currency} ${this.statement.totalAmount}`,
      NotificationHelper.createBillingActions(this.statement.id, 'statement'),
      {
        category: NotificationCategory.BILLING,
        notificationType: NotificationType.STATEMENT_AVAILABLE,
        priority: NotificationPriority.MEDIUM,
        entityId: this.statement.id,
        entityType: 'statement',
        statementMonth: this.statement.month,
        statementYear: this.statement.year,
        totalAmount: this.statement.totalAmount,
        currency: this.statement.currency,
        generatedAt: this.statement.generatedAt.toISOString()
      },
      'https://cdn.verful.com/icons/statement-icon.png'
    )
  }
}
