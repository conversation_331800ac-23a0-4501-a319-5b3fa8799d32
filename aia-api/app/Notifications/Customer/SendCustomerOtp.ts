import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'

export default class SendCustomerOtp implements NotificationContract {
  constructor(
    private otpCode: string,
    private purpose: 'verification' | 'login' | 'password_reset' = 'verification',
    private expiresInMinutes: number = 10
  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const purposeText = {
      'verification': 'account verification',
      'login': 'login',
      'password_reset': 'password reset'
    }

    const title = 'Verification Code'
    const body = `Your verification code for ${purposeText[this.purpose]} is: ${this.otpCode}. This code expires in ${this.expiresInMinutes} minutes.`

    const actions = [
      NotificationHelper.createAction(
        NotificationActionType.VERIFY_ACCOUNT,
        'Enter Code',
        {
          purpose: this.purpose,
          expiresAt: new Date(Date.now() + this.expiresInMinutes * 60 * 1000).toISOString()
        }
      )
    ]

    // Add purpose-specific actions
    if (this.purpose === 'password_reset') {
      actions.push(
        NotificationHelper.createAction(
          NotificationActionType.CONTACT_SUPPORT,
          'Need Help?',
          { issue: 'password_reset' }
        )
      )
    }

    return NotificationHelper.createNotificationData(
      title,
      body,
      actions,
      {
        category: NotificationCategory.ACCOUNT,
        notificationType: NotificationType.OTP_VERIFICATION,
        priority: NotificationPriority.HIGH,
        otpPurpose: this.purpose,
        expiresInMinutes: this.expiresInMinutes,
        generatedAt: new Date().toISOString()
      },
      'https://cdn.verful.com/icons/otp-icon.png'
    )
  }
}
