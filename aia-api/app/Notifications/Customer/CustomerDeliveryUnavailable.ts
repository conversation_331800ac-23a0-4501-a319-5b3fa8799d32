import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'

/**
 * Notification sent to customers when delivery is unavailable for their cart/location
 */
export default class CustomerDeliveryUnavailable implements NotificationContract {
  constructor(
    private cartData: {
      items: any[]
      vendorId?: string
      location?: { lat: number; lng: number; address?: string }
    },
    private reasons: string[]
  ) {}

  public via(notifiable: User): string[] {
    const channels: string[] = ['database']

    // Check user notification preferences
    const preferences = notifiable.notificationPreferences?.delivery || {}

    // Add push notifications if enabled
    if (preferences.push_enabled !== false) {
      channels.push('fcm')
    }

    return channels
  }

  public toDatabase(notifiable: User) {
    const primaryReason = this.reasons[0] || 'Delivery not available'
    const location = this.cartData.location

    return {
      title: 'Delivery Unavailable',
      message: `Delivery is not available for your location${location?.address ? ` (${location.address})` : ''}. ${primaryReason}`,
      data: {
        notification_type: 'CustomerDeliveryUnavailable',
        vendor_id: this.cartData.vendorId || null,
        location: location || null,
        reasons: this.reasons,
        items_count: this.cartData.items.length,
        suggested_alternatives: [
          'Try pickup instead',
          'Check if vendor offers delivery to nearby areas',
          'Contact vendor directly for special arrangements',
        ],
      },
      action_url: `/cart/fulfillment-options`,
      action_label: 'View Alternatives',
    }
  }

  public toFcm(notifiable: User) {
    const primaryReason = this.reasons[0] || 'Delivery not available'

    return {
      title: 'Delivery Unavailable',
      body: `${primaryReason}. Please consider pickup or other options.`,
      data: {
        type: 'delivery_unavailable',
        vendor_id: this.cartData.vendorId || '',
        reasons_count: this.reasons.length.toString(),
        action: 'view_alternatives',
      },
      android: {
        priority: 'normal',
        notification: {
          channelId: 'delivery_updates',
          sound: 'default',
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
          },
        },
      },
    }
  }
}
