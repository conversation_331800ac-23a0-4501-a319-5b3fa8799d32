import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'

interface Payment {
  id: string | number
  amount: number
  currency: string
  method: string
  reference: string
  billId?: string | number
  invoiceId?: string | number
  paidAt: Date
}

export default class CustomerPaymentReceived implements NotificationContract {
  constructor(
    private payment: Payment,

  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const title = 'Payment Received'
    const body = `Your payment of ${this.payment.currency} ${this.payment.amount} has been successfully processed. Reference: ${this.payment.reference}`

    // Create appropriate actions based on what was paid
    const actions = [
      NotificationHelper.createAction(
        NotificationActionType.VIEW_PAYMENT_HISTORY,
        'View Payment History',
        {}
      )
    ]

    // Add specific actions based on payment type
    if (this.payment.billId) {
      actions.unshift(
        NotificationHelper.createAction(
          NotificationActionType.VIEW_BILL_DETAILS,
          'View Bill',
          { billId: this.payment.billId }
        )
      )
    }

    if (this.payment.invoiceId) {
      actions.unshift(
        NotificationHelper.createAction(
          NotificationActionType.VIEW_INVOICE,
          'View Invoice',
          { invoiceId: this.payment.invoiceId }
        )
      )
    }

    return NotificationHelper.createNotificationData(
      title,
      body,
      actions,
      {
        category: NotificationCategory.BILLING,
        notificationType: NotificationType.PAYMENT_RECEIVED,
        priority: NotificationPriority.MEDIUM,
        entityId: this.payment.id,
        entityType: 'payment',
        amount: this.payment.amount,
        currency: this.payment.currency,
        paymentMethod: this.payment.method,
        paymentReference: this.payment.reference,
        billId: this.payment.billId,
        invoiceId: this.payment.invoiceId,
        paidAt: this.payment.paidAt.toISOString()
      },
      'https://cdn.verful.com/icons/payment-success-icon.png'
    )
  }
}
