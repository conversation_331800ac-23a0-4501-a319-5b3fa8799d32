import { NotificationContract } from '@ioc:Verful/Notification'
import Order from 'App/Models/Order'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

export default class CustomerOrderStatusChange implements NotificationContract {
  constructor(
    private order: Order,
    private oldStatus: string = 'Pending'
  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    // Determine priority based on status
    const getPriority = (): string => {
      switch (this.order.status.toLowerCase()) {
        case 'cancelled':
        case 'failed':
        case 'rejected':
          return NotificationPriority.HIGH
        case 'delivered':
        case 'completed':
          return NotificationPriority.MEDIUM
        default:
          return NotificationPriority.LOW
      }
    }

    const title = 'Order Status Update'
    const body = `Your order from ${this.order.vendor?.name || 'vendor'} changed from ${this.oldStatus} to ${this.order.status}`

    return NotificationHelper.createNotificationData(
      title,
      body,
      NotificationHelper.createOrderActions(this.order.id, this.order.status, this.order.vendor?.name),
      {
        category: NotificationCategory.ORDER,
        notificationType: NotificationType.ORDER_STATUS_CHANGE,
        priority: getPriority(),
        entityId: this.order.id,
        entityType: 'order',
        oldStatus: this.oldStatus,
        newStatus: this.order.status,
        vendorId: this.order.vendor?.id,
        vendorName: this.order.vendor?.name,
        orderTotal: this.order.total,
        currency: 'KES'
      },
      'https://cdn.verful.com/icons/order-icon.png'
    )
  }
}
