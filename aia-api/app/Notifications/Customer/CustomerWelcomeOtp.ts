import { NotificationContract } from '@ioc:Verful/Notification'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import User from 'App/Models/User'

export default class CustomerWelcomeOtp implements NotificationContract {
  public via(notifiable: User) {
    return notifiable.phone ? ['sms' as const] : []
  }

  public toSms(notifiable: User) {
    if (!notifiable.phone) {
      throw new Error('Cannot send SMS to user without phone number')
    }
    
    const text = `Hi ${notifiable.firstName}, use ${notifiable.otp} to verify your phone number`

    return {
      text,
      phone: notifiable.phone.includes('+')
        ? notifiable.phone.replace(/^\+/, '')
        : notifiable.phone,
    }
  }

  public toMail(notifiable: User) {
    return new CustomerMailer(notifiable, 'mails/welcome', {
      greeting: `Hello ${notifiable.firstName} ${notifiable.lastName}!`,
      intro: 'Welcome to the application',
      body: `Welcome to the AppInApp, use ${notifiable.otp} to verify your account and get started`,
    })
  }
}
