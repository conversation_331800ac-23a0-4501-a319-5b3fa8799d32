/**
 * Notification Categories
 * 
 * Defines the main categories for notifications to help with
 * frontend styling, filtering, and user experience.
 */
export class NotificationCategory {
  static readonly BILLING = 'BILLING'
  static readonly ORDER = 'ORDER'
  static readonly PROMOTION = 'PROMOTION'
  static readonly ACCOUNT = 'ACCOUNT'
  static readonly SYSTEM = 'SYSTEM'
  static readonly SUPPORT = 'SUPPORT'

  /**
   * Get all available categories
   */
  static getAll(): string[] {
    return [
      this.BILLING,
      this.ORDER,
      this.PROMOTION,
      this.ACCOUNT,
      this.SYSTEM,
      this.SUPPORT
    ]
  }

  /**
   * Check if category is valid
   */
  static isValid(category: string): boolean {
    return this.getAll().includes(category)
  }

  /**
   * Get category description for display
   */
  static getDescription(category: string): string {
    switch (category) {
      case this.BILLING:
        return 'Bills, payments, invoices, and financial matters'
      case this.ORDER:
        return 'Order updates, tracking, and delivery information'
      case this.PROMOTION:
        return 'Marketing offers, deals, and promotional content'
      case this.ACCOUNT:
        return 'Account-related notifications and profile updates'
      case this.SYSTEM:
        return 'System announcements and maintenance notifications'
      case this.SUPPORT:
        return 'Support requests and help-related notifications'
      default:
        return 'General notifications'
    }
  }

  /**
   * Get default priority for category
   */
  static getDefaultPriority(category: string): string {
    switch (category) {
      case this.BILLING:
        return 'HIGH'
      case this.ORDER:
        return 'MEDIUM'
      case this.PROMOTION:
        return 'LOW'
      case this.ACCOUNT:
        return 'MEDIUM'
      case this.SYSTEM:
        return 'HIGH'
      case this.SUPPORT:
        return 'HIGH'
      default:
        return 'MEDIUM'
    }
  }
}
