import Department from 'App/Models/Department'
import Factory from '@ioc:Adonis/Lucid/Factory'
import { ulid } from 'ulidx'

export default Factory.define(Department, ({ faker }) => {
  const departmentTypes = ['Kitchen', 'Bar', 'Pastry', 'Grill', 'Cold Station', 'Hot Station', 'Salad Bar', 'Dessert Station']
  const name = faker.helpers.arrayElement(departmentTypes)
  
  return {
    id: ulid().toLowerCase(),
    vendorId: ulid().toLowerCase(),
    branchId: ulid().toLowerCase(),
    name,
    description: faker.lorem.sentence(),
    active: faker.datatype.boolean({ probability: 0.9 }), // 90% chance of being active
    averagePreparationTime: faker.number.int({ min: 5, max: 45 }), // 5-45 minutes
    maxConcurrentOrders: faker.number.int({ min: 5, max: 20 }),
    priorityLevel: faker.number.int({ min: 1, max: 5 }),
    workingHours: {
      monday: { open: '06:00', close: '22:00' },
      tuesday: { open: '06:00', close: '22:00' },
      wednesday: { open: '06:00', close: '22:00' },
      thursday: { open: '06:00', close: '22:00' },
      friday: { open: '06:00', close: '23:00' },
      saturday: { open: '07:00', close: '23:00' },
      sunday: { open: '08:00', close: '21:00' }
    },
    breakTimes: {
      morning: { start: '10:00', end: '10:15' },
      afternoon: { start: '15:00', end: '15:15' }
    },
    workflowSettings: {
      autoAssignItems: true,
      requireQualityCheck: faker.datatype.boolean({ probability: 0.3 }),
      allowParallelPreparation: true,
      maxItemsPerStaff: faker.number.int({ min: 3, max: 8 })
    },
    notificationSettings: {
      overdueAlerts: true,
      capacityWarnings: true,
      qualityIssueAlerts: true,
      completionNotifications: true
    },
    efficiencyRating: faker.number.float({ min: 2.0, max: 5.0, fractionDigits: 2 }),
    totalOrdersCompleted: faker.number.int({ min: 0, max: 1000 }),
    averageCompletionTime: faker.number.float({ min: 5.0, max: 30.0, fractionDigits: 2 }),
    meta: {
      equipment: faker.helpers.arrayElements(['Oven', 'Grill', 'Fryer', 'Mixer', 'Blender'], { min: 1, max: 3 }),
      specialties: faker.helpers.arrayElements(['Vegetarian', 'Vegan', 'Gluten-Free', 'Halal'], { min: 0, max: 2 }),
      certifications: faker.helpers.arrayElements(['Food Safety', 'HACCP', 'Organic'], { min: 0, max: 2 })
    }
  }
})
.state('kitchen', (department) => ({
  name: 'Kitchen',
  averagePreparationTime: faker.number.int({ min: 10, max: 30 }),
  maxConcurrentOrders: faker.number.int({ min: 10, max: 20 }),
  priorityLevel: 1,
  workflowSettings: {
    autoAssignItems: true,
    requireQualityCheck: true,
    allowParallelPreparation: true,
    maxItemsPerStaff: 5
  }
}))
.state('bar', (department) => ({
  name: 'Bar',
  averagePreparationTime: faker.number.int({ min: 2, max: 10 }),
  maxConcurrentOrders: faker.number.int({ min: 15, max: 25 }),
  priorityLevel: 2,
  workflowSettings: {
    autoAssignItems: true,
    requireQualityCheck: false,
    allowParallelPreparation: true,
    maxItemsPerStaff: 8
  }
}))
.state('pastry', (department) => ({
  name: 'Pastry',
  averagePreparationTime: faker.number.int({ min: 15, max: 60 }),
  maxConcurrentOrders: faker.number.int({ min: 5, max: 10 }),
  priorityLevel: 3,
  workflowSettings: {
    autoAssignItems: false,
    requireQualityCheck: true,
    allowParallelPreparation: false,
    maxItemsPerStaff: 3
  }
}))
.state('inactive', (department) => ({
  active: false,
  efficiencyRating: null,
  totalOrdersCompleted: 0,
  averageCompletionTime: null
}))
.state('highVolume', (department) => ({
  maxConcurrentOrders: faker.number.int({ min: 20, max: 50 }),
  totalOrdersCompleted: faker.number.int({ min: 500, max: 2000 }),
  efficiencyRating: faker.number.float({ min: 4.0, max: 5.0, fractionDigits: 2 }),
  averageCompletionTime: faker.number.float({ min: 5.0, max: 15.0, fractionDigits: 2 })
}))
.state('lowPerformance', (department) => ({
  efficiencyRating: faker.number.float({ min: 1.0, max: 2.5, fractionDigits: 2 }),
  averageCompletionTime: faker.number.float({ min: 20.0, max: 45.0, fractionDigits: 2 }),
  workflowSettings: {
    autoAssignItems: false,
    requireQualityCheck: true,
    allowParallelPreparation: false,
    maxItemsPerStaff: 2
  }
}))
.build()
