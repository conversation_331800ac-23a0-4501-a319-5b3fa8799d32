import Factory from '@ioc:Adonis/Lucid/Factory'
import Duration from 'App/Models/Duration'

export default Factory.define(Duration, ({ faker }) => {
  const categories = ['short', 'medium', 'long', 'full-day'] as const
  const category = faker.helpers.arrayElement(categories)
  
  const minutesByCategory = {
    'short': faker.helpers.arrayElement([30, 45, 60]),
    'medium': faker.helpers.arrayElement([90, 120, 150]),
    'long': faker.helpers.arrayElement([180, 240, 300]),
    'full-day': faker.helpers.arrayElement([360, 420, 480])
  }
  
  const bufferByCategory = {
    'short': faker.helpers.arrayElement([15, 20, 30]),
    'medium': faker.helpers.arrayElement([30, 45, 60]),
    'long': faker.helpers.arrayElement([60, 90, 120]),
    'full-day': faker.helpers.arrayElement([90, 120, 150])
  }
  
  const maxConcurrentByCategory = {
    'short': faker.number.int({ min: 5, max: 10 }),
    'medium': faker.number.int({ min: 3, max: 6 }),
    'long': faker.number.int({ min: 1, max: 3 }),
    'full-day': 1
  }

  const minutes = minutesByCategory[category]
  const bufferMinutes = bufferByCategory[category]
  
  return {
    name: `${faker.company.buzzAdjective()} ${category} service (${minutes}min)`,
    description: faker.lorem.sentence(),
    minutes,
    bufferMinutes,
    category,
    maxConcurrent: maxConcurrentByCategory[category],
    allowsBackToBack: category === 'short' || category === 'medium',
    requiredBreakAfter: category === 'long' || category === 'full-day' ? faker.number.int({ min: 30, max: 120 }) : 0,
    schedulingRules: {
      minAdvanceHours: faker.helpers.arrayElement([1, 2, 4, 8, 24]),
      maxPerDay: faker.number.int({ min: 2, max: 8 }),
      timeSlots: faker.helpers.maybe(() => faker.helpers.arrayElements(['morning', 'afternoon', 'evening']), { probability: 0.3 }) || [],
      blackoutDays: faker.helpers.maybe(() => faker.helpers.arrayElements(['sunday', 'monday', 'saturday']), { probability: 0.2 }) || []
    },
    branchConstraints: {
      respectBranchHours: faker.datatype.boolean({ probability: 0.8 }),
      staffRequired: faker.number.int({ min: 1, max: 4 }),
      equipmentRequired: faker.helpers.maybe(() => faker.helpers.arrayElements(['basic-kit', 'premium-tools', 'vehicle', 'specialized-equipment']), { probability: 0.4 }) || []
    },
    active: faker.datatype.boolean({ probability: 0.9 })
  }
}).build()
