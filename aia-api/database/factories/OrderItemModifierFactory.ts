import OrderItemModifier from 'App/Models/OrderItemModifier'
import Factory from '@ioc:Adonis/Lucid/Factory'
import { DateTime } from 'luxon'
import { ulid } from 'ulidx'

export default Factory.define(OrderItemModifier, ({ faker }) => {
  const status = faker.helpers.arrayElement(['pending', 'preparing', 'completed', 'skipped', 'cancelled', 'failed', 'on_hold'])
  const quantity = faker.number.int({ min: 1, max: 3 })
  const price = faker.number.float({ min: 0.5, max: 15.0, fractionDigits: 2 })
  const complexityLevel = faker.number.int({ min: 1, max: 5 })
  
  // Generate timing data based on status
  let preparationStartedAt = null
  let preparationCompletedAt = null
  let actualPreparationTime = null
  
  if (['preparing', 'completed'].includes(status)) {
    preparationStartedAt = DateTime.now().minus({ minutes: faker.number.int({ min: 1, max: 10 }) })
  }
  
  if (status === 'completed') {
    const prepTime = faker.number.float({ min: 0.5, max: 8.0, fractionDigits: 1 })
    preparationCompletedAt = preparationStartedAt?.plus({ minutes: prepTime })
    actualPreparationTime = prepTime
  }

  return {
    orderItemId: faker.number.int({ min: 1, max: 1000 }),
    modifierOptionId: ulid().toLowerCase(),
    quantity,
    priceAtTimeOfOrder: price,
    status,
    preparedByStaffId: ['preparing', 'completed'].includes(status) ? ulid().toLowerCase() : null,
    preparationStartedAt,
    preparationCompletedAt,
    complexityLevel,
    requiresSpecialSkill: faker.datatype.boolean({ probability: 0.3 }),
    affectsAllergens: faker.datatype.boolean({ probability: 0.2 }),
    preparationNotes: ['preparing', 'completed', 'failed'].includes(status) ? faker.lorem.sentence() : null,
    specialInstructions: faker.datatype.boolean({ probability: 0.4 }) ? faker.lorem.sentence() : null,
    preparationSteps: complexityLevel > 3 ? {
      steps: [
        'Prepare base ingredient',
        'Apply modification technique',
        'Quality check',
        'Final presentation'
      ],
      currentStep: faker.number.int({ min: 1, max: 4 }),
      estimatedTimePerStep: faker.number.float({ min: 0.5, max: 2.0, fractionDigits: 1 })
    } : null,
    qualityCheckStatus: faker.helpers.arrayElement(['pending', 'passed', 'failed', 'not_required']),
    qualityCheckedBy: faker.datatype.boolean({ probability: 0.3 }) ? ulid().toLowerCase() : null,
    qualityCheckedAt: faker.datatype.boolean({ probability: 0.3 }) ? DateTime.now().minus({ minutes: faker.number.int({ min: 1, max: 5 }) }) : null,
    failureReason: status === 'failed' ? faker.helpers.arrayElement([
      'Ingredient not available',
      'Equipment malfunction',
      'Quality standards not met',
      'Customer changed mind'
    ]) : null,
    preparationAttempts: status === 'failed' ? faker.number.int({ min: 2, max: 4 }) : 1,
    attemptHistory: status === 'failed' ? {
      attempt_1: {
        status: 'failed',
        timestamp: DateTime.now().minus({ minutes: 10 }).toISO(),
        reason: 'First attempt failed'
      },
      attempt_2: {
        status: 'failed',
        timestamp: DateTime.now().minus({ minutes: 5 }).toISO(),
        reason: 'Second attempt failed'
      }
    } : null,
    customerApproved: ['completed', 'skipped'].includes(status) ? faker.datatype.boolean({ probability: 0.9 }) : null,
    customerFeedback: faker.datatype.boolean({ probability: 0.1 }) ? faker.lorem.sentence() : null,
    actualCost: faker.number.float({ min: price * 0.3, max: price * 0.7, fractionDigits: 2 }),
    affectsInventory: faker.datatype.boolean({ probability: 0.8 }),
    inventoryImpact: faker.datatype.boolean({ probability: 0.8 }) ? {
      ingredients: [
        {
          name: faker.commerce.productMaterial(),
          quantity: faker.number.float({ min: 0.1, max: 2.0, fractionDigits: 2 }),
          unit: faker.helpers.arrayElement(['g', 'ml', 'pieces', 'cups'])
        }
      ]
    } : null,
    actualPreparationTime,
    efficiencyScore: ['completed'].includes(status) ? faker.number.float({ min: 2.0, max: 5.0, fractionDigits: 2 }) : null
  }
})
.state('pending', (modifier) => ({
  status: 'pending',
  preparedByStaffId: null,
  preparationStartedAt: null,
  preparationCompletedAt: null,
  actualPreparationTime: null,
  preparationNotes: null,
  qualityCheckStatus: 'not_required'
}))
.state('preparing', (modifier) => ({
  status: 'preparing',
  preparedByStaffId: ulid().toLowerCase(),
  preparationStartedAt: DateTime.now().minus({ minutes: faker.number.int({ min: 1, max: 5 }) }),
  preparationCompletedAt: null,
  actualPreparationTime: null,
  preparationNotes: 'Modifier preparation in progress',
  qualityCheckStatus: 'pending'
}))
.state('completed', (modifier) => {
  const startTime = DateTime.now().minus({ minutes: faker.number.int({ min: 2, max: 8 }) })
  const prepTime = faker.number.float({ min: 1.0, max: 6.0, fractionDigits: 1 })
  const completedTime = startTime.plus({ minutes: prepTime })
  
  return {
    status: 'completed',
    preparedByStaffId: ulid().toLowerCase(),
    preparationStartedAt: startTime,
    preparationCompletedAt: completedTime,
    actualPreparationTime: prepTime,
    preparationNotes: 'Modifier completed successfully',
    qualityCheckStatus: 'passed',
    qualityCheckedBy: ulid().toLowerCase(),
    qualityCheckedAt: completedTime,
    customerApproved: true,
    efficiencyScore: faker.number.float({ min: 3.5, max: 5.0, fractionDigits: 2 })
  }
})
.state('skipped', (modifier) => ({
  status: 'skipped',
  preparationNotes: faker.helpers.arrayElement([
    'Customer requested to skip',
    'Ingredient not available',
    'Time constraints',
    'Already included in base item'
  ]),
  customerApproved: true
}))
.state('failed', (modifier) => ({
  status: 'failed',
  preparedByStaffId: ulid().toLowerCase(),
  preparationStartedAt: DateTime.now().minus({ minutes: faker.number.int({ min: 5, max: 15 }) }),
  failureReason: faker.helpers.arrayElement([
    'Equipment malfunction',
    'Ingredient quality issue',
    'Staff error',
    'Time exceeded'
  ]),
  preparationAttempts: faker.number.int({ min: 2, max: 3 }),
  qualityCheckStatus: 'failed',
  efficiencyScore: faker.number.float({ min: 1.0, max: 2.5, fractionDigits: 2 })
}))
.state('complex', (modifier) => ({
  complexityLevel: faker.number.int({ min: 4, max: 5 }),
  requiresSpecialSkill: true,
  preparationSteps: {
    steps: [
      'Prepare specialized ingredients',
      'Apply advanced technique',
      'Temperature control',
      'Precision timing',
      'Quality verification',
      'Final presentation'
    ],
    currentStep: faker.number.int({ min: 1, max: 6 }),
    estimatedTimePerStep: faker.number.float({ min: 1.0, max: 3.0, fractionDigits: 1 })
  },
  specialInstructions: 'Requires experienced staff member with specialized training'
}))
.state('allergenSensitive', (modifier) => ({
  affectsAllergens: true,
  requiresSpecialSkill: true,
  specialInstructions: 'ALLERGEN ALERT: Use dedicated equipment and follow cross-contamination protocols',
  qualityCheckStatus: 'pending',
  preparationSteps: {
    steps: [
      'Sanitize equipment',
      'Verify allergen-free ingredients',
      'Apply modification',
      'Final allergen check'
    ],
    currentStep: 1,
    estimatedTimePerStep: 1.5
  }
}))
.state('highValue', (modifier) => ({
  priceAtTimeOfOrder: faker.number.float({ min: 10.0, max: 25.0, fractionDigits: 2 }),
  complexityLevel: faker.number.int({ min: 3, max: 5 }),
  requiresSpecialSkill: true,
  qualityCheckStatus: 'pending',
  actualCost: faker.number.float({ min: 3.0, max: 8.0, fractionDigits: 2 })
}))
.build()
