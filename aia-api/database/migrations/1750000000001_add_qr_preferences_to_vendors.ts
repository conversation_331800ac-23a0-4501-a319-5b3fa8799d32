import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendors'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // QR Code preferences and settings
      table.json('qr_code_preferences').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('qr_code_preferences')
    })
  }
}
