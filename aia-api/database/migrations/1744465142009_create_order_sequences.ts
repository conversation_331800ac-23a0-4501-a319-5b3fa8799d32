import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'order_sequences'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').nullable()
      table.string('year').notNullable()
      table.integer('sequence').defaultTo(0)
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Composite unique constraint to ensure one sequence per vendor-branch-year
      table.unique(['vendor_id', 'branch_id', 'year'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 