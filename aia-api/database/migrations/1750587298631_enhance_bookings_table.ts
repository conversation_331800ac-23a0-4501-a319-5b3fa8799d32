import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'bookings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      
      // Core booking information
      table.string('order_id').references('id').inTable('orders').onDelete('CASCADE').nullable().index()
      table.string('customer_id').references('id').inTable('users').onDelete('CASCADE').index()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').index()
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE').index()
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE').index()
      
      // Appointment scheduling
      table.timestamp('appointment_start', { useTz: true }).notNullable().index()
      table.timestamp('appointment_end', { useTz: true }).notNullable().index()
      table.integer('duration_minutes').notNullable()
      table.integer('buffer_minutes').notNullable().defaultTo(0)
      
      // Status management
      table.enum('status', [
        'pending',
        'confirmed', 
        'in_progress',
        'completed',
        'cancelled',
        'no_show'
      ]).notNullable().defaultTo('pending').index()
      
      // Resource assignments
      table.json('staff_assignments').nullable() // Array of staff user IDs
      table.json('equipment_reservations').nullable() // Array of equipment/room IDs
      table.json('selected_service_options').nullable() // Selected service options with pricing
      
      // Pricing information
      table.decimal('base_price', 10, 2).notNullable()
      table.decimal('options_total', 10, 2).notNullable().defaultTo(0.00)
      table.decimal('total_price', 10, 2).notNullable()
      
      // Booking metadata
      table.json('booking_notes').nullable() // Customer notes, special requests
      table.json('internal_notes').nullable() // Staff notes, preparation instructions
      table.json('constraints_applied').nullable() // Applied scheduling constraints
      
      // Confirmation and communication
      table.timestamp('confirmed_at', { useTz: true }).nullable()
      table.string('confirmation_code').nullable().unique()
      table.boolean('reminder_sent').notNullable().defaultTo(false)
      table.timestamp('reminder_sent_at', { useTz: true }).nullable()
      
      // Cancellation tracking
      table.timestamp('cancelled_at', { useTz: true }).nullable()
      table.string('cancelled_by').references('id').inTable('users').onDelete('SET NULL').nullable()
      table.text('cancellation_reason').nullable()
      
      // Performance indexes
      table.index(['branch_id', 'appointment_start'])
      table.index(['status', 'appointment_start'])
      table.index(['customer_id', 'status'])
      table.index(['appointment_start', 'appointment_end'])
      
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
