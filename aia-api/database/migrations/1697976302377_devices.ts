import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'devices'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').unique().primary().index()
      table.string('name').notNullable()
      table.jsonb('details').nullable()
      table.string('token').nullable()
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.enum('status', ['Active', 'Inactive', 'Disabled', 'Deleted']).defaultTo('Active')
      table.jsonb('meta').nullable()

      table.unique(['id', 'user_id', 'token'])

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
