import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'order_items'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Status tracking
      table.enum('status', [
        'pending',      // Item is waiting to be prepared
        'preparing',    // Item is currently being prepared
        'ready',        // Item is ready for serving/pickup
        'served',       // Item has been served to customer
        'cancelled',    // Item was cancelled
        'on_hold',      // Item is temporarily on hold
        'delayed'       // Item preparation is delayed
      ]).defaultTo('pending').after('meta')

      // Department assignment
      table.string('department_id').nullable()
        .references('id').inTable('departments')
        .onDelete('SET NULL')
        .after('status')

      // Staff assignment
      table.string('assigned_staff_id').nullable()
        .references('id').inTable('users')
        .onDelete('SET NULL')
        .after('department_id')

      // Timing and preparation tracking
      table.integer('estimated_preparation_time').nullable() // in minutes
        .after('assigned_staff_id')
      table.timestamp('preparation_started_at', { useTz: true }).nullable()
        .after('estimated_preparation_time')
      table.timestamp('preparation_completed_at', { useTz: true }).nullable()
        .after('preparation_started_at')
      table.timestamp('served_at', { useTz: true }).nullable()
        .after('preparation_completed_at')

      // Priority and workflow management
      table.integer('priority_level').defaultTo(1) // 1 = highest priority
        .after('served_at')
      table.boolean('requires_special_attention').defaultTo(false)
        .after('priority_level')
      table.string('special_instructions').nullable()
        .after('requires_special_attention')

      // Preparation notes and communication
      table.json('preparation_notes').nullable() // Staff notes during preparation
        .after('special_instructions')
      table.json('status_history').nullable() // Track status changes with timestamps
        .after('preparation_notes')

      // Quality control and customer satisfaction
      table.enum('quality_check_status', [
        'pending',
        'passed',
        'failed',
        'not_required'
      ]).defaultTo('not_required').after('status_history')

      table.string('quality_checked_by').nullable()
        .references('id').inTable('users')
        .onDelete('SET NULL')
        .after('quality_check_status')

      // Customer feedback and modifications
      table.json('customer_modifications').nullable() // Any customer-requested changes
        .after('quality_checked_by')
      table.text('cancellation_reason').nullable()
        .after('customer_modifications')

      // Performance tracking
      table.decimal('actual_preparation_time', 8, 2).nullable() // in minutes
        .after('cancellation_reason')
      table.integer('preparation_attempts').defaultTo(1) // Number of times item was prepared
        .after('actual_preparation_time')
    })

    // Add indexes for performance optimization
    this.schema.alterTable(this.tableName, (table) => {
      table.index('status')
      table.index('department_id')
      table.index('assigned_staff_id')
      table.index('priority_level')
      table.index('quality_check_status')
      table.index('preparation_started_at')
      table.index('preparation_completed_at')
      table.index('served_at')
      table.index(['status', 'department_id'])
      table.index(['status', 'assigned_staff_id'])
      table.index(['department_id', 'status'])
      table.index(['assigned_staff_id', 'status'])
      table.index(['order_id', 'status'])
      table.index(['priority_level', 'status'])
      table.index(['requires_special_attention', 'status'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Drop indexes first
      table.dropIndex(['requires_special_attention', 'status'])
      table.dropIndex(['priority_level', 'status'])
      table.dropIndex(['order_id', 'status'])
      table.dropIndex(['assigned_staff_id', 'status'])
      table.dropIndex(['department_id', 'status'])
      table.dropIndex(['status', 'assigned_staff_id'])
      table.dropIndex(['status', 'department_id'])
      table.dropIndex('served_at')
      table.dropIndex('preparation_completed_at')
      table.dropIndex('preparation_started_at')
      table.dropIndex('quality_check_status')
      table.dropIndex('priority_level')
      table.dropIndex('assigned_staff_id')
      table.dropIndex('department_id')
      table.dropIndex('status')

      // Drop columns
      table.dropColumn('preparation_attempts')
      table.dropColumn('actual_preparation_time')
      table.dropColumn('cancellation_reason')
      table.dropColumn('customer_modifications')
      table.dropColumn('quality_checked_by')
      table.dropColumn('quality_check_status')
      table.dropColumn('status_history')
      table.dropColumn('preparation_notes')
      table.dropColumn('special_instructions')
      table.dropColumn('requires_special_attention')
      table.dropColumn('priority_level')
      table.dropColumn('served_at')
      table.dropColumn('preparation_completed_at')
      table.dropColumn('preparation_started_at')
      table.dropColumn('estimated_preparation_time')
      table.dropColumn('assigned_staff_id')
      table.dropColumn('department_id')
      table.dropColumn('status')
    })
  }
}
