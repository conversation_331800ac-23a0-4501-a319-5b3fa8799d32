import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'order_items'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add 'placed' status to the existing enum
      table.dropColumn('status')
    })

    this.schema.alterTable(this.tableName, (table) => {
      table.enum('status', [
        'pending',      // Item is waiting to be placed by waiter
        'placed',       // Item has been placed to POS by waiter
        'preparing',    // Item is currently being prepared (backward compatibility)
        'ready',        // Item is ready for serving/pickup
        'served',       // Item has been served to customer
        'cancelled',    // Item was cancelled
        'on_hold',      // Item is temporarily on hold
        'delayed'       // Item preparation is delayed
      ]).defaultTo('pending').after('meta')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove 'placed' status and revert to original enum
      table.dropColumn('status')
    })

    this.schema.alterTable(this.tableName, (table) => {
      table.enum('status', [
        'pending',      // Item is waiting to be prepared
        'preparing',    // Item is currently being prepared
        'ready',        // Item is ready for serving/pickup
        'served',       // Item has been served to customer
        'cancelled',    // Item was cancelled
        'on_hold',      // Item is temporarily on hold
        'delayed'       // Item preparation is delayed
      ]).defaultTo('pending').after('meta')
    })
  }
}
