import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_billing_configs'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Basic info
      table.string('name').notNullable()
      table.string('code').notNullable().unique()
      table.text('description').nullable()
      
      // Pricing
      table.decimal('base_price', 10, 2).notNullable().defaultTo(0)
      table.string('currency').notNullable().defaultTo('KES')
      table.string('billing_type').notNullable()
      
      // Status
      table.boolean('is_active').notNullable().defaultTo(true)
      
      // Configuration
      table.jsonb('meta').notNullable().defaultTo('{}')
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Indexes
      table.index(['code'])
      table.index(['billing_type'])
      table.index(['is_active'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 