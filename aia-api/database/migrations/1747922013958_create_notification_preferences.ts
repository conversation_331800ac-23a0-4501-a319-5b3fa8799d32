import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'notification_preferences'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE').nullable()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').nullable()
      table.string('type').notNullable() // 'delivery', 'system', etc.
      table.json('channels').notNullable() // ['email', 'sms', 'push', etc.]
      table.json('preferences').notNullable() // { email: true, sms: true, push: true }
      table.json('meta').nullable()

      // Add indexes
      table.index('user_id')
      table.index('vendor_id')
      table.index('type')
      table.unique(['user_id', 'vendor_id', 'type'])

      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 