import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'notification_billing_tiers'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Basic info
      table.string('name').notNullable()
      table.string('code').notNullable().unique()
      table.text('description').nullable()
      
      // Pricing
      table.decimal('base_price', 10, 2).notNullable()
      table.string('currency').notNullable().defaultTo('KES')
      
      // Usage limits
      table.integer('monthly_limit').notNullable()
      table.decimal('overage_rate', 10, 2).notNullable()
      
      // Channel-specific rates
      table.decimal('sms_rate', 10, 2).notNullable()
      table.decimal('email_rate', 10, 2).notNullable()
      table.decimal('push_rate', 10, 2).notNullable()
      
      // Status and metadata
      table.boolean('is_active').notNullable().defaultTo(true)
      table.jsonb('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 