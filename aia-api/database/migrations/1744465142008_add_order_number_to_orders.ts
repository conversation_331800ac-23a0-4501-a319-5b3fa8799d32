import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    const hasColumn = await this.schema.hasColumn(this.tableName, 'order_number')
    if (!hasColumn) {
      this.schema.alterTable(this.tableName, (table) => {
        table.string('order_number').unique().nullable()
      })
    }
  }

  public async down() {
    const hasColumn = await this.schema.hasColumn(this.tableName, 'order_number')
    if (hasColumn) {
      this.schema.alterTable(this.tableName, (table) => {
        table.dropColumn('order_number')
      })
    }
  }
} 