import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'department_category_mappings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('department_id').notNullable()
        .references('id').inTable('departments')
        .onDelete('CASCADE')
      table.string('product_category_id').notNullable()
        .references('id').inTable('product_categories')
        .onDelete('CASCADE')
      table.integer('priority').defaultTo(1)
        .comment('Priority for assignment when multiple departments match')
      table.boolean('active').defaultTo(true)
        .comment('Whether this mapping is currently active')
      table.json('conditions').nullable()
        .comment('Additional conditions for assignment (e.g., time-based, product-specific)')
      table.json('meta').nullable()
        .comment('Additional metadata for the mapping')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Indexes for performance
      table.index(['department_id'])
      table.index(['product_category_id'])
      table.index(['active'])
      table.index(['priority'])
      
      // Unique constraint to prevent duplicate mappings
      table.unique(['department_id', 'product_category_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
