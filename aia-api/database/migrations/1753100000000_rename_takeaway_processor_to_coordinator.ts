import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'roles'

  public async up() {
    // Rename takeaway_processor role to takeaway_coordinator to reflect unified scope
    await this.db.rawQuery(`
      UPDATE roles 
      SET name = 'takeaway_coordinator',
          updated_at = NOW()
      WHERE name = 'takeaway_processor';
    `)

    console.log('✅ Renamed takeaway_processor role to takeaway_coordinator')
  }

  public async down() {
    // Rollback: rename back to takeaway_processor
    await this.db.rawQuery(`
      UPDATE roles 
      SET name = 'takeaway_processor',
          updated_at = NOW()
      WHERE name = 'takeaway_coordinator';
    `)

    console.log('⏪ Rolled back takeaway_coordinator role to takeaway_processor')
  }
}
