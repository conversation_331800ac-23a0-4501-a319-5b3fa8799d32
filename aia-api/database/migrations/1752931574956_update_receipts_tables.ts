import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'receipts'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add new columns for enhanced receipt functionality
      table.string('file_path').nullable()
      table.string('file_url').nullable()
      table.enum('status', ['pending', 'generated', 'delivered', 'failed']).defaultTo('pending')
      table.enum('delivery_method', ['email', 'sms', 'download']).nullable()
      table.timestamp('delivered_at', { useTz: true }).nullable()
      table.json('metadata').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove the added columns
      table.dropColumn('file_path')
      table.dropColumn('file_url')
      table.dropColumn('status')
      table.dropColumn('delivery_method')
      table.dropColumn('delivered_at')
      table.dropColumn('metadata')
    })
  }
}
