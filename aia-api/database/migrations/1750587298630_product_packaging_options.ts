import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'product_packaging_options'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE')
      table.string('packaging_option_id').references('id').inTable('packaging_options').onDelete('CASCADE')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Ensure unique combinations and add indexes
      table.unique(['product_id', 'packaging_option_id'])
      table.index('product_id')
      table.index('packaging_option_id')
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
