import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'table_qr_codes'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      // Primary key
      table.string('id').primary()
      
      // Foreign key relationships
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE')
      table.string('section_id').references('id').inTable('sections').onDelete('CASCADE')
      table.string('lot_id').references('id').inTable('lots').onDelete('CASCADE')
      
      // Table information
      table.string('table_number').notNullable() // Human-readable table number
      table.text('qr_code_url').notNullable() // The URL encoded in QR code
      table.text('qr_code_image').nullable() // Base64 encoded QR code image
      
      // QR Code management
      table.boolean('is_active').defaultTo(true).index()
      table.timestamp('generated_at', { useTz: true }).notNullable()
      table.timestamp('last_scanned_at', { useTz: true }).nullable()
      table.integer('scan_count').defaultTo(0)
      
      // Additional metadata
      table.json('meta').nullable() // Store additional QR code metadata
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index(['vendor_id', 'is_active'])
      table.index(['branch_id', 'is_active'])
      table.index(['section_id', 'is_active'])
      table.index(['lot_id'])
      table.index(['table_number', 'vendor_id'])
      
      // Unique constraint: one QR code per table
      table.unique(['lot_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
