import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'staff'

  public async up() {
    // Enhance existing staff table with hierarchical relationships
    this.schema.alterTable(this.tableName, (table) => {
      // Hierarchical relationships - supervisor_id will reference users.id instead
      table
        .string('supervisor_id')
        .nullable()
        .references('id')
        .inTable('users')
        .onDelete('SET NULL')
      table
        .string('department_id')
        .nullable()
        .references('id')
        .inTable('departments')
        .onDelete('SET NULL')

      // Access level and permissions
      table
        .enum('access_level', [
          'staff', // Level 1: Individual staff member
          'supervisor', // Level 2: Department supervisor
          'manager', // Level 3: Branch manager
          'owner', // Level 4: Vendor owner/manager
        ])
        .defaultTo('staff')

      // Supervision capabilities
      table.boolean('can_supervise_department').defaultTo(false)
      table.boolean('can_manage_branch').defaultTo(false)
      table.boolean('can_manage_vendor').defaultTo(false)

      // Team and department assignments
      table.json('supervised_departments').nullable() // Array of department IDs this person supervises
      table.json('managed_branches').nullable() // Array of branch IDs this person manages

      // Hierarchy metadata
      table.integer('hierarchy_level').defaultTo(1) // 1=staff, 2=supervisor, 3=manager, 4=owner, 5=admin
      table.string('reporting_chain').nullable() // JSON path of supervisor hierarchy

      // Performance and access tracking
      table.timestamp('last_access_at').nullable()
      table.json('access_permissions').nullable() // Custom permissions override

      // Indexes for performance
      table.index(['supervisor_id'])
      table.index(['department_id'])
      table.index(['access_level'])
      table.index(['hierarchy_level'])
      table.index(['branch_id', 'access_level'])
      table.index(['vendor_id', 'access_level'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('supervisor_id')
      table.dropColumn('department_id')
      table.dropColumn('access_level')
      table.dropColumn('can_supervise_department')
      table.dropColumn('can_manage_branch')
      table.dropColumn('can_manage_vendor')
      table.dropColumn('supervised_departments')
      table.dropColumn('managed_branches')
      table.dropColumn('hierarchy_level')
      table.dropColumn('reporting_chain')
      table.dropColumn('last_access_at')
      table.dropColumn('access_permissions')
    })
  }
}
