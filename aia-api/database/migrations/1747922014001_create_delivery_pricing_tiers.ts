import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'delivery_pricing_tiers'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('delivery_pricing_config_id').notNullable()
      table.string('tier_name').notNullable() // e.g., "Zone 1", "Short Distance", "Long Distance"
      
      // Distance range for this tier
      table.decimal('distance_from_km', 8, 2).notNullable() // Start of distance range
      table.decimal('distance_to_km', 8, 2).nullable() // End of distance range (null = unlimited)
      
      // Pricing for this tier
      table.decimal('base_price', 10, 2).notNullable() // Base price for this tier
      table.decimal('price_per_km', 10, 2).notNullable() // Rate per km in this tier
      table.decimal('flat_rate', 10, 2).nullable() // Optional flat rate (overrides base + per km)
      
      // Time estimates for this tier
      table.integer('base_time_minutes').notNullable()
      table.decimal('time_per_km_minutes', 5, 2).notNullable()
      
      // Tier-specific constraints
      table.decimal('minimum_fee', 10, 2).nullable()
      table.decimal('maximum_fee', 10, 2).nullable()
      table.boolean('is_active').defaultTo(true)
      table.integer('priority').defaultTo(1) // Order of tier evaluation
      
      table.timestamps(true, true)

      // Foreign key constraint
      table.foreign('delivery_pricing_config_id').references('id').inTable('delivery_pricing_configs').onDelete('CASCADE')
      
      // Indexes
      table.index(['delivery_pricing_config_id', 'is_active'])
      table.index(['distance_from_km', 'distance_to_km'])
      table.index(['priority'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
