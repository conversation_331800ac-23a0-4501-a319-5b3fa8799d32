import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'payments'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('invoice_id').references('id').inTable('invoices').onDelete('CASCADE')
      table.string('ref').notNullable()
      table.string('req').nullable()
      table.string('receipt').nullable()
      table.string('payer').nullable()
      table.string('currency').nullable()
      table.decimal('amount', 10, 2)
      table.string('method').notNullable()
      table
        .enum('status', ['Pending', 'Success', 'Failed', 'Cancelled', 'Reversed'])
        .defaultTo('Pending')

      table.jsonb('meta').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
