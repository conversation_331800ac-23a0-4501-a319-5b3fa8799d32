import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'packaging_options'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('name').notNullable()
      table.text('description').nullable()
      table.decimal('price', 10, 2).notNullable()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.boolean('active').defaultTo(true)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Add indexes for better performance
      table.index('vendor_id')
      table.index('active')
      table.index(['vendor_id', 'active'])
      table.index('name')
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
