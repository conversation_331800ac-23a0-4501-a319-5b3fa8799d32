import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Delivery-specific fields
      table.string('delivery_vendor_id').references('id').inTable('vendors').onDelete('SET NULL')
      table.string('delivery_branch_id').references('id').inTable('branches').onDelete('SET NULL')
      table.enum('delivery_status', [
        'pending_assignment',
        'assigned',
        'picked_up',
        'in_transit',
        'delivered',
        'failed',
        'cancelled'
      ]).defaultTo('pending_assignment')
      table.json('delivery_location').nullable() // For delivery address
      table.json('pickup_location').nullable() // For pickup address
      table.float('delivery_distance').nullable() // In kilometers
      table.float('delivery_fee').nullable()
      table.float('estimated_delivery_time').nullable() // In minutes
      table.timestamp('actual_delivery_time', { useTz: true }).nullable()
      table.timestamp('pickup_time', { useTz: true }).nullable()
      table.json('delivery_notes').nullable()
      table.json('tracking_data').nullable() // For real-time tracking
      table.string('tracking_code').nullable()
      table.boolean('requires_signature').defaultTo(false)
      table.string('signature_image').nullable()
      table.json('delivery_meta').nullable() // For additional delivery-specific data

      // Add indexes
      table.index('delivery_vendor_id')
      table.index('delivery_branch_id')
      table.index('delivery_status')
      table.index('tracking_code')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove indexes first
      table.dropIndex('delivery_vendor_id')
      table.dropIndex('delivery_branch_id')
      table.dropIndex('delivery_status')
      table.dropIndex('tracking_code')

      // Then remove columns
      table.dropColumn('delivery_vendor_id')
      table.dropColumn('delivery_branch_id')
      table.dropColumn('delivery_status')
      table.dropColumn('delivery_location')
      table.dropColumn('pickup_location')
      table.dropColumn('delivery_distance')
      table.dropColumn('delivery_fee')
      table.dropColumn('estimated_delivery_time')
      table.dropColumn('actual_delivery_time')
      table.dropColumn('pickup_time')
      table.dropColumn('delivery_notes')
      table.dropColumn('tracking_data')
      table.dropColumn('tracking_code')
      table.dropColumn('requires_signature')
      table.dropColumn('signature_image')
      table.dropColumn('delivery_meta')
    })
  }
}
