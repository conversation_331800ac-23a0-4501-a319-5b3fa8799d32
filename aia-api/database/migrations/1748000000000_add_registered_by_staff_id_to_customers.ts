import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'customers'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add registered_by_staff_id column
      table.string('registered_by_staff_id').references('id').inTable('users').onDelete('SET NULL').nullable()
      
      // Add index for performance
      table.index('registered_by_staff_id')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove index first
      table.dropIndex('registered_by_staff_id')
      
      // Drop the column
      table.dropColumn('registered_by_staff_id')
    })
  }
}
