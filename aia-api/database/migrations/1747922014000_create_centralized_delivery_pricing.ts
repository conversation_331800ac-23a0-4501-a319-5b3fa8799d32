import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'delivery_pricing_configs'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('name').notNullable() // e.g., "Standard Delivery", "Express Delivery"
      table.string('description').nullable()
      table.string('vehicle_type').notNullable() // motorcycle, car, bicycle, van
      table.boolean('is_active').defaultTo(true)
      table.boolean('is_default').defaultTo(false) // One config should be default
      table.integer('priority').defaultTo(1) // Display order priority

      // Base pricing structure
      table.decimal('base_price', 10, 2).notNullable() // Base delivery fee
      table.decimal('price_per_km', 10, 2).notNullable() // Rate per kilometer
      table.decimal('minimum_fee', 10, 2).nullable() // Minimum delivery fee
      table.decimal('maximum_fee', 10, 2).nullable() // Maximum delivery fee cap

      // Distance-based constraints
      table.decimal('max_distance_km', 8, 2).nullable() // Maximum delivery distance
      table.decimal('min_distance_km', 8, 2).defaultTo(0) // Minimum distance for this option

      // Order value constraints
      table.decimal('minimum_order_value', 10, 2).nullable() // Minimum order value required
      table.decimal('free_delivery_threshold', 10, 2).nullable() // Free delivery above this amount

      // Time estimates
      table.integer('base_time_minutes').notNullable() // Base delivery time
      table.decimal('time_per_km_minutes', 5, 2).notNullable() // Additional time per km
      table.integer('preparation_buffer_minutes').defaultTo(0) // Extra preparation time

      // Availability settings
      table.json('working_hours').nullable() // When this delivery option is available
      table.json('blackout_dates').nullable() // Dates when this option is unavailable
      table.boolean('available_on_holidays').defaultTo(true)

      // Surge pricing (future enhancement)
      table.json('surge_multipliers').nullable() // Peak hour multipliers
      table.json('weather_adjustments').nullable() // Weather-based adjustments

      // Admin metadata
      table.string('created_by_admin_id').nullable()
      table.string('updated_by_admin_id').nullable()
      table.text('admin_notes').nullable()

      table.timestamps(true, true)

      // Indexes for performance
      table.index(['is_active', 'priority'])
      table.index(['vehicle_type', 'is_active'])
      table.index(['is_default'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
