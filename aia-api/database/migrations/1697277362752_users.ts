//users table
import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('idpass').nullable().unique()
      table.string('title').nullable()
      table.string('first_name')
      table.string('last_name').nullable()
      table.enum('gender', ['Male', 'Female', 'Other']).nullable()
      table.date('dob').nullable()
      table.string('otp').nullable()
      table.string('email', 255).notNullable().unique()
      table.string('phone', 13).nullable().unique()
      table.string('username', 255).nullable().unique()
      table.string('password', 180).notNullable()
      table.string('remember_me_token').nullable()
      table.text('details').nullable()
      table.json('location').nullable()
      table.geometry('geom').nullable()
      table.boolean('online').defaultTo(0)
      table.json('avatar').nullable()
      table.json('meta').nullable()
      table.enum('status', ['Active', 'Inactive', 'Suspended']).defaultTo('Active')

      /**
       * Uses timestampz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('email_verified_at', { useTz: true }).nullable()
      table.timestamp('phone_verified_at', { useTz: true }).nullable()

      table.timestamp('created_at', { useTz: true }).notNullable()
      table.timestamp('updated_at', { useTz: true }).notNullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
