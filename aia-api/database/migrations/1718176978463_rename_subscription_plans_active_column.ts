import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'subscription_plans'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.renameColumn('active', 'is_active')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.renameColumn('is_active', 'active')
    })
  }
} 