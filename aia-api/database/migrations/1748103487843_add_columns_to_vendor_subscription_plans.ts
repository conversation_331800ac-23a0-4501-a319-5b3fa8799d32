import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendor_subscription_plans'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add name and description
      table.string('name').notNullable()
      table.text('description').nullable()
      
      // Add pricing details
      table.decimal('price', 10, 2).notNullable()
      table.string('currency').notNullable().defaultTo('KES')
      
      // Add duration details
      table.integer('duration').notNullable()
      table.string('duration_type').notNullable() // 'day', 'week', 'month', 'year'
      
      // Add features and limits
      table.jsonb('features').nullable()
      table.integer('order_limit').nullable()
      table.integer('notification_limit').nullable()
      
      // Add status
      table.boolean('is_active').notNullable().defaultTo(true)
      
      // Add metadata
      table.jsonb('meta').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('name')
      table.dropColumn('description')
      table.dropColumn('price')
      table.dropColumn('currency')
      table.dropColumn('duration')
      table.dropColumn('duration_type')
      table.dropColumn('features')
      table.dropColumn('order_limit')
      table.dropColumn('notification_limit')
      table.dropColumn('is_active')
      table.dropColumn('meta')
    })
  }
}
