import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'delivery_provider_profiles'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').unique().references('id').inTable('vendors').onDelete('CASCADE')
      
      // Verification status and workflow
      table.enum('verification_status', [
        'pending',
        'under_review', 
        'verified',
        'rejected',
        'suspended'
      ]).defaultTo('pending')
      
      table.timestamp('application_date', { useTz: true }).defaultTo(this.now())
      table.timestamp('verified_at', { useTz: true }).nullable()
      table.text('verification_notes').nullable()
      table.string('verifier_admin_id').references('id').inTable('users').nullable()
      
      // Delivery capabilities (structured data instead of JSON)
      table.json('vehicle_types').nullable() // ['motorcycle', 'bicycle', 'car', 'van', 'truck']
      table.integer('max_delivery_distance').nullable() // in kilometers
      table.integer('max_concurrent_orders').nullable()
      table.decimal('max_order_weight', 8, 2).nullable() // in kg
      table.decimal('max_order_volume', 8, 2).nullable() // in cubic meters
      
      // Operational settings (structured data)
      table.json('working_hours').nullable() // {monday: {start: '09:00', end: '18:00'}, ...}
      table.json('break_times').nullable() // [{start: '12:00', end: '13:00'}, ...]
      table.boolean('auto_accept_orders').defaultTo(false)
      table.integer('max_orders_per_day').nullable()
      table.integer('preparation_buffer_minutes').defaultTo(15)
      
      // Compliance and documentation
      table.json('required_documents').nullable() // {driver_license: {status: 'verified', expires_at: '2025-12-31'}, ...}
      table.json('insurance_details').nullable() // {provider: 'ABC Insurance', policy_number: '123', expires_at: '2025-12-31'}
      table.json('vehicle_documentation').nullable() // {registration: {...}, inspection: {...}}
      
      // Performance metrics
      table.decimal('average_rating', 3, 2).nullable()
      table.integer('total_deliveries').defaultTo(0)
      table.integer('successful_deliveries').defaultTo(0)
      table.decimal('completion_rate', 5, 2).nullable() // percentage
      table.decimal('average_delivery_time', 8, 2).nullable() // in minutes
      table.integer('customer_complaints').defaultTo(0)
      
      // Status management
      table.text('suspension_reason').nullable()
      table.timestamp('suspended_at', { useTz: true }).nullable()
      table.boolean('active').defaultTo(true)
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })

    // Add indexes for efficient querying
    this.schema.alterTable(this.tableName, (table) => {
      table.index('vendor_id')
      table.index('verification_status')
      table.index('active')
      table.index(['verification_status', 'active']) // Composite index for filtering eligible vendors
      table.index('verifier_admin_id')
      table.index('application_date')
      table.index('verified_at')
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
