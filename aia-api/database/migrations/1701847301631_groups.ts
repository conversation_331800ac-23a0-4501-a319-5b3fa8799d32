import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'groups'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').nullable()
      table.string('name').notNullable()
      table.string('details').nullable()
      table.json('image').nullable()
      table.boolean('active').defaultTo(1)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
