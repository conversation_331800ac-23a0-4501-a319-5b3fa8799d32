import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'departments'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      // Primary key - using string ID for flexibility
      table.string('id').primary()
      
      // Foreign key relationships
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE')
      
      // Department information
      table.string('name').notNullable() // Kitchen, Bar, Pastry, etc.
      table.text('description').nullable()
      table.boolean('active').defaultTo(true)
      
      // Workflow and timing settings
      table.integer('average_preparation_time').nullable() // in minutes
      table.integer('max_concurrent_orders').nullable().defaultTo(10)
      table.integer('priority_level').defaultTo(1) // 1 = highest priority
      
      // Working hours and scheduling
      table.json('working_hours').nullable() // Store schedule per day
      table.json('break_times').nullable() // Store break schedules
      
      // Department configuration
      table.json('workflow_settings').nullable() // Custom workflow rules
      table.json('notification_settings').nullable() // Alert preferences
      table.json('meta').nullable() // Additional metadata
      
      // Performance tracking
      table.decimal('efficiency_rating', 3, 2).nullable() // 0.00 to 5.00
      table.integer('total_orders_completed').defaultTo(0)
      table.decimal('average_completion_time', 8, 2).nullable() // in minutes
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index('vendor_id')
      table.index('branch_id')
      table.index('active')
      table.index('priority_level')
      table.index(['vendor_id', 'branch_id'])
      table.index(['vendor_id', 'active'])
      table.index(['branch_id', 'active'])
      
      // Unique constraint to prevent duplicate department names per branch
      table.unique(['branch_id', 'name'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
