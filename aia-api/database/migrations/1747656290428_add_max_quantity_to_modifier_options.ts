import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'modifier_options'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('max_quantity').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('max_quantity')
    })
  }
}
