import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendors'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('user_id')
      table.index('service_id')
      table.index('name')
      table.index('slug')
      table.index('active')
      table.index('featured')
      table.index('created_at')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove indexes
      table.dropIndex('user_id')
      table.dropIndex('service_id')
      table.dropIndex('name')
      table.dropIndex('slug')
      table.dropIndex('active')
      table.dropIndex('featured')
      table.dropIndex('created_at')
    })
  }
} 