import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class AddItemsColumnToOrders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add items column to support direct order creation
      table.json('items').nullable().comment('Direct items storage for unified order system')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('items')
    })
  }
}
