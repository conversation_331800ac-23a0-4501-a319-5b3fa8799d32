import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class AddTakeawayProcessor<PERSON>ole extends BaseSchema {
  protected tableName = 'roles'

  public async up() {
    // Check if takeaway_processor role already exists to ensure idempotency
    const existingRole = await this.db
      .from(this.tableName)
      .where('name', 'takeaway_processor')
      .first()

    if (!existingRole) {
      // Insert takeaway_processor role
      await this.db.table(this.tableName).insert({
        name: 'takeaway_processor',
        created_at: this.now(),
        updated_at: this.now(),
      })

      console.log('✅ Created takeaway_processor role')
    } else {
      console.log('ℹ️  takeaway_processor role already exists, skipping creation')
    }

    // Verify the role was created/exists
    const verifyRole = await this.db
      .from(this.tableName)
      .where('name', 'takeaway_processor')
      .first()

    if (verifyRole) {
      console.log(`✅ takeaway_processor role verified (ID: ${verifyRole.id})`)
    } else {
      throw new Error('❌ Failed to create or verify takeaway_processor role')
    }

    // Optional: Create other essential staff roles if they don't exist
    const essentialRoles = [
      'Waiter',
      'Chef', 
      'Bartender',
      'Cashier',
      'Rider',
      'manager',
      'barista'
    ]

    for (const roleName of essentialRoles) {
      const existingEssentialRole = await this.db
        .from(this.tableName)
        .where('name', roleName)
        .first()

      if (!existingEssentialRole) {
        await this.db.table(this.tableName).insert({
          name: roleName,
          created_at: this.now(),
          updated_at: this.now(),
        })
        console.log(`✅ Created essential role: ${roleName}`)
      } else {
        console.log(`ℹ️  Essential role ${roleName} already exists`)
      }
    }

    // Final verification of all roles
    const allRoles = await this.db
      .from(this.tableName)
      .whereIn('name', ['takeaway_processor', ...essentialRoles])
      .select('id', 'name')

    console.log('📋 Final role verification:')
    allRoles.forEach(role => {
      console.log(`   - ${role.name} (ID: ${role.id})`)
    })
  }

  public async down() {
    // Remove takeaway_processor role and any users assigned to it
    console.log('🔄 Rolling back takeaway_processor role...')

    // First, check if the role exists
    const roleToDelete = await this.db
      .from(this.tableName)
      .where('name', 'takeaway_processor')
      .first()

    if (roleToDelete) {
      // Remove all user assignments for this role
      const userRoleAssignments = await this.db
        .from('user_roles')
        .where('role_id', roleToDelete.id)
        .select('model_id')

      if (userRoleAssignments.length > 0) {
        await this.db
          .from('user_roles')
          .where('role_id', roleToDelete.id)
          .delete()

        console.log(`🗑️  Removed ${userRoleAssignments.length} user assignments for takeaway_processor role`)
      }

      // Remove the role itself
      await this.db
        .from(this.tableName)
        .where('name', 'takeaway_processor')
        .delete()

      console.log('✅ Removed takeaway_processor role')
    } else {
      console.log('ℹ️  takeaway_processor role does not exist, nothing to remove')
    }

    // Verify removal
    const verifyRemoval = await this.db
      .from(this.tableName)
      .where('name', 'takeaway_processor')
      .first()

    if (!verifyRemoval) {
      console.log('✅ takeaway_processor role removal verified')
    } else {
      throw new Error('❌ Failed to remove takeaway_processor role')
    }
  }
}
