import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'receipts'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Increase the length of file_path and file_url columns
      table.text('file_path').alter()
      table.text('file_url').alter()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Revert back to varchar(255)
      table.string('file_path', 255).alter()
      table.string('file_url', 255).alter()
    })
  }
}
