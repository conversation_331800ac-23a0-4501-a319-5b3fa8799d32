import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('email')
      table.index('phone')
      table.index('status')
      table.index('first_name')
      table.index('last_name')
      table.index('created_at')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove indexes
      table.dropIndex('email')
      table.dropIndex('phone')
      table.dropIndex('status')
      table.dropIndex('first_name')
      table.dropIndex('last_name')
      table.dropIndex('created_at')
    })
  }
} 