import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'product_modifiers'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE')
      table.string('modifier_option_id').references('id').inTable('modifier_options').onDelete('CASCADE')
      table.decimal('price_adjustment_override', 10, 2).nullable()
      table.boolean('is_default').defaultTo(false)
      table.integer('sort_order').defaultTo(0)
      table.unique(['product_id', 'modifier_option_id'])
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 