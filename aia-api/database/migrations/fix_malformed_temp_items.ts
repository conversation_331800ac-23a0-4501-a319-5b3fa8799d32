import BaseSchema from '@ioc:Adonis/Lucid/Schema'
import Database from '@ioc:Adonis/Lucid/Database'

export default class FixMalformedTempItems extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    console.log('🔧 Starting malformed temp_items cleanup...')

    // Find orders with malformed temp_items
    const ordersWithMalformedTempItems = await Database.rawQuery(`
      SELECT id, meta
      FROM orders 
      WHERE meta->>'temp_items' IS NOT NULL
        AND (
          meta->'temp_items' ? 'productId' OR
          meta->'temp_items' ? 'quantity' OR  
          meta->'temp_items' ? 'price'
        )
    `)

    console.log(`Found ${ordersWithMalformedTempItems.rows.length} orders with malformed temp_items`)

    let fixedCount = 0
    let removedCount = 0

    for (const row of ordersWithMalformedTempItems.rows) {
      const orderId = row.id
      const meta = row.meta
      const tempItems = meta.temp_items || {}

      console.log(`Processing order ${orderId}...`)
      console.log(`Original temp_items:`, JSON.stringify(tempItems, null, 2))

      // Try to extract valid product IDs and quantities from malformed data
      const fixedTempItems = {}
      let hasValidItems = false

      for (const [key, value] of Object.entries(tempItems)) {
        // Skip obviously invalid keys
        if (key === 'productId' || key === 'quantity' || key === 'price') {
          console.log(`Skipping invalid key: ${key}`)
          continue
        }

        // Only process valid product IDs
        if (key.length > 10 && key.startsWith('01')) {
          let quantity = 1

          // Extract quantity from various formats
          if (typeof value === 'number') {
            quantity = value
          } else if (typeof value === 'object' && value !== null) {
            if (typeof value.quantity === 'number') {
              quantity = value.quantity
            } else if (typeof value.quantity === 'object' && value.quantity?.quantity) {
              quantity = value.quantity.quantity
            }
          }

          if (quantity > 0) {
            fixedTempItems[key] = {
              quantity: quantity,
              meta: typeof value === 'object' ? value : {}
            }
            hasValidItems = true
            console.log(`Fixed item: ${key} -> quantity: ${quantity}`)
          }
        }
      }

      // Update the order
      if (hasValidItems) {
        // Update with fixed temp_items
        const updatedMeta = {
          ...meta,
          temp_items: fixedTempItems,
          temp_items_fixed: true,
          temp_items_fixed_at: new Date().toISOString()
        }

        await Database.table('orders')
          .where('id', orderId)
          .update({
            meta: JSON.stringify(updatedMeta)
          })

        fixedCount++
        console.log(`✅ Fixed order ${orderId} with ${Object.keys(fixedTempItems).length} valid items`)
      } else {
        // Remove temp_items entirely if no valid items found
        const updatedMeta = { ...meta }
        delete updatedMeta.temp_items
        updatedMeta.temp_items_removed = true
        updatedMeta.temp_items_removed_at = new Date().toISOString()
        updatedMeta.temp_items_removed_reason = 'No valid items found in malformed temp_items'

        await Database.table('orders')
          .where('id', orderId)
          .update({
            meta: JSON.stringify(updatedMeta)
          })

        removedCount++
        console.log(`🗑️ Removed malformed temp_items from order ${orderId} (no valid items)`)
      }
    }

    console.log(`🎉 Cleanup complete!`)
    console.log(`✅ Fixed: ${fixedCount} orders`)
    console.log(`🗑️ Removed: ${removedCount} orders`)
    console.log(`📊 Total processed: ${fixedCount + removedCount} orders`)
  }

  public async down() {
    console.log('⚠️ This migration cannot be reversed automatically.')
    console.log('Manual intervention required to restore original malformed data.')
  }
}
