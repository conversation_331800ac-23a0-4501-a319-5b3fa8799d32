import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'sections'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE')
      table.string('name').notNullable()
      table.text('details').nullable()
      table.boolean('active').defaultTo(1)
      table.boolean('booked').defaultTo(1)
      table.boolean('featured').defaultTo(0)
      table.boolean('popular').defaultTo(0)
      table.boolean('recommended').defaultTo(0)
      table.boolean('top').defaultTo(0)
      table.integer('sort_order').nullable()
      table.string('image').nullable()
      table.string('banner').nullable()
      table.json('meta').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
