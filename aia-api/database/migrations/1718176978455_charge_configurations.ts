import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'charge_configurations'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Basic info
      table.string('name').notNullable()
      table.string('code').notNullable().unique()
      table.text('description').nullable()
      
      // Charge type and amount
      table.enum('type', ['fixed', 'percentage']).defaultTo('fixed')
      table.decimal('amount', 10, 2).defaultTo(0)
      table.decimal('percentage_rate', 5, 2).nullable()
      
      // Scope and applicability
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').nullable()
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE').nullable()
      table.string('service_id').references('id').inTable('services').onDelete('CASCADE').nullable()
      
      // Conditions
      table.jsonb('conditions').nullable().defaultTo('{}') // e.g., min_order_amount, applicable_products
      table.boolean('is_tax').defaultTo(false)
      table.boolean('is_mandatory').defaultTo(false)
      table.boolean('is_active').defaultTo(true)
      
      // Priority and order
      table.integer('priority').defaultTo(0)
      table.integer('sort_order').defaultTo(0)
      
      // Additional settings
      table.string('currency').nullable()
      table.jsonb('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 