import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'temp_orders'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').nullable()
      table.string('section_id').references('id').inTable('sections').nullable()
      table.string('lot_id').references('id').inTable('lots').nullable()
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.string('staff_id').references('id').inTable('users').onDelete('CASCADE').nullable()
      table
        .enum('action', ['Purchase', 'Reserve', 'Booking', 'Registration', 'Access', 'Process'])
        .defaultTo('Purchase')
      table.enum('type', ['Preorder', 'Instant']).defaultTo('Instant')
      table.enum('payment', ['Free', 'Prepaid', 'Postpaid']).defaultTo('Free')
      table.enum('delivery', ['Takeaway', 'Dinein', 'Delivery', 'Selfpick']).defaultTo('Delivery')
      table
        .enum('status', [
          'Pending',
          'Placed',
          'Processing',
          'Ready',
          'Delivering',
          'Delivered',
          'Completed',
          'Cancelled',
          'Rejected',
        ])
        .defaultTo('Pending')
      table.json('meta').nullable()
      table.json('items').nullable()
      table.string('ref').nullable()
      table.string('currency').nullable()
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('accepted_at', { useTz: true }).nullable()
      table.timestamp('start_at', { useTz: true }).nullable()
      table.timestamp('end_at', { useTz: true }).nullable()
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
