import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'order_item_modifiers'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Status tracking for individual modifiers
      table.enum('status', [
        'pending',      // Modifier is waiting to be applied
        'preparing',    // Modifier is currently being prepared/applied
        'completed',    // Modifier has been successfully applied
        'skipped',      // Modifier was intentionally skipped
        'cancelled',    // Modifier was cancelled
        'failed',       // Modifier preparation failed
        'on_hold'       // Modifier is temporarily on hold
      ]).defaultTo('pending').after('price_at_time_of_order')
      
      // Staff who prepared/applied the modifier
      table.string('prepared_by_staff_id').nullable()
        .references('id').inTable('users')
        .onDelete('SET NULL')
        .after('status')
      
      // Timing tracking for modifier preparation
      table.timestamp('preparation_started_at', { useTz: true }).nullable()
        .after('prepared_by_staff_id')
      table.timestamp('preparation_completed_at', { useTz: true }).nullable()
        .after('preparation_started_at')
      
      // Priority and complexity tracking
      table.integer('complexity_level').defaultTo(1) // 1 = simple, 5 = very complex
        .after('preparation_completed_at')
      table.boolean('requires_special_skill').defaultTo(false)
        .after('complexity_level')
      table.boolean('affects_allergens').defaultTo(false) // Important for allergy tracking
        .after('requires_special_skill')
      
      // Preparation details and notes
      table.text('preparation_notes').nullable() // Staff notes about preparation
        .after('affects_allergens')
      table.text('special_instructions').nullable() // Special customer instructions
        .after('preparation_notes')
      table.json('preparation_steps').nullable() // Step-by-step preparation guide
        .after('special_instructions')
      
      // Quality control and verification
      table.enum('quality_check_status', [
        'pending',
        'passed',
        'failed',
        'not_required'
      ]).defaultTo('not_required').after('preparation_steps')
      
      table.string('quality_checked_by').nullable()
        .references('id').inTable('users')
        .onDelete('SET NULL')
        .after('quality_check_status')
      
      table.timestamp('quality_checked_at', { useTz: true }).nullable()
        .after('quality_checked_by')
      
      // Error handling and retry tracking
      table.text('failure_reason').nullable() // Why preparation failed
        .after('quality_checked_at')
      table.integer('preparation_attempts').defaultTo(1) // Number of attempts
        .after('failure_reason')
      table.json('attempt_history').nullable() // History of all attempts
        .after('preparation_attempts')
      
      // Customer satisfaction and feedback
      table.boolean('customer_approved').nullable() // Customer approved the modifier
        .after('attempt_history')
      table.text('customer_feedback').nullable()
        .after('customer_approved')
      
      // Cost and inventory impact
      table.decimal('actual_cost', 10, 2).nullable() // Actual cost incurred
        .after('customer_feedback')
      table.boolean('affects_inventory').defaultTo(true) // Whether this affects inventory
        .after('actual_cost')
      table.json('inventory_impact').nullable() // Detailed inventory changes
        .after('affects_inventory')
      
      // Performance metrics
      table.decimal('actual_preparation_time', 8, 2).nullable() // in minutes
        .after('inventory_impact')
      table.decimal('efficiency_score', 3, 2).nullable() // 0.00 to 5.00
        .after('actual_preparation_time')
    })
    
    // Add indexes for performance optimization
    this.schema.alterTable(this.tableName, (table) => {
      table.index('status')
      table.index('prepared_by_staff_id')
      table.index('complexity_level')
      table.index('quality_check_status')
      table.index('preparation_started_at')
      table.index('preparation_completed_at')
      table.index('quality_checked_at')
      table.index('requires_special_skill')
      table.index('affects_allergens')
      table.index('customer_approved')
      table.index(['order_item_id', 'status'])
      table.index(['status', 'prepared_by_staff_id'])
      table.index(['status', 'complexity_level'])
      table.index(['prepared_by_staff_id', 'status'])
      table.index(['quality_check_status', 'status'])
      table.index(['affects_allergens', 'status'])
      table.index(['requires_special_skill', 'status'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Drop indexes first
      table.dropIndex(['requires_special_skill', 'status'])
      table.dropIndex(['affects_allergens', 'status'])
      table.dropIndex(['quality_check_status', 'status'])
      table.dropIndex(['prepared_by_staff_id', 'status'])
      table.dropIndex(['status', 'complexity_level'])
      table.dropIndex(['status', 'prepared_by_staff_id'])
      table.dropIndex(['order_item_id', 'status'])
      table.dropIndex('customer_approved')
      table.dropIndex('affects_allergens')
      table.dropIndex('requires_special_skill')
      table.dropIndex('quality_checked_at')
      table.dropIndex('preparation_completed_at')
      table.dropIndex('preparation_started_at')
      table.dropIndex('quality_check_status')
      table.dropIndex('complexity_level')
      table.dropIndex('prepared_by_staff_id')
      table.dropIndex('status')
      
      // Drop columns
      table.dropColumn('efficiency_score')
      table.dropColumn('actual_preparation_time')
      table.dropColumn('inventory_impact')
      table.dropColumn('affects_inventory')
      table.dropColumn('actual_cost')
      table.dropColumn('customer_feedback')
      table.dropColumn('customer_approved')
      table.dropColumn('attempt_history')
      table.dropColumn('preparation_attempts')
      table.dropColumn('failure_reason')
      table.dropColumn('quality_checked_at')
      table.dropColumn('quality_checked_by')
      table.dropColumn('quality_check_status')
      table.dropColumn('preparation_steps')
      table.dropColumn('special_instructions')
      table.dropColumn('preparation_notes')
      table.dropColumn('affects_allergens')
      table.dropColumn('requires_special_skill')
      table.dropColumn('complexity_level')
      table.dropColumn('preparation_completed_at')
      table.dropColumn('preparation_started_at')
      table.dropColumn('prepared_by_staff_id')
      table.dropColumn('status')
    })
  }
}
