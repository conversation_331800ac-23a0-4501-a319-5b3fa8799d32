import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'department_staff'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      // Primary key
      table.increments('id')
      
      // Foreign key relationships
      table.string('department_id').references('id').inTable('departments').onDelete('CASCADE')
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      
      // Staff assignment details
      table.enum('role', [
        'supervisor',    // Department supervisor/manager
        'lead',         // Lead staff member
        'staff',        // Regular staff member
        'trainee',      // Trainee/apprentice
        'substitute'    // Temporary/substitute staff
      ]).defaultTo('staff')
      
      // Assignment status and scheduling
      table.boolean('active').defaultTo(true)
      table.boolean('is_primary_department').defaultTo(false) // Main department for this staff
      table.date('start_date').nullable()
      table.date('end_date').nullable()
      
      // Skill and performance tracking
      table.integer('skill_level').defaultTo(1) // 1-5 skill rating
      table.decimal('performance_rating', 3, 2).nullable() // 0.00 to 5.00
      table.integer('orders_completed').defaultTo(0)
      table.decimal('average_completion_time', 8, 2).nullable() // in minutes
      
      // Availability and scheduling
      table.json('availability_schedule').nullable() // Weekly availability
      table.json('shift_preferences').nullable() // Preferred shifts
      table.boolean('can_work_overtime').defaultTo(false)
      table.boolean('can_supervise').defaultTo(false)
      
      // Training and certification
      table.json('certifications').nullable() // Food safety, etc.
      table.json('training_completed').nullable() // Training modules
      table.date('last_training_date').nullable()
      
      // Additional metadata
      table.json('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index('department_id')
      table.index('user_id')
      table.index('active')
      table.index('role')
      table.index('is_primary_department')
      table.index('skill_level')
      table.index(['department_id', 'active'])
      table.index(['user_id', 'active'])
      table.index(['department_id', 'role'])
      table.index(['department_id', 'is_primary_department'])
      
      // Unique constraint to prevent duplicate assignments
      table.unique(['department_id', 'user_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
