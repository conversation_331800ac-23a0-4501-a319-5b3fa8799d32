import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'notes'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add product_id column with foreign key constraint
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE').nullable()
      
      // Add index for performance
      table.index('product_id')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove index first
      table.dropIndex('product_id')
      
      // Drop the foreign key constraint and column
      table.dropColumn('product_id')
    })
  }
}
