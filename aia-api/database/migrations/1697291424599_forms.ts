import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'forms'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('name').notNullable()
      table.text('details').nullable()
      table.json('sections').notNullable()
      table.enum('action', ['Save', 'Send', 'Submit', 'Pay', 'SaveSend']).defaultTo('Save')
      table.string('link').nullable()
      table.json('image').nullable()
      table.json('auth').nullable()
      table.json('headers').nullable()
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE').nullable()
      table.boolean('validated').defaultTo(0)
      table.jsonb('meta').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
