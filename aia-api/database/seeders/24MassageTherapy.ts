import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class MassageTherapySeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-massage-therapy',
      name: 'Massage Therapy Template',
      description: 'Standard options for massage therapy services',
      serviceId: 'massage-therapy',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-massage-30min',
        serviceConfigurationId: config.id,
        name: '30 min',
        type: ServiceOptionType.DURATION,
        description: 'Short session',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-1hr',
        serviceConfigurationId: config.id,
        name: '1 hr',
        type: ServiceOptionType.DURATION,
        description: 'Standard session',
        priceAdjustment: 20,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-1-5hr',
        serviceConfigurationId: config.id,
        name: '1.5 hr',
        type: ServiceOptionType.DURATION,
        description: 'Extended session',
        priceAdjustment: 35,
        durationId: 'duration-1-5hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-2hr',
        serviceConfigurationId: config.id,
        name: '2 hr',
        type: ServiceOptionType.DURATION,
        description: 'Full session',
        priceAdjustment: 50,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-massage-junior',
        serviceConfigurationId: config.id,
        name: 'Junior Therapist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Junior therapist',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Therapist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior therapist',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-massage-aroma',
        serviceConfigurationId: config.id,
        name: 'Aromatherapy',
        type: ServiceOptionType.ADD_ON,
        description: 'Aromatherapy add-on',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-hotstones',
        serviceConfigurationId: config.id,
        name: 'Hot Stones',
        type: ServiceOptionType.ADD_ON,
        description: 'Hot stones add-on',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-deeptissue',
        serviceConfigurationId: config.id,
        name: 'Deep Tissue',
        type: ServiceOptionType.ADD_ON,
        description: 'Deep tissue massage',
        priceAdjustment: 18,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-couples',
        serviceConfigurationId: config.id,
        name: 'Couples Session',
        type: ServiceOptionType.ADD_ON,
        description: 'Session for two',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Room/Equipment
      {
        id: 'opt-massage-standardroom',
        serviceConfigurationId: config.id,
        name: 'Standard Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard massage room',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-viproom',
        serviceConfigurationId: config.id,
        name: 'VIP Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'VIP massage room',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-massage-specialequip',
        serviceConfigurationId: config.id,
        name: 'Special Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Special massage equipment',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
