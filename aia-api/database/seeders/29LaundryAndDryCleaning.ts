import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class LaundryAndDryCleaningSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-laundry-dryclean',
      name: 'Laundry & Dry Cleaning Template',
      description: 'Standard options for laundry and dry cleaning services',
      serviceId: 'laundry-dryclean',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-laundry-24hr',
        serviceConfigurationId: config.id,
        name: '24 Hour Turnaround',
        type: ServiceOptionType.DURATION,
        description: 'Standard 24 hour service',
        priceAdjustment: 0,
        durationId: 'duration-24hr',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-laundry-sameday',
        serviceConfigurationId: config.id,
        name: 'Same Day',
        type: ServiceOptionType.DURATION,
        description: 'Same day service',
        priceAdjustment: 20,
        durationId: 'duration-8hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Service Types
      {
        id: 'opt-laundry-washfold',
        serviceConfigurationId: config.id,
        name: 'Wash & Fold',
        type: ServiceOptionType.CUSTOM,
        description: 'Standard wash and fold',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-laundry-dryclean',
        serviceConfigurationId: config.id,
        name: 'Dry Cleaning',
        type: ServiceOptionType.CUSTOM,
        description: 'Dry cleaning service',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-laundry-ironing',
        serviceConfigurationId: config.id,
        name: 'Ironing',
        type: ServiceOptionType.CUSTOM,
        description: 'Ironing service',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-laundry-pickup',
        serviceConfigurationId: config.id,
        name: 'Pickup & Delivery',
        type: ServiceOptionType.ADD_ON,
        description: 'Pickup and delivery service',
        priceAdjustment: 8,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-laundry-stain',
        serviceConfigurationId: config.id,
        name: 'Stain Removal',
        type: ServiceOptionType.ADD_ON,
        description: 'Stain removal service',
        priceAdjustment: 5,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-laundry-standard',
        serviceConfigurationId: config.id,
        name: 'Standard Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard laundry equipment',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-laundry-premium',
        serviceConfigurationId: config.id,
        name: 'Premium Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium laundry equipment',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
