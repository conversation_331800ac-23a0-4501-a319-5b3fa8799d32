import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Department from 'App/Models/Department'
import ProductCategory from 'App/Models/ProductCategory'
import Vendor from 'App/Models/Vendor'
import Branch from 'App/Models/Branch'
import { ulid } from 'ulidx'

export default class RestaurantDepartmentSeederSeeder extends BaseSeeder {
  public async run() {
    // Default restaurant departments with their configurations
    const defaultDepartments = [
      {
        name: 'Kitchen',
        description: 'Main food preparation area for hot meals, cooking, and plating',
        category_keywords: ['food', 'meal', 'main', 'hot', 'cooked', 'fried', 'grilled', 'baked'],
        average_preparation_time: 15,
        max_concurrent_orders: 10,
        priority_level: 1,
        requires_special_equipment: true,
        equipment_required: ['stove', 'oven', 'grill', 'fryer'],
        skills_required: ['cooking', 'food_safety', 'knife_skills'],
        active: true,
      },
      {
        name: 'Cold Station',
        description: 'Preparation of salads, cold appetizers, desserts, and beverages',
        category_keywords: [
          'salad',
          'cold',
          'dessert',
          'beverage',
          'drink',
          'ice cream',
          'smoothie',
        ],
        average_preparation_time: 8,
        max_concurrent_orders: 15,
        priority_level: 2,
        requires_special_equipment: false,
        equipment_required: ['refrigerator', 'blender', 'ice_machine'],
        skills_required: ['food_presentation', 'beverage_preparation'],
        active: true,
      },
      {
        name: 'Grill Station',
        description: 'Specialized grilling and barbecue preparation',
        category_keywords: ['grill', 'bbq', 'barbecue', 'steak', 'burger', 'meat', 'chicken'],
        average_preparation_time: 12,
        max_concurrent_orders: 8,
        priority_level: 1,
        requires_special_equipment: true,
        equipment_required: ['grill', 'tongs', 'thermometer'],
        skills_required: ['grilling', 'meat_preparation', 'temperature_control'],
        active: true,
      },
      {
        name: 'Pastry & Bakery',
        description: 'Baking, pastries, bread, and specialized dessert preparation',
        category_keywords: ['pastry', 'bakery', 'bread', 'cake', 'cookie', 'muffin', 'croissant'],
        average_preparation_time: 25,
        max_concurrent_orders: 6,
        priority_level: 3,
        requires_special_equipment: true,
        equipment_required: ['oven', 'mixer', 'proofing_cabinet', 'scales'],
        skills_required: ['baking', 'pastry_arts', 'decoration'],
        active: true,
      },
      {
        name: 'Bar',
        description: 'Alcoholic and non-alcoholic beverage preparation and service',
        category_keywords: ['bar', 'cocktail', 'beer', 'wine', 'alcohol', 'mixed_drink', 'spirits'],
        average_preparation_time: 5,
        max_concurrent_orders: 20,
        priority_level: 2,
        requires_special_equipment: true,
        equipment_required: ['bar_tools', 'shaker', 'strainer', 'jigger'],
        skills_required: ['mixology', 'beverage_knowledge', 'customer_service'],
        active: true,
      },
      {
        name: 'Pizza Station',
        description: 'Pizza preparation, dough handling, and wood-fired oven operations',
        category_keywords: ['pizza', 'dough', 'wood_fired', 'italian', 'flatbread'],
        average_preparation_time: 18,
        max_concurrent_orders: 12,
        priority_level: 1,
        requires_special_equipment: true,
        equipment_required: ['pizza_oven', 'pizza_peel', 'dough_mixer'],
        skills_required: ['dough_handling', 'oven_operation', 'pizza_assembly'],
        active: true,
      },
    ]

    // Get all vendors to create departments for
    const vendors = await Vendor.query().preload('branches')

    for (const vendor of vendors) {
      console.log(`Creating departments for vendor: ${vendor.name}`)

      for (const branch of vendor.branches) {
        console.log(`  Creating departments for branch: ${branch.name}`)

        for (const deptConfig of defaultDepartments) {
          // Check if department already exists
          const existingDept = await Department.query()
            .where('vendor_id', vendor.id)
            .where('branch_id', branch.id)
            .where('name', deptConfig.name)
            .first()

          if (!existingDept) {
            const department = await Department.create({
              id: ulid().toLowerCase(),
              vendorId: vendor.id,
              branchId: branch.id,
              name: deptConfig.name,
              description: deptConfig.description,
              averagePreparationTime: deptConfig.average_preparation_time,
              maxConcurrentOrders: deptConfig.max_concurrent_orders,
              priorityLevel: deptConfig.priority_level,
              active: deptConfig.active,
              meta: {
                category_keywords: deptConfig.category_keywords,
                requires_special_equipment: deptConfig.requires_special_equipment,
                equipment_required: deptConfig.equipment_required,
                skills_required: deptConfig.skills_required,
                auto_assignment_rules: {
                  enabled: true,
                  priority_order: deptConfig.priority_level,
                  fallback_department: deptConfig.name === 'Kitchen' ? true : false,
                },
              },
            })

            console.log(`    ✅ Created department: ${department.name}`)
          } else {
            console.log(`    ⏭️  Department already exists: ${deptConfig.name}`)
          }
        }
      }
    }

    // Create category mappings for auto-assignment
    await this.createCategoryMappings()
  }

  private async createCategoryMappings() {
    console.log('Creating product category to department mappings...')

    const categoryMappings = [
      { categoryNames: ['Main Course', 'Entrees', 'Hot Food'], departmentName: 'Kitchen' },
      { categoryNames: ['Salads', 'Cold Appetizers', 'Desserts'], departmentName: 'Cold Station' },
      { categoryNames: ['Grilled Items', 'BBQ', 'Steaks'], departmentName: 'Grill Station' },
      { categoryNames: ['Pastries', 'Bakery', 'Bread'], departmentName: 'Pastry & Bakery' },
      { categoryNames: ['Beverages', 'Cocktails', 'Bar'], departmentName: 'Bar' },
      { categoryNames: ['Pizza', 'Italian'], departmentName: 'Pizza Station' },
    ]

    for (const mapping of categoryMappings) {
      for (const categoryName of mapping.categoryNames) {
        const categories = await ProductCategory.query().where('name', 'ILIKE', `%${categoryName}%`)

        for (const category of categories) {
          const departments = await Department.query().where('name', mapping.departmentName)

          for (const department of departments) {
            // Update department meta to include category mapping
            const currentMeta = department.meta || {}
            const categoryMappings = currentMeta.category_mappings || []

            if (!categoryMappings.includes(category.id)) {
              categoryMappings.push(category.id)

              await department
                .merge({
                  meta: {
                    ...currentMeta,
                    category_mappings: categoryMappings,
                  },
                })
                .save()

              console.log(
                `    ✅ Mapped category "${category.name}" to department "${department.name}"`
              )
            }
          }
        }
      }
    }
  }
}
