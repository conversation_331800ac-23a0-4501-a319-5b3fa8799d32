import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class CarWashAndDetailSeeder extends BaseSeeder {
  public async run () {
    // Create the service configuration template
    const config = await ServiceConfiguration.create({
      id: 'config-car-wash-detail',
      name: 'Car Wash & Detail Template',
      description: 'Standard options for car wash and detailing services',
      serviceId: 'car-wash-detail', // Adjust as needed for your service model
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    // Create options for the template
    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-carwash-express',
        serviceConfigurationId: config.id,
        name: 'Express (30 min)',
        type: 'duration',
        description: 'Quick exterior wash',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-standard',
        serviceConfigurationId: config.id,
        name: 'Standard (1 hr)',
        type: 'duration',
        description: 'Standard wash and interior vacuum',
        priceAdjustment: 10,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-premium',
        serviceConfigurationId: config.id,
        name: 'Premium (2 hr)',
        type: 'duration',
        description: 'Full detail with wax and engine cleaning',
        priceAdjustment: 30,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-carwash-1attendant',
        serviceConfigurationId: config.id,
        name: '1 Attendant',
        type: 'personnel',
        description: 'Single attendant for standard service',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-2attendants',
        serviceConfigurationId: config.id,
        name: '2 Attendants',
        type: 'personnel',
        description: 'Faster service with two attendants',
        priceAdjustment: 8,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-carwash-vacuum',
        serviceConfigurationId: config.id,
        name: 'Interior Vacuum',
        type: 'add_on',
        description: 'Thorough vacuuming of car interior',
        priceAdjustment: 5,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-wax',
        serviceConfigurationId: config.id,
        name: 'Wax',
        type: 'add_on',
        description: 'Protective wax coating',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-engine',
        serviceConfigurationId: config.id,
        name: 'Engine Cleaning',
        type: 'add_on',
        description: 'Engine bay cleaning',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-pet',
        serviceConfigurationId: config.id,
        name: 'Pet Hair Removal',
        type: 'add_on',
        description: 'Removes stubborn pet hair from seats and carpets',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-carwash-standard-equip',
        serviceConfigurationId: config.id,
        name: 'Standard Equipment',
        type: 'equipment',
        description: 'Standard cleaning tools and products',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-premium-equip',
        serviceConfigurationId: config.id,
        name: 'Premium Equipment',
        type: 'equipment',
        description: 'Premium tools and eco-friendly products',
        priceAdjustment: 7,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Location
      {
        id: 'opt-carwash-onsite',
        serviceConfigurationId: config.id,
        name: 'On-site',
        type: 'location',
        description: 'Service at our location',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-carwash-pickup',
        serviceConfigurationId: config.id,
        name: 'Pick-up & Drop-off',
        type: 'location',
        description: 'We pick up and return your car',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
} 