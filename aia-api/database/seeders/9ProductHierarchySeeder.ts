import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Database from '@ioc:Adonis/Lucid/Database'
import Task from 'App/Models/Task'
import Service from 'App/Models/Service'
import ProductType from 'App/Models/ProductType'
import ProductCategory from 'App/Models/ProductCategory'

interface ServiceData {
  task: string
  service: string
  product_type: string
  product_category: string
}

export default class ProductHierarchySeeder extends BaseSeeder {
  public async run() {
    // Raw data from CSV - Part 1
    const rawData: ServiceData[] = [
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Agrochemicals', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Animal Feed & Nutrition', product_category: 'Cattle Feed' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Animal Feed & Nutrition', product_category: 'Fish Feed' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Animal Feed & Nutrition', product_category: 'Goat & Sheep Feed' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Animal Feed & Nutrition', product_category: 'Poultry Feed' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Farm Tools & Equipment', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Livestock & Products', product_category: 'Cattle' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Livestock & Products', product_category: 'Goats' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Livestock & Products', product_category: 'Poultry' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Livestock & Products', product_category: 'Sheep' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Seeds & Planting Materials', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Agriculture, Livestock & Farming Supplies', product_type: 'Veterinary Supplies', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Beers & Ciders', product_category: 'Ale' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Beers & Ciders', product_category: 'Dry Cider' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Beers & Ciders', product_category: 'Lager' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Beers & Ciders', product_category: 'Stout' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Beers & Ciders', product_category: 'Sweet Cider' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Champagne', product_category: 'Aged Champagne' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Champagne', product_category: 'Grape Champagne' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Champagne', product_category: 'Sweet Champagne' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Non-Alcoholic Beverages & Mixers', product_category: 'Non Alcoholic Products' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Smoking and Vaping Products', product_category: 'E-cigarettes' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Smoking and Vaping Products', product_category: 'Tobacco Productz' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Brandy' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Gin' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Liqueurs' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Rum' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Tequila' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Vodka' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Spirits', product_category: 'Whiskey' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Wines', product_category: 'Red Wine' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Wines', product_category: 'Rose\' Wine' },
      { task: 'Order & Buy', service: 'Buy Alcohol & Tobacco', product_type: 'Wines', product_category: 'White Wine' },
      // Baby Products
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Bath & Skincare', product_category: 'Baby Bathtubs' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Bath & Skincare', product_category: 'Baby Soap & Shampoo' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Bath & Skincare', product_category: 'Lotions & Oils' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Bath & Skincare', product_category: 'Towels & Washcloths' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Clothing & Accessories', product_category: 'Baby Shoes' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Clothing & Accessories', product_category: 'Hats,Socks,Mittens' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Clothing & Accessories', product_category: 'Infant(3-12months)' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Clothing & Accessories', product_category: 'Newborn Clothing(0-3months)' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Clothing & Accessories', product_category: 'Toddler(1-3years)' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Diapers & Wipes', product_category: 'Baby Wipes' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Diapers & Wipes', product_category: 'Cloth Diapers' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Diapers & Wipes', product_category: 'Daiper Creams' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Diapers & Wipes', product_category: 'Disposable Diapers' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Feeding & Nursing', product_category: 'Baby Formular & Food' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Feeding & Nursing', product_category: 'Bottles & Nipples' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Feeding & Nursing', product_category: 'Breast Pumps' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Feeding & Nursing', product_category: 'High Chairs' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Health & Safety', product_category: 'Baby Monitors' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Health & Safety', product_category: 'Baby Proofing Kits' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Health & Safety', product_category: 'Nail Clippers' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Health & Safety', product_category: 'Thermometers' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Nursery & Bedding', product_category: 'Blankets & Swaddles' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Nursery & Bedding', product_category: 'Cribs & Bassinets' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Nursery & Bedding', product_category: 'Mattresses & Bed Sheets' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Nursery & Bedding', product_category: 'Mosquito Nets' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Toys & Learning', product_category: 'Activity Gyms' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Toys & Learning', product_category: 'Educational Toys' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Toys & Learning', product_category: 'Rattlers & Teethers' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Toys & Learning', product_category: 'Soft Plush Toys' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Travel & Gear', product_category: 'Baby Carriers' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Travel & Gear', product_category: 'Car Seats' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Travel & Gear', product_category: 'Daiper Bags' },
      { task: 'Order & Buy', service: 'Buy Baby Products & Essentials', product_type: 'Travel & Gear', product_category: 'Strollers' },
      // Beauty & Personal Care
      { task: 'Order & Buy', service: 'Buy Beauty & Personal Care', product_type: 'Personal Care & Grooming', product_category: '' },
      // Books & Magazines
      { task: 'Order & Buy', service: 'Buy Books & Magazines', product_type: '', product_category: '' },
      // Clothing & Apparel
      { task: 'Order & Buy', service: 'Buy Clothing & Apparel', product_type: 'Men\'s Wear', product_category: 'Men\'s Bags' },
      { task: 'Order & Buy', service: 'Buy Clothing & Apparel', product_type: 'Men\'s Wear', product_category: 'Street Wear' },
      { task: 'Order & Buy', service: 'Buy Clothing & Apparel', product_type: 'Unisex Fits', product_category: 'Sporty' },
      { task: 'Order & Buy', service: 'Buy Clothing & Apparel', product_type: 'Women\'s Wear', product_category: 'Street Wear' },
      { task: 'Order & Buy', service: 'Buy Clothing & Apparel', product_type: 'Women\'s Wear', product_category: 'Women\'s Bags' },
      // Electronics
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Computers & Accessories', product_category: 'Desktops' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Computers & Accessories', product_category: 'External Hard Drives & Storage' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Computers & Accessories', product_category: 'Laptops' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Computers & Accessories', product_category: 'Monitors' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Consumer Electronics', product_category: 'Home Theater Systems' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Consumer Electronics', product_category: 'Set Top Boxes' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Consumer Electronics', product_category: 'TV Sets' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Home & Kitchen Appliances', product_category: 'Cookers & Ovens' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Home & Kitchen Appliances', product_category: 'Fridges & Freezers' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Home & Kitchen Appliances', product_category: 'Microwaves' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Home & Kitchen Appliances', product_category: 'Washing Machines & Dishwashers' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Mobile Devices & Accessories', product_category: 'Mobile Internet Devices' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Mobile Devices & Accessories', product_category: 'Phone Accessories' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Mobile Devices & Accessories', product_category: 'Routers & Modems' },
      { task: 'Order & Buy', service: 'Buy Electronics', product_type: 'Mobile Devices & Accessories', product_category: 'Smartphones' },
      // Entertainment Tickets
      { task: 'Order & Buy', service: 'Buy Entertainment Ticket', product_type: 'Concerts', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Entertainment Ticket', product_type: 'Movies', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Entertainment Ticket', product_type: 'Parks & Entry Tickets', product_category: 'KWS Park Tickets' },
      { task: 'Order & Buy', service: 'Buy Entertainment Ticket', product_type: 'Stadiums', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Entertainment Ticket', product_type: 'Tour Tickets', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Entertainment Ticket', product_type: 'Trade Fairs & Exhibitions', product_category: '' },
      // Food & Drinks
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Bread' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Cakes' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Cookies & Biscuits' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Muffins & Donuts' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Pastries' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Pies & Tarts' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Baked Goods', product_category: 'Specialty & Ethnic Bakes' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Home-Made Food', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Meat & Seafood', product_category: 'Beef' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Meat & Seafood', product_category: 'Chicken' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Meat & Seafood', product_category: 'Fish' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Meat & Seafood', product_category: 'Goat Meat (Mbuzi)' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Meat & Seafood', product_category: 'Lamb & Mutton' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Meat & Seafood', product_category: 'Pork' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Packed & Frozen Foods', product_category: 'Packaged Combo Meals' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Packed & Frozen Foods', product_category: 'Ready Frozen Food' },
      // Restaurant Foods
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Beverages' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Combo Meals / Meal Deals' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Desserts' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Gluten-Free Options' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Kids Meals' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Main Courses / Entrees' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Mezzes & Platters' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Salads' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Side Dishes' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Soups' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Starters / Appetizers' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Restaurant Foods', product_category: 'Vegetarian Options' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'School Food', product_category: 'Daily School Meals' },
      { task: 'Order & Buy', service: 'Buy Food & Drinks', product_type: 'Supermarket Prepacked & Frozen', product_category: 'Freshly Baked Delights' },
      // Furniture & Décor
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Home Furniture & Decor', product_category: 'Bedroom Furniture' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Home Furniture & Decor', product_category: 'Home Decor' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Home Furniture & Decor', product_category: 'Kitchen & Dinning Furniture' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Home Furniture & Decor', product_category: 'Living Room Furniture' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Office Furniture & Decor', product_category: 'Filling Cabinets' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Office Furniture & Decor', product_category: 'Office Decor' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Office Furniture & Decor', product_category: 'Tables, Desks & Chairs' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Office Furniture & Decor', product_category: 'Work Stations' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Outdoor Furniture & Decor', product_category: 'Benches & Swing Seats' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Outdoor Furniture & Decor', product_category: 'Garden Chairs & Tables' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Outdoor Furniture & Decor', product_category: 'Outdoor Decor' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Outdoor Furniture & Decor', product_category: 'Outdoor Umbrellas' },
      { task: 'Order & Buy', service: 'Buy Furniture & Décor', product_type: 'Outdoor Furniture & Decor', product_category: 'Patio Sets' },
      // Gifts & Flowers
      { task: 'Order & Buy', service: 'Buy Gifts & Flowers', product_type: '', product_category: '' },
      // Groceries & Market Products
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Cereals, Legumes & Grains' },
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Cut Fruits' },
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Herbs & Spices' },
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Leafy, Stem & Root Vegetables' },
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Nuts & Seeds' },
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Tubers & Roots' },
      { task: 'Order & Buy', service: 'Buy Groceries & Market Products', product_type: 'Fresh Produce', product_category: 'Whole Fruits' },
      // Health & Wellness Products
      { task: 'Order & Buy', service: 'Buy Health & Wellness Products', product_type: 'Nutrional Suppliments', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Health & Wellness Products', product_type: 'Pharmaceutical Drugs', product_category: 'Chronic Illness Meds' },
      { task: 'Order & Buy', service: 'Buy Health & Wellness Products', product_type: 'Pharmaceutical Drugs', product_category: 'Over-the-Counter (OTC) Drugs' },
      { task: 'Order & Buy', service: 'Buy Health & Wellness Products', product_type: 'Pharmaceutical Drugs', product_category: 'Prescription Drugs' },
      // Insurance
      { task: 'Order & Buy', service: 'Buy Insurance', product_type: 'General Insurance', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Insurance', product_type: 'Health Insurance', product_category: 'Inpatient Cover' },
      { task: 'Order & Buy', service: 'Buy Insurance', product_type: 'Health Insurance', product_category: 'Maternity Cover' },
      { task: 'Order & Buy', service: 'Buy Insurance', product_type: 'Travel Insurance', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Insurance', product_type: 'Vehicle Insurance', product_category: 'Motobike Insurance' },
      { task: 'Order & Buy', service: 'Buy Insurance', product_type: 'Vehicle Insurance', product_category: 'Vehicle Policy' },
      // Motor Vehicle
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Auto Spares', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Motorbikes', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'New Cars ( Zero Millage)', product_category: 'New Cars' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Tractors', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Trucks & Lorries', product_category: 'Lorries' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Trucks & Lorries', product_category: 'Trailers' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Used Cars', product_category: 'Used Pick Up Trucks' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Used Cars', product_category: 'Used Sedans' },
      { task: 'Order & Buy', service: 'Buy Motor Vehicle', product_type: 'Used Cars', product_category: 'Used SUVs' },
      // Office Supplies
      { task: 'Order & Buy', service: 'Buy Office Supplies', product_type: '', product_category: '' },
      // Property
      { task: 'Order & Buy', service: 'Buy Property', product_type: 'Commercial Units', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Property', product_type: 'Land', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Property', product_type: 'Property & Real Estate', product_category: 'Property Purchase' },
      { task: 'Order & Buy', service: 'Buy Property', product_type: 'Residential Units', product_category: 'Appartments' },
      { task: 'Order & Buy', service: 'Buy Property', product_type: 'Residential Units', product_category: 'Bungalows' },
      // Sports & Fitness Gear
      { task: 'Order & Buy', service: 'Buy Sports & Fitness Gear', product_type: 'Football Gear', product_category: 'Academy Jerseys' },
      { task: 'Order & Buy', service: 'Buy Sports & Fitness Gear', product_type: 'Football Gear', product_category: 'Football Kits' },
      // Sports Wear
      { task: 'Order & Buy', service: 'Buy Sports Wear', product_type: 'Sports Wear', product_category: '' },
      // Toys & Games
      { task: 'Order & Buy', service: 'Buy Toys & Games', product_type: '', product_category: '' },
      // Travel Tickets
      { task: 'Order & Buy', service: 'Buy Travel Tickets', product_type: 'Bus Tickets', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Travel Tickets', product_type: 'Ferry Tickets', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Travel Tickets', product_type: 'Matatu', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Travel Tickets', product_type: 'Plane Tickets', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Travel Tickets', product_type: 'Shuttle Tickets', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Travel Tickets', product_type: 'Train Tickets', product_category: '' },
      // Vouchers & Coupons
      { task: 'Order & Buy', service: 'Buy Vouchers & Coupons', product_type: 'Airtime', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Vouchers & Coupons', product_type: 'Electricity Tokens', product_category: '' },
      { task: 'Order & Buy', service: 'Buy Vouchers & Coupons', product_type: 'Internet Bundle', product_category: 'Airtel Bundles' },
      { task: 'Order & Buy', service: 'Buy Vouchers & Coupons', product_type: 'Internet Bundle', product_category: 'Safaricom Bundles' },
    ]

    // Start transaction
    const trx = await Database.transaction()

    try {
      // Create or find the "Order & Buy" task
      let orderBuyTask = await Task.query({ client: trx })
        .where('name', 'Order & Buy')
        .first()

      if (!orderBuyTask) {
        orderBuyTask = await Task.create({
          name: 'Order & Buy',
          active: true
        }, { client: trx })
        console.log('Created task: Order & Buy')
      }

      // Group data by service to process efficiently
      const serviceGroups = this.groupByService(rawData)

      for (const [serviceName, serviceData] of Object.entries(serviceGroups)) {
        console.log(`Processing service: ${serviceName}`)
        
        // Create or find service
        let service = await Service.query({ client: trx })
          .where('name', serviceName)
          .where('task_id', orderBuyTask.id)
          .first()

        if (!service) {
          service = await Service.create({
            name: serviceName,
            taskId: orderBuyTask.id,
            active: true,
            order: 0,
            featured: false
          }, { client: trx })
          console.log(`Created service: ${serviceName}`)
        }

        // Group by product type
        const productTypeGroups = this.groupByProductType(serviceData as ServiceData[])

        for (const [productTypeName, productTypeData] of Object.entries(productTypeGroups)) {
          console.log(`  Processing product type: ${productTypeName}`)
          
          // Create or find product type
          let productType = await ProductType.query({ client: trx })
            .where('name', productTypeName)
            .where('service_id', service.id)
            .first()

          if (!productType) {
            productType = await ProductType.create({
              name: productTypeName,
              serviceId: service.id,
              details: undefined,
            }, { client: trx })
            console.log(`    Created product type: ${productTypeName}`)
          }

          // Process product categories
          const categories = (productTypeData as ServiceData[])
            .map(item => item.product_category)
            .filter((category): category is string => category !== undefined && category.trim() !== '')

          for (const categoryName of [...new Set(categories)]) {
            console.log(`    Processing product category: ${categoryName}`)
            
            // Create or find product category
            let productCategory = await ProductCategory.query({ client: trx })
              .where('name', categoryName)
              .where('product_type_id', productType.id)
              .first()

            if (!productCategory) {
              productCategory = await ProductCategory.create({
                name: categoryName,
                productTypeId: productType.id,
                details: undefined,
              }, { client: trx })
              console.log(`      Created product category: ${categoryName}`)
            }
          }
        }
      }

      await trx.commit()
      console.log('Product hierarchy seeding completed successfully!')

    } catch (error) {
      await trx.rollback()
      console.error('Error seeding product hierarchy:', error)
      throw error
    }
  }

  private groupByService(data: ServiceData[]) {
    return data.reduce<Record<string, ServiceData[]>>((groups, item) => {
      const service = item.service
      if (!groups[service]) {
        groups[service] = []
      }
      groups[service].push(item)
      return groups
    }, {})
  }

  private groupByProductType(data: ServiceData[]) {
    return data.reduce<Record<string, ServiceData[]>>((groups, item) => {
      const productType = item.product_type
      if (!groups[productType]) {
        groups[productType] = []
      }
      groups[productType].push(item)
      return groups
    }, {})
  }
}