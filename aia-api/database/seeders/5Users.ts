import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User from 'App/Models/User'

export default class extends BaseSeeder {
  public async run() {
    const epic = await User.create({
      firstName: 'Epic',
      lastName: 'App',
      phone: '254700555000',
      email: '<EMAIL>',
      password: '254700555000',
    })

    await epic.assignRole('admin')

    const gideon = await User.create({
      firstName: '<PERSON>',
      lastName: 'Omodho',
      phone: '254721216388',
      email: '<EMAIL>',
      password: '254721216388',
    })

    await gideon.assignRole('admin')

    const mauko = await User.create({
      firstName: 'Mauko',
      lastName: 'Maunde',
      phone: '254705459494',
      email: '<EMAIL>',
      password: '254705459494',
    })

    await mauko.assignRole('admin')
  }
}
