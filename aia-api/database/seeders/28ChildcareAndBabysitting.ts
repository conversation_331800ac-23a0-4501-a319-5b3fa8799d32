import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class ChildcareAndBabysittingSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-childcare-babysitting',
      name: 'Childcare & Babysitting Template',
      description: 'Standard options for childcare and babysitting services',
      serviceId: 'childcare-babysitting',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-childcare-1hr',
        serviceConfigurationId: config.id,
        name: '1 Hour',
        type: ServiceOptionType.DURATION,
        description: '1 hour session',
        priceAdjustment: 0,
        durationId: 'duration-1hr',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-2hr',
        serviceConfigurationId: config.id,
        name: '2 Hours',
        type: ServiceOptionType.DURATION,
        description: '2 hour session',
        priceAdjustment: 15,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-4hr',
        serviceConfigurationId: config.id,
        name: '4 Hours',
        type: ServiceOptionType.DURATION,
        description: '4 hour session',
        priceAdjustment: 30,
        durationId: 'duration-4hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-childcare-babysitter',
        serviceConfigurationId: config.id,
        name: 'Babysitter',
        type: ServiceOptionType.PERSONNEL,
        description: 'Babysitter',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-nanny',
        serviceConfigurationId: config.id,
        name: 'Nanny',
        type: ServiceOptionType.PERSONNEL,
        description: 'Nanny',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-specialist',
        serviceConfigurationId: config.id,
        name: 'Childcare Specialist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Specialist for children with special needs',
        priceAdjustment: 35,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-childcare-meal',
        serviceConfigurationId: config.id,
        name: 'Meal Preparation',
        type: ServiceOptionType.ADD_ON,
        description: 'Meal preparation for children',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-transport',
        serviceConfigurationId: config.id,
        name: 'Transport',
        type: ServiceOptionType.ADD_ON,
        description: 'Transport for children',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-overnight',
        serviceConfigurationId: config.id,
        name: 'Overnight Care',
        type: ServiceOptionType.ADD_ON,
        description: 'Overnight babysitting',
        priceAdjustment: 40,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-childcare-standardkit',
        serviceConfigurationId: config.id,
        name: 'Standard Kit',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard childcare kit',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-premiumkit',
        serviceConfigurationId: config.id,
        name: 'Premium Kit',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium childcare kit',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-childcare-safety',
        serviceConfigurationId: config.id,
        name: 'Safety Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Safety equipment for children',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
