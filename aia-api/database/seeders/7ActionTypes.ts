import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ActionType from 'App/Models/ActionType'

export default class extends BaseSeeder {
  public async run() {
    // Route Button Action Type
    await ActionType.create({
      type: 'route_button',
      name: 'Route Button',
      description: 'Button that navigates to a specific route with various actions',
      configSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: [
              'buy',
              'view',
              'apply',
              'book',
              'order',
              'schedule',
              'report',
              'Purchase',
              'Booking',
              'Registration',
              'Access',
              'Process',
              'Reserve',
              'Application',
              'Query',
              'Reservation',
              'Prequalification'
            ],
            description: 'The action to perform'
          },
          route: {
            type: 'string',
            description: 'Route path (e.g., /vendors/{id}, /product/{id})'
          },
          button_text: {
            type: 'string',
            description: 'Text to display on the button'
          },
          params: {
            type: 'object',
            description: 'Route parameters to be filled in'
          },
          object_type: {
            type: 'string',
            enum: ['product', 'service', 'vendor', 'order'],
            description: 'Type of the target object'
          },
          delivery: {
            type: 'string',
            enum: ['Takeaway', 'Dinein', 'Delivery', 'Selfpick'],
            description: 'Delivery method (if applicable)'
          },
          type: {
            type: 'string',
            enum: ['Preorder', 'Instant'],
            description: 'Order type (if applicable)'
          }
        },
        required: ['action', 'route', 'button_text']
      },
      defaultConfig: {
        button_text: 'Proceed',
        type: 'Instant',
        delivery: 'Delivery'
      },
      isActive: true
    })

    // Payment Action Type
    await ActionType.create({
      type: 'payment_action',
      name: 'Payment Action',
      description: 'Button that initiates a payment process',
      configSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['Purchase', 'Booking', 'Registration', 'Access', 'Process'],
            description: 'The type of action being paid for'
          },
          payment_method: {
            type: 'string',
            enum: ['Mpesa', 'Cash', 'Card', 'Wallet', 'Bank', 'Cheque', 'Online'],
            description: 'The payment method to use'
          },
          amount: {
            type: 'number',
            description: 'The amount to be paid'
          },
          order_id: {
            type: 'string',
            description: 'The ID of the order being paid for'
          },
          vendor_id: {
            type: 'string',
            description: 'The ID of the vendor'
          },
          button_text: {
            type: 'string',
            description: 'Text to display on the button'
          },
          phone: {
            type: 'string',
            description: 'Phone number for M-Pesa payments'
          }
        },
        required: ['action', 'payment_method', 'amount', 'order_id', 'vendor_id']
      },
      defaultConfig: {
        button_text: 'Pay Now'
      },
      isActive: true
    })
  }
} 