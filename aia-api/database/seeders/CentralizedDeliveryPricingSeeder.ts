import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import DeliveryPricingConfig from 'App/Models/DeliveryPricingConfig'
import DeliveryPricingTier from 'App/Models/DeliveryPricingTier'

export default class CentralizedDeliveryPricingSeeder extends BaseSeeder {
  public async run() {
    // Create Standard Motorcycle Delivery (Default)
    const standardConfig = await DeliveryPricingConfig.create({
      id: 'standard-motorcycle-delivery',
      name: 'Standard Delivery',
      description: 'Affordable motorcycle delivery for most orders',
      vehicleType: 'motorcycle',
      isActive: true,
      isDefault: true,
      priority: 1,
      
      // Base pricing (matches current system)
      basePrice: 200,
      pricePerKm: 25,
      minimumFee: 150,
      maximumFee: 1000,
      
      // Distance constraints
      maxDistanceKm: 25,
      minDistanceKm: 0,
      
      // Order constraints
      minimumOrderValue: 0,
      freeDeliveryThreshold: 3000,
      
      // Time estimates
      baseTimeMinutes: 30,
      timePerKmMinutes: 3,
      preparationBufferMinutes: 10,
      
      // Availability
      availableOnHolidays: true,
      
      adminNotes: 'Default delivery option for most vendors'
    })

    // Create Express Car Delivery
    const expressConfig = await DeliveryPricingConfig.create({
      id: 'express-car-delivery',
      name: 'Express Delivery',
      description: 'Fast car delivery for urgent orders',
      vehicleType: 'car',
      isActive: true,
      isDefault: false,
      priority: 2,
      
      // Higher pricing for express service
      basePrice: 300,
      pricePerKm: 40,
      minimumFee: 250,
      maximumFee: 1500,
      
      // Distance constraints
      maxDistanceKm: 30,
      minDistanceKm: 0,
      
      // Order constraints
      minimumOrderValue: 500, // Higher minimum for express
      freeDeliveryThreshold: 5000,
      
      // Faster time estimates
      baseTimeMinutes: 20,
      timePerKmMinutes: 2,
      preparationBufferMinutes: 5,
      
      // Availability
      availableOnHolidays: true,
      
      adminNotes: 'Premium express delivery option'
    })

    // Create Economy Bicycle Delivery
    const economyConfig = await DeliveryPricingConfig.create({
      id: 'economy-bicycle-delivery',
      name: 'Economy Delivery',
      description: 'Budget-friendly bicycle delivery for nearby locations',
      vehicleType: 'bicycle',
      isActive: true,
      isDefault: false,
      priority: 3,
      
      // Lower pricing for economy service
      basePrice: 100,
      pricePerKm: 15,
      minimumFee: 80,
      maximumFee: 400,
      
      // Limited distance for bicycles
      maxDistanceKm: 8,
      minDistanceKm: 0,
      
      // Order constraints
      minimumOrderValue: 0,
      freeDeliveryThreshold: 2000,
      
      // Slower time estimates
      baseTimeMinutes: 45,
      timePerKmMinutes: 5,
      preparationBufferMinutes: 15,
      
      // Availability
      availableOnHolidays: false, // Limited availability
      
      adminNotes: 'Budget option for short distances'
    })

    // Create tiered pricing for Standard Delivery
    await DeliveryPricingTier.createMany([
      {
        id: 'standard-tier-1',
        deliveryPricingConfigId: standardConfig.id,
        tierName: 'Zone 1 (0-5km)',
        distanceFromKm: 0,
        distanceToKm: 5,
        basePrice: 150,
        pricePerKm: 20,
        flatRate: null,
        baseTimeMinutes: 25,
        timePerKmMinutes: 2.5,
        minimumFee: 150,
        maximumFee: null,
        isActive: true,
        priority: 1
      },
      {
        id: 'standard-tier-2',
        deliveryPricingConfigId: standardConfig.id,
        tierName: 'Zone 2 (5-15km)',
        distanceFromKm: 5,
        distanceToKm: 15,
        basePrice: 200,
        pricePerKm: 25,
        flatRate: null,
        baseTimeMinutes: 30,
        timePerKmMinutes: 3,
        minimumFee: 200,
        maximumFee: null,
        isActive: true,
        priority: 2
      },
      {
        id: 'standard-tier-3',
        deliveryPricingConfigId: standardConfig.id,
        tierName: 'Zone 3 (15-25km)',
        distanceFromKm: 15,
        distanceToKm: 25,
        basePrice: 300,
        pricePerKm: 35,
        flatRate: null,
        baseTimeMinutes: 45,
        timePerKmMinutes: 4,
        minimumFee: 300,
        maximumFee: 1000,
        isActive: true,
        priority: 3
      }
    ])

    // Create tiered pricing for Express Delivery
    await DeliveryPricingTier.createMany([
      {
        id: 'express-tier-1',
        deliveryPricingConfigId: expressConfig.id,
        tierName: 'Express Zone 1 (0-10km)',
        distanceFromKm: 0,
        distanceToKm: 10,
        basePrice: 250,
        pricePerKm: 30,
        flatRate: null,
        baseTimeMinutes: 15,
        timePerKmMinutes: 1.5,
        minimumFee: 250,
        maximumFee: null,
        isActive: true,
        priority: 1
      },
      {
        id: 'express-tier-2',
        deliveryPricingConfigId: expressConfig.id,
        tierName: 'Express Zone 2 (10-30km)',
        distanceFromKm: 10,
        distanceToKm: 30,
        basePrice: 400,
        pricePerKm: 50,
        flatRate: null,
        baseTimeMinutes: 25,
        timePerKmMinutes: 2,
        minimumFee: 400,
        maximumFee: 1500,
        isActive: true,
        priority: 2
      }
    ])

    console.log('✅ Centralized delivery pricing configurations seeded successfully')
  }
}
