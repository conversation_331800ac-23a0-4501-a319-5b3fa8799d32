import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ChargeConfiguration from 'App/Models/ChargeConfiguration'

export default class CreateKFCDeliveryChargesSeeder extends BaseSeeder {
  public async run() {
    const kfcVendorId = '01js9a10hy5aj1c4m3j06vxx0z'

    // Create delivery charge configuration for KFC
    const deliveryCharge = {
      name: 'Delivery Charge',
      type: 'delivery',
      amount: 100.00, // 100 KES delivery charge
      percentage_rate: null,
      is_active: true,
      is_mandatory: true,
      vendor_id: kfcVendorId,
      priority: 2,
      sort_order: 2,
      conditions: JSON.stringify({
        delivery_types: ['Delivery'],
        min_order_amount: 0
      }),
      is_tax: false,
    }

    await ChargeConfiguration.create(deliveryCharge)

    console.log('✅ Created delivery charge configuration for KFC vendor')
  }
}
