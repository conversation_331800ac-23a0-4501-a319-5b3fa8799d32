import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class WomenHairStylistSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-women-hair-stylist',
      name: 'Women Hair Stylist Template',
      description: 'Standard options for women hair styling services',
      serviceId: 'women-hair-stylist',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-hair-blowdry',
        serviceConfigurationId: config.id,
        name: 'Blow Dry (30 min)',
        type: ServiceOptionType.DURATION,
        description: 'Quick blow dry',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-cut',
        serviceConfigurationId: config.id,
        name: 'Cut (1 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Haircut',
        priceAdjustment: 20,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-color',
        serviceConfigurationId: config.id,
        name: 'Color (2 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Coloring service',
        priceAdjustment: 50,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-fulltreat',
        serviceConfigurationId: config.id,
        name: 'Full Treatment (3 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Full hair treatment',
        priceAdjustment: 80,
        durationId: 'duration-3hr',
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-hair-stylist',
        serviceConfigurationId: config.id,
        name: 'Stylist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Stylist',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Stylist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior stylist',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-colorist',
        serviceConfigurationId: config.id,
        name: 'Colorist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Color specialist',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-hair-treatment',
        serviceConfigurationId: config.id,
        name: 'Hair Treatment',
        type: ServiceOptionType.ADD_ON,
        description: 'Special hair treatment',
        priceAdjustment: 18,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-scalp',
        serviceConfigurationId: config.id,
        name: 'Scalp Massage',
        type: ServiceOptionType.ADD_ON,
        description: 'Relaxing scalp massage',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-extensions',
        serviceConfigurationId: config.id,
        name: 'Extensions',
        type: ServiceOptionType.ADD_ON,
        description: 'Hair extensions',
        priceAdjustment: 40,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-bridal',
        serviceConfigurationId: config.id,
        name: 'Bridal Styling',
        type: ServiceOptionType.ADD_ON,
        description: 'Special bridal styling',
        priceAdjustment: 60,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-hair-standard-equip',
        serviceConfigurationId: config.id,
        name: 'Standard Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard styling tools',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-hair-premium-equip',
        serviceConfigurationId: config.id,
        name: 'Premium Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium tools and products',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
