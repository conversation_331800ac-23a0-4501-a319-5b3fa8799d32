import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class MedicalAndHealthServiceSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-medical-health',
      name: 'Medical & Health Services Template',
      description: 'Standard options for medical and health services',
      serviceId: 'medical-health',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-medical-consult',
        serviceConfigurationId: config.id,
        name: 'Consultation (30 min)',
        type: ServiceOptionType.DURATION,
        description: 'Consultation session',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-standard',
        serviceConfigurationId: config.id,
        name: 'Standard Appointment (1 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Standard appointment',
        priceAdjustment: 25,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-extended',
        serviceConfigurationId: config.id,
        name: 'Extended (2 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Extended appointment',
        priceAdjustment: 50,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-medical-nurse',
        serviceConfigurationId: config.id,
        name: 'Nurse',
        type: ServiceOptionType.PERSONNEL,
        description: 'Nurse',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-doctor',
        serviceConfigurationId: config.id,
        name: 'Doctor',
        type: ServiceOptionType.PERSONNEL,
        description: 'Doctor',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-specialist',
        serviceConfigurationId: config.id,
        name: 'Specialist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Specialist',
        priceAdjustment: 50,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-medical-lab',
        serviceConfigurationId: config.id,
        name: 'Lab Tests',
        type: ServiceOptionType.ADD_ON,
        description: 'Lab tests',
        priceAdjustment: 40,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-vaccine',
        serviceConfigurationId: config.id,
        name: 'Vaccination',
        type: ServiceOptionType.ADD_ON,
        description: 'Vaccination service',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-homevisit',
        serviceConfigurationId: config.id,
        name: 'Home Visit',
        type: ServiceOptionType.ADD_ON,
        description: 'Home visit by medical staff',
        priceAdjustment: 60,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-prescription',
        serviceConfigurationId: config.id,
        name: 'Prescription Delivery',
        type: ServiceOptionType.ADD_ON,
        description: 'Prescription delivery service',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Room/Equipment
      {
        id: 'opt-medical-standardroom',
        serviceConfigurationId: config.id,
        name: 'Standard Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard medical room',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-privateroom',
        serviceConfigurationId: config.id,
        name: 'Private Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Private medical room',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-medical-specialequip',
        serviceConfigurationId: config.id,
        name: 'Special Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Special medical equipment',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
