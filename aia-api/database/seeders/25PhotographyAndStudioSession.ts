import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class PhotographyAndStudioSessionSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-photography-studio',
      name: 'Photography & Studio Sessions Template',
      description: 'Standard options for photography and studio sessions',
      serviceId: 'photography-studio',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-photo-mini',
        serviceConfigurationId: config.id,
        name: 'Mini Session (30 min)',
        type: ServiceOptionType.DURATION,
        description: 'Mini photo session',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-standard',
        serviceConfigurationId: config.id,
        name: 'Standard (1 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Standard photo session',
        priceAdjustment: 30,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-extended',
        serviceConfigurationId: config.id,
        name: 'Extended (2 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Extended photo session',
        priceAdjustment: 60,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-fullday',
        serviceConfigurationId: config.id,
        name: 'Full Day (8 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Full day photo session',
        priceAdjustment: 200,
        durationId: 'duration-8hr',
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-photo-photographer',
        serviceConfigurationId: config.id,
        name: 'Photographer',
        type: ServiceOptionType.PERSONNEL,
        description: 'Photographer',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Photographer',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior photographer',
        priceAdjustment: 40,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-assistant',
        serviceConfigurationId: config.id,
        name: 'Assistant',
        type: ServiceOptionType.PERSONNEL,
        description: 'Photography assistant',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-photo-prints',
        serviceConfigurationId: config.id,
        name: 'Extra Prints',
        type: ServiceOptionType.ADD_ON,
        description: 'Extra photo prints',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-edits',
        serviceConfigurationId: config.id,
        name: 'Digital Edits',
        type: ServiceOptionType.ADD_ON,
        description: 'Digital photo editing',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-location',
        serviceConfigurationId: config.id,
        name: 'On-location',
        type: ServiceOptionType.ADD_ON,
        description: 'On-location photo shoot',
        priceAdjustment: 50,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-props',
        serviceConfigurationId: config.id,
        name: 'Props Rental',
        type: ServiceOptionType.ADD_ON,
        description: 'Props rental for session',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-photo-standard-equip',
        serviceConfigurationId: config.id,
        name: 'Standard Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard photography equipment',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-premium-equip',
        serviceConfigurationId: config.id,
        name: 'Premium Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium photography equipment',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-photo-lighting',
        serviceConfigurationId: config.id,
        name: 'Lighting Kit',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Professional lighting kit',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 14,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
