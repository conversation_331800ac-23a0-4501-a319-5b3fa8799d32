import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class BeautyCareSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-beauty-care',
      name: 'Beauty Care Template',
      description: 'Standard options for beauty care services',
      serviceId: 'beauty-care',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-beauty-manicure',
        serviceConfigurationId: config.id,
        name: 'Manicure (30 min)',
        type: 'duration',
        description: 'Manicure session',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-pedicure',
        serviceConfigurationId: config.id,
        name: 'Pedicure (1 hr)',
        type: 'duration',
        description: 'Pedicure session',
        priceAdjustment: 15,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-facial',
        serviceConfigurationId: config.id,
        name: 'Facial (1.5 hr)',
        type: 'duration',
        description: 'Facial treatment',
        priceAdjustment: 30,
        durationId: 'duration-1-5hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-fullpackage',
        serviceConfigurationId: config.id,
        name: 'Full Package (2 hr)',
        type: 'duration',
        description: 'Full beauty care package',
        priceAdjustment: 50,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-beauty-technician',
        serviceConfigurationId: config.id,
        name: 'Technician',
        type: 'personnel',
        description: 'Beauty technician',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Technician',
        type: 'personnel',
        description: 'Senior beauty technician',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-beauty-gel',
        serviceConfigurationId: config.id,
        name: 'Gel Polish',
        type: 'add_on',
        description: 'Gel polish for nails',
        priceAdjustment: 8,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-nailart',
        serviceConfigurationId: config.id,
        name: 'Nail Art',
        type: 'add_on',
        description: 'Decorative nail art',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-paraffin',
        serviceConfigurationId: config.id,
        name: 'Paraffin',
        type: 'add_on',
        description: 'Paraffin wax treatment',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-eyebrow',
        serviceConfigurationId: config.id,
        name: 'Eyebrow Shaping',
        type: 'add_on',
        description: 'Eyebrow shaping and styling',
        priceAdjustment: 7,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Room/Equipment
      {
        id: 'opt-beauty-standardroom',
        serviceConfigurationId: config.id,
        name: 'Standard',
        type: 'equipment',
        description: 'Standard beauty care room',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-beauty-vip',
        serviceConfigurationId: config.id,
        name: 'VIP',
        type: 'equipment',
        description: 'VIP beauty care room',
        priceAdjustment: 18,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
