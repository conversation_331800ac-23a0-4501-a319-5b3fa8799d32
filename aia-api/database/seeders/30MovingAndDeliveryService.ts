import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class MovingAndDeliveryServiceSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-moving-delivery',
      name: 'Moving & Delivery Service Template',
      description: 'Standard options for moving and delivery services',
      serviceId: 'moving-delivery',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-moving-2hr',
        serviceConfigurationId: config.id,
        name: '2 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Short move',
        priceAdjustment: 0,
        durationId: 'duration-2hr',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-4hr',
        serviceConfigurationId: config.id,
        name: '4 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Half-day move',
        priceAdjustment: 40,
        durationId: 'duration-4hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-8hr',
        serviceConfigurationId: config.id,
        name: '8 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Full-day move',
        priceAdjustment: 90,
        durationId: 'duration-8hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Service Types
      {
        id: 'opt-moving-local',
        serviceConfigurationId: config.id,
        name: 'Local Move',
        type: ServiceOptionType.CUSTOM,
        description: 'Move within city',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-longdistance',
        serviceConfigurationId: config.id,
        name: 'Long Distance',
        type: ServiceOptionType.CUSTOM,
        description: 'Long distance move',
        priceAdjustment: 60,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-moving-packing',
        serviceConfigurationId: config.id,
        name: 'Packing Service',
        type: ServiceOptionType.ADD_ON,
        description: 'Packing and unpacking',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-storage',
        serviceConfigurationId: config.id,
        name: 'Storage',
        type: ServiceOptionType.ADD_ON,
        description: 'Temporary storage',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-insurance',
        serviceConfigurationId: config.id,
        name: 'Insurance',
        type: ServiceOptionType.ADD_ON,
        description: 'Insurance for goods',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment/Vehicles
      {
        id: 'opt-moving-standardtruck',
        serviceConfigurationId: config.id,
        name: 'Standard Truck',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard moving truck',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-largevan',
        serviceConfigurationId: config.id,
        name: 'Large Van',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Large van for moving',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-moving-specialequip',
        serviceConfigurationId: config.id,
        name: 'Special Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Special moving equipment',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
