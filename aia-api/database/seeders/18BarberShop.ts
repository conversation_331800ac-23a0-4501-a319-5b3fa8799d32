import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class BarberShopSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-barber-shop',
      name: 'Barber Shop Template',
      description: 'Standard options for barber shop services',
      serviceId: 'barber-shop',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-barber-express',
        serviceConfigurationId: config.id,
        name: 'Express (15 min)',
        type: ServiceOptionType.DURATION,
        description: 'Quick cut or trim',
        priceAdjustment: 0,
        durationId: 'duration-15min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-standard',
        serviceConfigurationId: config.id,
        name: 'Standard (30 min)',
        type: ServiceOptionType.DURATION,
        description: 'Standard haircut',
        priceAdjustment: 10,
        durationId: 'duration-30min',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-deluxe',
        serviceConfigurationId: config.id,
        name: 'Deluxe (45 min)',
        type: ServiceOptionType.DURATION,
        description: 'Deluxe service with extras',
        priceAdjustment: 20,
        durationId: 'duration-45min',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-barber-junior',
        serviceConfigurationId: config.id,
        name: 'Junior Barber',
        type: ServiceOptionType.PERSONNEL,
        description: 'Junior barber',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Barber',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior barber',
        priceAdjustment: 8,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-barber-beard',
        serviceConfigurationId: config.id,
        name: 'Beard Trim',
        type: ServiceOptionType.ADD_ON,
        description: 'Beard trimming',
        priceAdjustment: 5,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-towel',
        serviceConfigurationId: config.id,
        name: 'Hot Towel',
        type: ServiceOptionType.ADD_ON,
        description: 'Hot towel treatment',
        priceAdjustment: 4,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-wash',
        serviceConfigurationId: config.id,
        name: 'Hair Wash',
        type: ServiceOptionType.ADD_ON,
        description: 'Hair washing',
        priceAdjustment: 6,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-kids',
        serviceConfigurationId: config.id,
        name: 'Kids Cut',
        type: ServiceOptionType.ADD_ON,
        description: "Children's haircut",
        priceAdjustment: 3,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-barber-standard-equip',
        serviceConfigurationId: config.id,
        name: 'Standard Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard barber tools',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-barber-premium-equip',
        serviceConfigurationId: config.id,
        name: 'Premium Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium tools and products',
        priceAdjustment: 7,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
