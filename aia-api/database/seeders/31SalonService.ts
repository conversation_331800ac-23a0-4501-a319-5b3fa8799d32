import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class SalonServiceSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-salon',
      name: 'Salon Service Template',
      description: 'Standard options for salon services',
      serviceId: 'salon',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-salon-30min',
        serviceConfigurationId: config.id,
        name: '30 Minutes',
        type: ServiceOptionType.DURATION,
        description: 'Quick service',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-1hr',
        serviceConfigurationId: config.id,
        name: '1 Hour',
        type: ServiceOptionType.DURATION,
        description: 'Standard service',
        priceAdjustment: 20,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-2hr',
        serviceConfigurationId: config.id,
        name: '2 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Extended service',
        priceAdjustment: 40,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-salon-junior',
        serviceConfigurationId: config.id,
        name: 'Junior Stylist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Junior stylist',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Stylist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior stylist',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-colorist',
        serviceConfigurationId: config.id,
        name: 'Colorist',
        type: ServiceOptionType.PERSONNEL,
        description: 'Hair color specialist',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-salon-treatment',
        serviceConfigurationId: config.id,
        name: 'Hair Treatment',
        type: ServiceOptionType.ADD_ON,
        description: 'Special hair treatment',
        priceAdjustment: 18,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-scalpmassage',
        serviceConfigurationId: config.id,
        name: 'Scalp Massage',
        type: ServiceOptionType.ADD_ON,
        description: 'Relaxing scalp massage',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-blowdry',
        serviceConfigurationId: config.id,
        name: 'Blow Dry',
        type: ServiceOptionType.ADD_ON,
        description: 'Professional blow dry',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-styling',
        serviceConfigurationId: config.id,
        name: 'Styling',
        type: ServiceOptionType.ADD_ON,
        description: 'Hair styling',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-salon-chair',
        serviceConfigurationId: config.id,
        name: 'Standard Chair',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard salon chair',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-viproom',
        serviceConfigurationId: config.id,
        name: 'VIP Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Private VIP room',
        priceAdjustment: 25,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-salon-specialequip',
        serviceConfigurationId: config.id,
        name: 'Special Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Special salon equipment',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
} 