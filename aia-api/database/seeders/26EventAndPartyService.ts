import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class EventAndPartyServiceSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-event-party',
      name: 'Event & Party Service Template',
      description: 'Standard options for event and party services',
      serviceId: 'event-party',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-event-2hr',
        serviceConfigurationId: config.id,
        name: '2 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Short event',
        priceAdjustment: 0,
        durationId: 'duration-2hr',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-4hr',
        serviceConfigurationId: config.id,
        name: '4 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Half-day event',
        priceAdjustment: 50,
        durationId: 'duration-4hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-8hr',
        serviceConfigurationId: config.id,
        name: '8 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Full-day event',
        priceAdjustment: 120,
        durationId: 'duration-8hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-event-coordinator',
        serviceConfigurationId: config.id,
        name: 'Event Coordinator',
        type: ServiceOptionType.PERSONNEL,
        description: 'Event coordinator',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-staff',
        serviceConfigurationId: config.id,
        name: 'Support Staff',
        type: ServiceOptionType.PERSONNEL,
        description: 'Support staff for event',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-security',
        serviceConfigurationId: config.id,
        name: 'Security Personnel',
        type: ServiceOptionType.PERSONNEL,
        description: 'Security for event',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-event-catering',
        serviceConfigurationId: config.id,
        name: 'Catering',
        type: ServiceOptionType.ADD_ON,
        description: 'Catering services',
        priceAdjustment: 100,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-decoration',
        serviceConfigurationId: config.id,
        name: 'Decoration',
        type: ServiceOptionType.ADD_ON,
        description: 'Event decoration',
        priceAdjustment: 60,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-photography',
        serviceConfigurationId: config.id,
        name: 'Photography',
        type: ServiceOptionType.ADD_ON,
        description: 'Event photography',
        priceAdjustment: 80,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-music',
        serviceConfigurationId: config.id,
        name: 'Music & DJ',
        type: ServiceOptionType.ADD_ON,
        description: 'Music and DJ services',
        priceAdjustment: 70,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment/Venue
      {
        id: 'opt-event-standardvenue',
        serviceConfigurationId: config.id,
        name: 'Standard Venue',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard event venue',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-premiumvenue',
        serviceConfigurationId: config.id,
        name: 'Premium Venue',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium event venue',
        priceAdjustment: 150,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-event-av',
        serviceConfigurationId: config.id,
        name: 'A/V Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Audio/Visual equipment',
        priceAdjustment: 40,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
