import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class HomeCleaningAndMaidServiceSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-home-cleaning-maid',
      name: 'Home Cleaning & Maid Services Template',
      description: 'Standard options for home cleaning and maid services',
      serviceId: 'home-cleaning-maid',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-cleaning-quick',
        serviceConfigurationId: config.id,
        name: 'Quick Clean (1 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Quick cleaning session',
        priceAdjustment: 0,
        durationId: 'duration-1hr',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-standard',
        serviceConfigurationId: config.id,
        name: 'Standard Clean (2 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Standard cleaning session',
        priceAdjustment: 20,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-deep',
        serviceConfigurationId: config.id,
        name: 'Deep Clean (4 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Deep cleaning session',
        priceAdjustment: 50,
        durationId: 'duration-4hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-fullday',
        serviceConfigurationId: config.id,
        name: 'Full Day (8 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Full day cleaning',
        priceAdjustment: 100,
        durationId: 'duration-8hr',
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-cleaning-1cleaner',
        serviceConfigurationId: config.id,
        name: '1 Cleaner',
        type: ServiceOptionType.PERSONNEL,
        description: 'Single cleaner',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-2cleaners',
        serviceConfigurationId: config.id,
        name: '2 Cleaners',
        type: ServiceOptionType.PERSONNEL,
        description: 'Two cleaners',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-team',
        serviceConfigurationId: config.id,
        name: 'Team (3+)',
        type: ServiceOptionType.PERSONNEL,
        description: 'Team of three or more',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-cleaning-window',
        serviceConfigurationId: config.id,
        name: 'Window Cleaning',
        type: ServiceOptionType.ADD_ON,
        description: 'Window cleaning service',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-carpet',
        serviceConfigurationId: config.id,
        name: 'Carpet Cleaning',
        type: ServiceOptionType.ADD_ON,
        description: 'Carpet cleaning service',
        priceAdjustment: 18,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-fridge',
        serviceConfigurationId: config.id,
        name: 'Fridge/Oven Cleaning',
        type: ServiceOptionType.ADD_ON,
        description: 'Fridge and oven cleaning',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-laundry',
        serviceConfigurationId: config.id,
        name: 'Laundry',
        type: ServiceOptionType.ADD_ON,
        description: 'Laundry service',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-cleaning-basic-kit',
        serviceConfigurationId: config.id,
        name: 'Basic Kit',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Basic cleaning kit',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-premium-kit',
        serviceConfigurationId: config.id,
        name: 'Premium Kit',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium cleaning kit',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-cleaning-eco',
        serviceConfigurationId: config.id,
        name: 'Eco-Friendly Supplies',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Eco-friendly cleaning supplies',
        priceAdjustment: 8,
        durationId: null,
        isDefault: false,
        sortOrder: 14,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
