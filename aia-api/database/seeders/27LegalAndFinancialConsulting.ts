import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class LegalAndFinancialConsultingSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-legal-financial',
      name: 'Legal & Financial Consulting Template',
      description: 'Standard options for legal and financial consulting services',
      serviceId: 'legal-financial',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-legal-30min',
        serviceConfigurationId: config.id,
        name: '30 Minutes',
        type: ServiceOptionType.DURATION,
        description: 'Short consultation',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-1hr',
        serviceConfigurationId: config.id,
        name: '1 Hour',
        type: ServiceOptionType.DURATION,
        description: 'Standard consultation',
        priceAdjustment: 40,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-2hr',
        serviceConfigurationId: config.id,
        name: '2 Hours',
        type: ServiceOptionType.DURATION,
        description: 'Extended consultation',
        priceAdjustment: 75,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-legal-consultant',
        serviceConfigurationId: config.id,
        name: 'Consultant',
        type: ServiceOptionType.PERSONNEL,
        description: 'Consultant',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Consultant',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior consultant',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-partner',
        serviceConfigurationId: config.id,
        name: 'Partner',
        type: ServiceOptionType.PERSONNEL,
        description: 'Partner/Principal',
        priceAdjustment: 60,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-legal-docreview',
        serviceConfigurationId: config.id,
        name: 'Document Review',
        type: ServiceOptionType.ADD_ON,
        description: 'Review of legal/financial documents',
        priceAdjustment: 35,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-contractdraft',
        serviceConfigurationId: config.id,
        name: 'Contract Drafting',
        type: ServiceOptionType.ADD_ON,
        description: 'Drafting of contracts/agreements',
        priceAdjustment: 50,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-court',
        serviceConfigurationId: config.id,
        name: 'Court Representation',
        type: ServiceOptionType.ADD_ON,
        description: 'Representation in court',
        priceAdjustment: 120,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Room/Equipment
      {
        id: 'opt-legal-standardroom',
        serviceConfigurationId: config.id,
        name: 'Standard Meeting Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard meeting room',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-premiumroom',
        serviceConfigurationId: config.id,
        name: 'Premium Meeting Room',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium meeting room',
        priceAdjustment: 40,
        durationId: null,
        isDefault: false,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-legal-virtual',
        serviceConfigurationId: config.id,
        name: 'Virtual Session',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Virtual/online session',
        priceAdjustment: -10,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
