import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Database from '@ioc:Adonis/Lucid/Database'
import DepartmentCategoryMapping from 'App/Models/DepartmentCategoryMapping'

export default class extends BaseSeeder {
  public async run() {
    // Clear existing mappings
    await DepartmentCategoryMapping.truncate()

    // Get all vendors with departments
    const vendorsWithDepartments = await Database
      .from('departments')
      .select('vendor_id')
      .distinct()
      .where('active', true)

    for (const { vendor_id } of vendorsWithDepartments) {
      await this.createMappingsForVendor(vendor_id)
    }
  }

  private async createMappingsForVendor(vendorId: string) {
    // Get departments for this vendor
    const departments = await Database
      .from('departments')
      .select('id', 'name')
      .where('vendor_id', vendorId)
      .where('active', true)

    // Get product categories that have products for this vendor
    const categories = await Database
      .from('product_categories')
      .select('product_categories.id', 'product_categories.name')
      .join('products', 'products.product_category_id', 'product_categories.id')
      .where('products.vendor_id', vendorId)
      .where('products.active', true)
      .distinct()

    // Create mappings based on category names and department names
    for (const category of categories) {
      const departmentId = this.determineDepartmentForCategory(category.name, departments)
      
      if (departmentId) {
        await DepartmentCategoryMapping.createMapping(
          departmentId,
          category.id,
          this.getPriorityForCategory(category.name)
        )
      }
    }
  }

  private determineDepartmentForCategory(categoryName: string, departments: any[]): string | null {
    const categoryLower = categoryName.toLowerCase()

    // Food-related categories go to Kitchen
    const foodKeywords = [
      'food', 'main', 'course', 'seafood', 'meat', 'chicken', 'beef', 'fish', 
      'ugali', 'rice', 'pasta', 'soup', 'stew', 'grilled', 'fried', 'baked',
      'appetizer', 'starter', 'entree', 'special', 'traditional', 'local',
      'breakfast', 'lunch', 'dinner', 'snack', 'side'
    ]

    // Beverage categories go to Bar
    const beverageKeywords = [
      'drink', 'beverage', 'beer', 'wine', 'cocktail', 'juice', 'soda',
      'coffee', 'tea', 'water', 'alcohol', 'spirit', 'liquor', 'bar'
    ]

    // Dessert categories go to Kitchen (or dedicated Pastry if available)
    const dessertKeywords = [
      'dessert', 'sweet', 'cake', 'ice cream', 'pastry', 'cookie', 'pie'
    ]

    // Service-related categories go to Service department
    const serviceKeywords = [
      'service', 'account', 'registration', 'booking', 'reservation'
    ]

    // Find Kitchen department
    const kitchenDept = departments.find(d => 
      d.name.toLowerCase().includes('kitchen') || 
      d.name.toLowerCase().includes('food')
    )

    // Find Bar department
    const barDept = departments.find(d => 
      d.name.toLowerCase().includes('bar') || 
      d.name.toLowerCase().includes('beverage')
    )

    // Find Service department
    const serviceDept = departments.find(d => 
      d.name.toLowerCase().includes('service') || 
      d.name.toLowerCase().includes('front')
    )

    // Find Pastry department (if available)
    const pastryDept = departments.find(d => 
      d.name.toLowerCase().includes('pastry') || 
      d.name.toLowerCase().includes('dessert')
    )

    // Match categories to departments
    if (foodKeywords.some(keyword => categoryLower.includes(keyword))) {
      return kitchenDept?.id || null
    }

    if (beverageKeywords.some(keyword => categoryLower.includes(keyword))) {
      return barDept?.id || kitchenDept?.id || null
    }

    if (dessertKeywords.some(keyword => categoryLower.includes(keyword))) {
      return pastryDept?.id || kitchenDept?.id || null
    }

    if (serviceKeywords.some(keyword => categoryLower.includes(keyword))) {
      return serviceDept?.id || null
    }

    // Default to Kitchen for food vendors, Service for others
    return kitchenDept?.id || serviceDept?.id || departments[0]?.id || null
  }

  private getPriorityForCategory(categoryName: string): number {
    const categoryLower = categoryName.toLowerCase()

    // High priority for main courses and popular items
    if (categoryLower.includes('main') || categoryLower.includes('special')) {
      return 1
    }

    // Medium priority for appetizers and sides
    if (categoryLower.includes('appetizer') || categoryLower.includes('side')) {
      return 2
    }

    // Lower priority for beverages and desserts
    if (categoryLower.includes('beverage') || categoryLower.includes('dessert')) {
      return 3
    }

    // Default priority
    return 2
  }
}
