import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import PackagingOption from 'App/Models/PackagingOption'

export default class CreateKFCPackagingOptionsSeeder extends BaseSeeder {
  public async run() {
    const kfcVendorId = '01js9a10hy5aj1c4m3j06vxx0z'

    // Create packaging options for KFC
    const packagingOptions = [
      {
        name: 'Standard Food Container',
        description: 'Standard container for hot food items',
        price: 25.00,
        vendor_id: kfcVendorId,
        active: true,
      },
      {
        name: 'Insulated Bag',
        description: 'Insulated bag for delivery orders',
        price: 50.00,
        vendor_id: kfcVendorId,
        active: true,
      },
      {
        name: 'Eco-Friendly Container',
        description: 'Biodegradable container for environmentally conscious customers',
        price: 35.00,
        vendor_id: kfcVendorId,
        active: true,
      }
    ]

    for (const option of packagingOptions) {
      await PackagingOption.create(option)
    }

    console.log('✅ Created packaging options for KFC vendor')
  }
}
