import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import { DateTime } from 'luxon'

export default class AutoServiceSeeder extends BaseSeeder {
  public async run () {
    const config = await ServiceConfiguration.create({
      id: 'config-auto-service',
      name: 'Auto Services Template',
      description: 'Standard options for auto services',
      serviceId: 'auto-service',
      active: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    })

    await ServiceConfigurationOption.createMany([
      // Durations
      {
        id: 'opt-auto-inspection',
        serviceConfigurationId: config.id,
        name: 'Inspection (30 min)',
        type: ServiceOptionType.DURATION,
        description: 'Vehicle inspection',
        priceAdjustment: 0,
        durationId: 'duration-30min',
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-oilchange',
        serviceConfigurationId: config.id,
        name: 'Oil Change (1 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Oil change service',
        priceAdjustment: 15,
        durationId: 'duration-1hr',
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-brake',
        serviceConfigurationId: config.id,
        name: 'Brake Service (2 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Brake service',
        priceAdjustment: 40,
        durationId: 'duration-2hr',
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-major',
        serviceConfigurationId: config.id,
        name: 'Major Repair (4 hr)',
        type: ServiceOptionType.DURATION,
        description: 'Major repair service',
        priceAdjustment: 100,
        durationId: 'duration-4hr',
        isDefault: false,
        sortOrder: 4,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Personnel
      {
        id: 'opt-auto-mechanic',
        serviceConfigurationId: config.id,
        name: 'Mechanic',
        type: ServiceOptionType.PERSONNEL,
        description: 'Mechanic',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 5,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-senior',
        serviceConfigurationId: config.id,
        name: 'Senior Mechanic',
        type: ServiceOptionType.PERSONNEL,
        description: 'Senior mechanic',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 6,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Add-ons
      {
        id: 'opt-auto-tire',
        serviceConfigurationId: config.id,
        name: 'Tire Rotation',
        type: ServiceOptionType.ADD_ON,
        description: 'Tire rotation service',
        priceAdjustment: 10,
        durationId: null,
        isDefault: false,
        sortOrder: 7,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-ac',
        serviceConfigurationId: config.id,
        name: 'AC Check',
        type: ServiceOptionType.ADD_ON,
        description: 'Air conditioning check',
        priceAdjustment: 12,
        durationId: null,
        isDefault: false,
        sortOrder: 8,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-battery',
        serviceConfigurationId: config.id,
        name: 'Battery Replacement',
        type: ServiceOptionType.ADD_ON,
        description: 'Battery replacement',
        priceAdjustment: 30,
        durationId: null,
        isDefault: false,
        sortOrder: 9,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-detail',
        serviceConfigurationId: config.id,
        name: 'Detailing',
        type: ServiceOptionType.ADD_ON,
        description: 'Vehicle detailing',
        priceAdjustment: 50,
        durationId: null,
        isDefault: false,
        sortOrder: 10,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      // Equipment
      {
        id: 'opt-auto-standard-equip',
        serviceConfigurationId: config.id,
        name: 'Standard Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Standard tools',
        priceAdjustment: 0,
        durationId: null,
        isDefault: true,
        sortOrder: 11,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-premium-equip',
        serviceConfigurationId: config.id,
        name: 'Premium Equipment',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Premium tools',
        priceAdjustment: 15,
        durationId: null,
        isDefault: false,
        sortOrder: 12,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'opt-auto-diagnostic',
        serviceConfigurationId: config.id,
        name: 'Diagnostic Tools',
        type: ServiceOptionType.EQUIPMENT,
        description: 'Diagnostic equipment',
        priceAdjustment: 20,
        durationId: null,
        isDefault: false,
        sortOrder: 13,
        constraints: {},
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
