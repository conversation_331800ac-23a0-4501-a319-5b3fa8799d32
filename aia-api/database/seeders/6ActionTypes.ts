import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ActionType from 'App/Models/ActionType'

export default class extends BaseSeeder {
  public async run() {
    // Basic Action Type
    await ActionType.create({
      type: 'basic_action',
      name: 'Basic Action',
      description: 'Simple action with a single task',
      configSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['buy', 'view', 'apply', 'book', 'order', 'schedule', 'report'],
            description: 'The action to perform'
          },
          object_id: {
            type: 'number',
            description: 'ID of the target object (product, service, etc.)'
          },
          object_type: {
            type: 'string',
            enum: ['product', 'service', 'vendor', 'order'],
            description: 'Type of the target object'
          },
          button_text: {
            type: 'string',
            description: 'Text to display on the button'
          }
        },
        required: ['action', 'object_id', 'object_type']
      },
      defaultConfig: {
        button_text: 'Proceed'
      },
      isActive: true
    })

    // Response Action Type
    await ActionType.create({
      type: 'response_action',
      name: 'Response Action',
      description: 'Simple yes/no or multiple choice response',
      configSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['confirm', 'remind', 'schedule', 'report'],
            description: 'The response action'
          },
          message: {
            type: 'string',
            description: 'Message to display'
          }
        },
        required: ['action', 'message']
      },
      defaultConfig: {
        message: 'Would you like to proceed?'
      },
      isActive: true
    })

    // Link Action Type
    await ActionType.create({
      type: 'link_action',
      name: 'Link Action',
      description: 'Simple link to a specific page or resource',
      configSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['view', 'browse', 'list'],
            description: 'The link action'
          },
          path: {
            type: 'string',
            description: 'Path to the resource (e.g., /products/123)'
          },
          button_text: {
            type: 'string',
            description: 'Text to display on the button'
          }
        },
        required: ['action', 'path']
      },
      defaultConfig: {
        button_text: 'View'
      },
      isActive: true
    })

    // Yes/No Action Type
    await ActionType.create({
      type: 'yes_no_action',
      name: 'Yes/No Action',
      description: 'Simple yes/no response with custom message',
      configSchema: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            description: 'Question or message to display'
          },
          yes_text: {
            type: 'string',
            description: 'Text for yes button',
            default: 'Yes'
          },
          no_text: {
            type: 'string',
            description: 'Text for no button',
            default: 'No'
          }
        },
        required: ['message']
      },
      defaultConfig: {
        yes_text: 'Yes',
        no_text: 'No'
      },
      isActive: true
    })

    // Vendor Subscribe Action Type
    await ActionType.create({
      type: 'vendor_subscribe',
      name: 'Vendor Subscribe',
      description: 'Subscribe to vendor notifications and updates',
      configSchema: {
        type: 'object',
        properties: {
          vendor_id: {
            type: 'number',
            description: 'ID of the vendor to subscribe to'
          },
          notification_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['new_products', 'promotions', 'updates', 'all']
            },
            description: 'Types of notifications to receive'
          },
          button_text: {
            type: 'string',
            description: 'Text to display on the button'
          }
        },
        required: ['vendor_id', 'notification_types']
      },
      defaultConfig: {
        notification_types: ['all'],
        button_text: 'Subscribe to Updates'
      },
      isActive: true
    })
  }
} 