import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Duration from 'App/Models/Duration'
import { DateTime } from 'luxon'

export default class DurationSeeder extends BaseSeeder {
  public async run () {
    await Duration.createMany([
      {
        id: 'duration-15min',
        name: 'Express (15 min)',
        description: 'Quick express service, ideal for very short appointments.',
        minutes: 15,
        bufferMinutes: 5,
        category: 'short',
        maxConcurrent: 10,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 1,
          maxPerDay: 12,
          timeSlots: [],
          blackoutDays: []
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 1,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-30min',
        name: 'Quick (30 min)',
        description: 'Standard quick service, suitable for most short tasks.',
        minutes: 30,
        bufferMinutes: 10,
        category: 'short',
        maxConcurrent: 8,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 2,
          maxPerDay: 10,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: []
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 1,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-45min',
        name: 'Standard (45 min)',
        description: 'Standard appointment for moderate-length services.',
        minutes: 45,
        bufferMinutes: 15,
        category: 'short',
        maxConcurrent: 6,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 2,
          maxPerDay: 8,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: []
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 1,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-1hr',
        name: 'Standard (1 hour)',
        description: 'Most common duration for standard services.',
        minutes: 60,
        bufferMinutes: 15,
        category: 'medium',
        maxConcurrent: 5,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 2,
          maxPerDay: 8,
          timeSlots: ['morning', 'afternoon', 'evening'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 1,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-1-5hr',
        name: 'Extended (1.5 hours)',
        description: 'For services that require a bit more time than standard.',
        minutes: 90,
        bufferMinutes: 15,
        category: 'medium',
        maxConcurrent: 4,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 2,
          maxPerDay: 6,
          timeSlots: ['morning', 'afternoon', 'evening'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 1,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-2hr',
        name: 'Deep Clean/Consultation (2 hours)',
        description: 'Ideal for deep cleaning or in-depth consultations.',
        minutes: 120,
        bufferMinutes: 20,
        category: 'medium',
        maxConcurrent: 3,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 4,
          maxPerDay: 5,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 2,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-3hr',
        name: 'Premium (3 hours)',
        description: 'Premium or complex services requiring more time.',
        minutes: 180,
        bufferMinutes: 30,
        category: 'medium',
        maxConcurrent: 2,
        allowsBackToBack: false,
        requiredBreakAfter: 15,
        schedulingRules: {
          minAdvanceHours: 4,
          maxPerDay: 3,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 2,
          equipmentRequired: []
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-4hr',
        name: 'Half Day (4 hours)',
        description: 'Half-day service, suitable for large jobs.',
        minutes: 240,
        bufferMinutes: 30,
        category: 'long',
        maxConcurrent: 2,
        allowsBackToBack: false,
        requiredBreakAfter: 30,
        schedulingRules: {
          minAdvanceHours: 6,
          maxPerDay: 2,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 2,
          equipmentRequired: ['basic-kit']
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-6hr',
        name: 'Extended (6 hours)',
        description: 'Extended service for very large or complex jobs.',
        minutes: 360,
        bufferMinutes: 30,
        category: 'long',
        maxConcurrent: 1,
        allowsBackToBack: false,
        requiredBreakAfter: 60,
        schedulingRules: {
          minAdvanceHours: 12,
          maxPerDay: 1,
          timeSlots: ['morning'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 3,
          equipmentRequired: ['premium-kit']
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-8hr',
        name: 'Full Day (8 hours)',
        description: 'Full-day service, blocks the calendar for the entire day.',
        minutes: 480,
        bufferMinutes: 60,
        category: 'full-day',
        maxConcurrent: 1,
        allowsBackToBack: false,
        requiredBreakAfter: 60,
        schedulingRules: {
          minAdvanceHours: 24,
          maxPerDay: 1,
          timeSlots: ['morning'],
          blackoutDays: ['sunday', 'holiday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 3,
          equipmentRequired: ['premium-kit']
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-12hr',
        name: 'Extended Day (12 hours)',
        description: 'For all-day or special event services.',
        minutes: 720,
        bufferMinutes: 60,
        category: 'full-day',
        maxConcurrent: 1,
        allowsBackToBack: false,
        requiredBreakAfter: 120,
        schedulingRules: {
          minAdvanceHours: 24,
          maxPerDay: 1,
          timeSlots: ['morning'],
          blackoutDays: ['sunday', 'holiday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 4,
          equipmentRequired: ['premium-kit', 'vehicle']
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-overnight',
        name: 'Overnight',
        description: 'Overnight service, e.g., 10pm–6am.',
        minutes: 480,
        bufferMinutes: 30,
        category: 'full-day',
        maxConcurrent: 1,
        allowsBackToBack: false,
        requiredBreakAfter: 120,
        schedulingRules: {
          minAdvanceHours: 24,
          maxPerDay: 1,
          timeSlots: ['night'],
          blackoutDays: ['saturday', 'sunday']
        },
        branchConstraints: {
          respectBranchHours: false,
          staffRequired: 2,
          equipmentRequired: ['night-kit']
        },
        active: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
      {
        id: 'duration-custom',
        name: 'Custom',
        description: 'Custom duration for admin or special use.',
        minutes: 0,
        bufferMinutes: 0,
        category: 'short',
        maxConcurrent: 1,
        allowsBackToBack: true,
        requiredBreakAfter: 0,
        schedulingRules: {
          minAdvanceHours: 0,
          maxPerDay: 100,
          timeSlots: [],
          blackoutDays: []
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 0,
          equipmentRequired: []
        },
        active: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      },
    ])
  }
}
