import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Database from '@ioc:Adonis/Lucid/Database'

export default class FixProductPackagingOptionsSeeder extends BaseSeeder {
  public async run() {
    // Associate products with packaging options from the same vendor
    await Database.rawQuery(`
      INSERT INTO product_packaging_options (product_id, packaging_option_id)
      SELECT DISTINCT p.id as product_id, po.id as packaging_option_id
      FROM products p
      JOIN packaging_options po ON po.vendor_id = p.vendor_id
      WHERE po.active = true 
        AND p.active = true 
        AND p.status = 'Published'
        AND NOT EXISTS (
          SELECT 1 FROM product_packaging_options ppo 
          WHERE ppo.product_id = p.id AND ppo.packaging_option_id = po.id
        )
    `)

    console.log('✅ Associated products with packaging options from their vendors')
  }
}
