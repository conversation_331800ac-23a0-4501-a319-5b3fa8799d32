/**
 * Contract source: https://git.io/JfefG
 *
 * Feel free to let us know via PR, if you find something broken in this contract
 * file.
 */

declare module '@ioc:Adonis/Core/Event' {
  /*
  |--------------------------------------------------------------------------
  | Define typed events
  |--------------------------------------------------------------------------
  |
  | You can define types for events inside the following interface and
  | AdonisJS will make sure that all listeners and emit calls adheres
  | to the defined types.
  |
  | For example:
  |
  | interface EventsList {
  |   'new:user': UserModel
  | }
  |
  | Now calling `Event.emit('new:user')` will statically ensure that passed value is
  | an instance of the the UserModel only.
  |
  */
  interface EventsList {
    'lot:created': { lot: import('App/Models/Lot').default }
    'qr:generated': {
      lotId: string
      qrCodeId?: string
      tableNumber: string
      vendorId: string
      success: boolean
      error?: string
      isAutoGenerated: boolean
    }
    'qr:generation:failed': {
      lotId: string
      vendorId: string
      error: string
      isAutoGenerated: boolean
      finalFailure: boolean
    }
    'qr:bulk:completed': {
      vendorId: string
      totalLots: number
      successCount: number
      errorCount: number
      sectionIds?: string[]
      branchIds?: string[]
      lotIds?: string[]
    }
    'qr:bulk:failed': {
      vendorId: string
      error: string
      sectionIds?: string[]
      branchIds?: string[]
      lotIds?: string[]
      finalFailure?: boolean
    }
  }
}
