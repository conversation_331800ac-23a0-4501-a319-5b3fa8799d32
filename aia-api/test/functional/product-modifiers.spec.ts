import { test } from '@japa/runner'
import Database from '@ioc:Adonis/Lucid/Database'
import Product from 'App/Models/Product'
import ModifierOption from 'App/Models/ModifierOption'
import Vendor from 'App/Models/Vendor'
import User from 'App/Models/User'

test.group('Product Modifiers API', (group) => {
  let product: Product
  let modifierOption: ModifierOption
  let vendor: Vendor
  let user: User
  let authToken: string

  group.setup(async () => {
    // Create test data
    vendor = await Vendor.create({
      name: 'Test Vendor',
      email: '<EMAIL>',
      phone: '+1234567890',
      active: true,
    })

    user = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+1234567890',
      password: 'password123',
    })

    product = await Product.create({
      name: 'Test Product',
      details: 'Test product for modifier testing',
      price: 10.00,
      active: true,
      status: 'Published',
      visibility: 'Public',
      vendorId: vendor.id,
      userId: user.id,
      productCategoryId: 'test-category-id', // This would need to be a real category in actual tests
      branchId: 'test-branch-id', // This would need to be a real branch in actual tests
      serviceId: 'test-service-id', // This would need to be a real service in actual tests
    })

    modifierOption = await ModifierOption.create({
      vendorId: vendor.id,
      name: 'Extra Cheese',
      type: 'extra',
      description: 'Add extra cheese',
      defaultPriceAdjustment: 2.50,
      active: true,
    })

    // Generate auth token (simplified - in real tests you'd use proper auth)
    authToken = 'Bearer test-token'
  })

  group.teardown(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('GET /v1/products/:id/modifiers - should list product modifiers', async ({ client }) => {
    // First attach a modifier to the product
    await product.related('availableModifiers').attach({
      [modifierOption.id]: {
        price_adjustment_override: 3.00,
        is_default: true,
        sort_order: 1,
      }
    })

    const response = await client
      .get(`/v1/products/${product.id}/modifiers`)
      .header('Authorization', authToken)

    response.assertStatus(200)
    response.assertBodyContains({
      data: {
        extra: [
          {
            id: modifierOption.id,
            name: 'Extra Cheese',
            type: 'extra',
            priceAdjustment: 3.00,
            isDefault: true,
            sortOrder: 1,
          }
        ]
      }
    })
  })

  test('POST /v1/products/:id/modifiers - should attach modifier to product', async ({ client }) => {
    const response = await client
      .post(`/v1/products/${product.id}/modifiers`)
      .header('Authorization', authToken)
      .json({
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: 2.75,
        isDefault: false,
        sortOrder: 2,
      })

    response.assertStatus(201)
    response.assertBodyContains({
      success: true,
      data: {
        productId: product.id,
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: 2.75,
        isDefault: false,
        sortOrder: 2,
      }
    })

    // Verify the relationship was created
    const attachedModifiers = await product
      .related('availableModifiers')
      .query()
      .where('modifier_option_id', modifierOption.id)

    assert.lengthOf(attachedModifiers, 1)
    assert.equal(attachedModifiers[0].$extras.price_adjustment_override, 2.75)
  })

  test('POST /v1/products/:id/modifiers - should return 409 when modifier already attached', async ({ client }) => {
    // First attach the modifier
    await product.related('availableModifiers').attach({
      [modifierOption.id]: {
        price_adjustment_override: 2.50,
        is_default: false,
        sort_order: 1,
      }
    })

    // Try to attach the same modifier again
    const response = await client
      .post(`/v1/products/${product.id}/modifiers`)
      .header('Authorization', authToken)
      .json({
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: 3.00,
      })

    response.assertStatus(409)
    response.assertBodyContains({
      message: 'Modifier already attached to product'
    })
  })

  test('PUT /v1/products/:id/modifiers/:modifierId - should update modifier relationship', async ({ client }) => {
    // First attach a modifier
    await product.related('availableModifiers').attach({
      [modifierOption.id]: {
        price_adjustment_override: 2.50,
        is_default: false,
        sort_order: 1,
      }
    })

    const response = await client
      .put(`/v1/products/${product.id}/modifiers/${modifierOption.id}`)
      .header('Authorization', authToken)
      .json({
        priceAdjustmentOverride: 3.50,
        isDefault: true,
        sortOrder: 5,
      })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      data: {
        productId: product.id,
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: 3.50,
        isDefault: true,
        sortOrder: 5,
      }
    })
  })

  test('DELETE /v1/products/:id/modifiers/:modifierId - should detach modifier from product', async ({ client }) => {
    // First attach a modifier
    await product.related('availableModifiers').attach({
      [modifierOption.id]: {
        price_adjustment_override: 2.50,
        is_default: false,
        sort_order: 1,
      }
    })

    const response = await client
      .delete(`/v1/products/${product.id}/modifiers/${modifierOption.id}`)
      .header('Authorization', authToken)

    response.assertStatus(204)

    // Verify the relationship was removed
    const attachedModifiers = await product
      .related('availableModifiers')
      .query()
      .where('modifier_option_id', modifierOption.id)

    assert.lengthOf(attachedModifiers, 0)
  })

  test('should return 404 for non-existent product', async ({ client }) => {
    const response = await client
      .get('/v1/products/non-existent-id/modifiers')
      .header('Authorization', authToken)

    response.assertStatus(404)
    response.assertBodyContains({
      message: 'Product not found'
    })
  })

  test('should return 404 for non-existent modifier', async ({ client }) => {
    const response = await client
      .post(`/v1/products/${product.id}/modifiers`)
      .header('Authorization', authToken)
      .json({
        modifierOptionId: 'non-existent-modifier-id',
        priceAdjustmentOverride: 2.50,
      })

    response.assertStatus(404)
    response.assertBodyContains({
      message: 'Product or modifier not found'
    })
  })

  test('should validate required fields', async ({ client }) => {
    const response = await client
      .post(`/v1/products/${product.id}/modifiers`)
      .header('Authorization', authToken)
      .json({
        // Missing modifierOptionId
        priceAdjustmentOverride: 2.50,
      })

    response.assertStatus(422)
    response.assertBodyContains({
      message: 'Validation failed',
      errors: {
        modifierOptionId: ['Modifier option ID is required']
      }
    })
  })

  test('should validate price adjustment range', async ({ client }) => {
    const response = await client
      .post(`/v1/products/${product.id}/modifiers`)
      .header('Authorization', authToken)
      .json({
        modifierOptionId: modifierOption.id,
        priceAdjustmentOverride: 1000.00, // Exceeds max range
      })

    response.assertStatus(422)
    response.assertBodyContains({
      message: 'Validation failed',
      errors: {
        priceAdjustmentOverride: ['Price adjustment must be between -999.99 and 999.99']
      }
    })
  })
})
